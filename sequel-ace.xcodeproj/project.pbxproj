// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		1141A389117BBFF200126A28 /* SPTableCopy.m in Sources */ = {isa = PBXBuildFile; fileRef = 1141A388117BBFF200126A28 /* SPTableCopy.m */; };
		1198F5B31174EDD500670590 /* SPDatabaseCopy.m in Sources */ = {isa = PBXBuildFile; fileRef = 1198F5B21174EDD500670590 /* SPDatabaseCopy.m */; };
		11B55BFE1189E3B2009EF465 /* SPDatabaseAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 11B55BFD1189E3B2009EF465 /* SPDatabaseAction.m */; };
		11C211301180EC9A00758039 /* SPDatabaseRename.m in Sources */ = {isa = PBXBuildFile; fileRef = 11C2109D1180E70800758039 /* SPDatabaseRename.m */; };
		171312CE109D23C700FB465F /* SPTableTextFieldCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 171312CD109D23C700FB465F /* SPTableTextFieldCell.m */; };
		1717FA401558313A0065C036 /* RegexKitLite.m in Sources */ = {isa = PBXBuildFile; fileRef = 296DC8AB0F909194002A3258 /* RegexKitLite.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		1717FA43155831600065C036 /* libicucore.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 296DC8BE0F9091DF002A3258 /* libicucore.dylib */; };
		17292443107AC41000B21980 /* SPXMLExporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 17292442107AC41000B21980 /* SPXMLExporter.m */; };
		172A65110F7BED7A001E861A /* SPConsoleMessage.m in Sources */ = {isa = PBXBuildFile; fileRef = 172A65100F7BED7A001E861A /* SPConsoleMessage.m */; };
		173284EA1088FEDE0062E892 /* SPConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = 173284E91088FEDE0062E892 /* SPConstants.m */; };
		173C4366104455E0001F3A30 /* SPQueryFavoriteManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 173C4365104455E0001F3A30 /* SPQueryFavoriteManager.m */; };
		173C44D81044A6B0001F3A30 /* SPOutlineView.m in Sources */ = {isa = PBXBuildFile; fileRef = 173C44D71044A6B0001F3A30 /* SPOutlineView.m */; };
		173C837211AAD26E00B8B084 /* SPExportUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = 173C837011AAD26E00B8B084 /* SPExportUtilities.m */; };
		173C837911AAD2AE00B8B084 /* SPDotExporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 173C837411AAD2AE00B8B084 /* SPDotExporter.m */; };
		173C837A11AAD2AE00B8B084 /* SPHTMLExporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 173C837611AAD2AE00B8B084 /* SPHTMLExporter.m */; };
		173C837B11AAD2AE00B8B084 /* SPPDFExporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 173C837811AAD2AE00B8B084 /* SPPDFExporter.m */; };
		1740FABB0FC4372F00CF3699 /* SPDatabaseData.m in Sources */ = {isa = PBXBuildFile; fileRef = 1740FABA0FC4372F00CF3699 /* SPDatabaseData.m */; };
		174CE14210AB9281008F892B /* SPProcessListController.m in Sources */ = {isa = PBXBuildFile; fileRef = 174CE14110AB9281008F892B /* SPProcessListController.m */; };
		176E14D115570FE300FAF326 /* SPBundleCommandRunner.m in Sources */ = {isa = PBXBuildFile; fileRef = 176E14D015570FE300FAF326 /* SPBundleCommandRunner.m */; };
		177E792E0FCB54EC00E9E122 /* database-small.png in Resources */ = {isa = PBXBuildFile; fileRef = 177E792B0FCB54EC00E9E122 /* database-small.png */; };
		177E792F0FCB54EC00E9E122 /* dummy-small.png in Resources */ = {isa = PBXBuildFile; fileRef = 177E792C0FCB54EC00E9E122 /* dummy-small.png */; };
		177E7A230FCB6A2E00E9E122 /* SPExtendedTableInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 177E7A220FCB6A2E00E9E122 /* SPExtendedTableInfo.m */; };
		1785E9F7127D8C7500F468C8 /* SPPreferencePane.m in Sources */ = {isa = PBXBuildFile; fileRef = 1785E9F6127D8C7500F468C8 /* SPPreferencePane.m */; };
		1785EA23127DAF3300F468C8 /* SPTablesPreferencePane.m in Sources */ = {isa = PBXBuildFile; fileRef = 1785EA22127DAF3300F468C8 /* SPTablesPreferencePane.m */; };
		1785EB60127DD5A800F468C8 /* SPNotificationsPreferencePane.m in Sources */ = {isa = PBXBuildFile; fileRef = 1785EB5F127DD5A800F468C8 /* SPNotificationsPreferencePane.m */; };
		1785EB66127DD5EA00F468C8 /* SPNetworkPreferencePane.m in Sources */ = {isa = PBXBuildFile; fileRef = 1785EB65127DD5EA00F468C8 /* SPNetworkPreferencePane.m */; };
		1785EB6A127DD79300F468C8 /* SPEditorPreferencePane.m in Sources */ = {isa = PBXBuildFile; fileRef = 1785EB69127DD79300F468C8 /* SPEditorPreferencePane.m */; };
		1789343C0F30C1DD0097539A /* SPStringAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1789343B0F30C1DD0097539A /* SPStringAdditions.m */; };
		1792C13710AD75C800ABE758 /* SPServerVariablesController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1792C13610AD75C800ABE758 /* SPServerVariablesController.m */; };
		1798F1871550175B004B0AB8 /* SPFavoritesExporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 1798F1821550175B004B0AB8 /* SPFavoritesExporter.m */; };
		1798F1881550175B004B0AB8 /* SPFavoritesImporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 1798F1851550175B004B0AB8 /* SPFavoritesImporter.m */; };
		1798F1951550181B004B0AB8 /* SPGroupNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 1798F1941550181B004B0AB8 /* SPGroupNode.m */; };
		1798F19815501838004B0AB8 /* SPMutableArrayAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1798F19715501838004B0AB8 /* SPMutableArrayAdditions.m */; };
		1798F19B1550185B004B0AB8 /* SPTreeNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 1798F19A1550185B004B0AB8 /* SPTreeNode.m */; };
		1798F1C4155018E2004B0AB8 /* SPMutableArrayAdditionsTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 1798F1C2155018D4004B0AB8 /* SPMutableArrayAdditionsTests.m */; };
		179ECECA11F265FC009C6A40 /* libbz2.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 179ECEC611F265EE009C6A40 /* libbz2.dylib */; };
		179F15060F7C433C00579954 /* SPEditorTokens.l in Sources */ = {isa = PBXBuildFile; fileRef = 179F15050F7C433C00579954 /* SPEditorTokens.l */; };
		17A20AC6124F9B110095CEFB /* SPServerSupport.m in Sources */ = {isa = PBXBuildFile; fileRef = 17A20AC5124F9B110095CEFB /* SPServerSupport.m */; };
		17A7773411C52D8E001E27B4 /* SPIndexesController.m in Sources */ = {isa = PBXBuildFile; fileRef = 17A7773311C52D8E001E27B4 /* SPIndexesController.m */; };
		17AED4161888BD67008E380F /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5EAC0FC0EC87FF900CC579C /* Security.framework */; };
		17B548631E81FFA600175D5A /* SPCreateDatabaseInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 17B548621E81FFA600175D5A /* SPCreateDatabaseInfo.m */; };
		17C058880FC9FC390077E9CF /* SPNarrowDownCompletion.m in Sources */ = {isa = PBXBuildFile; fileRef = 17C058870FC9FC390077E9CF /* SPNarrowDownCompletion.m */; };
		17CC97F310B4ABE90034CD7A /* SPAboutController.m in Sources */ = {isa = PBXBuildFile; fileRef = 17CC97F210B4ABE90034CD7A /* SPAboutController.m */; };
		17D38F701279E23A00672B13 /* SPTableFieldValidation.m in Sources */ = {isa = PBXBuildFile; fileRef = 17D38F6F1279E23A00672B13 /* SPTableFieldValidation.m */; };
		17D390C8127B65AF00672B13 /* SPGeneralPreferencePane.m in Sources */ = {isa = PBXBuildFile; fileRef = 17D390C7127B65AF00672B13 /* SPGeneralPreferencePane.m */; };
		17D390CB127B6BF800672B13 /* SPPreferencesUpgrade.m in Sources */ = {isa = PBXBuildFile; fileRef = 17D390CA127B6BF800672B13 /* SPPreferencesUpgrade.m */; };
		17D3C22212859E070047709F /* SPFavoriteNode.m in Sources */ = {isa = PBXBuildFile; fileRef = 17D3C22112859E070047709F /* SPFavoriteNode.m */; };
		17D3C66E128AD4710047709F /* SPFavoritesController.m in Sources */ = {isa = PBXBuildFile; fileRef = 17D3C66D128AD4710047709F /* SPFavoritesController.m */; };
		17D3C671128AD8160047709F /* SPSingleton.m in Sources */ = {isa = PBXBuildFile; fileRef = 17D3C670128AD8160047709F /* SPSingleton.m */; };
		17D3C6D3128B1C900047709F /* SPFavoritesOutlineView.m in Sources */ = {isa = PBXBuildFile; fileRef = 17D3C6D2128B1C900047709F /* SPFavoritesOutlineView.m */; };
		17D41A8221700F8200B1888D /* SPFunctions.m in Sources */ = {isa = PBXBuildFile; fileRef = 507FF1111BBCC57600104523 /* SPFunctions.m */; };
		17D5B49E1553059F00EF3BB3 /* SPViewCopy.m in Sources */ = {isa = PBXBuildFile; fileRef = 17D5B49D1553059F00EF3BB3 /* SPViewCopy.m */; };
		17DB5F441555CA300046834B /* SPMutableArrayAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1798F19715501838004B0AB8 /* SPMutableArrayAdditions.m */; };
		17DD52B7115071D0007D8950 /* SPPrintTemplate.html in Resources */ = {isa = PBXBuildFile; fileRef = 17DD52B6115071D0007D8950 /* SPPrintTemplate.html */; };
		17DD52C6115074CB007D8950 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = 17DD52C4115074CB007D8950 /* Localizable.strings */; };
		17E0937E114AE154007FC1B4 /* SPTableInfoPrintTemplate.html in Resources */ = {isa = PBXBuildFile; fileRef = 17E0937D114AE154007FC1B4 /* SPTableInfoPrintTemplate.html */; };
		17E641460EF01EB5001BC333 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E641440EF01EB5001BC333 /* main.m */; };
		17E641560EF01EF6001BC333 /* SPCustomQuery.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E641490EF01EF6001BC333 /* SPCustomQuery.m */; };
		17E641570EF01EF6001BC333 /* SPAppController.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E6414B0EF01EF6001BC333 /* SPAppController.m */; };
		17E641590EF01EF6001BC333 /* SPTableContent.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E6414F0EF01EF6001BC333 /* SPTableContent.m */; };
		17E6415A0EF01EF6001BC333 /* SPDatabaseDocument.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E641510EF01EF6001BC333 /* SPDatabaseDocument.m */; };
		17E6415B0EF01EF6001BC333 /* SPDataImport.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E641530EF01EF6001BC333 /* SPDataImport.m */; };
		17E6415C0EF01EF6001BC333 /* SPTableStructure.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E641550EF01EF6001BC333 /* SPTableStructure.m */; };
		17E641640EF01F15001BC333 /* SPTableInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E6415F0EF01F15001BC333 /* SPTableInfo.m */; };
		17E641650EF01F15001BC333 /* SPTablesList.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E641610EF01F15001BC333 /* SPTablesList.m */; };
		17E6416C0EF01F37001BC333 /* ImageAndTextCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E641690EF01F37001BC333 /* ImageAndTextCell.m */; };
		17E641750EF01F80001BC333 /* SPKeychain.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E641740EF01F80001BC333 /* SPKeychain.m */; };
		17E641830EF01FA8001BC333 /* SPImageView.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E6417F0EF01FA8001BC333 /* SPImageView.m */; };
		17E641840EF01FA8001BC333 /* SPTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E641810EF01FA8001BC333 /* SPTextView.m */; };
		17E641D10EF02036001BC333 /* grabber-horizontal.png in Resources */ = {isa = PBXBuildFile; fileRef = 17E6419D0EF02036001BC333 /* grabber-horizontal.png */; };
		17E641D20EF02036001BC333 /* grabber-vertical.png in Resources */ = {isa = PBXBuildFile; fileRef = 17E6419E0EF02036001BC333 /* grabber-vertical.png */; };
		17E641F20EF02036001BC333 /* toolbar-switch-to-structure.png in Resources */ = {isa = PBXBuildFile; fileRef = 17E641BE0EF02036001BC333 /* toolbar-switch-to-structure.png */; };
		17E641F30EF02036001BC333 /* toolbar-switch-to-table-info.png in Resources */ = {isa = PBXBuildFile; fileRef = 17E641BF0EF02036001BC333 /* toolbar-switch-to-table-info.png */; };
		17E641FC0EF02088001BC333 /* sequel-pro.scriptSuite in Resources */ = {isa = PBXBuildFile; fileRef = 17E641F70EF02088001BC333 /* sequel-pro.scriptSuite */; };
		17E641FD0EF02088001BC333 /* sequel-pro.scriptTerminology in Resources */ = {isa = PBXBuildFile; fileRef = 17E641F80EF02088001BC333 /* sequel-pro.scriptTerminology */; };
		17F5B1511048C4E400FC794F /* SPCSVExporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 17F5B1501048C4E400FC794F /* SPCSVExporter.m */; };
		17F5B1541048C50D00FC794F /* SPExporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 17F5B1531048C50D00FC794F /* SPExporter.m */; };
		17F5B39C1049B96A00FC794F /* SPSQLExporter.m in Sources */ = {isa = PBXBuildFile; fileRef = 17F5B39B1049B96A00FC794F /* SPSQLExporter.m */; };
		17F90E481210B42700274C98 /* SPExportFile.m in Sources */ = {isa = PBXBuildFile; fileRef = 17F90E471210B42700274C98 /* SPExportFile.m */; };
		17FDB04C1280778B00DBBBC2 /* SPFontPreviewTextField.m in Sources */ = {isa = PBXBuildFile; fileRef = 17FDB04B1280778B00DBBBC2 /* SPFontPreviewTextField.m */; };
		1A071B8D254D983700246912 /* SPNSMutableDictionaryAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A071B8C254D983700246912 /* SPNSMutableDictionaryAdditions.m */; };
		1A0FA3EE258B758B00486D52 /* SPArrayAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = B52460D40F8EF92300171639 /* SPArrayAdditions.m */; };
		1A11E50425A327F1001CB721 /* SPPanelOptions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A11E50325A327F1001CB721 /* SPPanelOptions.m */; };
		1A11E51625A36C62001CB721 /* HyperlinkTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A11E51525A36C62001CB721 /* HyperlinkTextField.swift */; };
		1A1667112584D94800A9686E /* SPURLAdditionsTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A8B53672584552F00526DED /* SPURLAdditionsTests.m */; };
		1A19962A257A624200F5B0F1 /* BundleExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A199629257A624200F5B0F1 /* BundleExtension.swift */; };
		1A1EE94A2551185D0056FECD /* DateFormatterExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1EE9492551185D0056FECD /* DateFormatterExtension.swift */; };
		1A1EE9582551249C0056FECD /* NumberFormatterExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1EE9572551249C0056FECD /* NumberFormatterExtension.swift */; };
		1A24B627258A2E9A00541E88 /* SecureBookmarkData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A24B626258A2E9A00541E88 /* SecureBookmarkData.swift */; };
		1A2711CF2539D9B10066ED58 /* SPReachability.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A2711CE2539D9B10066ED58 /* SPReachability.m */; settings = {COMPILER_FLAGS = "-fobjc-arc"; }; };
		1A2711E82539E2BB0066ED58 /* local-connection.html in Resources */ = {isa = PBXBuildFile; fileRef = 1A2711E72539E2BB0066ED58 /* local-connection.html */; };
		1A2DD55125939B6400616E7E /* SPArrayAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A2DD55025939B6400616E7E /* SPArrayAdditions.m */; };
		1A2DD55A25939BEE00616E7E /* SPTestingUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A2DD55925939BEE00616E7E /* SPTestingUtils.m */; };
		1A31FE3925F2132F000DD1D1 /* SPTaskAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A31FE3525F2132F000DD1D1 /* SPTaskAdditions.m */; };
		1A3FA7962495FC2D00B7291A /* SPMainThreadTrampoline.m in Sources */ = {isa = PBXBuildFile; fileRef = 589582141154F8F400EDCC28 /* SPMainThreadTrampoline.m */; };
		1A4152F125AF531000B17249 /* GeneralSwiftTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A4152ED25AF530F00B17249 /* GeneralSwiftTests.swift */; };
		1A445DAA25BACBE5004E9A77 /* naughty_strings.txt in Resources */ = {isa = PBXBuildFile; fileRef = 1A445DA925BACBE5004E9A77 /* naughty_strings.txt */; };
		1A4CB03425923C4B00EDF804 /* StringRegexExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A4CB03325923C4B00EDF804 /* StringRegexExtension.swift */; };
		1A4CB04F2592535300EDF804 /* StringRegexExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A4CB03325923C4B00EDF804 /* StringRegexExtension.swift */; };
		1A4CB06B25926D7C00EDF804 /* StringRegexExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A4CB03325923C4B00EDF804 /* StringRegexExtension.swift */; };
		1A4DC22D25DECEA000DA4FE1 /* ProgressWindowController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A4DC22C25DECEA000DA4FE1 /* ProgressWindowController.swift */; };
		1A564F74237E2E4958CA593A /* SPPillAttachmentCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A56463D14569A0B56EE8BAC /* SPPillAttachmentCell.m */; };
		1A5A83532545DA8B00EDC196 /* SPObjectAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 584D878A15140FEB00F24774 /* SPObjectAdditions.m */; };
		1A6377D4259B414400B1E96D /* SecureBookmark.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A96E4B42588F34C0055F5F5 /* SecureBookmark.swift */; };
		1A6447912588CD8B00927DB3 /* License.rtf in Resources */ = {isa = PBXBuildFile; fileRef = 17CC993A10B4C9C80034CD7A /* License.rtf */; };
		1A6447992588CD9200927DB3 /* Credits.rtf in Resources */ = {isa = PBXBuildFile; fileRef = 517E44FB257A94C400ED333B /* Credits.rtf */; };
		1A6D28FE25DD8018007509F1 /* ProgressWindowController.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1A6D28FD25DD8018007509F1 /* ProgressWindowController.storyboard */; };
		1A70BC6625EF4243004BB992 /* ReportExceptionApplication.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A0751DF25EAF37700FFDF6B /* ReportExceptionApplication.m */; };
		1A70BC6B25EF439F004BB992 /* FileManagerExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A70BC6A25EF439F004BB992 /* FileManagerExtension.swift */; };
		1A70BC9225EF7EE9004BB992 /* FileManagerExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A70BC6A25EF439F004BB992 /* FileManagerExtension.swift */; };
		1A85CB8D2493BC4A00B57B93 /* SPSyncTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A85CB8C2493BC4A00B57B93 /* SPSyncTests.m */; };
		1A89556625D6BEED0060CE72 /* GitHubReleaseManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A89556525D6BEED0060CE72 /* GitHubReleaseManager.swift */; };
		1A89556F25D6C8880060CE72 /* Alamofire in Frameworks */ = {isa = PBXBuildFile; productRef = 1A89556E25D6C8880060CE72 /* Alamofire */; };
		1A89558725D6E15D0060CE72 /* GitHub.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A89557825D6DE860060CE72 /* GitHub.swift */; };
		1A8B53572584520800526DED /* SPURLAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A8B53562584520800526DED /* SPURLAdditions.m */; };
		1A8B53A52584650700526DED /* SPURLAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A8B53562584520800526DED /* SPURLAdditions.m */; };
		1A8B582A25EEE15900DFC54A /* ByteCountFormatterExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A8B582925EEE15900DFC54A /* ByteCountFormatterExtension.swift */; };
		1A94988E25516057000BC793 /* DateExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A94988D25516057000BC793 /* DateExtension.swift */; };
		1A9498C125517191000BC793 /* DateExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A94988D25516057000BC793 /* DateExtension.swift */; };
		1A9498DE2551776D000BC793 /* DateFormatterExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1EE9492551185D0056FECD /* DateFormatterExtension.swift */; };
		1A94997A25518549000BC793 /* DateComponentsFormatterExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9D83A325514E740024B563 /* DateComponentsFormatterExtension.swift */; };
		1A96E4B72588F34C0055F5F5 /* SecureBookmarkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A96E4B52588F34C0055F5F5 /* SecureBookmarkManager.swift */; };
		1A9A40D525875EC9009F0E71 /* NSMutableArray-MultipleSort.m in Sources */ = {isa = PBXBuildFile; fileRef = 584192A0101E57BB0089807F /* NSMutableArray-MultipleSort.m */; };
		1A9D83A425514E740024B563 /* DateComponentsFormatterExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9D83A325514E740024B563 /* DateComponentsFormatterExtension.swift */; };
		1A9EB9AE25651F5000FE60FF /* SQLiteHistoryManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A9EB9AD25651F5000FE60FF /* SQLiteHistoryManager.swift */; };
		1A9F343F257B0DBE0062EC87 /* SPBundleManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A9F343E257B0DBE0062EC87 /* SPBundleManager.m */; };
		1AB068A824A355CC00E2AAC2 /* client-cert-crlf.pem in Resources */ = {isa = PBXBuildFile; fileRef = 1AB0688F24A355B500E2AAC2 /* client-cert-crlf.pem */; };
		1AB068A924A355CC00E2AAC2 /* client-cert-bad-end.pem in Resources */ = {isa = PBXBuildFile; fileRef = 1AB0689024A355B500E2AAC2 /* client-cert-bad-end.pem */; };
		1AB068AA24A355CC00E2AAC2 /* client-cert-lf.pem in Resources */ = {isa = PBXBuildFile; fileRef = 1AB0689124A355B500E2AAC2 /* client-cert-lf.pem */; };
		1AB068AB24A355CC00E2AAC2 /* client-cert-cr.pem in Resources */ = {isa = PBXBuildFile; fileRef = 1AB0689224A355B500E2AAC2 /* client-cert-cr.pem */; };
		1AB068AC24A355CC00E2AAC2 /* client-cert-bad-start.pem in Resources */ = {isa = PBXBuildFile; fileRef = 1AB0689324A355B500E2AAC2 /* client-cert-bad-start.pem */; };
		1AB068AD24A355CC00E2AAC2 /* client-key-bad-end.pem in Resources */ = {isa = PBXBuildFile; fileRef = 1AB0689524A355B500E2AAC2 /* client-key-bad-end.pem */; };
		1AB068AE24A355CC00E2AAC2 /* client-key-cr.pem in Resources */ = {isa = PBXBuildFile; fileRef = 1AB0689624A355B500E2AAC2 /* client-key-cr.pem */; };
		1AB068AF24A355CC00E2AAC2 /* client-key-bad-start.pem in Resources */ = {isa = PBXBuildFile; fileRef = 1AB0689724A355B500E2AAC2 /* client-key-bad-start.pem */; };
		1AB068B024A355CC00E2AAC2 /* client-key-lf.pem in Resources */ = {isa = PBXBuildFile; fileRef = 1AB0689824A355B500E2AAC2 /* client-key-lf.pem */; };
		1AB068B124A355CC00E2AAC2 /* client-key-crlf.pem in Resources */ = {isa = PBXBuildFile; fileRef = 1AB0689924A355B500E2AAC2 /* client-key-crlf.pem */; };
		1AB068B424A3577C00E2AAC2 /* SPValidateKeyAndCertFiles.m in Sources */ = {isa = PBXBuildFile; fileRef = 1AB068B224A3575600E2AAC2 /* SPValidateKeyAndCertFiles.m */; };
		1AB28D8125DBD3B500E62BF5 /* ProgressViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1AB28D7F25DBD3B500E62BF5 /* ProgressViewController.swift */; };
		1AB925922551550200063446 /* NumberFormatterExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A1EE9572551249C0056FECD /* NumberFormatterExtension.swift */; };
		1ABC770125E3895300E8EE01 /* DispatchQueueExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1ABC770025E3895300E8EE01 /* DispatchQueueExtension.swift */; };
		1ACA0B6625BEBE18002FA618 /* PopupButtonExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1ACA0B6525BEBE18002FA618 /* PopupButtonExtensions.swift */; };
		1AD785E725B749760007E153 /* OSLog.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1AD785E325B749760007E153 /* OSLog.swift */; };
		1ADEA5A324BF1C4800D2140B /* SPDateAdditionsTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 1ADEA5A224BF1C4800D2140B /* SPDateAdditionsTests.m */; };
		1ADEA5A424BF1FFB00D2140B /* SPDateAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 58DF9F3215AB26C2003B4330 /* SPDateAdditions.m */; };
		1AE6C1CD25B07E9500880D73 /* SPFunctionsTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 1AE6C1CC25B07E9500880D73 /* SPFunctionsTests.m */; };
		1AEA768425EF05D500AC4DA6 /* ByteCountFormatterExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A8B582925EEE15900DFC54A /* ByteCountFormatterExtension.swift */; };
		1AF0DA7F259F5D8C00961974 /* SPPointerArrayAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1AF0DA7E259F5D8C00961974 /* SPPointerArrayAdditions.m */; };
		1AF0DA84259F631000961974 /* SPPointerArrayAdditionsTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 1AF0DA83259F631000961974 /* SPPointerArrayAdditionsTests.m */; };
		1AF0DA8B259F657200961974 /* SPPointerArrayAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1AF0DA7E259F5D8C00961974 /* SPPointerArrayAdditions.m */; };
		1AF5A261250AC401009885DF /* SPBracketHighlighter.m in Sources */ = {isa = PBXBuildFile; fileRef = 1AF5A25E250AC401009885DF /* SPBracketHighlighter.m */; };
		1AF5A262250AC401009885DF /* SPBrackets.m in Sources */ = {isa = PBXBuildFile; fileRef = 1AF5A25F250AC401009885DF /* SPBrackets.m */; };
		265446DF24A1616900376B48 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 38C613721C8977E600B3B6EF /* libz.tbd */; };
		296DC89F0F8FD336002A3258 /* WebKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 296DC89E0F8FD336002A3258 /* WebKit.framework */; };
		296DC8B60F909194002A3258 /* MGTemplateEngine.m in Sources */ = {isa = PBXBuildFile; fileRef = 296DC8A70F909194002A3258 /* MGTemplateEngine.m */; };
		296DC8B70F909194002A3258 /* RegexKitLite.m in Sources */ = {isa = PBXBuildFile; fileRef = 296DC8AB0F909194002A3258 /* RegexKitLite.m */; settings = {COMPILER_FLAGS = "-fno-objc-arc"; }; };
		296DC8B80F909194002A3258 /* ICUTemplateMatcher.m in Sources */ = {isa = PBXBuildFile; fileRef = 296DC8AC0F909194002A3258 /* ICUTemplateMatcher.m */; };
		296DC8B90F909194002A3258 /* MGTemplateStandardMarkers.m in Sources */ = {isa = PBXBuildFile; fileRef = 296DC8AD0F909194002A3258 /* MGTemplateStandardMarkers.m */; };
		296DC8BA0F909194002A3258 /* NSArray_DeepMutableCopy.m in Sources */ = {isa = PBXBuildFile; fileRef = 296DC8AE0F909194002A3258 /* NSArray_DeepMutableCopy.m */; };
		296DC8BB0F909194002A3258 /* NSDictionary_DeepMutableCopy.m in Sources */ = {isa = PBXBuildFile; fileRef = 296DC8B10F909194002A3258 /* NSDictionary_DeepMutableCopy.m */; };
		296DC8BC0F909194002A3258 /* MGTemplateStandardFilters.m in Sources */ = {isa = PBXBuildFile; fileRef = 296DC8B40F909194002A3258 /* MGTemplateStandardFilters.m */; };
		296DC8BF0F9091DF002A3258 /* libicucore.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 296DC8BE0F9091DF002A3258 /* libicucore.dylib */; };
		29A1B7E50FD1293A000B88E8 /* SPPrintAccessory.m in Sources */ = {isa = PBXBuildFile; fileRef = 29A1B7E40FD1293A000B88E8 /* SPPrintAccessory.m */; };
		29FA88231114619E00D1AF3D /* SPTableTriggers.m in Sources */ = {isa = PBXBuildFile; fileRef = 29FA88221114619E00D1AF3D /* SPTableTriggers.m */; };
		380F4EF50FC0B68F00B0BFD7 /* SPStringAdditionsTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 380F4EF40FC0B68F00B0BFD7 /* SPStringAdditionsTests.m */; };
		384582C40FB95FF800DDACB6 /* func-small.png in Resources */ = {isa = PBXBuildFile; fileRef = 384582C30FB95FF800DDACB6 /* func-small.png */; };
		384582C70FB9603600DDACB6 /* proc-small.png in Resources */ = {isa = PBXBuildFile; fileRef = 384582C60FB9603600DDACB6 /* proc-small.png */; };
		3876E15D1CC0BA0300D85154 /* SPTableFooterPopUpButtonCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 3876E15C1CC0BA0300D85154 /* SPTableFooterPopUpButtonCell.m */; };
		387BBBA80FBCB6CB00B31746 /* SPTableRelations.m in Sources */ = {isa = PBXBuildFile; fileRef = 387BBBA70FBCB6CB00B31746 /* SPTableRelations.m */; };
		3E242D4F20FEB44D0015470D /* button_bar_spacer_dark.png in Resources */ = {isa = PBXBuildFile; fileRef = 3E242D4B20FEB44D0015470D /* button_bar_spacer_dark.png */; };
		3E242D5020FEB44D0015470D /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 3E242D4E20FEB44D0015470D /* <EMAIL> */; };
		44011FFBC7DF57126761313F /* SQLitePinnedTableManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 44011DFD0DC2836DB08A0619 /* SQLitePinnedTableManager.swift */; };
		4D90B79A101E0CDF00D116A1 /* SPUserManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D90B799101E0CDF00D116A1 /* SPUserManager.m */; };
		4D90B79E101E0CF200D116A1 /* SPUserManager.xcdatamodel in Sources */ = {isa = PBXBuildFile; fileRef = 4D90B79B101E0CF200D116A1 /* SPUserManager.xcdatamodel */; };
		4D90B79F101E0CF200D116A1 /* SPUserMO.m in Sources */ = {isa = PBXBuildFile; fileRef = 4D90B79D101E0CF200D116A1 /* SPUserMO.m */; };
		500C1F921BFB5F9F0095DC7F /* SPPrivilegesMO.m in Sources */ = {isa = PBXBuildFile; fileRef = 500C1F911BFB5F9F0095DC7F /* SPPrivilegesMO.m */; };
		500DA4B71BEFF877000773FE /* SPComboBoxCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 500DA4B61BEFF877000773FE /* SPComboBoxCell.m */; };
		500DA4BC1BF0CD57000773FE /* SPScreenAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 500DA4BB1BF0CD57000773FE /* SPScreenAdditions.m */; };
		501B1D181728A3DA0017C92E /* SPCharsetCollationHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = 501B1D171728A3DA0017C92E /* SPCharsetCollationHelper.m */; };
		502D21F61BA50710000D4CE7 /* SPDataAdditionsTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 502D21F51BA50710000D4CE7 /* SPDataAdditionsTests.m */; };
		502D21F81BA50966000D4CE7 /* SPDataAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = BC2C16D30FEBEDF10003993B /* SPDataAdditions.m */; };
		502D22151BA62FA5000D4CE7 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5EAC0FC0EC87FF900CC579C /* Security.framework */; };
		503B02CA1AE82C5E0060CAB1 /* SPTableFilterParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 503B02C91AE82C5E0060CAB1 /* SPTableFilterParser.m */; };
		503B02CF1AE95C2C0060CAB1 /* SPTableFilterParserTest.m in Sources */ = {isa = PBXBuildFile; fileRef = 503B02CE1AE95C2C0060CAB1 /* SPTableFilterParserTest.m */; };
		503B02D11AE95DD40060CAB1 /* SPTableFilterParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 503B02C91AE82C5E0060CAB1 /* SPTableFilterParser.m */; };
		503B02D21AE95E010060CAB1 /* SPConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = 173284E91088FEDE0062E892 /* SPConstants.m */; };
		503CDBB21ACDC204004F8A2F /* Quartz.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 503CDBB11ACDC204004F8A2F /* Quartz.framework */; };
		505F568F1BCEE485007467DD /* SPFunctions.m in Sources */ = {isa = PBXBuildFile; fileRef = 507FF1111BBCC57600104523 /* SPFunctions.m */; };
		506CE9311A311C6C0039F736 /* SPRuleFilterController.m in Sources */ = {isa = PBXBuildFile; fileRef = 506CE9301A311C6C0039F736 /* SPRuleFilterController.m */; };
		507FF1121BBCC57600104523 /* SPFunctions.m in Sources */ = {isa = PBXBuildFile; fileRef = 507FF1111BBCC57600104523 /* SPFunctions.m */; };
		507FF1621BBF0D5000104523 /* SPTableCopyTest.m in Sources */ = {isa = PBXBuildFile; fileRef = 112730551180788A000737FD /* SPTableCopyTest.m */; };
		50805B0D1BF2A068005F7A99 /* SPPopUpButtonCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 50805B0C1BF2A068005F7A99 /* SPPopUpButtonCell.m */; };
		50837F741E50DCD4004FAE8A /* SPJSONFormatterTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 50837F731E50DCD4004FAE8A /* SPJSONFormatterTests.m */; };
		50837F771E50E007004FAE8A /* SPJSONFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 73F70A951E4E547500636550 /* SPJSONFormatter.m */; };
		5089B0271BE714E300E226CD /* SPIdMenu.m in Sources */ = {isa = PBXBuildFile; fileRef = 5089B0261BE714E300E226CD /* SPIdMenu.m */; };
		50A9F8B119EAD4B90053E571 /* SPGotoDatabaseController.m in Sources */ = {isa = PBXBuildFile; fileRef = 50A9F8B019EAD4B90053E571 /* SPGotoDatabaseController.m */; };
		50D3C3521A77135F00B5429C /* SPParserUtils.c in Sources */ = {isa = PBXBuildFile; fileRef = 50D3C3501A77135F00B5429C /* SPParserUtils.c */; };
		50D3C35C1A771C4C00B5429C /* SPParserUtilsTest.m in Sources */ = {isa = PBXBuildFile; fileRef = 50D3C35B1A771C4C00B5429C /* SPParserUtilsTest.m */; };
		50D3C35D1A77217800B5429C /* SPParserUtils.c in Sources */ = {isa = PBXBuildFile; fileRef = 50D3C3501A77135F00B5429C /* SPParserUtils.c */; };
		50E217B318174246009D3580 /* SPColorSelectorView.m in Sources */ = {isa = PBXBuildFile; fileRef = 50E217B218174246009D3580 /* SPColorSelectorView.m */; };
		50E217B618174280009D3580 /* SPFavoriteColorSupport.m in Sources */ = {isa = PBXBuildFile; fileRef = 50E217B518174280009D3580 /* SPFavoriteColorSupport.m */; };
		50EA92641AB23EAD008D3C4F /* SPDatabaseCopy.m in Sources */ = {isa = PBXBuildFile; fileRef = 1198F5B21174EDD500670590 /* SPDatabaseCopy.m */; };
		50EA92651AB23EC8008D3C4F /* SPDatabaseAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 11B55BFD1189E3B2009EF465 /* SPDatabaseAction.m */; };
		50EA92661AB23ED3008D3C4F /* SPMySQL.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = 584D876815140D3500F24774 /* SPMySQL.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		50EA92671AB23EE1008D3C4F /* SPMySQL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 584D876815140D3500F24774 /* SPMySQL.framework */; };
		50EA92681AB23EFC008D3C4F /* SPTableCopy.m in Sources */ = {isa = PBXBuildFile; fileRef = 1141A388117BBFF200126A28 /* SPTableCopy.m */; };
		50EA926A1AB246B8008D3C4F /* SPDatabaseActionTest.m in Sources */ = {isa = PBXBuildFile; fileRef = 50EA92691AB246B8008D3C4F /* SPDatabaseActionTest.m */; };
		50F530521ABCF66B002F2C1A /* resetTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 50F530511ABCF66B002F2C1A /* resetTemplate.pdf */; };
		5132930C25F586A900D803AD /* NotificationToken.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5132930925F586A900D803AD /* NotificationToken.swift */; };
		5132930D25F586A900D803AD /* TabManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5132930A25F586A900D803AD /* TabManager.swift */; };
		513515D2259354BB001E4533 /* NSImageExtensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 513515D1259354BB001E4533 /* NSImageExtensions.swift */; };
		51384AC22903461000FEC501 /* NSDictionaryExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51384AC12903461000FEC501 /* NSDictionaryExtension.swift */; };
		513C8CE12BBC4132001CCE3A /* OCMock in Frameworks */ = {isa = PBXBuildFile; productRef = 513C8CE02BBC4132001CCE3A /* OCMock */; };
		514DD98925FACF1500EA3B3B /* SPDatabaseDocument.swift in Sources */ = {isa = PBXBuildFile; fileRef = 514DD98825FACF1500EA3B3B /* SPDatabaseDocument.swift */; };
		515D303F25BD7DE60021CF1E /* AppCenterCrashes in Frameworks */ = {isa = PBXBuildFile; productRef = 515D303E25BD7DE60021CF1E /* AppCenterCrashes */; };
		515D304125BD7DE60021CF1E /* AppCenterAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = 515D304025BD7DE60021CF1E /* AppCenterAnalytics */; };
		517412302573E10C00EB6935 /* SPPrintUtility.m in Sources */ = {isa = PBXBuildFile; fileRef = 5174122F2573E10C00EB6935 /* SPPrintUtility.m */; };
		517412382573E8F900EB6935 /* EditorQuickLookTypes.plist in Resources */ = {isa = PBXBuildFile; fileRef = 517412372573E8F900EB6935 /* EditorQuickLookTypes.plist */; };
		517E4512257A954000ED333B /* ContentFilters.plist in Resources */ = {isa = PBXBuildFile; fileRef = 517E4511257A954000ED333B /* ContentFilters.plist */; };
		5193502F2567D2FB001272B5 /* CollectionExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5193502E2567D2FB001272B5 /* CollectionExtension.swift */; };
		519350302567D2FB001272B5 /* CollectionExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5193502E2567D2FB001272B5 /* CollectionExtension.swift */; };
		51A709392565B99F001F1D2F /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 51A709382565B99F001F1D2F /* Images.xcassets */; };
		51BB391C25F82B060048CA69 /* SPAppController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51BB391B25F82B060048CA69 /* SPAppController.swift */; };
		51BC14F825BE135500F1CDC9 /* SPWindowController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51BC14F725BE135500F1CDC9 /* SPWindowController.swift */; };
		51BC150125BE138700F1CDC9 /* SnapKit in Frameworks */ = {isa = PBXBuildFile; productRef = 51BC150025BE138700F1CDC9 /* SnapKit */; };
		51BC150625BE13C400F1CDC9 /* NSViewExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51BC150525BE13C400F1CDC9 /* NSViewExtension.swift */; };
		51C4626D254ED02500F63E70 /* UserDefaultsExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51C4626C254ED02500F63E70 /* UserDefaultsExtension.swift */; };
		51C6288C24D196E8006491E9 /* StringExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51C8597B24C8A31400A8C7C4 /* StringExtension.swift */; };
		51C8597C24C8A31400A8C7C4 /* StringExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51C8597B24C8A31400A8C7C4 /* StringExtension.swift */; };
		51C8598024C8A6D700A8C7C4 /* SPStringAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1789343B0F30C1DD0097539A /* SPStringAdditions.m */; };
		51CD0BDC258D06D8009E2484 /* PrintAccessory.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BBE258D06D7009E2484 /* PrintAccessory.xib */; };
		51CD0BDD258D06D8009E2484 /* DataMigrationDialog.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BBF258D06D7009E2484 /* DataMigrationDialog.xib */; };
		51CD0BDE258D06D8009E2484 /* Preferences.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BC0258D06D7009E2484 /* Preferences.xib */; };
		51CD0BDF258D06D8009E2484 /* FilterTableWindow.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BC1258D06D7009E2484 /* FilterTableWindow.xib */; };
		51CD0BE0258D06D8009E2484 /* FieldEditorSheet.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BC2258D06D7009E2484 /* FieldEditorSheet.xib */; };
		51CD0BE1258D06D8009E2484 /* GotoDatabaseDialog.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BC3258D06D7009E2484 /* GotoDatabaseDialog.xib */; };
		51CD0BE2258D06D8009E2484 /* IndexesView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BC4258D06D7009E2484 /* IndexesView.xib */; };
		51CD0BE3258D06D8009E2484 /* DBView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BC5258D06D7009E2484 /* DBView.xib */; };
		51CD0BE4258D06D8009E2484 /* HelpViewer.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BC6258D06D7009E2484 /* HelpViewer.xib */; };
		51CD0BE5258D06D8009E2484 /* Navigator.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BC7258D06D8009E2484 /* Navigator.xib */; };
		51CD0BE7258D06D8009E2484 /* ImportAccessory.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BC9258D06D8009E2484 /* ImportAccessory.xib */; };
		51CD0BE8258D06D8009E2484 /* MainMenu.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BCA258D06D8009E2484 /* MainMenu.xib */; };
		51CD0BE9258D06D8009E2484 /* Console.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BCB258D06D8009E2484 /* Console.xib */; };
		51CD0BEA258D06D8009E2484 /* DatabaseServerVariables.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BCC258D06D8009E2484 /* DatabaseServerVariables.xib */; };
		51CD0BEB258D06D8009E2484 /* ConnectionErrorDialog.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BCD258D06D8009E2484 /* ConnectionErrorDialog.xib */; };
		51CD0BEC258D06D8009E2484 /* ProgressIndicatorLayer.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BCE258D06D8009E2484 /* ProgressIndicatorLayer.xib */; };
		51CD0BED258D06D8009E2484 /* SaveSPFAccessory.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BCF258D06D8009E2484 /* SaveSPFAccessory.xib */; };
		51CD0BEE258D06D8009E2484 /* UserManagerView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BD0258D06D8009E2484 /* UserManagerView.xib */; };
		51CD0BEF258D06D8009E2484 /* ExportDialog.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BD1258D06D8009E2484 /* ExportDialog.xib */; };
		51CD0BF0258D06D8009E2484 /* BundleEditor.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BD2258D06D8009E2484 /* BundleEditor.xib */; };
		51CD0BF1258D06D8009E2484 /* ContentFilterManager.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BD3258D06D8009E2484 /* ContentFilterManager.xib */; };
		51CD0BF2258D06D8009E2484 /* EncodingPopupView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BD4258D06D8009E2484 /* EncodingPopupView.xib */; };
		51CD0BF3258D06D8009E2484 /* ContentPaginationView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BD5258D06D8009E2484 /* ContentPaginationView.xib */; };
		51CD0BF4258D06D8009E2484 /* SSHQuestionDialog.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BD6258D06D8009E2484 /* SSHQuestionDialog.xib */; };
		51CD0BF5258D06D8009E2484 /* QueryFavoriteManager.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BD7258D06D8009E2484 /* QueryFavoriteManager.xib */; };
		51CD0BF6258D06D8009E2484 /* ConnectionView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BD8258D06D8009E2484 /* ConnectionView.xib */; };
		51CD0BF7258D06D8009E2484 /* AboutPanel.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BD9258D06D8009E2484 /* AboutPanel.xib */; };
		51CD0BF8258D06D8009E2484 /* DatabaseProcessList.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BDA258D06D8009E2484 /* DatabaseProcessList.xib */; };
		51CD0BF9258D06D8009E2484 /* BundleHTMLOutput.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51CD0BDB258D06D8009E2484 /* BundleHTMLOutput.xib */; };
		51D9527625AE2B5300574BEB /* FMDB in Frameworks */ = {isa = PBXBuildFile; productRef = 51D9527525AE2B5300574BEB /* FMDB */; };
		51DCBEF0257134190098E303 /* libz.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 38C613721C8977E600B3B6EF /* libz.tbd */; };
		51F41FBA25F56A5900594BA5 /* MainWindow.xib in Resources */ = {isa = PBXBuildFile; fileRef = 51F41FB625F56A5900594BA5 /* MainWindow.xib */; };
		51F4AFBF24B26665006144D5 /* NSAlertExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 51F4AFBE24B26665006144D5 /* NSAlertExtension.swift */; };
		5806B76411A991EC00813A88 /* SPDocumentController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5806B76311A991EC00813A88 /* SPDocumentController.m */; };
		581068B61015411B0068C6E2 /* link-arrow-highlighted.png in Resources */ = {isa = PBXBuildFile; fileRef = 581068B51015411B0068C6E2 /* link-arrow-highlighted.png */; };
		5822C9B51000DB2400DCC3D6 /* SPConnectionController.m in Sources */ = {isa = PBXBuildFile; fileRef = 5822C9B41000DB2400DCC3D6 /* SPConnectionController.m */; };
		5822D3091061833C00CE2157 /* SPCSVParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 5822D3081061833C00CE2157 /* SPCSVParser.m */; };
		582A01E9107C0C170027D42B /* SPNotLoaded.m in Sources */ = {isa = PBXBuildFile; fileRef = 582A01E8107C0C170027D42B /* SPNotLoaded.m */; };
		582E939D168296F3003459FD /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 582E9399168296F3003459FD /* <EMAIL> */; };
		582E939E168296F3003459FD /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 582E939A168296F3003459FD /* <EMAIL> */; };
		582E939F168296F3003459FD /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 582E939B168296F3003459FD /* <EMAIL> */; };
		582E93A0168296F3003459FD /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 582E939C168296F3003459FD /* <EMAIL> */; };
		582E940E1682A2AD003459FD /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 582E940D1682A2AD003459FD /* <EMAIL> */; };
		582E942E1683658A003459FD /* clearconsole.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E942D1683658A003459FD /* clearconsole.png */; };
		582E944A168374C1003459FD /* field-small-square.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E9449168374C1003459FD /* field-small-square.png */; };
		582E944C16837986003459FD /* hideconsole.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E944B16837986003459FD /* hideconsole.png */; };
		582E945016837AA9003459FD /* network-small.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E944F16837AA9003459FD /* network-small.png */; };
		582E947016837DB2003459FD /* showconsole.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E946F16837DB2003459FD /* showconsole.png */; };
		582E9483168380D6003459FD /* sync_arrows_01.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E947D168380D6003459FD /* sync_arrows_01.png */; };
		582E9484168380D6003459FD /* sync_arrows_02.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E947E168380D6003459FD /* sync_arrows_02.png */; };
		582E9485168380D6003459FD /* sync_arrows_03.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E947F168380D6003459FD /* sync_arrows_03.png */; };
		582E9486168380D6003459FD /* sync_arrows_04.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E9480168380D6003459FD /* sync_arrows_04.png */; };
		582E9487168380D6003459FD /* sync_arrows_05.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E9481168380D6003459FD /* sync_arrows_05.png */; };
		582E9488168380D6003459FD /* sync_arrows_06.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E9482168380D6003459FD /* sync_arrows_06.png */; };
		582E948F168383F0003459FD /* table-view-small-square.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E948D168383F0003459FD /* table-view-small-square.png */; };
		582E9490168383F0003459FD /* table-view-small.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E948E168383F0003459FD /* table-view-small.png */; };
		582E94A816839AD5003459FD /* toolbar-preferences-autoupdate.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E94A716839AD5003459FD /* toolbar-preferences-autoupdate.png */; };
		582E94AA16839AEF003459FD /* toolbar-preferences-general.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E94A916839AEF003459FD /* toolbar-preferences-general.png */; };
		582E94AE16839C4A003459FD /* toolbar-preferences-notifications.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E94AD16839C4A003459FD /* toolbar-preferences-notifications.png */; };
		582E94C716839D83003459FD /* toolbar-preferences-tables.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E94C616839D83003459FD /* toolbar-preferences-tables.png */; };
		582E94F816839E83003459FD /* toolbar-preferences-network.png in Resources */ = {isa = PBXBuildFile; fileRef = 582E94F716839E83003459FD /* toolbar-preferences-network.png */; };
		582F02311370B52600B30621 /* SPExportFileNameTokenObject.m in Sources */ = {isa = PBXBuildFile; fileRef = 582F02301370B52600B30621 /* SPExportFileNameTokenObject.m */; };
		583A278E23F06F1000FBE97B /* Colors.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 583A278D23F06F1000FBE97B /* Colors.xcassets */; };
		583CA21512EC8B2200C9E763 /* SPWindow.m in Sources */ = {isa = PBXBuildFile; fileRef = 583CA21412EC8B2200C9E763 /* SPWindow.m */; };
		5841423F0F97E11000A34B47 /* NoodleLineNumberView.m in Sources */ = {isa = PBXBuildFile; fileRef = 5841423E0F97E11000A34B47 /* NoodleLineNumberView.m */; };
		584192A1101E57BB0089807F /* NSMutableArray-MultipleSort.m in Sources */ = {isa = PBXBuildFile; fileRef = 584192A0101E57BB0089807F /* NSMutableArray-MultipleSort.m */; };
		5843DA6C161FA35600EAA6D1 /* key-icon-alternate.png in Resources */ = {isa = PBXBuildFile; fileRef = 5843DA68161FA35600EAA6D1 /* key-icon-alternate.png */; };
		5843DA6D161FA35600EAA6D1 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 5843DA69161FA35600EAA6D1 /* <EMAIL> */; };
		5843DA6E161FA35600EAA6D1 /* key-icon.png in Resources */ = {isa = PBXBuildFile; fileRef = 5843DA6A161FA35600EAA6D1 /* key-icon.png */; };
		5843DA6F161FA35600EAA6D1 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 5843DA6B161FA35600EAA6D1 /* <EMAIL> */; };
		5843E247162B555B00EAA6D1 /* SPThreadAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 5843E246162B555B00EAA6D1 /* SPThreadAdditions.m */; };
		584D878B15140FEB00F24774 /* SPObjectAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 584D878A15140FEB00F24774 /* SPObjectAdditions.m */; };
		584D87921514101E00F24774 /* SPDatabaseStructure.m in Sources */ = {isa = PBXBuildFile; fileRef = 584D87911514101E00F24774 /* SPDatabaseStructure.m */; };
		584D88A91515034200F24774 /* NSNotificationCenterThreadingAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 584D88A81515034200F24774 /* NSNotificationCenterThreadingAdditions.m */; };
		584D88AA1515034200F24774 /* NSNotificationCenterThreadingAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 584D88A81515034200F24774 /* NSNotificationCenterThreadingAdditions.m */; };
		584D899D15162CBE00F24774 /* SPDataBase64EncodingAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 584D899C15162CBE00F24774 /* SPDataBase64EncodingAdditions.m */; };
		586F457E0FDB280100B428D7 /* libicucore.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 296DC8BE0F9091DF002A3258 /* libicucore.dylib */; };
		5870868410FA3E9C00D58E1C /* SPDataStorage.m in Sources */ = {isa = PBXBuildFile; fileRef = 5870868310FA3E9C00D58E1C /* SPDataStorage.m */; };
		5885CF4A116A63B200A85ACB /* SPFileHandle.m in Sources */ = {isa = PBXBuildFile; fileRef = 5885CF49116A63B200A85ACB /* SPFileHandle.m */; };
		588B2CC80FE5641E00EC5FC0 /* ssh-connected.png in Resources */ = {isa = PBXBuildFile; fileRef = 588B2CC50FE5641E00EC5FC0 /* ssh-connected.png */; };
		588B2CC90FE5641E00EC5FC0 /* ssh-connecting.png in Resources */ = {isa = PBXBuildFile; fileRef = 588B2CC60FE5641E00EC5FC0 /* ssh-connecting.png */; };
		588B2CCA0FE5641E00EC5FC0 /* ssh-disconnected.png in Resources */ = {isa = PBXBuildFile; fileRef = 588B2CC70FE5641E00EC5FC0 /* ssh-disconnected.png */; };
		589235321020C1230011DE00 /* SPHistoryController.m in Sources */ = {isa = PBXBuildFile; fileRef = 589235301020C1230011DE00 /* SPHistoryController.m */; };
		589582151154F8F400EDCC28 /* SPMainThreadTrampoline.m in Sources */ = {isa = PBXBuildFile; fileRef = 589582141154F8F400EDCC28 /* SPMainThreadTrampoline.m */; };
		58B9097B11C3A4A2000826E5 /* xibLocalizationPostprocessor.m in Sources */ = {isa = PBXBuildFile; fileRef = 58B9095B11C3A3EC000826E5 /* xibLocalizationPostprocessor.m */; };
		58C56EF50F438E120035701E /* SPDataCellFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 58C56EF40F438E120035701E /* SPDataCellFormatter.m */; };
		58CDB3300FCE138D00F8ACA3 /* SPSSHTunnel.m in Sources */ = {isa = PBXBuildFile; fileRef = 58CDB32F0FCE138D00F8ACA3 /* SPSSHTunnel.m */; };
		58CDB3400FCE13EF00F8ACA3 /* Security.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5EAC0FC0EC87FF900CC579C /* Security.framework */; };
		58CDB3410FCE141900F8ACA3 /* SequelAceTunnelAssistant.m in Sources */ = {isa = PBXBuildFile; fileRef = 58CDB3310FCE139C00F8ACA3 /* SequelAceTunnelAssistant.m */; };
		58CDB3420FCE142500F8ACA3 /* SPKeychain.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E641740EF01F80001BC333 /* SPKeychain.m */; };
		58D2A6A716FBDEFF002EB401 /* SPComboPopupButton.m in Sources */ = {isa = PBXBuildFile; fileRef = 58D2A6A616FBDEFF002EB401 /* SPComboPopupButton.m */; };
		58D2E229101222670063EF1D /* SPTextAndLinkCell.m in Sources */ = {isa = PBXBuildFile; fileRef = 58D2E227101222670063EF1D /* SPTextAndLinkCell.m */; };
		58D2E22E101222870063EF1D /* link-arrow-clicked.png in Resources */ = {isa = PBXBuildFile; fileRef = 58D2E22B101222870063EF1D /* link-arrow-clicked.png */; };
		58D2E22F101222870063EF1D /* link-arrow-highlighted-clicked.png in Resources */ = {isa = PBXBuildFile; fileRef = 58D2E22C101222870063EF1D /* link-arrow-highlighted-clicked.png */; };
		58D2E230101222870063EF1D /* link-arrow.png in Resources */ = {isa = PBXBuildFile; fileRef = 58D2E22D101222870063EF1D /* link-arrow.png */; };
		58DA8863103E15B5000B98DF /* SPLogger.m in Sources */ = {isa = PBXBuildFile; fileRef = 58DA8862103E15B5000B98DF /* SPLogger.m */; };
		58DF9F3315AB26C2003B4330 /* SPDateAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 58DF9F3215AB26C2003B4330 /* SPDateAdditions.m */; };
		58DF9F7315AB8509003B4330 /* SPSplitView.m in Sources */ = {isa = PBXBuildFile; fileRef = 58DF9F7215AB8509003B4330 /* SPSplitView.m */; };
		58E205FC1234FE4F00A97059 /* KeyTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 58E205FB1234FE4F00A97059 /* KeyTemplate.pdf */; };
		58F48AA3161D03C6008536A1 /* quick-connect-icon.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 58F48AA2161D03C6008536A1 /* quick-connect-icon.pdf */; };
		58F48B2E161D08C0008536A1 /* quick-connect-icon-white.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 58F48B2D161D08C0008536A1 /* quick-connect-icon-white.pdf */; };
		58FEF16D0F23D66600518E8E /* SPSQLParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 58FEF16C0F23D66600518E8E /* SPSQLParser.m */; };
		58FEF57E0F3B4E9700518E8E /* SPTableData.m in Sources */ = {isa = PBXBuildFile; fileRef = 58FEF57D0F3B4E9700518E8E /* SPTableData.m */; };
		65F52CC824AE21D600FED3CB /* SPFilePreferencePane.m in Sources */ = {isa = PBXBuildFile; fileRef = 65F52CC724AE21D600FED3CB /* SPFilePreferencePane.m */; };
		6F0EA8ED272F33B700514FF1 /* SPBundleManagerAdditions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6F0EA8EC272F33B700514FF1 /* SPBundleManagerAdditions.swift */; };
		6F0EA9242734ABE200514FF1 /* SABundleRunner.m in Sources */ = {isa = PBXBuildFile; fileRef = 6F0EA9232734ABE200514FF1 /* SABundleRunner.m */; };
		73F70A961E4E547500636550 /* SPJSONFormatter.m in Sources */ = {isa = PBXBuildFile; fileRef = 73F70A951E4E547500636550 /* SPJSONFormatter.m */; };
		8831EFA8224011B700D10172 /* button_addTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFA5224011B700D10172 /* button_addTemplate.pdf */; };
		8831EFAA2240128700D10172 /* button_removeTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFA92240128600D10172 /* button_removeTemplate.pdf */; };
		8831EFAC2240131500D10172 /* button_editTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFAB2240131400D10172 /* button_editTemplate.pdf */; };
		8831EFAE2240135400D10172 /* button_duplicateTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFAD2240135300D10172 /* button_duplicateTemplate.pdf */; };
		8831EFB0224013AF00D10172 /* button_refreshTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFAF224013AF00D10172 /* button_refreshTemplate.pdf */; };
		8831EFB22240143600D10172 /* button_bar_handleTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFB12240143600D10172 /* button_bar_handleTemplate.pdf */; };
		8831EFB4224014C700D10172 /* button_clearTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFB3224014C700D10172 /* button_clearTemplate.pdf */; };
		8831EFB62240150A00D10172 /* button_add_folderTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFB52240150A00D10172 /* button_add_folderTemplate.pdf */; };
		8831EFB92240154A00D10172 /* button_leftTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFB72240154A00D10172 /* button_leftTemplate.pdf */; };
		8831EFBA2240154A00D10172 /* button_rightTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFB82240154A00D10172 /* button_rightTemplate.pdf */; };
		8831EFBD2240159E00D10172 /* button_pane_hideTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFBB2240159D00D10172 /* button_pane_hideTemplate.pdf */; };
		8831EFBE2240159E00D10172 /* button_pane_showTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFBC2240159E00D10172 /* button_pane_showTemplate.pdf */; };
		8831EFC12240166D00D10172 /* button_select_allTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFBF2240166D00D10172 /* button_select_allTemplate.pdf */; };
		8831EFC22240166D00D10172 /* button_select_noneTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFC02240166D00D10172 /* button_select_noneTemplate.pdf */; };
		8831EFC7224016E000D10172 /* button_filterTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFC3224016DF00D10172 /* button_filterTemplate.pdf */; };
		8831EFC8224016E000D10172 /* button_filter_activeTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFC4224016DF00D10172 /* button_filter_activeTemplate.pdf */; };
		8831EFC9224016E000D10172 /* button_edit_mode_selectedTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFC5224016E000D10172 /* button_edit_mode_selectedTemplate.pdf */; };
		8831EFCA224016E000D10172 /* button_edit_modeTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFC6224016E000D10172 /* button_edit_modeTemplate.pdf */; };
		8831EFCD2240175400D10172 /* button_actionTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFCB2240175300D10172 /* button_actionTemplate.pdf */; };
		8831EFCE2240175400D10172 /* button_paginationTemplate.pdf in Resources */ = {isa = PBXBuildFile; fileRef = 8831EFCC2240175300D10172 /* button_paginationTemplate.pdf */; };
		8AEAC27B5C5B866575ECDB62 /* TableSortHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8AEACD304316A0931DF8ED26 /* TableSortHelper.swift */; };
		8AEACED018AABDD8CBB42877 /* TableSortHelperTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8AEACADD4B36D9819ADC93DD /* TableSortHelperTests.swift */; };
		8D15AC340486D014006FF6A4 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1058C7A7FEA54F5311CA2CBB /* Cocoa.framework */; };
		963DA1C72CACC71000B5A544 /* QuickLookUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 963DA1C22CACC6F000B5A544 /* QuickLookUI.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		963DA1C82CACC71B00B5A544 /* QuickLookUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 963DA1C22CACC6F000B5A544 /* QuickLookUI.framework */; settings = {ATTRIBUTES = (Weak, ); }; };
		964908C8249A77D00052FC4A /* ssh_config in Resources */ = {isa = PBXBuildFile; fileRef = 964908C4249A77CF0052FC4A /* ssh_config */; };
		9651262224926F1200E65B53 /* QueryKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 17E5955314F304000054EE08 /* QueryKit.framework */; };
		9651262324926F1200E65B53 /* QueryKit.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = 17E5955314F304000054EE08 /* QueryKit.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		9651262424926F1600E65B53 /* SPMySQL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 584D876815140D3500F24774 /* SPMySQL.framework */; };
		9651262524926F1600E65B53 /* SPMySQL.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = 584D876815140D3500F24774 /* SPMySQL.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		967D875B24B67B4300BAE934 /* SPAutosizingTextView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 967D875A24B67B4300BAE934 /* SPAutosizingTextView.swift */; };
		969178A524A5640D0012ED42 /* Default Bundles in Copy Default Themes and Default Bundles */ = {isa = PBXBuildFile; fileRef = 969178A324A5640D0012ED42 /* Default Bundles */; };
		969178A624A5640D0012ED42 /* Default Themes in Copy Default Themes and Default Bundles */ = {isa = PBXBuildFile; fileRef = 969178A424A5640D0012ED42 /* Default Themes */; };
		96A5DDB82D63C8E70079105E /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 96A5DDB42D63C8E70079105E /* libc++.tbd */; };
		96B0A1512493DF3E007BB270 /* ShortcutRecorder.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 96B0A1502493DF16007BB270 /* ShortcutRecorder.framework */; };
		96B0A1522493DF3E007BB270 /* ShortcutRecorder.framework in Copy Frameworks */ = {isa = PBXBuildFile; fileRef = 96B0A1502493DF16007BB270 /* ShortcutRecorder.framework */; settings = {ATTRIBUTES = (CodeSignOnCopy, RemoveHeadersOnCopy, ); }; };
		96B0A16124941670007BB270 /* SequelAceTunnelAssistant in Copy SequelAceTunnelAssistant and sign */ = {isa = PBXBuildFile; fileRef = 58CDB3360FCE13C900F8ACA3 /* SequelAceTunnelAssistant */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
		96CA8F9C2783A9010061C2D1 /* Menlo.ttc in Resources */ = {isa = PBXBuildFile; fileRef = 96CA8F9B2783A8FE0061C2D1 /* Menlo.ttc */; };
		96CA8FA12783AA650061C2D1 /* Menlo.ttc in Copy Fonts */ = {isa = PBXBuildFile; fileRef = 96CA8F9B2783A8FE0061C2D1 /* Menlo.ttc */; };
		9BE765682376A00C82FB93AA /* SPHelpViewerClient.m in Sources */ = {isa = PBXBuildFile; fileRef = 9BE764320CE8E86E8F63647B /* SPHelpViewerClient.m */; };
		9BE765EBBDFD2F121C13D274 /* SPFillView.m in Sources */ = {isa = PBXBuildFile; fileRef = 9BE768F3989033CEDDC2027E /* SPFillView.m */; };
		9BE76F2886901784E4FD2321 /* SPFilterTableController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9BE76A3D5C9830E2F7738770 /* SPFilterTableController.m */; };
		9BE76F2B943AFDBA6EDC52BE /* SPHelpViewerController.m in Sources */ = {isa = PBXBuildFile; fileRef = 9BE760B3C4586EA3B1A48600 /* SPHelpViewerController.m */; };
		B51D6B9E114C310C0074704E /* toolbar-switch-to-table-triggers.png in Resources */ = {isa = PBXBuildFile; fileRef = B51D6B9D114C310C0074704E /* toolbar-switch-to-table-triggers.png */; };
		B52460D70F8EF92300171639 /* SPArrayAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = B52460D40F8EF92300171639 /* SPArrayAdditions.m */; };
		B52460D80F8EF92300171639 /* SPTextViewAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = B52460D60F8EF92300171639 /* SPTextViewAdditions.m */; };
		B54F25E60FD909C400E2CF36 /* toolbar-switch-to-table-relations.png in Resources */ = {isa = PBXBuildFile; fileRef = B54F25E50FD909C400E2CF36 /* toolbar-switch-to-table-relations.png */; };
		B57747D40F7A8974003B34F9 /* SPPreferenceController.m in Sources */ = {isa = PBXBuildFile; fileRef = B57747D30F7A8974003B34F9 /* SPPreferenceController.m */; };
		B57747D90F7A8990003B34F9 /* SPWindowAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = B57747D80F7A8990003B34F9 /* SPWindowAdditions.m */; };
		B57747DC0F7A89D0003B34F9 /* SPFavoriteTextFieldCell.m in Sources */ = {isa = PBXBuildFile; fileRef = B57747DB0F7A89D0003B34F9 /* SPFavoriteTextFieldCell.m */; };
		B57748420F7A8B57003B34F9 /* database.png in Resources */ = {isa = PBXBuildFile; fileRef = B577483A0F7A8B57003B34F9 /* database.png */; };
		B58731280F838C9E00087794 /* PreferenceDefaults.plist in Resources */ = {isa = PBXBuildFile; fileRef = B58731270F838C9E00087794 /* PreferenceDefaults.plist */; };
		B5E2C5FA0F2353B5007446E0 /* table-property.png in Resources */ = {isa = PBXBuildFile; fileRef = B5E2C5F90F2353B5007446E0 /* table-property.png */; };
		B5E92F1C0F75B2E800012500 /* SPExportController.m in Sources */ = {isa = PBXBuildFile; fileRef = B5E92F1B0F75B2E800012500 /* SPExportController.m */; };
		BC01BCCF104024BE006BDEE7 /* SPEncodingPopupAccessory.m in Sources */ = {isa = PBXBuildFile; fileRef = BC01BCCE104024BE006BDEE7 /* SPEncodingPopupAccessory.m */; };
		BC05F1C5101241DF008A97F8 /* YRKSpinningProgressIndicator.m in Sources */ = {isa = PBXBuildFile; fileRef = BC05F1C4101241DF008A97F8 /* YRKSpinningProgressIndicator.m */; };
		BC09D7DE12A786FB0030DB64 /* cancel-clicked-highlighted.png in Resources */ = {isa = PBXBuildFile; fileRef = BC09D7D812A786FB0030DB64 /* cancel-clicked-highlighted.png */; };
		BC09D7DF12A786FB0030DB64 /* cancel-clicked.png in Resources */ = {isa = PBXBuildFile; fileRef = BC09D7D912A786FB0030DB64 /* cancel-clicked.png */; };
		BC09D7E012A786FB0030DB64 /* cancel-highlighted.png in Resources */ = {isa = PBXBuildFile; fileRef = BC09D7DA12A786FB0030DB64 /* cancel-highlighted.png */; };
		BC09D7E112A786FB0030DB64 /* cancel-hovered-highlighted.png in Resources */ = {isa = PBXBuildFile; fileRef = BC09D7DB12A786FB0030DB64 /* cancel-hovered-highlighted.png */; };
		BC09D7E212A786FB0030DB64 /* cancel-hovered.png in Resources */ = {isa = PBXBuildFile; fileRef = BC09D7DC12A786FB0030DB64 /* cancel-hovered.png */; };
		BC09D7E312A786FB0030DB64 /* cancel.png in Resources */ = {isa = PBXBuildFile; fileRef = BC09D7DD12A786FB0030DB64 /* cancel.png */; };
		BC0ED3DA12A9196C00088461 /* SPChooseMenuItemDialog.m in Sources */ = {isa = PBXBuildFile; fileRef = BC0ED3D912A9196C00088461 /* SPChooseMenuItemDialog.m */; };
		BC1847EA0FE6EC8400094BFB /* SPEditSheetTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = BC1847E90FE6EC8400094BFB /* SPEditSheetTextView.m */; };
		BC1944D01297291800A236CD /* SPBundleCommandTextView.m in Sources */ = {isa = PBXBuildFile; fileRef = BC1944CF1297291800A236CD /* SPBundleCommandTextView.m */; };
		BC2777A011514B940034DF6A /* SPNavigatorController.m in Sources */ = {isa = PBXBuildFile; fileRef = BC27779F11514B940034DF6A /* SPNavigatorController.m */; };
		BC2898F3125F4488001B50E1 /* SPGeometryDataView.m in Sources */ = {isa = PBXBuildFile; fileRef = BC2898F2125F4488001B50E1 /* SPGeometryDataView.m */; };
		BC29C37F10501EFD00DD6C6E /* SPQueryController.m in Sources */ = {isa = PBXBuildFile; fileRef = BC29C37E10501EFD00DD6C6E /* SPQueryController.m */; };
		BC2C16D40FEBEDF10003993B /* SPDataAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = BC2C16D30FEBEDF10003993B /* SPDataAdditions.m */; };
		BC2C8E220FA8C2DB008468C7 /* SPMySQLHelpTemplate.html in Resources */ = {isa = PBXBuildFile; fileRef = BC2C8E210FA8C2DB008468C7 /* SPMySQLHelpTemplate.html */; };
		BC32F242121D66260067305E /* SPFileManagerAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = BC32F241121D66260067305E /* SPFileManagerAdditions.m */; };
		BC398A2D121D526200BE3EF4 /* SPCopyTable.m in Sources */ = {isa = PBXBuildFile; fileRef = BC398A2C121D526200BE3EF4 /* SPCopyTable.m */; };
		BC4DF1981158FB280059FABD /* SPNavigatorOutlineView.m in Sources */ = {isa = PBXBuildFile; fileRef = BC4DF1971158FB280059FABD /* SPNavigatorOutlineView.m */; };
		BC5750D512A6233900911BA2 /* SPActivityTextFieldCell.m in Sources */ = {isa = PBXBuildFile; fileRef = BC5750D312A6233900911BA2 /* SPActivityTextFieldCell.m */; };
		BC675A141072039C00C5ACD4 /* SPContentFilterManager.m in Sources */ = {isa = PBXBuildFile; fileRef = BC675A131072039C00C5ACD4 /* SPContentFilterManager.m */; };
		BC68BFC7128D4EAE004907D9 /* SPBundleEditorController.m in Sources */ = {isa = PBXBuildFile; fileRef = BC68BFC6128D4EAE004907D9 /* SPBundleEditorController.m */; };
		BC77C5E4129AA69E009AD832 /* SPBundleHTMLOutputController.m in Sources */ = {isa = PBXBuildFile; fileRef = BC77C5E3129AA69E009AD832 /* SPBundleHTMLOutputController.m */; };
		BC85F5D012193B7D00E255B5 /* SPColorAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = BC85F5CF12193B7D00E255B5 /* SPColorAdditions.m */; };
		BC878A71121A836F00AE5066 /* SPColorWellCell.m in Sources */ = {isa = PBXBuildFile; fileRef = BC878A70121A836F00AE5066 /* SPColorWellCell.m */; };
		BC8C8532100E0A8000D7A129 /* SPTableView.m in Sources */ = {isa = PBXBuildFile; fileRef = BC8C8531100E0A8000D7A129 /* SPTableView.m */; };
		BC962D661144EACA006170BD /* CompletionTokens.plist in Resources */ = {isa = PBXBuildFile; fileRef = BC962D651144EACA006170BD /* CompletionTokens.plist */; };
		BC9F0881100FCF2C00A80D32 /* SPFieldEditorController.m in Sources */ = {isa = PBXBuildFile; fileRef = BC9F0880100FCF2C00A80D32 /* SPFieldEditorController.m */; };
		BCA6271C1031B9D40047E5D5 /* SPTooltip.m in Sources */ = {isa = PBXBuildFile; fileRef = BCA6271B1031B9D40047E5D5 /* SPTooltip.m */; };
		BCD0AD490FBBFC340066EA5C /* SPSQLTokenizer.l in Sources */ = {isa = PBXBuildFile; fileRef = BCD0AD480FBBFC340066EA5C /* SPSQLTokenizer.l */; };
		BCE0025D11173D2A009DA533 /* SPFieldMapperController.m in Sources */ = {isa = PBXBuildFile; fileRef = BCE0025C11173D2A009DA533 /* SPFieldMapperController.m */; };
		C9AD7C781676138000234EEE /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = C9AD7C771676138000234EEE /* <EMAIL> */; };
		C9AD7C7B1676158C00234EEE /* toolbar-switch-to-sql.png in Resources */ = {isa = PBXBuildFile; fileRef = C9AD7C791676158C00234EEE /* toolbar-switch-to-sql.png */; };
		C9AD7C7C1676158C00234EEE /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = C9AD7C7A1676158C00234EEE /* <EMAIL> */; };
		C9C994411678A439001F5DA8 /* button_bar_spacer.png in Resources */ = {isa = PBXBuildFile; fileRef = C9C9943F1678A439001F5DA8 /* button_bar_spacer.png */; };
		C9C994491678B3E6001F5DA8 /* table-small-square.png in Resources */ = {isa = PBXBuildFile; fileRef = C9C994471678B3E6001F5DA8 /* table-small-square.png */; };
		C9C9944A1678B3E6001F5DA8 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = C9C994481678B3E6001F5DA8 /* <EMAIL> */; };
		C9C9944D1678BCFA001F5DA8 /* table-small.png in Resources */ = {isa = PBXBuildFile; fileRef = C9C9944B1678BCFA001F5DA8 /* table-small.png */; };
		C9C9944E1678BCFA001F5DA8 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = C9C9944C1678BCFA001F5DA8 /* <EMAIL> */; };
		C9F92710162D38D70051CB2E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = C9F9270F162D38D70051CB2E /* <EMAIL> */; };
		C9F92712162D39E60051CB2E /* toolbar-switch-to-browse.png in Resources */ = {isa = PBXBuildFile; fileRef = C9F92711162D39E60051CB2E /* toolbar-switch-to-browse.png */; };
		C9F92714162D39FE0051CB2E /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = C9F92713162D39FE0051CB2E /* <EMAIL> */; };
		D35577F52728C6CF002B3989 /* SPWindowTabAccessory.swift in Sources */ = {isa = PBXBuildFile; fileRef = D35577F42728C6CF002B3989 /* SPWindowTabAccessory.swift */; };
		FD0E72EA2C38DEA1007EF348 /* SATableHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD0E72E92C38DC0E007EF348 /* SATableHeaderView.swift */; };
		FD0E72EC2C391F44007EF348 /* SQLiteDisplayFormatManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD0E72EB2C391F44007EF348 /* SQLiteDisplayFormatManager.swift */; };
		FD18C8992C3C9B2F002A5D57 /* SAUuidFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD18C8982C3C9B2F002A5D57 /* SAUuidFormatter.swift */; };
		FD2056052C3A7E90008DD271 /* SABaseFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD2056042C3A7E90008DD271 /* SABaseFormatter.swift */; };
		FD6DFF872ADB40630057B713 /* SPTableHistoryManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD6DFF862ADB40630057B713 /* SPTableHistoryManager.swift */; };
		FD8099A72C59DCFA0084646F /* SAUuidFormatterTests.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD8099A62C59DCFA0084646F /* SAUuidFormatterTests.swift */; };
		FD8099A82C59DDF70084646F /* SAUuidFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD18C8982C3C9B2F002A5D57 /* SAUuidFormatter.swift */; };
		FD8099A92C59DE1D0084646F /* SABaseFormatter.swift in Sources */ = {isa = PBXBuildFile; fileRef = FD2056042C3A7E90008DD271 /* SABaseFormatter.swift */; };
		FDCF55E52788278500D30655 /* TableSortHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8AEACD304316A0931DF8ED26 /* TableSortHelper.swift */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		17D41AB12171157B00B1888D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 584D876015140D3500F24774 /* SPMySQLFramework.xcodeproj */;
			proxyType = 1;
			remoteGlobalIDString = 8DC2EF4F0486A6940098B216;
			remoteInfo = SPMySQL.framework;
		};
		17E5955214F304000054EE08 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 17E5954E14F304000054EE08 /* QueryKit.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 8DC2EF5B0486A6940098B216;
			remoteInfo = QueryKit;
		};
		17E596A114F307CE0054EE08 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 17E5954E14F304000054EE08 /* QueryKit.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 17E5969E14F307CE0054EE08;
			remoteInfo = Tests;
		};
		29B70BD51C805B2A00D1BE0C /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 584D876015140D3500F24774 /* SPMySQLFramework.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 507FF1D51BC0D7D300104523;
			remoteInfo = "SPMySQL Unit Tests";
		};
		518402D824A3FF4F004693B0 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2A37F4A9FDCFA73011CA2CEA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8D15AC270486D014006FF6A4;
			remoteInfo = "Sequel Ace";
		};
		584D876715140D3500F24774 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 584D876015140D3500F24774 /* SPMySQLFramework.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 8DC2EF5B0486A6940098B216;
			remoteInfo = SPMySQL.framework;
		};
		58B9096F11C3A462000826E5 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2A37F4A9FDCFA73011CA2CEA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 58B9096011C3A42B000826E5;
			remoteInfo = xibLocalizationPostprocessor;
		};
		58CDB34A0FCE144000F8ACA3 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 2A37F4A9FDCFA73011CA2CEA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 58CDB3350FCE13C900F8ACA3;
			remoteInfo = TunnelPassphraseRequester;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		17E20DEF12D6602F007F75A6 /* Copy Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				50EA92661AB23ED3008D3C4F /* SPMySQL.framework in Copy Frameworks */,
			);
			name = "Copy Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		4DECC4940EC2B447008D359E /* Copy Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
				9651262324926F1200E65B53 /* QueryKit.framework in Copy Frameworks */,
				96B0A1522493DF3E007BB270 /* ShortcutRecorder.framework in Copy Frameworks */,
				9651262524926F1600E65B53 /* SPMySQL.framework in Copy Frameworks */,
			);
			name = "Copy Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		9691789224A562330012ED42 /* Copy Default Themes and Default Bundles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 12;
			dstPath = "";
			dstSubfolderSpec = 12;
			files = (
				969178A524A5640D0012ED42 /* Default Bundles in Copy Default Themes and Default Bundles */,
				969178A624A5640D0012ED42 /* Default Themes in Copy Default Themes and Default Bundles */,
			);
			name = "Copy Default Themes and Default Bundles";
			runOnlyForDeploymentPostprocessing = 0;
		};
		96B0A162249416B1007BB270 /* Copy SequelAceTunnelAssistant and sign */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 6;
			files = (
				96B0A16124941670007BB270 /* SequelAceTunnelAssistant in Copy SequelAceTunnelAssistant and sign */,
			);
			name = "Copy SequelAceTunnelAssistant and sign";
			runOnlyForDeploymentPostprocessing = 0;
		};
		96CA8FA02783AA560061C2D1 /* Copy Fonts */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = Fonts;
			dstSubfolderSpec = 7;
			files = (
				96CA8FA12783AA650061C2D1 /* Menlo.ttc in Copy Fonts */,
			);
			name = "Copy Fonts";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		1058C7A7FEA54F5311CA2CBB /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = /System/Library/Frameworks/Cocoa.framework; sourceTree = "<absolute>"; };
		112730551180788A000737FD /* SPTableCopyTest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableCopyTest.m; sourceTree = "<group>"; };
		1141A387117BBFF200126A28 /* SPTableCopy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTableCopy.h; sourceTree = "<group>"; };
		1141A388117BBFF200126A28 /* SPTableCopy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableCopy.m; sourceTree = "<group>"; };
		1198F5B11174EDD500670590 /* SPDatabaseCopy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDatabaseCopy.h; sourceTree = "<group>"; };
		1198F5B21174EDD500670590 /* SPDatabaseCopy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDatabaseCopy.m; sourceTree = "<group>"; };
		1198F5C31174EF3F00670590 /* SPDatabaseCopyTest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDatabaseCopyTest.m; sourceTree = "<group>"; };
		11B55BFC1189E3B2009EF465 /* SPDatabaseAction.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDatabaseAction.h; sourceTree = "<group>"; };
		11B55BFD1189E3B2009EF465 /* SPDatabaseAction.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDatabaseAction.m; sourceTree = "<group>"; };
		11C2109C1180E70800758039 /* SPDatabaseRename.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDatabaseRename.h; sourceTree = "<group>"; };
		11C2109D1180E70800758039 /* SPDatabaseRename.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDatabaseRename.m; sourceTree = "<group>"; };
		11C210DE1180E9B800758039 /* SPDatabaseRenameTest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDatabaseRenameTest.m; sourceTree = "<group>"; };
		171312CC109D23C700FB465F /* SPTableTextFieldCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTableTextFieldCell.h; sourceTree = "<group>"; };
		171312CD109D23C700FB465F /* SPTableTextFieldCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableTextFieldCell.m; sourceTree = "<group>"; };
		171C398D16BD634600209EC6 /* SPDatabaseContentViewDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDatabaseContentViewDelegate.h; sourceTree = "<group>"; };
		17292441107AC41000B21980 /* SPXMLExporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPXMLExporter.h; sourceTree = "<group>"; };
		17292442107AC41000B21980 /* SPXMLExporter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPXMLExporter.m; sourceTree = "<group>"; };
		172A650F0F7BED7A001E861A /* SPConsoleMessage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPConsoleMessage.h; sourceTree = "<group>"; };
		172A65100F7BED7A001E861A /* SPConsoleMessage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPConsoleMessage.m; sourceTree = "<group>"; };
		173284E81088FEDE0062E892 /* SPConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPConstants.h; sourceTree = "<group>"; };
		173284E91088FEDE0062E892 /* SPConstants.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPConstants.m; sourceTree = "<group>"; };
		173C4364104455E0001F3A30 /* SPQueryFavoriteManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPQueryFavoriteManager.h; sourceTree = "<group>"; };
		173C4365104455E0001F3A30 /* SPQueryFavoriteManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPQueryFavoriteManager.m; sourceTree = "<group>"; };
		173C44D61044A6AF001F3A30 /* SPOutlineView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPOutlineView.h; sourceTree = "<group>"; };
		173C44D71044A6B0001F3A30 /* SPOutlineView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPOutlineView.m; sourceTree = "<group>"; };
		173C836F11AAD26E00B8B084 /* SPExportUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPExportUtilities.h; sourceTree = "<group>"; };
		173C837011AAD26E00B8B084 /* SPExportUtilities.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPExportUtilities.m; sourceTree = "<group>"; };
		173C837311AAD2AE00B8B084 /* SPDotExporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDotExporter.h; sourceTree = "<group>"; };
		173C837411AAD2AE00B8B084 /* SPDotExporter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDotExporter.m; sourceTree = "<group>"; };
		173C837511AAD2AE00B8B084 /* SPHTMLExporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPHTMLExporter.h; sourceTree = "<group>"; };
		173C837611AAD2AE00B8B084 /* SPHTMLExporter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPHTMLExporter.m; sourceTree = "<group>"; };
		173C837711AAD2AE00B8B084 /* SPPDFExporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPPDFExporter.h; sourceTree = "<group>"; };
		173C837811AAD2AE00B8B084 /* SPPDFExporter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPPDFExporter.m; sourceTree = "<group>"; };
		173C837E11AAD2FF00B8B084 /* SPCSVExporterProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPCSVExporterProtocol.h; sourceTree = "<group>"; };
		173C837F11AAD2FF00B8B084 /* SPDotExporterProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDotExporterProtocol.h; sourceTree = "<group>"; };
		173C838011AAD2FF00B8B084 /* SPHTMLExporterProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPHTMLExporterProtocol.h; sourceTree = "<group>"; };
		173C838111AAD2FF00B8B084 /* SPPDFExporterProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPPDFExporterProtocol.h; sourceTree = "<group>"; };
		173C838211AAD2FF00B8B084 /* SPSQLExporterProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPSQLExporterProtocol.h; sourceTree = "<group>"; };
		173C838311AAD2FF00B8B084 /* SPXMLExporterProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPXMLExporterProtocol.h; sourceTree = "<group>"; };
		1740FAB90FC4372F00CF3699 /* SPDatabaseData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDatabaseData.h; sourceTree = "<group>"; };
		1740FABA0FC4372F00CF3699 /* SPDatabaseData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDatabaseData.m; sourceTree = "<group>"; };
		174CE14010AB9281008F892B /* SPProcessListController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPProcessListController.h; sourceTree = "<group>"; };
		174CE14110AB9281008F892B /* SPProcessListController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPProcessListController.m; sourceTree = "<group>"; };
		1755A25C16B33BEA00B35787 /* SPSyntaxParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPSyntaxParser.h; sourceTree = "<group>"; };
		175EC64C12733CDF009A7C0F /* SPCategoryAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPCategoryAdditions.h; sourceTree = "<group>"; };
		176E14CF15570FE300FAF326 /* SPBundleCommandRunner.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPBundleCommandRunner.h; sourceTree = "<group>"; };
		176E14D015570FE300FAF326 /* SPBundleCommandRunner.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPBundleCommandRunner.m; sourceTree = "<group>"; };
		177E792B0FCB54EC00E9E122 /* database-small.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "database-small.png"; sourceTree = "<group>"; };
		177E792C0FCB54EC00E9E122 /* dummy-small.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "dummy-small.png"; sourceTree = "<group>"; };
		177E7A210FCB6A2E00E9E122 /* SPExtendedTableInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPExtendedTableInfo.h; sourceTree = "<group>"; };
		177E7A220FCB6A2E00E9E122 /* SPExtendedTableInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPExtendedTableInfo.m; sourceTree = "<group>"; };
		1785E9F5127D8C7500F468C8 /* SPPreferencePane.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPPreferencePane.h; sourceTree = "<group>"; };
		1785E9F6127D8C7500F468C8 /* SPPreferencePane.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPPreferencePane.m; sourceTree = "<group>"; };
		1785EA21127DAF3300F468C8 /* SPTablesPreferencePane.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTablesPreferencePane.h; sourceTree = "<group>"; };
		1785EA22127DAF3300F468C8 /* SPTablesPreferencePane.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTablesPreferencePane.m; sourceTree = "<group>"; };
		1785EB5E127DD5A800F468C8 /* SPNotificationsPreferencePane.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPNotificationsPreferencePane.h; sourceTree = "<group>"; };
		1785EB5F127DD5A800F468C8 /* SPNotificationsPreferencePane.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPNotificationsPreferencePane.m; sourceTree = "<group>"; };
		1785EB64127DD5EA00F468C8 /* SPNetworkPreferencePane.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPNetworkPreferencePane.h; sourceTree = "<group>"; };
		1785EB65127DD5EA00F468C8 /* SPNetworkPreferencePane.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPNetworkPreferencePane.m; sourceTree = "<group>"; };
		1785EB68127DD79300F468C8 /* SPEditorPreferencePane.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPEditorPreferencePane.h; sourceTree = "<group>"; };
		1785EB69127DD79300F468C8 /* SPEditorPreferencePane.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPEditorPreferencePane.m; sourceTree = "<group>"; };
		1789343A0F30C1DD0097539A /* SPStringAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPStringAdditions.h; sourceTree = "<group>"; };
		1789343B0F30C1DD0097539A /* SPStringAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPStringAdditions.m; sourceTree = "<group>"; };
		1792C13510AD75C800ABE758 /* SPServerVariablesController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPServerVariablesController.h; sourceTree = "<group>"; };
		1792C13610AD75C800ABE758 /* SPServerVariablesController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPServerVariablesController.m; sourceTree = "<group>"; };
		1798F17E1550171B004B0AB8 /* LICENSE */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = LICENSE; sourceTree = "<group>"; };
		1798F1811550175B004B0AB8 /* SPFavoritesExporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFavoritesExporter.h; sourceTree = "<group>"; };
		1798F1821550175B004B0AB8 /* SPFavoritesExporter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFavoritesExporter.m; sourceTree = "<group>"; };
		1798F1831550175B004B0AB8 /* SPFavoritesExportProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFavoritesExportProtocol.h; sourceTree = "<group>"; };
		1798F1841550175B004B0AB8 /* SPFavoritesImporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFavoritesImporter.h; sourceTree = "<group>"; };
		1798F1851550175B004B0AB8 /* SPFavoritesImporter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFavoritesImporter.m; sourceTree = "<group>"; };
		1798F1861550175B004B0AB8 /* SPFavoritesImportProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFavoritesImportProtocol.h; sourceTree = "<group>"; };
		1798F1931550181B004B0AB8 /* SPGroupNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPGroupNode.h; sourceTree = "<group>"; };
		1798F1941550181B004B0AB8 /* SPGroupNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPGroupNode.m; sourceTree = "<group>"; };
		1798F19615501838004B0AB8 /* SPMutableArrayAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPMutableArrayAdditions.h; sourceTree = "<group>"; };
		1798F19715501838004B0AB8 /* SPMutableArrayAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPMutableArrayAdditions.m; sourceTree = "<group>"; };
		1798F1991550185B004B0AB8 /* SPTreeNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTreeNode.h; sourceTree = "<group>"; };
		1798F19A1550185B004B0AB8 /* SPTreeNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTreeNode.m; sourceTree = "<group>"; };
		1798F1C2155018D4004B0AB8 /* SPMutableArrayAdditionsTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPMutableArrayAdditionsTests.m; sourceTree = "<group>"; };
		179ECEC611F265EE009C6A40 /* libbz2.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libbz2.dylib; path = usr/lib/libbz2.dylib; sourceTree = SDKROOT; };
		179F15040F7C433C00579954 /* SPEditorTokens.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPEditorTokens.h; sourceTree = "<group>"; };
		179F15050F7C433C00579954 /* SPEditorTokens.l */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.lex; path = SPEditorTokens.l; sourceTree = "<group>"; };
		17A20AC4124F9B110095CEFB /* SPServerSupport.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPServerSupport.h; sourceTree = "<group>"; };
		17A20AC5124F9B110095CEFB /* SPServerSupport.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPServerSupport.m; sourceTree = "<group>"; };
		17A7773211C52D8E001E27B4 /* SPIndexesController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPIndexesController.h; sourceTree = "<group>"; };
		17A7773311C52D8E001E27B4 /* SPIndexesController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPIndexesController.m; sourceTree = "<group>"; };
		17B548611E81FFA600175D5A /* SPCreateDatabaseInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPCreateDatabaseInfo.h; sourceTree = "<group>"; };
		17B548621E81FFA600175D5A /* SPCreateDatabaseInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPCreateDatabaseInfo.m; sourceTree = "<group>"; };
		17B7B591101602AE00F057DE /* libssl.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libssl.dylib; path = /usr/lib/libssl.dylib; sourceTree = "<absolute>"; };
		17C058860FC9FC390077E9CF /* SPNarrowDownCompletion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPNarrowDownCompletion.h; sourceTree = "<group>"; };
		17C058870FC9FC390077E9CF /* SPNarrowDownCompletion.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPNarrowDownCompletion.m; sourceTree = "<group>"; };
		17CC97F110B4ABE90034CD7A /* SPAboutController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPAboutController.h; sourceTree = "<group>"; };
		17CC97F210B4ABE90034CD7A /* SPAboutController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPAboutController.m; sourceTree = "<group>"; };
		17CC993A10B4C9C80034CD7A /* License.rtf */ = {isa = PBXFileReference; lastKnownFileType = text.rtf; path = License.rtf; sourceTree = "<group>"; };
		17D38F6E1279E23A00672B13 /* SPTableFieldValidation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTableFieldValidation.h; sourceTree = "<group>"; };
		17D38F6F1279E23A00672B13 /* SPTableFieldValidation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableFieldValidation.m; sourceTree = "<group>"; };
		17D38FC3127B0CFC00672B13 /* SPConnectionControllerDelegateProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPConnectionControllerDelegateProtocol.h; sourceTree = "<group>"; };
		17D390A8127B556F00672B13 /* SPPreferencePaneProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPPreferencePaneProtocol.h; sourceTree = "<group>"; };
		17D390C6127B65AF00672B13 /* SPGeneralPreferencePane.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPGeneralPreferencePane.h; sourceTree = "<group>"; };
		17D390C7127B65AF00672B13 /* SPGeneralPreferencePane.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPGeneralPreferencePane.m; sourceTree = "<group>"; };
		17D390C9127B6BF800672B13 /* SPPreferencesUpgrade.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPPreferencesUpgrade.h; sourceTree = "<group>"; };
		17D390CA127B6BF800672B13 /* SPPreferencesUpgrade.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPPreferencesUpgrade.m; sourceTree = "<group>"; };
		17D3C22012859E070047709F /* SPFavoriteNode.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFavoriteNode.h; sourceTree = "<group>"; };
		17D3C22112859E070047709F /* SPFavoriteNode.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFavoriteNode.m; sourceTree = "<group>"; };
		17D3C66C128AD4710047709F /* SPFavoritesController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFavoritesController.h; sourceTree = "<group>"; };
		17D3C66D128AD4710047709F /* SPFavoritesController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFavoritesController.m; sourceTree = "<group>"; };
		17D3C66F128AD8160047709F /* SPSingleton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPSingleton.h; sourceTree = "<group>"; };
		17D3C670128AD8160047709F /* SPSingleton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPSingleton.m; sourceTree = "<group>"; };
		17D3C6D1128B1C900047709F /* SPFavoritesOutlineView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFavoritesOutlineView.h; sourceTree = "<group>"; };
		17D3C6D2128B1C900047709F /* SPFavoritesOutlineView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFavoritesOutlineView.m; sourceTree = "<group>"; };
		17D5B49C1553059F00EF3BB3 /* SPViewCopy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPViewCopy.h; sourceTree = "<group>"; };
		17D5B49D1553059F00EF3BB3 /* SPViewCopy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPViewCopy.m; sourceTree = "<group>"; };
		17DA04EA0FC1A7DA00D66140 /* Unit Tests-Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = "Unit Tests-Info.plist"; path = "Plists/Unit Tests-Info.plist"; sourceTree = "<group>"; };
		17DD52B6115071D0007D8950 /* SPPrintTemplate.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = SPPrintTemplate.html; path = Templates/SPPrintTemplate.html; sourceTree = "<group>"; };
		17E0937D114AE154007FC1B4 /* SPTableInfoPrintTemplate.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = SPTableInfoPrintTemplate.html; path = Templates/SPTableInfoPrintTemplate.html; sourceTree = "<group>"; };
		17E5954E14F304000054EE08 /* QueryKit.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = QueryKit.xcodeproj; path = Frameworks/QueryKit/QueryKit.xcodeproj; sourceTree = "<group>"; };
		17E641440EF01EB5001BC333 /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		17E641450EF01EB5001BC333 /* Sequel-Ace.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "Sequel-Ace.pch"; sourceTree = "<group>"; };
		17E641480EF01EF6001BC333 /* SPCustomQuery.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPCustomQuery.h; sourceTree = "<group>"; };
		17E641490EF01EF6001BC333 /* SPCustomQuery.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPCustomQuery.m; sourceTree = "<group>"; };
		17E6414A0EF01EF6001BC333 /* SPAppController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPAppController.h; sourceTree = "<group>"; };
		17E6414B0EF01EF6001BC333 /* SPAppController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPAppController.m; sourceTree = "<group>"; };
		17E6414E0EF01EF6001BC333 /* SPTableContent.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTableContent.h; sourceTree = "<group>"; };
		17E6414F0EF01EF6001BC333 /* SPTableContent.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableContent.m; sourceTree = "<group>"; };
		17E641500EF01EF6001BC333 /* SPDatabaseDocument.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDatabaseDocument.h; sourceTree = "<group>"; };
		17E641510EF01EF6001BC333 /* SPDatabaseDocument.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDatabaseDocument.m; sourceTree = "<group>"; };
		17E641520EF01EF6001BC333 /* SPDataImport.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDataImport.h; sourceTree = "<group>"; };
		17E641530EF01EF6001BC333 /* SPDataImport.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDataImport.m; sourceTree = "<group>"; };
		17E641540EF01EF6001BC333 /* SPTableStructure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTableStructure.h; sourceTree = "<group>"; };
		17E641550EF01EF6001BC333 /* SPTableStructure.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableStructure.m; sourceTree = "<group>"; };
		17E6415E0EF01F15001BC333 /* SPTableInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTableInfo.h; sourceTree = "<group>"; };
		17E6415F0EF01F15001BC333 /* SPTableInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableInfo.m; sourceTree = "<group>"; };
		17E641600EF01F15001BC333 /* SPTablesList.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTablesList.h; sourceTree = "<group>"; };
		17E641610EF01F15001BC333 /* SPTablesList.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTablesList.m; sourceTree = "<group>"; };
		17E641680EF01F37001BC333 /* ImageAndTextCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ImageAndTextCell.h; sourceTree = "<group>"; };
		17E641690EF01F37001BC333 /* ImageAndTextCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ImageAndTextCell.m; sourceTree = "<group>"; };
		17E641730EF01F80001BC333 /* SPKeychain.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPKeychain.h; sourceTree = "<group>"; };
		17E641740EF01F80001BC333 /* SPKeychain.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPKeychain.m; sourceTree = "<group>"; };
		17E6417E0EF01FA8001BC333 /* SPImageView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPImageView.h; sourceTree = "<group>"; };
		17E6417F0EF01FA8001BC333 /* SPImageView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPImageView.m; sourceTree = "<group>"; };
		17E641800EF01FA8001BC333 /* SPTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTextView.h; sourceTree = "<group>"; };
		17E641810EF01FA8001BC333 /* SPTextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTextView.m; sourceTree = "<group>"; };
		17E6419D0EF02036001BC333 /* grabber-horizontal.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "grabber-horizontal.png"; sourceTree = "<group>"; };
		17E6419E0EF02036001BC333 /* grabber-vertical.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "grabber-vertical.png"; sourceTree = "<group>"; };
		17E641BE0EF02036001BC333 /* toolbar-switch-to-structure.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "toolbar-switch-to-structure.png"; sourceTree = "<group>"; };
		17E641BF0EF02036001BC333 /* toolbar-switch-to-table-info.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "toolbar-switch-to-table-info.png"; sourceTree = "<group>"; };
		17E641F60EF02088001BC333 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = Plists/Info.plist; sourceTree = "<group>"; };
		17E641F70EF02088001BC333 /* sequel-pro.scriptSuite */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.scriptSuite; path = "sequel-pro.scriptSuite"; sourceTree = "<group>"; };
		17E641F80EF02088001BC333 /* sequel-pro.scriptTerminology */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.scriptTerminology; path = "sequel-pro.scriptTerminology"; sourceTree = "<group>"; };
		17F5B14F1048C4E400FC794F /* SPCSVExporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPCSVExporter.h; sourceTree = "<group>"; };
		17F5B1501048C4E400FC794F /* SPCSVExporter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPCSVExporter.m; sourceTree = "<group>"; };
		17F5B1521048C50D00FC794F /* SPExporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPExporter.h; sourceTree = "<group>"; };
		17F5B1531048C50D00FC794F /* SPExporter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPExporter.m; sourceTree = "<group>"; };
		17F5B39A1049B96A00FC794F /* SPSQLExporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPSQLExporter.h; sourceTree = "<group>"; };
		17F5B39B1049B96A00FC794F /* SPSQLExporter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPSQLExporter.m; sourceTree = "<group>"; };
		17F90E461210B42700274C98 /* SPExportFile.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPExportFile.h; sourceTree = "<group>"; };
		17F90E471210B42700274C98 /* SPExportFile.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPExportFile.m; sourceTree = "<group>"; };
		17FDB04A1280778B00DBBBC2 /* SPFontPreviewTextField.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFontPreviewTextField.h; sourceTree = "<group>"; };
		17FDB04B1280778B00DBBBC2 /* SPFontPreviewTextField.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFontPreviewTextField.m; sourceTree = "<group>"; };
		1A071B8B254D983700246912 /* SPNSMutableDictionaryAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SPNSMutableDictionaryAdditions.h; sourceTree = "<group>"; };
		1A071B8C254D983700246912 /* SPNSMutableDictionaryAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SPNSMutableDictionaryAdditions.m; sourceTree = "<group>"; };
		1A0751DF25EAF37700FFDF6B /* ReportExceptionApplication.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ReportExceptionApplication.m; sourceTree = "<group>"; };
		1A0751E725EAF83300FFDF6B /* SPTaskAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SPTaskAdditions.h; sourceTree = "<group>"; };
		1A11E4FC25A32789001CB721 /* SPPanelOptions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SPPanelOptions.h; sourceTree = "<group>"; };
		1A11E50325A327F1001CB721 /* SPPanelOptions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPPanelOptions.m; sourceTree = "<group>"; };
		1A11E51525A36C62001CB721 /* HyperlinkTextField.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HyperlinkTextField.swift; sourceTree = "<group>"; };
		1A199629257A624200F5B0F1 /* BundleExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BundleExtension.swift; sourceTree = "<group>"; };
		1A1EE9492551185D0056FECD /* DateFormatterExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DateFormatterExtension.swift; sourceTree = "<group>"; };
		1A1EE9572551249C0056FECD /* NumberFormatterExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NumberFormatterExtension.swift; sourceTree = "<group>"; };
		1A1EE986255131560056FECD /* Quartz.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Quartz.framework; path = System/Library/Frameworks/Quartz.framework; sourceTree = SDKROOT; };
		1A24B626258A2E9A00541E88 /* SecureBookmarkData.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SecureBookmarkData.swift; sourceTree = "<group>"; };
		1A2711CD2539D9B10066ED58 /* SPReachability.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPReachability.h; sourceTree = "<group>"; };
		1A2711CE2539D9B10066ED58 /* SPReachability.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPReachability.m; sourceTree = "<group>"; };
		1A2711E72539E2BB0066ED58 /* local-connection.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; path = "local-connection.html"; sourceTree = "<group>"; };
		1A2DD55025939B6400616E7E /* SPArrayAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SPArrayAdditions.m; sourceTree = "<group>"; };
		1A2DD55925939BEE00616E7E /* SPTestingUtils.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SPTestingUtils.m; sourceTree = "<group>"; };
		1A2DD55F25939C1500616E7E /* SPTestingUtils.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SPTestingUtils.h; sourceTree = "<group>"; };
		1A31FE3525F2132F000DD1D1 /* SPTaskAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTaskAdditions.m; sourceTree = "<group>"; };
		1A4152ED25AF530F00B17249 /* GeneralSwiftTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GeneralSwiftTests.swift; sourceTree = "<group>"; };
		1A445DA925BACBE5004E9A77 /* naughty_strings.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = naughty_strings.txt; sourceTree = "<group>"; };
		1A479B15254375BD00D29E6E /* License.md */ = {isa = PBXFileReference; lastKnownFileType = net.daringfireball.markdown; path = License.md; sourceTree = "<group>"; };
		1A4CB03325923C4B00EDF804 /* StringRegexExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StringRegexExtension.swift; sourceTree = "<group>"; };
		1A4DC22C25DECEA000DA4FE1 /* ProgressWindowController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProgressWindowController.swift; sourceTree = "<group>"; };
		1A56463D14569A0B56EE8BAC /* SPPillAttachmentCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPPillAttachmentCell.m; sourceTree = "<group>"; };
		1A564C0C0FFB444D2E5CA447 /* SPPillAttachmentCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPPillAttachmentCell.h; sourceTree = "<group>"; };
		1A6D28FD25DD8018007509F1 /* ProgressWindowController.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = ProgressWindowController.storyboard; sourceTree = "<group>"; };
		1A70BC6A25EF439F004BB992 /* FileManagerExtension.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FileManagerExtension.swift; sourceTree = "<group>"; };
		1A85CB8C2493BC4A00B57B93 /* SPSyncTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SPSyncTests.m; sourceTree = "<group>"; };
		1A89556525D6BEED0060CE72 /* GitHubReleaseManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GitHubReleaseManager.swift; sourceTree = "<group>"; };
		1A89557825D6DE860060CE72 /* GitHub.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GitHub.swift; sourceTree = "<group>"; };
		1A8B53552584520800526DED /* SPURLAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SPURLAdditions.h; sourceTree = "<group>"; };
		1A8B53562584520800526DED /* SPURLAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SPURLAdditions.m; sourceTree = "<group>"; };
		1A8B53672584552F00526DED /* SPURLAdditionsTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SPURLAdditionsTests.m; sourceTree = "<group>"; };
		1A8B582925EEE15900DFC54A /* ByteCountFormatterExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ByteCountFormatterExtension.swift; sourceTree = "<group>"; };
		1A94988D25516057000BC793 /* DateExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DateExtension.swift; sourceTree = "<group>"; };
		1A96E4B42588F34C0055F5F5 /* SecureBookmark.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SecureBookmark.swift; sourceTree = "<group>"; };
		1A96E4B52588F34C0055F5F5 /* SecureBookmarkManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SecureBookmarkManager.swift; sourceTree = "<group>"; };
		1A9D83A325514E740024B563 /* DateComponentsFormatterExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DateComponentsFormatterExtension.swift; sourceTree = "<group>"; };
		1A9EB9AD25651F5000FE60FF /* SQLiteHistoryManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SQLiteHistoryManager.swift; sourceTree = "<group>"; };
		1A9F343E257B0DBE0062EC87 /* SPBundleManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SPBundleManager.m; sourceTree = "<group>"; };
		1A9F344B257B0DDB0062EC87 /* SPBundleManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SPBundleManager.h; sourceTree = "<group>"; };
		1AB0688F24A355B500E2AAC2 /* client-cert-crlf.pem */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = "client-cert-crlf.pem"; sourceTree = "<group>"; };
		1AB0689024A355B500E2AAC2 /* client-cert-bad-end.pem */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = "client-cert-bad-end.pem"; sourceTree = "<group>"; };
		1AB0689124A355B500E2AAC2 /* client-cert-lf.pem */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = "client-cert-lf.pem"; sourceTree = "<group>"; };
		1AB0689224A355B500E2AAC2 /* client-cert-cr.pem */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = "client-cert-cr.pem"; sourceTree = "<group>"; };
		1AB0689324A355B500E2AAC2 /* client-cert-bad-start.pem */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = "client-cert-bad-start.pem"; sourceTree = "<group>"; };
		1AB0689524A355B500E2AAC2 /* client-key-bad-end.pem */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = "client-key-bad-end.pem"; sourceTree = "<group>"; };
		1AB0689624A355B500E2AAC2 /* client-key-cr.pem */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = "client-key-cr.pem"; sourceTree = "<group>"; };
		1AB0689724A355B500E2AAC2 /* client-key-bad-start.pem */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = "client-key-bad-start.pem"; sourceTree = "<group>"; };
		1AB0689824A355B500E2AAC2 /* client-key-lf.pem */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = "client-key-lf.pem"; sourceTree = "<group>"; };
		1AB0689924A355B500E2AAC2 /* client-key-crlf.pem */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = "client-key-crlf.pem"; sourceTree = "<group>"; };
		1AB068B224A3575600E2AAC2 /* SPValidateKeyAndCertFiles.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPValidateKeyAndCertFiles.m; sourceTree = "<group>"; };
		1AB28D7F25DBD3B500E62BF5 /* ProgressViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ProgressViewController.swift; sourceTree = "<group>"; };
		1ABC770025E3895300E8EE01 /* DispatchQueueExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DispatchQueueExtension.swift; sourceTree = "<group>"; };
		1ACA0B6525BEBE18002FA618 /* PopupButtonExtensions.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PopupButtonExtensions.swift; sourceTree = "<group>"; };
		1AD785E325B749760007E153 /* OSLog.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OSLog.swift; sourceTree = "<group>"; };
		1ADEA5A224BF1C4800D2140B /* SPDateAdditionsTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SPDateAdditionsTests.m; sourceTree = "<group>"; };
		1AE6C1CC25B07E9500880D73 /* SPFunctionsTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SPFunctionsTests.m; sourceTree = "<group>"; };
		1AF0DA7D259F5D3100961974 /* SPPointerArrayAdditions.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SPPointerArrayAdditions.h; sourceTree = "<group>"; };
		1AF0DA7E259F5D8C00961974 /* SPPointerArrayAdditions.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SPPointerArrayAdditions.m; sourceTree = "<group>"; };
		1AF0DA83259F631000961974 /* SPPointerArrayAdditionsTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SPPointerArrayAdditionsTests.m; sourceTree = "<group>"; };
		1AF5A25D250AC401009885DF /* SPBracketHighlighter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPBracketHighlighter.h; sourceTree = "<group>"; };
		1AF5A25E250AC401009885DF /* SPBracketHighlighter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPBracketHighlighter.m; sourceTree = "<group>"; };
		1AF5A25F250AC401009885DF /* SPBrackets.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPBrackets.m; sourceTree = "<group>"; };
		1AF5A260250AC401009885DF /* SPBrackets.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPBrackets.h; sourceTree = "<group>"; };
		296DC89E0F8FD336002A3258 /* WebKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WebKit.framework; path = /System/Library/Frameworks/WebKit.framework; sourceTree = "<absolute>"; };
		296DC8A50F909194002A3258 /* MGTemplateMarker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MGTemplateMarker.h; sourceTree = "<group>"; };
		296DC8A60F909194002A3258 /* MGTemplateFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MGTemplateFilter.h; sourceTree = "<group>"; };
		296DC8A70F909194002A3258 /* MGTemplateEngine.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MGTemplateEngine.m; sourceTree = "<group>"; };
		296DC8A80F909194002A3258 /* MGTemplateEngine.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MGTemplateEngine.h; sourceTree = "<group>"; };
		296DC8A90F909194002A3258 /* ICUTemplateMatcher.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ICUTemplateMatcher.h; sourceTree = "<group>"; };
		296DC8AB0F909194002A3258 /* RegexKitLite.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RegexKitLite.m; sourceTree = "<group>"; };
		296DC8AC0F909194002A3258 /* ICUTemplateMatcher.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ICUTemplateMatcher.m; sourceTree = "<group>"; };
		296DC8AD0F909194002A3258 /* MGTemplateStandardMarkers.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MGTemplateStandardMarkers.m; sourceTree = "<group>"; };
		296DC8AE0F909194002A3258 /* NSArray_DeepMutableCopy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NSArray_DeepMutableCopy.m; sourceTree = "<group>"; };
		296DC8AF0F909194002A3258 /* NSArray_DeepMutableCopy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NSArray_DeepMutableCopy.h; sourceTree = "<group>"; };
		296DC8B00F909194002A3258 /* RegexKitLite.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RegexKitLite.h; sourceTree = "<group>"; };
		296DC8B10F909194002A3258 /* NSDictionary_DeepMutableCopy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NSDictionary_DeepMutableCopy.m; sourceTree = "<group>"; };
		296DC8B20F909194002A3258 /* NSDictionary_DeepMutableCopy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NSDictionary_DeepMutableCopy.h; sourceTree = "<group>"; };
		296DC8B30F909194002A3258 /* MGTemplateStandardMarkers.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MGTemplateStandardMarkers.h; sourceTree = "<group>"; };
		296DC8B40F909194002A3258 /* MGTemplateStandardFilters.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = MGTemplateStandardFilters.m; sourceTree = "<group>"; };
		296DC8B50F909194002A3258 /* MGTemplateStandardFilters.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = MGTemplateStandardFilters.h; sourceTree = "<group>"; };
		296DC8BE0F9091DF002A3258 /* libicucore.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libicucore.dylib; path = usr/lib/libicucore.dylib; sourceTree = SDKROOT; };
		29A1B7E30FD1293A000B88E8 /* SPPrintAccessory.h */ = {isa = PBXFileReference; explicitFileType = sourcecode.c.objc; fileEncoding = 4; path = SPPrintAccessory.h; sourceTree = "<group>"; };
		29A1B7E40FD1293A000B88E8 /* SPPrintAccessory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPPrintAccessory.m; sourceTree = "<group>"; wrapsLines = 0; };
		29FA88211114619E00D1AF3D /* SPTableTriggers.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTableTriggers.h; sourceTree = "<group>"; };
		29FA88221114619E00D1AF3D /* SPTableTriggers.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableTriggers.m; sourceTree = "<group>"; };
		2A37F4C4FDCFA73011CA2CEA /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = /System/Library/Frameworks/AppKit.framework; sourceTree = "<absolute>"; };
		2A37F4C5FDCFA73011CA2CEA /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = /System/Library/Frameworks/Foundation.framework; sourceTree = "<absolute>"; };
		380F4ED90FC0B50500B0BFD7 /* Unit Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "Unit Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		380F4EF40FC0B68F00B0BFD7 /* SPStringAdditionsTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPStringAdditionsTests.m; sourceTree = "<group>"; };
		384582C30FB95FF800DDACB6 /* func-small.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "func-small.png"; sourceTree = "<group>"; };
		384582C60FB9603600DDACB6 /* proc-small.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "proc-small.png"; sourceTree = "<group>"; };
		3876E15B1CC0BA0300D85154 /* SPTableFooterPopUpButtonCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTableFooterPopUpButtonCell.h; sourceTree = "<group>"; };
		3876E15C1CC0BA0300D85154 /* SPTableFooterPopUpButtonCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableFooterPopUpButtonCell.m; sourceTree = "<group>"; };
		387BBBA60FBCB6CB00B31746 /* SPTableRelations.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTableRelations.h; sourceTree = "<group>"; };
		387BBBA70FBCB6CB00B31746 /* SPTableRelations.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableRelations.m; sourceTree = "<group>"; };
		38C613721C8977E600B3B6EF /* libz.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = libz.tbd; path = usr/lib/libz.tbd; sourceTree = SDKROOT; };
		38F37194182F0621008EB031 /* readme.md */ = {isa = PBXFileReference; lastKnownFileType = text; path = readme.md; sourceTree = "<group>"; };
		3E242D4B20FEB44D0015470D /* button_bar_spacer_dark.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = button_bar_spacer_dark.png; sourceTree = "<group>"; };
		3E242D4E20FEB44D0015470D /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		44011DFD0DC2836DB08A0619 /* SQLitePinnedTableManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SQLitePinnedTableManager.swift; sourceTree = "<group>"; };
		4D90B798101E0CDF00D116A1 /* SPUserManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPUserManager.h; sourceTree = "<group>"; };
		4D90B799101E0CDF00D116A1 /* SPUserManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPUserManager.m; sourceTree = "<group>"; };
		4D90B79B101E0CF200D116A1 /* SPUserManager.xcdatamodel */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = wrapper.xcdatamodel; path = SPUserManager.xcdatamodel; sourceTree = "<group>"; };
		4D90B79C101E0CF200D116A1 /* SPUserMO.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPUserMO.h; sourceTree = "<group>"; };
		4D90B79D101E0CF200D116A1 /* SPUserMO.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPUserMO.m; sourceTree = "<group>"; };
		500C1F901BFB5F9F0095DC7F /* SPPrivilegesMO.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPPrivilegesMO.h; sourceTree = "<group>"; };
		500C1F911BFB5F9F0095DC7F /* SPPrivilegesMO.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPPrivilegesMO.m; sourceTree = "<group>"; };
		500DA4B51BEFF877000773FE /* SPComboBoxCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPComboBoxCell.h; sourceTree = "<group>"; };
		500DA4B61BEFF877000773FE /* SPComboBoxCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPComboBoxCell.m; sourceTree = "<group>"; };
		500DA4BA1BF0CD57000773FE /* SPScreenAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPScreenAdditions.h; sourceTree = "<group>"; };
		500DA4BB1BF0CD57000773FE /* SPScreenAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPScreenAdditions.m; sourceTree = "<group>"; };
		501B1D161728A3DA0017C92E /* SPCharsetCollationHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPCharsetCollationHelper.h; sourceTree = "<group>"; };
		501B1D171728A3DA0017C92E /* SPCharsetCollationHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPCharsetCollationHelper.m; sourceTree = "<group>"; };
		502D21F51BA50710000D4CE7 /* SPDataAdditionsTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDataAdditionsTests.m; sourceTree = "<group>"; };
		5037F79A1B00148000733564 /* SPNamedNode.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SPNamedNode.h; sourceTree = "<group>"; };
		503B02C81AE82C5E0060CAB1 /* SPTableFilterParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTableFilterParser.h; sourceTree = "<group>"; };
		503B02C91AE82C5E0060CAB1 /* SPTableFilterParser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableFilterParser.m; sourceTree = "<group>"; };
		503B02CE1AE95C2C0060CAB1 /* SPTableFilterParserTest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableFilterParserTest.m; sourceTree = "<group>"; };
		503CDBB11ACDC204004F8A2F /* Quartz.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Quartz.framework; path = System/Library/Frameworks/Quartz.framework; sourceTree = SDKROOT; };
		506CE92F1A311C6C0039F736 /* SPRuleFilterController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPRuleFilterController.h; sourceTree = "<group>"; };
		506CE9301A311C6C0039F736 /* SPRuleFilterController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPRuleFilterController.m; sourceTree = "<group>"; };
		507FF1101BBCC4C400104523 /* SPFunctions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFunctions.h; sourceTree = "<group>"; };
		507FF1111BBCC57600104523 /* SPFunctions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFunctions.m; sourceTree = "<group>"; };
		50805B0B1BF2A068005F7A99 /* SPPopUpButtonCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPPopUpButtonCell.h; sourceTree = "<group>"; };
		50805B0C1BF2A068005F7A99 /* SPPopUpButtonCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPPopUpButtonCell.m; sourceTree = "<group>"; };
		50837F731E50DCD4004FAE8A /* SPJSONFormatterTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPJSONFormatterTests.m; sourceTree = "<group>"; };
		5089B0251BE714E300E226CD /* SPIdMenu.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPIdMenu.h; sourceTree = "<group>"; };
		5089B0261BE714E300E226CD /* SPIdMenu.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPIdMenu.m; sourceTree = "<group>"; };
		50A77DA61E8EB903007466BC /* SPCompatibility.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SPCompatibility.h; sourceTree = "<group>"; };
		50A9F8AF19EAD4B90053E571 /* SPGotoDatabaseController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPGotoDatabaseController.h; sourceTree = "<group>"; };
		50A9F8B019EAD4B90053E571 /* SPGotoDatabaseController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPGotoDatabaseController.m; sourceTree = "<group>"; };
		50D3C3501A77135F00B5429C /* SPParserUtils.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = SPParserUtils.c; sourceTree = "<group>"; };
		50D3C3511A77135F00B5429C /* SPParserUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPParserUtils.h; sourceTree = "<group>"; };
		50D3C35B1A771C4C00B5429C /* SPParserUtilsTest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPParserUtilsTest.m; sourceTree = "<group>"; };
		50E217B118174246009D3580 /* SPColorSelectorView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPColorSelectorView.h; sourceTree = "<group>"; };
		50E217B218174246009D3580 /* SPColorSelectorView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPColorSelectorView.m; sourceTree = "<group>"; };
		50E217B418174280009D3580 /* SPFavoriteColorSupport.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFavoriteColorSupport.h; sourceTree = "<group>"; };
		50E217B518174280009D3580 /* SPFavoriteColorSupport.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFavoriteColorSupport.m; sourceTree = "<group>"; };
		50EA92691AB246B8008D3C4F /* SPDatabaseActionTest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDatabaseActionTest.m; sourceTree = "<group>"; };
		50F530511ABCF66B002F2C1A /* resetTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = resetTemplate.pdf; sourceTree = "<group>"; };
		510EE15C27B6C35F008544E7 /* fr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = fr; path = fr.lproj/Localizable.strings; sourceTree = "<group>"; };
		511B309F25ABBC6500D010E3 /* es */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = es; path = es.lproj/Localizable.strings; sourceTree = "<group>"; };
		511E8F6926EF72FD001FC731 /* ru */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ru; path = ru.lproj/Localizable.strings; sourceTree = "<group>"; };
		5127EAF62B568DCC009CFC6A /* cs */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = cs; path = cs.lproj/Localizable.strings; sourceTree = "<group>"; };
		5127EAF72B568E2B009CFC6A /* eo */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = eo; path = eo.lproj/Localizable.strings; sourceTree = "<group>"; };
		5132930925F586A900D803AD /* NotificationToken.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationToken.swift; sourceTree = "<group>"; };
		5132930A25F586A900D803AD /* TabManager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TabManager.swift; sourceTree = "<group>"; };
		513515D1259354BB001E4533 /* NSImageExtensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NSImageExtensions.swift; sourceTree = "<group>"; };
		51384AC12903461000FEC501 /* NSDictionaryExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NSDictionaryExtension.swift; sourceTree = "<group>"; };
		514D620D2B0278D900571694 /* ar */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ar; path = ar.lproj/Localizable.strings; sourceTree = "<group>"; };
		514DD98825FACF1500EA3B3B /* SPDatabaseDocument.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SPDatabaseDocument.swift; sourceTree = "<group>"; };
		516A76C52653B4E800DC6501 /* ja */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = ja; path = ja.lproj/Localizable.strings; sourceTree = "<group>"; };
		51731E7B27DFB99A00D81B98 /* it */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = it; path = it.lproj/Localizable.strings; sourceTree = "<group>"; };
		5174122E2573E10C00EB6935 /* SPPrintUtility.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SPPrintUtility.h; sourceTree = "<group>"; };
		5174122F2573E10C00EB6935 /* SPPrintUtility.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SPPrintUtility.m; sourceTree = "<group>"; };
		517412372573E8F900EB6935 /* EditorQuickLookTypes.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = EditorQuickLookTypes.plist; sourceTree = "<group>"; };
		517E44C2257A8F2C00ED333B /* AppleScriptObjC.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppleScriptObjC.framework; path = System/Library/Frameworks/AppleScriptObjC.framework; sourceTree = SDKROOT; };
		517E44FB257A94C400ED333B /* Credits.rtf */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.rtf; path = Credits.rtf; sourceTree = "<group>"; };
		517E4511257A954000ED333B /* ContentFilters.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = ContentFilters.plist; path = Plists/ContentFilters.plist; sourceTree = "<group>"; };
		51905622267DFF1700212442 /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/Localizable.strings; sourceTree = "<group>"; };
		5193502E2567D2FB001272B5 /* CollectionExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CollectionExtension.swift; sourceTree = "<group>"; };
		5199783E27760147008DEB7B /* tr */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = tr; path = tr.lproj/Localizable.strings; sourceTree = "<group>"; };
		519F2829261A5A1800370B14 /* de */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = de; path = de.lproj/Localizable.strings; sourceTree = "<group>"; };
		51A709382565B99F001F1D2F /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		51BB391B25F82B060048CA69 /* SPAppController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SPAppController.swift; sourceTree = "<group>"; };
		51BB3DFC25AEF020005AAE44 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/Localizable.strings"; sourceTree = "<group>"; };
		51BB3E0425AEF0D7005AAE44 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/Localizable.strings"; sourceTree = "<group>"; };
		51BC14F725BE135500F1CDC9 /* SPWindowController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SPWindowController.swift; sourceTree = "<group>"; };
		51BC150525BE13C400F1CDC9 /* NSViewExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NSViewExtension.swift; sourceTree = "<group>"; };
		51C04C98261E4CF5001F5554 /* pt-BR */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "pt-BR"; path = "pt-BR.lproj/Localizable.strings"; sourceTree = "<group>"; };
		51C04C9D261E4DB6001F5554 /* pt */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = pt; path = pt.lproj/Localizable.strings; sourceTree = "<group>"; };
		51C4626C254ED02500F63E70 /* UserDefaultsExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UserDefaultsExtension.swift; sourceTree = "<group>"; };
		51C8597B24C8A31400A8C7C4 /* StringExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StringExtension.swift; sourceTree = "<group>"; };
		51CD0BBE258D06D7009E2484 /* PrintAccessory.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = PrintAccessory.xib; sourceTree = "<group>"; };
		51CD0BBF258D06D7009E2484 /* DataMigrationDialog.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = DataMigrationDialog.xib; sourceTree = "<group>"; };
		51CD0BC0258D06D7009E2484 /* Preferences.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = Preferences.xib; sourceTree = "<group>"; };
		51CD0BC1258D06D7009E2484 /* FilterTableWindow.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = FilterTableWindow.xib; sourceTree = "<group>"; };
		51CD0BC2258D06D7009E2484 /* FieldEditorSheet.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = FieldEditorSheet.xib; sourceTree = "<group>"; };
		51CD0BC3258D06D7009E2484 /* GotoDatabaseDialog.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = GotoDatabaseDialog.xib; sourceTree = "<group>"; };
		51CD0BC4258D06D7009E2484 /* IndexesView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = IndexesView.xib; sourceTree = "<group>"; };
		51CD0BC5258D06D7009E2484 /* DBView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = DBView.xib; sourceTree = "<group>"; };
		51CD0BC6258D06D7009E2484 /* HelpViewer.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = HelpViewer.xib; sourceTree = "<group>"; };
		51CD0BC7258D06D8009E2484 /* Navigator.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = Navigator.xib; sourceTree = "<group>"; };
		51CD0BC9258D06D8009E2484 /* ImportAccessory.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ImportAccessory.xib; sourceTree = "<group>"; };
		51CD0BCA258D06D8009E2484 /* MainMenu.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = MainMenu.xib; sourceTree = "<group>"; };
		51CD0BCB258D06D8009E2484 /* Console.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = Console.xib; sourceTree = "<group>"; };
		51CD0BCC258D06D8009E2484 /* DatabaseServerVariables.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = DatabaseServerVariables.xib; sourceTree = "<group>"; };
		51CD0BCD258D06D8009E2484 /* ConnectionErrorDialog.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ConnectionErrorDialog.xib; sourceTree = "<group>"; };
		51CD0BCE258D06D8009E2484 /* ProgressIndicatorLayer.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ProgressIndicatorLayer.xib; sourceTree = "<group>"; };
		51CD0BCF258D06D8009E2484 /* SaveSPFAccessory.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = SaveSPFAccessory.xib; sourceTree = "<group>"; };
		51CD0BD0258D06D8009E2484 /* UserManagerView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = UserManagerView.xib; sourceTree = "<group>"; };
		51CD0BD1258D06D8009E2484 /* ExportDialog.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ExportDialog.xib; sourceTree = "<group>"; };
		51CD0BD2258D06D8009E2484 /* BundleEditor.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BundleEditor.xib; sourceTree = "<group>"; };
		51CD0BD3258D06D8009E2484 /* ContentFilterManager.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ContentFilterManager.xib; sourceTree = "<group>"; };
		51CD0BD4258D06D8009E2484 /* EncodingPopupView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = EncodingPopupView.xib; sourceTree = "<group>"; };
		51CD0BD5258D06D8009E2484 /* ContentPaginationView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ContentPaginationView.xib; sourceTree = "<group>"; };
		51CD0BD6258D06D8009E2484 /* SSHQuestionDialog.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = SSHQuestionDialog.xib; sourceTree = "<group>"; };
		51CD0BD7258D06D8009E2484 /* QueryFavoriteManager.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = QueryFavoriteManager.xib; sourceTree = "<group>"; };
		51CD0BD8258D06D8009E2484 /* ConnectionView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = ConnectionView.xib; sourceTree = "<group>"; };
		51CD0BD9258D06D8009E2484 /* AboutPanel.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = AboutPanel.xib; sourceTree = "<group>"; };
		51CD0BDA258D06D8009E2484 /* DatabaseProcessList.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = DatabaseProcessList.xib; sourceTree = "<group>"; };
		51CD0BDB258D06D8009E2484 /* BundleHTMLOutput.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BundleHTMLOutput.xib; sourceTree = "<group>"; };
		51F41FB625F56A5900594BA5 /* MainWindow.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = MainWindow.xib; sourceTree = "<group>"; };
		51F4AFBD24B26665006144D5 /* Sequel-Ace-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Sequel-Ace-Bridging-Header.h"; sourceTree = "<group>"; };
		51F4AFBE24B26665006144D5 /* NSAlertExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NSAlertExtension.swift; sourceTree = "<group>"; };
		5806B76211A991EC00813A88 /* SPDocumentController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDocumentController.h; sourceTree = "<group>"; };
		5806B76311A991EC00813A88 /* SPDocumentController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDocumentController.m; sourceTree = "<group>"; };
		581068B51015411B0068C6E2 /* link-arrow-highlighted.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "link-arrow-highlighted.png"; sourceTree = "<group>"; };
		5822C9B31000DB2400DCC3D6 /* SPConnectionController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPConnectionController.h; sourceTree = "<group>"; };
		5822C9B41000DB2400DCC3D6 /* SPConnectionController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPConnectionController.m; sourceTree = "<group>"; };
		5822D3071061833C00CE2157 /* SPCSVParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPCSVParser.h; sourceTree = "<group>"; };
		5822D3081061833C00CE2157 /* SPCSVParser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPCSVParser.m; sourceTree = "<group>"; };
		582A01E7107C0C170027D42B /* SPNotLoaded.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPNotLoaded.h; sourceTree = "<group>"; };
		582A01E8107C0C170027D42B /* SPNotLoaded.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPNotLoaded.m; sourceTree = "<group>"; };
		582E9399168296F3003459FD /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		582E939A168296F3003459FD /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		582E939B168296F3003459FD /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		582E939C168296F3003459FD /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		582E940D1682A2AD003459FD /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		582E942D1683658A003459FD /* clearconsole.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = clearconsole.png; sourceTree = "<group>"; };
		582E9449168374C1003459FD /* field-small-square.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "field-small-square.png"; sourceTree = "<group>"; };
		582E944B16837986003459FD /* hideconsole.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = hideconsole.png; sourceTree = "<group>"; };
		582E944F16837AA9003459FD /* network-small.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "network-small.png"; sourceTree = "<group>"; };
		582E946F16837DB2003459FD /* showconsole.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = showconsole.png; sourceTree = "<group>"; };
		582E947D168380D6003459FD /* sync_arrows_01.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = sync_arrows_01.png; sourceTree = "<group>"; };
		582E947E168380D6003459FD /* sync_arrows_02.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = sync_arrows_02.png; sourceTree = "<group>"; };
		582E947F168380D6003459FD /* sync_arrows_03.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = sync_arrows_03.png; sourceTree = "<group>"; };
		582E9480168380D6003459FD /* sync_arrows_04.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = sync_arrows_04.png; sourceTree = "<group>"; };
		582E9481168380D6003459FD /* sync_arrows_05.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = sync_arrows_05.png; sourceTree = "<group>"; };
		582E9482168380D6003459FD /* sync_arrows_06.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = sync_arrows_06.png; sourceTree = "<group>"; };
		582E948D168383F0003459FD /* table-view-small-square.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "table-view-small-square.png"; sourceTree = "<group>"; };
		582E948E168383F0003459FD /* table-view-small.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "table-view-small.png"; sourceTree = "<group>"; };
		582E94A716839AD5003459FD /* toolbar-preferences-autoupdate.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "toolbar-preferences-autoupdate.png"; sourceTree = "<group>"; };
		582E94A916839AEF003459FD /* toolbar-preferences-general.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "toolbar-preferences-general.png"; sourceTree = "<group>"; };
		582E94AD16839C4A003459FD /* toolbar-preferences-notifications.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "toolbar-preferences-notifications.png"; sourceTree = "<group>"; };
		582E94C616839D83003459FD /* toolbar-preferences-tables.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "toolbar-preferences-tables.png"; sourceTree = "<group>"; };
		582E94F716839E83003459FD /* toolbar-preferences-network.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "toolbar-preferences-network.png"; sourceTree = "<group>"; };
		582F022F1370B52600B30621 /* SPExportFileNameTokenObject.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPExportFileNameTokenObject.h; sourceTree = "<group>"; };
		582F02301370B52600B30621 /* SPExportFileNameTokenObject.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPExportFileNameTokenObject.m; sourceTree = "<group>"; };
		583A278D23F06F1000FBE97B /* Colors.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Colors.xcassets; sourceTree = "<group>"; };
		583CA21312EC8B2200C9E763 /* SPWindow.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPWindow.h; sourceTree = "<group>"; };
		583CA21412EC8B2200C9E763 /* SPWindow.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPWindow.m; sourceTree = "<group>"; };
		5841423D0F97E11000A34B47 /* NoodleLineNumberView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NoodleLineNumberView.h; sourceTree = "<group>"; };
		5841423E0F97E11000A34B47 /* NoodleLineNumberView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NoodleLineNumberView.m; sourceTree = "<group>"; };
		5841929F101E57BB0089807F /* NSMutableArray-MultipleSort.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSMutableArray-MultipleSort.h"; sourceTree = "<group>"; };
		584192A0101E57BB0089807F /* NSMutableArray-MultipleSort.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSMutableArray-MultipleSort.m"; sourceTree = "<group>"; };
		5843DA68161FA35600EAA6D1 /* key-icon-alternate.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "key-icon-alternate.png"; sourceTree = "<group>"; };
		5843DA69161FA35600EAA6D1 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		5843DA6A161FA35600EAA6D1 /* key-icon.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "key-icon.png"; sourceTree = "<group>"; };
		5843DA6B161FA35600EAA6D1 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		5843E245162B555B00EAA6D1 /* SPThreadAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPThreadAdditions.h; sourceTree = "<group>"; };
		5843E246162B555B00EAA6D1 /* SPThreadAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPThreadAdditions.m; sourceTree = "<group>"; };
		58475685120A065B0057631F /* CoreFoundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreFoundation.framework; path = System/Library/Frameworks/CoreFoundation.framework; sourceTree = SDKROOT; };
		584756B4120A06740057631F /* CoreServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreServices.framework; path = /System/Library/Frameworks/CoreServices.framework; sourceTree = "<absolute>"; };
		584756B6120A067B0057631F /* ApplicationServices.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ApplicationServices.framework; path = /System/Library/Frameworks/ApplicationServices.framework; sourceTree = "<absolute>"; };
		584756B8120A06830057631F /* QuickLook.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuickLook.framework; path = /System/Library/Frameworks/QuickLook.framework; sourceTree = "<absolute>"; };
		584D876015140D3500F24774 /* SPMySQLFramework.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = SPMySQLFramework.xcodeproj; path = Frameworks/SPMySQLFramework/SPMySQLFramework.xcodeproj; sourceTree = "<group>"; };
		584D878915140FEB00F24774 /* SPObjectAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPObjectAdditions.h; sourceTree = "<group>"; };
		584D878A15140FEB00F24774 /* SPObjectAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPObjectAdditions.m; sourceTree = "<group>"; };
		584D87901514101E00F24774 /* SPDatabaseStructure.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDatabaseStructure.h; sourceTree = "<group>"; };
		584D87911514101E00F24774 /* SPDatabaseStructure.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDatabaseStructure.m; sourceTree = "<group>"; };
		584D87BE15141A4A00F24774 /* libz.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libz.dylib; path = usr/lib/libz.dylib; sourceTree = SDKROOT; };
		584D88A71515034200F24774 /* NSNotificationCenterThreadingAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NSNotificationCenterThreadingAdditions.h; sourceTree = "<group>"; };
		584D88A81515034200F24774 /* NSNotificationCenterThreadingAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NSNotificationCenterThreadingAdditions.m; sourceTree = "<group>"; };
		584D899B15162CBE00F24774 /* SPDataBase64EncodingAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDataBase64EncodingAdditions.h; sourceTree = "<group>"; };
		584D899C15162CBE00F24774 /* SPDataBase64EncodingAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDataBase64EncodingAdditions.m; sourceTree = "<group>"; };
		5870868210FA3E9C00D58E1C /* SPDataStorage.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDataStorage.h; sourceTree = "<group>"; };
		5870868310FA3E9C00D58E1C /* SPDataStorage.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDataStorage.m; sourceTree = "<group>"; };
		5885CF48116A63B200A85ACB /* SPFileHandle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFileHandle.h; sourceTree = "<group>"; };
		5885CF49116A63B200A85ACB /* SPFileHandle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFileHandle.m; sourceTree = "<group>"; };
		588B2CC50FE5641E00EC5FC0 /* ssh-connected.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "ssh-connected.png"; sourceTree = "<group>"; };
		588B2CC60FE5641E00EC5FC0 /* ssh-connecting.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "ssh-connecting.png"; sourceTree = "<group>"; };
		588B2CC70FE5641E00EC5FC0 /* ssh-disconnected.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "ssh-disconnected.png"; sourceTree = "<group>"; };
		589235301020C1230011DE00 /* SPHistoryController.m */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.objc; path = SPHistoryController.m; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		589235311020C1230011DE00 /* SPHistoryController.h */ = {isa = PBXFileReference; fileEncoding = 4; indentWidth = 4; lastKnownFileType = sourcecode.c.h; path = SPHistoryController.h; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		589582131154F8F400EDCC28 /* SPMainThreadTrampoline.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPMainThreadTrampoline.h; sourceTree = "<group>"; };
		589582141154F8F400EDCC28 /* SPMainThreadTrampoline.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPMainThreadTrampoline.m; sourceTree = "<group>"; };
		58B9077D11BD9B64000826E5 /* Carbon.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Carbon.framework; path = System/Library/Frameworks/Carbon.framework; sourceTree = SDKROOT; };
		58B9095B11C3A3EC000826E5 /* xibLocalizationPostprocessor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = xibLocalizationPostprocessor.m; sourceTree = "<group>"; };
		58B9096111C3A42B000826E5 /* xibLocalizationPostprocessor */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = xibLocalizationPostprocessor; sourceTree = BUILT_PRODUCTS_DIR; };
		58C56EF30F438E120035701E /* SPDataCellFormatter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDataCellFormatter.h; sourceTree = "<group>"; };
		58C56EF40F438E120035701E /* SPDataCellFormatter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDataCellFormatter.m; sourceTree = "<group>"; };
		58CDB32E0FCE138D00F8ACA3 /* SPSSHTunnel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPSSHTunnel.h; sourceTree = "<group>"; };
		58CDB32F0FCE138D00F8ACA3 /* SPSSHTunnel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPSSHTunnel.m; sourceTree = "<group>"; };
		58CDB3310FCE139C00F8ACA3 /* SequelAceTunnelAssistant.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SequelAceTunnelAssistant.m; sourceTree = "<group>"; };
		58CDB3360FCE13C900F8ACA3 /* SequelAceTunnelAssistant */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.executable"; includeInIndex = 0; path = SequelAceTunnelAssistant; sourceTree = BUILT_PRODUCTS_DIR; };
		58D2A6A516FBDEFF002EB401 /* SPComboPopupButton.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPComboPopupButton.h; sourceTree = "<group>"; };
		58D2A6A616FBDEFF002EB401 /* SPComboPopupButton.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPComboPopupButton.m; sourceTree = "<group>"; };
		58D2E227101222670063EF1D /* SPTextAndLinkCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTextAndLinkCell.m; sourceTree = "<group>"; };
		58D2E228101222670063EF1D /* SPTextAndLinkCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTextAndLinkCell.h; sourceTree = "<group>"; };
		58D2E22B101222870063EF1D /* link-arrow-clicked.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "link-arrow-clicked.png"; sourceTree = "<group>"; };
		58D2E22C101222870063EF1D /* link-arrow-highlighted-clicked.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "link-arrow-highlighted-clicked.png"; sourceTree = "<group>"; };
		58D2E22D101222870063EF1D /* link-arrow.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "link-arrow.png"; sourceTree = "<group>"; };
		58DA8861103E15B5000B98DF /* SPLogger.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPLogger.h; sourceTree = "<group>"; };
		58DA8862103E15B5000B98DF /* SPLogger.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPLogger.m; sourceTree = "<group>"; };
		58DF9F3115AB26C2003B4330 /* SPDateAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDateAdditions.h; sourceTree = "<group>"; };
		58DF9F3215AB26C2003B4330 /* SPDateAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDateAdditions.m; sourceTree = "<group>"; };
		58DF9F7115AB8509003B4330 /* SPSplitView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPSplitView.h; sourceTree = "<group>"; };
		58DF9F7215AB8509003B4330 /* SPSplitView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPSplitView.m; sourceTree = "<group>"; };
		58E205FB1234FE4F00A97059 /* KeyTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = KeyTemplate.pdf; sourceTree = "<group>"; };
		58F48AA2161D03C6008536A1 /* quick-connect-icon.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = "quick-connect-icon.pdf"; sourceTree = "<group>"; };
		58F48B2D161D08C0008536A1 /* quick-connect-icon-white.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = "quick-connect-icon-white.pdf"; sourceTree = "<group>"; };
		58FEF16B0F23D66600518E8E /* SPSQLParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPSQLParser.h; sourceTree = "<group>"; };
		58FEF16C0F23D66600518E8E /* SPSQLParser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPSQLParser.m; sourceTree = "<group>"; };
		58FEF57C0F3B4E9700518E8E /* SPTableData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTableData.h; sourceTree = "<group>"; };
		58FEF57D0F3B4E9700518E8E /* SPTableData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableData.m; sourceTree = "<group>"; };
		65F52CC724AE21D600FED3CB /* SPFilePreferencePane.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFilePreferencePane.m; sourceTree = "<group>"; };
		65F52CCC24AE21F200FED3CB /* SPFilePreferencePane.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SPFilePreferencePane.h; sourceTree = "<group>"; };
		6F0EA8EC272F33B700514FF1 /* SPBundleManagerAdditions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SPBundleManagerAdditions.swift; sourceTree = "<group>"; };
		6F0EA9222734ABE200514FF1 /* SABundleRunner.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SABundleRunner.h; sourceTree = "<group>"; };
		6F0EA9232734ABE200514FF1 /* SABundleRunner.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SABundleRunner.m; sourceTree = "<group>"; };
		73F70A941E4E547500636550 /* SPJSONFormatter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPJSONFormatter.h; sourceTree = "<group>"; };
		73F70A951E4E547500636550 /* SPJSONFormatter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPJSONFormatter.m; sourceTree = "<group>"; };
		8831EFA5224011B700D10172 /* button_addTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_addTemplate.pdf; sourceTree = "<group>"; };
		8831EFA92240128600D10172 /* button_removeTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_removeTemplate.pdf; sourceTree = "<group>"; };
		8831EFAB2240131400D10172 /* button_editTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_editTemplate.pdf; sourceTree = "<group>"; };
		8831EFAD2240135300D10172 /* button_duplicateTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_duplicateTemplate.pdf; sourceTree = "<group>"; };
		8831EFAF224013AF00D10172 /* button_refreshTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_refreshTemplate.pdf; sourceTree = "<group>"; };
		8831EFB12240143600D10172 /* button_bar_handleTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_bar_handleTemplate.pdf; sourceTree = "<group>"; };
		8831EFB3224014C700D10172 /* button_clearTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_clearTemplate.pdf; sourceTree = "<group>"; };
		8831EFB52240150A00D10172 /* button_add_folderTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_add_folderTemplate.pdf; sourceTree = "<group>"; };
		8831EFB72240154A00D10172 /* button_leftTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_leftTemplate.pdf; sourceTree = "<group>"; };
		8831EFB82240154A00D10172 /* button_rightTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_rightTemplate.pdf; sourceTree = "<group>"; };
		8831EFBB2240159D00D10172 /* button_pane_hideTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_pane_hideTemplate.pdf; sourceTree = "<group>"; };
		8831EFBC2240159E00D10172 /* button_pane_showTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_pane_showTemplate.pdf; sourceTree = "<group>"; };
		8831EFBF2240166D00D10172 /* button_select_allTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_select_allTemplate.pdf; sourceTree = "<group>"; };
		8831EFC02240166D00D10172 /* button_select_noneTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_select_noneTemplate.pdf; sourceTree = "<group>"; };
		8831EFC3224016DF00D10172 /* button_filterTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_filterTemplate.pdf; sourceTree = "<group>"; };
		8831EFC4224016DF00D10172 /* button_filter_activeTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_filter_activeTemplate.pdf; sourceTree = "<group>"; };
		8831EFC5224016E000D10172 /* button_edit_mode_selectedTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_edit_mode_selectedTemplate.pdf; sourceTree = "<group>"; };
		8831EFC6224016E000D10172 /* button_edit_modeTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_edit_modeTemplate.pdf; sourceTree = "<group>"; };
		8831EFCB2240175300D10172 /* button_actionTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_actionTemplate.pdf; sourceTree = "<group>"; };
		8831EFCC2240175300D10172 /* button_paginationTemplate.pdf */ = {isa = PBXFileReference; lastKnownFileType = image.pdf; path = button_paginationTemplate.pdf; sourceTree = "<group>"; };
		8AEACADD4B36D9819ADC93DD /* TableSortHelperTests.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TableSortHelperTests.swift; sourceTree = "<group>"; };
		8AEACD304316A0931DF8ED26 /* TableSortHelper.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TableSortHelper.swift; sourceTree = "<group>"; };
		8D15AC370486D014006FF6A4 /* Sequel Ace.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Sequel Ace.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		963DA1C22CACC6F000B5A544 /* QuickLookUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = QuickLookUI.framework; path = System/Library/Frameworks/QuickLookUI.framework; sourceTree = SDKROOT; };
		964908C4249A77CF0052FC4A /* ssh_config */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = ssh_config; sourceTree = "<group>"; };
		965125F524926B6300E65B53 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; usesTabs = 0; };
		9651261F24926C7900E65B53 /* Sequel Ace.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Sequel Ace.entitlements"; sourceTree = "<group>"; };
		967D875A24B67B4300BAE934 /* SPAutosizingTextView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SPAutosizingTextView.swift; sourceTree = "<group>"; };
		969178A324A5640D0012ED42 /* Default Bundles */ = {isa = PBXFileReference; lastKnownFileType = folder; name = "Default Bundles"; path = "SharedSupport/Default Bundles"; sourceTree = "<group>"; };
		969178A424A5640D0012ED42 /* Default Themes */ = {isa = PBXFileReference; lastKnownFileType = folder; name = "Default Themes"; path = "SharedSupport/Default Themes"; sourceTree = "<group>"; };
		9696538624931AD10003321E /* SequelAceTunnelAssistant.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = SequelAceTunnelAssistant.entitlements; sourceTree = "<group>"; };
		9696538724931AE10003321E /* xibLocalizationPostprocessor.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = xibLocalizationPostprocessor.entitlements; sourceTree = "<group>"; };
		96A5DDB42D63C8E70079105E /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		96B0A1502493DF16007BB270 /* ShortcutRecorder.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ShortcutRecorder.framework; path = Frameworks/ShortcutRecorder.framework; sourceTree = "<group>"; };
		96B0A1532493F43B007BB270 /* TunnelAssistant-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "TunnelAssistant-Info.plist"; path = "Plists/TunnelAssistant-Info.plist"; sourceTree = "<group>"; };
		96CA8F9B2783A8FE0061C2D1 /* Menlo.ttc */ = {isa = PBXFileReference; lastKnownFileType = file; name = Menlo.ttc; path = Fonts/Menlo.ttc; sourceTree = "<group>"; };
		9BE760B3C4586EA3B1A48600 /* SPHelpViewerController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPHelpViewerController.m; sourceTree = "<group>"; };
		9BE761AE45D4F6B9B90E67DE /* SPHelpViewerClient.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPHelpViewerClient.h; sourceTree = "<group>"; };
		9BE764320CE8E86E8F63647B /* SPHelpViewerClient.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPHelpViewerClient.m; sourceTree = "<group>"; };
		9BE768F3989033CEDDC2027E /* SPFillView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFillView.m; sourceTree = "<group>"; };
		9BE76A3D5C9830E2F7738770 /* SPFilterTableController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFilterTableController.m; sourceTree = "<group>"; };
		9BE76C6E8377959C794385E5 /* SPHelpViewerController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPHelpViewerController.h; sourceTree = "<group>"; };
		9BE76CC0CCE3A4A74E3E8D5E /* SPFillView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFillView.h; sourceTree = "<group>"; };
		9BE76F9BF9BDA2921CDD05AF /* SPFilterTableController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFilterTableController.h; sourceTree = "<group>"; };
		B51D6B9D114C310C0074704E /* toolbar-switch-to-table-triggers.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "toolbar-switch-to-table-triggers.png"; sourceTree = "<group>"; };
		B52460D30F8EF92300171639 /* SPArrayAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPArrayAdditions.h; sourceTree = "<group>"; };
		B52460D40F8EF92300171639 /* SPArrayAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPArrayAdditions.m; sourceTree = "<group>"; };
		B52460D50F8EF92300171639 /* SPTextViewAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTextViewAdditions.h; sourceTree = "<group>"; };
		B52460D60F8EF92300171639 /* SPTextViewAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTextViewAdditions.m; sourceTree = "<group>"; };
		B54F25E50FD909C400E2CF36 /* toolbar-switch-to-table-relations.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "toolbar-switch-to-table-relations.png"; sourceTree = "<group>"; };
		B57747D30F7A8974003B34F9 /* SPPreferenceController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPPreferenceController.m; sourceTree = "<group>"; };
		B57747D50F7A8978003B34F9 /* SPPreferenceController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPPreferenceController.h; sourceTree = "<group>"; };
		B57747D70F7A8990003B34F9 /* SPWindowAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPWindowAdditions.h; sourceTree = "<group>"; };
		B57747D80F7A8990003B34F9 /* SPWindowAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPWindowAdditions.m; sourceTree = "<group>"; };
		B57747DA0F7A89D0003B34F9 /* SPFavoriteTextFieldCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFavoriteTextFieldCell.h; sourceTree = "<group>"; };
		B57747DB0F7A89D0003B34F9 /* SPFavoriteTextFieldCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFavoriteTextFieldCell.m; sourceTree = "<group>"; };
		B577483A0F7A8B57003B34F9 /* database.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = database.png; sourceTree = "<group>"; };
		B58731270F838C9E00087794 /* PreferenceDefaults.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = PreferenceDefaults.plist; path = Plists/PreferenceDefaults.plist; sourceTree = "<group>"; };
		B5E2C5F90F2353B5007446E0 /* table-property.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "table-property.png"; sourceTree = "<group>"; };
		B5E92F1A0F75B2E800012500 /* SPExportController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPExportController.h; sourceTree = "<group>"; };
		B5E92F1B0F75B2E800012500 /* SPExportController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPExportController.m; sourceTree = "<group>"; };
		B5EAC0FC0EC87FF900CC579C /* Security.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Security.framework; path = System/Library/Frameworks/Security.framework; sourceTree = SDKROOT; };
		BC01BCCD104024BE006BDEE7 /* SPEncodingPopupAccessory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPEncodingPopupAccessory.h; sourceTree = "<group>"; };
		BC01BCCE104024BE006BDEE7 /* SPEncodingPopupAccessory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPEncodingPopupAccessory.m; sourceTree = "<group>"; };
		BC05F1C3101241DF008A97F8 /* YRKSpinningProgressIndicator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = YRKSpinningProgressIndicator.h; sourceTree = "<group>"; };
		BC05F1C4101241DF008A97F8 /* YRKSpinningProgressIndicator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = YRKSpinningProgressIndicator.m; sourceTree = "<group>"; };
		BC09D7D812A786FB0030DB64 /* cancel-clicked-highlighted.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "cancel-clicked-highlighted.png"; sourceTree = "<group>"; };
		BC09D7D912A786FB0030DB64 /* cancel-clicked.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "cancel-clicked.png"; sourceTree = "<group>"; };
		BC09D7DA12A786FB0030DB64 /* cancel-highlighted.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "cancel-highlighted.png"; sourceTree = "<group>"; };
		BC09D7DB12A786FB0030DB64 /* cancel-hovered-highlighted.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "cancel-hovered-highlighted.png"; sourceTree = "<group>"; };
		BC09D7DC12A786FB0030DB64 /* cancel-hovered.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "cancel-hovered.png"; sourceTree = "<group>"; };
		BC09D7DD12A786FB0030DB64 /* cancel.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = cancel.png; sourceTree = "<group>"; };
		BC0ED3D812A9196C00088461 /* SPChooseMenuItemDialog.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPChooseMenuItemDialog.h; sourceTree = "<group>"; };
		BC0ED3D912A9196C00088461 /* SPChooseMenuItemDialog.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPChooseMenuItemDialog.m; sourceTree = "<group>"; };
		BC1847E80FE6EC8400094BFB /* SPEditSheetTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPEditSheetTextView.h; sourceTree = "<group>"; };
		BC1847E90FE6EC8400094BFB /* SPEditSheetTextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPEditSheetTextView.m; sourceTree = "<group>"; };
		BC1944CE1297291800A236CD /* SPBundleCommandTextView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPBundleCommandTextView.h; sourceTree = "<group>"; };
		BC1944CF1297291800A236CD /* SPBundleCommandTextView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPBundleCommandTextView.m; sourceTree = "<group>"; };
		BC27779E11514B940034DF6A /* SPNavigatorController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPNavigatorController.h; sourceTree = "<group>"; };
		BC27779F11514B940034DF6A /* SPNavigatorController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPNavigatorController.m; sourceTree = "<group>"; };
		BC2898F1125F4488001B50E1 /* SPGeometryDataView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPGeometryDataView.h; sourceTree = "<group>"; };
		BC2898F2125F4488001B50E1 /* SPGeometryDataView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPGeometryDataView.m; sourceTree = "<group>"; };
		BC29C37D10501EFD00DD6C6E /* SPQueryController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPQueryController.h; sourceTree = "<group>"; };
		BC29C37E10501EFD00DD6C6E /* SPQueryController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPQueryController.m; sourceTree = "<group>"; };
		BC2C16D20FEBEDF10003993B /* SPDataAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPDataAdditions.h; sourceTree = "<group>"; };
		BC2C16D30FEBEDF10003993B /* SPDataAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPDataAdditions.m; sourceTree = "<group>"; };
		BC2C8E210FA8C2DB008468C7 /* SPMySQLHelpTemplate.html */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.html; name = SPMySQLHelpTemplate.html; path = Templates/SPMySQLHelpTemplate.html; sourceTree = "<group>"; };
		BC32F240121D66260067305E /* SPFileManagerAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFileManagerAdditions.h; sourceTree = "<group>"; };
		BC32F241121D66260067305E /* SPFileManagerAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFileManagerAdditions.m; sourceTree = "<group>"; };
		BC398A2B121D526200BE3EF4 /* SPCopyTable.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPCopyTable.h; sourceTree = "<group>"; };
		BC398A2C121D526200BE3EF4 /* SPCopyTable.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPCopyTable.m; sourceTree = "<group>"; };
		BC4DF1961158FB280059FABD /* SPNavigatorOutlineView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPNavigatorOutlineView.h; sourceTree = "<group>"; };
		BC4DF1971158FB280059FABD /* SPNavigatorOutlineView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPNavigatorOutlineView.m; sourceTree = "<group>"; };
		BC5750D312A6233900911BA2 /* SPActivityTextFieldCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPActivityTextFieldCell.m; sourceTree = "<group>"; };
		BC5750D412A6233900911BA2 /* SPActivityTextFieldCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPActivityTextFieldCell.h; sourceTree = "<group>"; };
		BC675A121072039C00C5ACD4 /* SPContentFilterManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPContentFilterManager.h; sourceTree = "<group>"; };
		BC675A131072039C00C5ACD4 /* SPContentFilterManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPContentFilterManager.m; sourceTree = "<group>"; };
		BC68BFC5128D4EAE004907D9 /* SPBundleEditorController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPBundleEditorController.h; sourceTree = "<group>"; };
		BC68BFC6128D4EAE004907D9 /* SPBundleEditorController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPBundleEditorController.m; sourceTree = "<group>"; };
		BC77C5E2129AA69E009AD832 /* SPBundleHTMLOutputController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPBundleHTMLOutputController.h; sourceTree = "<group>"; };
		BC77C5E3129AA69E009AD832 /* SPBundleHTMLOutputController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPBundleHTMLOutputController.m; sourceTree = "<group>"; };
		BC85F5CE12193B7D00E255B5 /* SPColorAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPColorAdditions.h; sourceTree = "<group>"; };
		BC85F5CF12193B7D00E255B5 /* SPColorAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPColorAdditions.m; sourceTree = "<group>"; };
		BC878A6F121A836F00AE5066 /* SPColorWellCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPColorWellCell.h; sourceTree = "<group>"; };
		BC878A70121A836F00AE5066 /* SPColorWellCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPColorWellCell.m; sourceTree = "<group>"; };
		BC8C8530100E0A8000D7A129 /* SPTableView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTableView.h; sourceTree = "<group>"; };
		BC8C8531100E0A8000D7A129 /* SPTableView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTableView.m; sourceTree = "<group>"; };
		BC962D651144EACA006170BD /* CompletionTokens.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = CompletionTokens.plist; path = Plists/CompletionTokens.plist; sourceTree = "<group>"; };
		BC9F087F100FCF2C00A80D32 /* SPFieldEditorController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFieldEditorController.h; sourceTree = "<group>"; };
		BC9F0880100FCF2C00A80D32 /* SPFieldEditorController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFieldEditorController.m; sourceTree = "<group>"; };
		BCA6271A1031B9D40047E5D5 /* SPTooltip.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPTooltip.h; sourceTree = "<group>"; };
		BCA6271B1031B9D40047E5D5 /* SPTooltip.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPTooltip.m; sourceTree = "<group>"; };
		BCD0AD480FBBFC340066EA5C /* SPSQLTokenizer.l */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.lex; path = SPSQLTokenizer.l; sourceTree = "<group>"; };
		BCD0AD4A0FBBFC480066EA5C /* SPSQLTokenizer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPSQLTokenizer.h; sourceTree = "<group>"; };
		BCE0025B11173D2A009DA533 /* SPFieldMapperController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SPFieldMapperController.h; sourceTree = "<group>"; };
		BCE0025C11173D2A009DA533 /* SPFieldMapperController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPFieldMapperController.m; sourceTree = "<group>"; };
		C9AD7C771676138000234EEE /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		C9AD7C791676158C00234EEE /* toolbar-switch-to-sql.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "toolbar-switch-to-sql.png"; sourceTree = "<group>"; };
		C9AD7C7A1676158C00234EEE /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		C9C9943F1678A439001F5DA8 /* button_bar_spacer.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = button_bar_spacer.png; sourceTree = "<group>"; };
		C9C994471678B3E6001F5DA8 /* table-small-square.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "table-small-square.png"; sourceTree = "<group>"; };
		C9C994481678B3E6001F5DA8 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		C9C9944B1678BCFA001F5DA8 /* table-small.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "table-small.png"; sourceTree = "<group>"; };
		C9C9944C1678BCFA001F5DA8 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		C9F9270F162D38D70051CB2E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		C9F92711162D39E60051CB2E /* toolbar-switch-to-browse.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "toolbar-switch-to-browse.png"; sourceTree = "<group>"; };
		C9F92713162D39FE0051CB2E /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		D35577F42728C6CF002B3989 /* SPWindowTabAccessory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SPWindowTabAccessory.swift; sourceTree = "<group>"; };
		FD0E72E92C38DC0E007EF348 /* SATableHeaderView.swift */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.swift; path = SATableHeaderView.swift; sourceTree = "<group>"; };
		FD0E72EB2C391F44007EF348 /* SQLiteDisplayFormatManager.swift */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.swift; path = SQLiteDisplayFormatManager.swift; sourceTree = "<group>"; };
		FD18C8982C3C9B2F002A5D57 /* SAUuidFormatter.swift */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.swift; path = SAUuidFormatter.swift; sourceTree = "<group>"; };
		FD2056042C3A7E90008DD271 /* SABaseFormatter.swift */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.swift; path = SABaseFormatter.swift; sourceTree = "<group>"; };
		FD6DFF862ADB40630057B713 /* SPTableHistoryManager.swift */ = {isa = PBXFileReference; indentWidth = 4; lastKnownFileType = sourcecode.swift; path = SPTableHistoryManager.swift; sourceTree = "<group>"; tabWidth = 4; usesTabs = 1; };
		FD8099A62C59DCFA0084646F /* SAUuidFormatterTests.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SAUuidFormatterTests.swift; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		17E20DFF12D6609E007F75A6 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				513C8CE12BBC4132001CCE3A /* OCMock in Frameworks */,
				51DCBEF0257134190098E303 /* libz.tbd in Frameworks */,
				502D22151BA62FA5000D4CE7 /* Security.framework in Frameworks */,
				1717FA43155831600065C036 /* libicucore.dylib in Frameworks */,
				50EA92671AB23EE1008D3C4F /* SPMySQL.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58CDB3340FCE13C900F8ACA3 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				963DA1C82CACC71B00B5A544 /* QuickLookUI.framework in Frameworks */,
				586F457E0FDB280100B428D7 /* libicucore.dylib in Frameworks */,
				58CDB3400FCE13EF00F8ACA3 /* Security.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D15AC330486D014006FF6A4 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				96A5DDB82D63C8E70079105E /* libc++.tbd in Frameworks */,
				963DA1C72CACC71000B5A544 /* QuickLookUI.framework in Frameworks */,
				1A89556F25D6C8880060CE72 /* Alamofire in Frameworks */,
				503CDBB21ACDC204004F8A2F /* Quartz.framework in Frameworks */,
				265446DF24A1616900376B48 /* libz.tbd in Frameworks */,
				17AED4161888BD67008E380F /* Security.framework in Frameworks */,
				51BC150125BE138700F1CDC9 /* SnapKit in Frameworks */,
				296DC8BF0F9091DF002A3258 /* libicucore.dylib in Frameworks */,
				8D15AC340486D014006FF6A4 /* Cocoa.framework in Frameworks */,
				296DC89F0F8FD336002A3258 /* WebKit.framework in Frameworks */,
				96B0A1512493DF3E007BB270 /* ShortcutRecorder.framework in Frameworks */,
				515D303F25BD7DE60021CF1E /* AppCenterCrashes in Frameworks */,
				515D304125BD7DE60021CF1E /* AppCenterAnalytics in Frameworks */,
				9651262224926F1200E65B53 /* QueryKit.framework in Frameworks */,
				179ECECA11F265FC009C6A40 /* libbz2.dylib in Frameworks */,
				9651262424926F1600E65B53 /* SPMySQL.framework in Frameworks */,
				51D9527625AE2B5300574BEB /* FMDB in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		9D68E8A3FC5FD471945E3BA0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1058C7A6FEA54F5311CA2CBB /* Linked Frameworks */ = {
			isa = PBXGroup;
			children = (
				503CDBB11ACDC204004F8A2F /* Quartz.framework */,
				2A37F4C4FDCFA73011CA2CEA /* AppKit.framework */,
				2A37F4C5FDCFA73011CA2CEA /* Foundation.framework */,
				B5EAC0FC0EC87FF900CC579C /* Security.framework */,
				58475685120A065B0057631F /* CoreFoundation.framework */,
				58B9077D11BD9B64000826E5 /* Carbon.framework */,
				1058C7A7FEA54F5311CA2CBB /* Cocoa.framework */,
				96B0A1502493DF16007BB270 /* ShortcutRecorder.framework */,
				296DC89E0F8FD336002A3258 /* WebKit.framework */,
				584756B4120A06740057631F /* CoreServices.framework */,
				584756B6120A067B0057631F /* ApplicationServices.framework */,
				584756B8120A06830057631F /* QuickLook.framework */,
				296DC8BE0F9091DF002A3258 /* libicucore.dylib */,
				179ECEC611F265EE009C6A40 /* libbz2.dylib */,
				17B7B591101602AE00F057DE /* libssl.dylib */,
				584D87BE15141A4A00F24774 /* libz.dylib */,
			);
			name = "Linked Frameworks";
			sourceTree = "<group>";
		};
		1198F5B01174EDA700670590 /* Database Actions */ = {
			isa = PBXGroup;
			children = (
				17D5B49C1553059F00EF3BB3 /* SPViewCopy.h */,
				17D5B49D1553059F00EF3BB3 /* SPViewCopy.m */,
				1141A387117BBFF200126A28 /* SPTableCopy.h */,
				1141A388117BBFF200126A28 /* SPTableCopy.m */,
				1198F5B11174EDD500670590 /* SPDatabaseCopy.h */,
				1198F5B21174EDD500670590 /* SPDatabaseCopy.m */,
				11C2109C1180E70800758039 /* SPDatabaseRename.h */,
				11C2109D1180E70800758039 /* SPDatabaseRename.m */,
				11B55BFC1189E3B2009EF465 /* SPDatabaseAction.h */,
				11B55BFD1189E3B2009EF465 /* SPDatabaseAction.m */,
				17B548611E81FFA600175D5A /* SPCreateDatabaseInfo.h */,
				17B548621E81FFA600175D5A /* SPCreateDatabaseInfo.m */,
			);
			name = "Database Actions";
			path = DatabaseActions;
			sourceTree = "<group>";
		};
		1198F5B41174EDDE00670590 /* Database Actions */ = {
			isa = PBXGroup;
			children = (
				112730551180788A000737FD /* SPTableCopyTest.m */,
				1198F5C31174EF3F00670590 /* SPDatabaseCopyTest.m */,
				11C210DE1180E9B800758039 /* SPDatabaseRenameTest.m */,
				50EA92691AB246B8008D3C4F /* SPDatabaseActionTest.m */,
			);
			name = "Database Actions";
			sourceTree = "<group>";
		};
		17005CB016D6CEA400AF81F4 /* Table Triggers */ = {
			isa = PBXGroup;
			children = (
				29FA88211114619E00D1AF3D /* SPTableTriggers.h */,
				29FA88221114619E00D1AF3D /* SPTableTriggers.m */,
			);
			name = "Table Triggers";
			path = TableTriggers;
			sourceTree = "<group>";
		};
		171156501E8B0F40002E6363 /* Table Relations */ = {
			isa = PBXGroup;
			children = (
				387BBBA60FBCB6CB00B31746 /* SPTableRelations.h */,
				387BBBA70FBCB6CB00B31746 /* SPTableRelations.m */,
			);
			name = "Table Relations";
			path = TableRelations;
			sourceTree = "<group>";
		};
		171312CF109D23CA00FB465F /* Cells */ = {
			isa = PBXGroup;
			children = (
				BC878A6F121A836F00AE5066 /* SPColorWellCell.h */,
				BC878A70121A836F00AE5066 /* SPColorWellCell.m */,
				17E641680EF01F37001BC333 /* ImageAndTextCell.h */,
				17E641690EF01F37001BC333 /* ImageAndTextCell.m */,
				58D2E228101222670063EF1D /* SPTextAndLinkCell.h */,
				58D2E227101222670063EF1D /* SPTextAndLinkCell.m */,
				171312CC109D23C700FB465F /* SPTableTextFieldCell.h */,
				171312CD109D23C700FB465F /* SPTableTextFieldCell.m */,
				B57747DA0F7A89D0003B34F9 /* SPFavoriteTextFieldCell.h */,
				B57747DB0F7A89D0003B34F9 /* SPFavoriteTextFieldCell.m */,
				BC5750D412A6233900911BA2 /* SPActivityTextFieldCell.h */,
				BC5750D312A6233900911BA2 /* SPActivityTextFieldCell.m */,
				1A56463D14569A0B56EE8BAC /* SPPillAttachmentCell.m */,
				1A564C0C0FFB444D2E5CA447 /* SPPillAttachmentCell.h */,
				500DA4B51BEFF877000773FE /* SPComboBoxCell.h */,
				500DA4B61BEFF877000773FE /* SPComboBoxCell.m */,
			);
			path = Cells;
			sourceTree = "<group>";
		};
		173284E51088FEC20062E892 /* Data */ = {
			isa = PBXGroup;
			children = (
				173284E81088FEDE0062E892 /* SPConstants.h */,
				173284E91088FEDE0062E892 /* SPConstants.m */,
			);
			path = Data;
			sourceTree = "<group>";
		};
		173567BA12AC1306000DCCEF /* Bundle Support */ = {
			isa = PBXGroup;
			children = (
				BC68BFC5128D4EAE004907D9 /* SPBundleEditorController.h */,
				BC68BFC6128D4EAE004907D9 /* SPBundleEditorController.m */,
				176E14CF15570FE300FAF326 /* SPBundleCommandRunner.h */,
				176E14D015570FE300FAF326 /* SPBundleCommandRunner.m */,
				6F0EA9222734ABE200514FF1 /* SABundleRunner.h */,
				6F0EA9232734ABE200514FF1 /* SABundleRunner.m */,
				BC77C5E2129AA69E009AD832 /* SPBundleHTMLOutputController.h */,
				BC77C5E3129AA69E009AD832 /* SPBundleHTMLOutputController.m */,
			);
			name = "Bundle Support";
			path = BundleSupport;
			sourceTree = "<group>";
		};
		17386E08151924E9002DC206 /* Table Content */ = {
			isa = PBXGroup;
			children = (
				17E6414E0EF01EF6001BC333 /* SPTableContent.h */,
				17E6414F0EF01EF6001BC333 /* SPTableContent.m */,
			);
			name = "Table Content";
			path = TableContent;
			sourceTree = "<group>";
		};
		173C836C11AAD24300B8B084 /* Exporters */ = {
			isa = PBXGroup;
			children = (
				17F5B1521048C50D00FC794F /* SPExporter.h */,
				17F5B1531048C50D00FC794F /* SPExporter.m */,
				17F5B14F1048C4E400FC794F /* SPCSVExporter.h */,
				17F5B1501048C4E400FC794F /* SPCSVExporter.m */,
				17F5B39A1049B96A00FC794F /* SPSQLExporter.h */,
				17F5B39B1049B96A00FC794F /* SPSQLExporter.m */,
				17292441107AC41000B21980 /* SPXMLExporter.h */,
				17292442107AC41000B21980 /* SPXMLExporter.m */,
				173C837311AAD2AE00B8B084 /* SPDotExporter.h */,
				173C837411AAD2AE00B8B084 /* SPDotExporter.m */,
				173C837711AAD2AE00B8B084 /* SPPDFExporter.h */,
				173C837811AAD2AE00B8B084 /* SPPDFExporter.m */,
				173C837511AAD2AE00B8B084 /* SPHTMLExporter.h */,
				173C837611AAD2AE00B8B084 /* SPHTMLExporter.m */,
				173C837C11AAD2C500B8B084 /* Delegate Protocols */,
			);
			path = Exporters;
			sourceTree = "<group>";
		};
		173C837C11AAD2C500B8B084 /* Delegate Protocols */ = {
			isa = PBXGroup;
			children = (
				173C837E11AAD2FF00B8B084 /* SPCSVExporterProtocol.h */,
				173C838211AAD2FF00B8B084 /* SPSQLExporterProtocol.h */,
				173C838311AAD2FF00B8B084 /* SPXMLExporterProtocol.h */,
				173C837F11AAD2FF00B8B084 /* SPDotExporterProtocol.h */,
				173C838111AAD2FF00B8B084 /* SPPDFExporterProtocol.h */,
				173C838011AAD2FF00B8B084 /* SPHTMLExporterProtocol.h */,
			);
			name = "Delegate Protocols";
			sourceTree = "<group>";
		};
		173E70A1107FF495008733C9 /* Core Data */ = {
			isa = PBXGroup;
			children = (
				500C1F901BFB5F9F0095DC7F /* SPPrivilegesMO.h */,
				500C1F911BFB5F9F0095DC7F /* SPPrivilegesMO.m */,
				4D90B79B101E0CF200D116A1 /* SPUserManager.xcdatamodel */,
				4D90B79C101E0CF200D116A1 /* SPUserMO.h */,
				4D90B79D101E0CF200D116A1 /* SPUserMO.m */,
			);
			name = "Core Data";
			path = CoreData;
			sourceTree = "<group>";
		};
		173E70A6107FF61D008733C9 /* Main View Controllers */ = {
			isa = PBXGroup;
			children = (
				17D38FC2127B0C9500672B13 /* Connection View */,
				1AF5A25D250AC401009885DF /* SPBracketHighlighter.h */,
				1AF5A25E250AC401009885DF /* SPBracketHighlighter.m */,
				1AF5A260250AC401009885DF /* SPBrackets.h */,
				1AF5A25F250AC401009885DF /* SPBrackets.m */,
				17E641480EF01EF6001BC333 /* SPCustomQuery.h */,
				17E641490EF01EF6001BC333 /* SPCustomQuery.m */,
				17E641500EF01EF6001BC333 /* SPDatabaseDocument.h */,
				17E641510EF01EF6001BC333 /* SPDatabaseDocument.m */,
				514DD98825FACF1500EA3B3B /* SPDatabaseDocument.swift */,
				177E7A210FCB6A2E00E9E122 /* SPExtendedTableInfo.h */,
				177E7A220FCB6A2E00E9E122 /* SPExtendedTableInfo.m */,
				17386E08151924E9002DC206 /* Table Content */,
				171156501E8B0F40002E6363 /* Table Relations */,
				17D38F691279E17D00672B13 /* Table Structure */,
				17005CB016D6CEA400AF81F4 /* Table Triggers */,
			);
			name = "Main View Controllers";
			path = MainViewControllers;
			sourceTree = "<group>";
		};
		173E70D2107FF687008733C9 /* Subview Controllers */ = {
			isa = PBXGroup;
			children = (
				967D875A24B67B4300BAE934 /* SPAutosizingTextView.swift */,
				501B1D161728A3DA0017C92E /* SPCharsetCollationHelper.h */,
				501B1D171728A3DA0017C92E /* SPCharsetCollationHelper.m */,
				17CC97F110B4ABE90034CD7A /* SPAboutController.h */,
				17CC97F210B4ABE90034CD7A /* SPAboutController.m */,
				BC675A121072039C00C5ACD4 /* SPContentFilterManager.h */,
				BC675A131072039C00C5ACD4 /* SPContentFilterManager.m */,
				BC9F087F100FCF2C00A80D32 /* SPFieldEditorController.h */,
				BC9F0880100FCF2C00A80D32 /* SPFieldEditorController.m */,
				173C4364104455E0001F3A30 /* SPQueryFavoriteManager.h */,
				173C4365104455E0001F3A30 /* SPQueryFavoriteManager.m */,
				1792C13510AD75C800ABE758 /* SPServerVariablesController.h */,
				1792C13610AD75C800ABE758 /* SPServerVariablesController.m */,
				17E6415E0EF01F15001BC333 /* SPTableInfo.h */,
				17E6415F0EF01F15001BC333 /* SPTableInfo.m */,
				17E641600EF01F15001BC333 /* SPTablesList.h */,
				17E641610EF01F15001BC333 /* SPTablesList.m */,
				BC27779E11514B940034DF6A /* SPNavigatorController.h */,
				BC27779F11514B940034DF6A /* SPNavigatorController.m */,
				17A7773211C52D8E001E27B4 /* SPIndexesController.h */,
				17A7773311C52D8E001E27B4 /* SPIndexesController.m */,
				50A9F8AF19EAD4B90053E571 /* SPGotoDatabaseController.h */,
				50A9F8B019EAD4B90053E571 /* SPGotoDatabaseController.m */,
				506CE92F1A311C6C0039F736 /* SPRuleFilterController.h */,
				506CE9301A311C6C0039F736 /* SPRuleFilterController.m */,
				9BE76A3D5C9830E2F7738770 /* SPFilterTableController.m */,
				9BE76F9BF9BDA2921CDD05AF /* SPFilterTableController.h */,
				BC29C37D10501EFD00DD6C6E /* SPQueryController.h */,
				BC29C37E10501EFD00DD6C6E /* SPQueryController.m */,
				4D90B798101E0CDF00D116A1 /* SPUserManager.h */,
				4D90B799101E0CDF00D116A1 /* SPUserManager.m */,
				174CE14010AB9281008F892B /* SPProcessListController.h */,
				174CE14110AB9281008F892B /* SPProcessListController.m */,
				17F2FB322172561400CA4358 /* Help Viewer */,
			);
			name = "Subview Controllers";
			path = SubviewControllers;
			sourceTree = "<group>";
		};
		173E70D4107FF6E7008733C9 /* Data Controllers */ = {
			isa = PBXGroup;
			children = (
				58FEF57C0F3B4E9700518E8E /* SPTableData.h */,
				58FEF57D0F3B4E9700518E8E /* SPTableData.m */,
				1740FAB90FC4372F00CF3699 /* SPDatabaseData.h */,
				1740FABA0FC4372F00CF3699 /* SPDatabaseData.m */,
				17A20AC4124F9B110095CEFB /* SPServerSupport.h */,
				17A20AC5124F9B110095CEFB /* SPServerSupport.m */,
				17D3C66C128AD4710047709F /* SPFavoritesController.h */,
				17D3C66D128AD4710047709F /* SPFavoritesController.m */,
				584D87901514101E00F24774 /* SPDatabaseStructure.h */,
				584D87911514101E00F24774 /* SPDatabaseStructure.m */,
			);
			name = "Data Controllers";
			path = DataControllers;
			sourceTree = "<group>";
		};
		173E70D5107FF7AF008733C9 /* Other */ = {
			isa = PBXGroup;
			children = (
				1A0751DF25EAF37700FFDF6B /* ReportExceptionApplication.m */,
				1A96E4B42588F34C0055F5F5 /* SecureBookmark.swift */,
				1A24B626258A2E9A00541E88 /* SecureBookmarkData.swift */,
				1A96E4B52588F34C0055F5F5 /* SecureBookmarkManager.swift */,
				589235311020C1230011DE00 /* SPHistoryController.h */,
				589235301020C1230011DE00 /* SPHistoryController.m */,
				FD6DFF862ADB40630057B713 /* SPTableHistoryManager.swift */,
				5806B76211A991EC00813A88 /* SPDocumentController.h */,
				5806B76311A991EC00813A88 /* SPDocumentController.m */,
				1A9EB9AD25651F5000FE60FF /* SQLiteHistoryManager.swift */,
				1A9F344B257B0DDB0062EC87 /* SPBundleManager.h */,
				1A9F343E257B0DBE0062EC87 /* SPBundleManager.m */,
				6F0EA8EC272F33B700514FF1 /* SPBundleManagerAdditions.swift */,
				1A89556525D6BEED0060CE72 /* GitHubReleaseManager.swift */,
				1A89557825D6DE860060CE72 /* GitHub.swift */,
				1AB28D7F25DBD3B500E62BF5 /* ProgressViewController.swift */,
				1A6D28FD25DD8018007509F1 /* ProgressWindowController.storyboard */,
				1A4DC22C25DECEA000DA4FE1 /* ProgressWindowController.swift */,
				44011DFD0DC2836DB08A0619 /* SQLitePinnedTableManager.swift */,
				FD0E72EB2C391F44007EF348 /* SQLiteDisplayFormatManager.swift */,
			);
			path = Other;
			sourceTree = "<group>";
		};
		1740F8350FC3069700CF3699 /* Templates */ = {
			isa = PBXGroup;
			children = (
				1A2711E72539E2BB0066ED58 /* local-connection.html */,
				17DD52B6115071D0007D8950 /* SPPrintTemplate.html */,
				BC2C8E210FA8C2DB008468C7 /* SPMySQLHelpTemplate.html */,
				17E0937D114AE154007FC1B4 /* SPTableInfoPrintTemplate.html */,
			);
			name = Templates;
			sourceTree = "<group>";
		};
		1740F8360FC306AE00CF3699 /* Scripting */ = {
			isa = PBXGroup;
			children = (
				17E641F70EF02088001BC333 /* sequel-pro.scriptSuite */,
				17E641F80EF02088001BC333 /* sequel-pro.scriptTerminology */,
			);
			path = Scripting;
			sourceTree = "<group>";
		};
		1740F8370FC306C900CF3699 /* Plists */ = {
			isa = PBXGroup;
			children = (
				517412372573E8F900EB6935 /* EditorQuickLookTypes.plist */,
				17E641F60EF02088001BC333 /* Info.plist */,
				96B0A1532493F43B007BB270 /* TunnelAssistant-Info.plist */,
				17DA04EA0FC1A7DA00D66140 /* Unit Tests-Info.plist */,
				B58731270F838C9E00087794 /* PreferenceDefaults.plist */,
				517E4511257A954000ED333B /* ContentFilters.plist */,
				BC962D651144EACA006170BD /* CompletionTokens.plist */,
			);
			name = Plists;
			sourceTree = "<group>";
		};
		177486451528EE820036121C /* RegexKitLite */ = {
			isa = PBXGroup;
			children = (
				296DC8B00F909194002A3258 /* RegexKitLite.h */,
				296DC8AB0F909194002A3258 /* RegexKitLite.m */,
			);
			path = RegexKitLite;
			sourceTree = "<group>";
		};
		1798F1801550172A004B0AB8 /* Import & Export */ = {
			isa = PBXGroup;
			children = (
				1798F1841550175B004B0AB8 /* SPFavoritesImporter.h */,
				1798F1851550175B004B0AB8 /* SPFavoritesImporter.m */,
				1798F1861550175B004B0AB8 /* SPFavoritesImportProtocol.h */,
				1798F1811550175B004B0AB8 /* SPFavoritesExporter.h */,
				1798F1821550175B004B0AB8 /* SPFavoritesExporter.m */,
				1798F1831550175B004B0AB8 /* SPFavoritesExportProtocol.h */,
			);
			name = "Import & Export";
			path = ImportExport;
			sourceTree = "<group>";
		};
		1798F192155017FB004B0AB8 /* Tree Nodes */ = {
			isa = PBXGroup;
			children = (
				1798F1991550185B004B0AB8 /* SPTreeNode.h */,
				1798F19A1550185B004B0AB8 /* SPTreeNode.m */,
				1798F1931550181B004B0AB8 /* SPGroupNode.h */,
				1798F1941550181B004B0AB8 /* SPGroupNode.m */,
				17D3C22012859E070047709F /* SPFavoriteNode.h */,
				17D3C22112859E070047709F /* SPFavoriteNode.m */,
				5037F79A1B00148000733564 /* SPNamedNode.h */,
			);
			name = "Tree Nodes";
			path = TreeNodes;
			sourceTree = "<group>";
		};
		17D3583C1533766800A654D7 /* Window */ = {
			isa = PBXGroup;
			children = (
				5132930925F586A900D803AD /* NotificationToken.swift */,
				51BC14F725BE135500F1CDC9 /* SPWindowController.swift */,
				5132930A25F586A900D803AD /* TabManager.swift */,
			);
			path = Window;
			sourceTree = "<group>";
		};
		17D38EFB12777B2300672B13 /* Data Import */ = {
			isa = PBXGroup;
			children = (
				17E641520EF01EF6001BC333 /* SPDataImport.h */,
				17E641530EF01EF6001BC333 /* SPDataImport.m */,
				BCE0025B11173D2A009DA533 /* SPFieldMapperController.h */,
				BCE0025C11173D2A009DA533 /* SPFieldMapperController.m */,
			);
			name = "Data Import";
			path = DataImport;
			sourceTree = "<group>";
		};
		17D38F691279E17D00672B13 /* Table Structure */ = {
			isa = PBXGroup;
			children = (
				17E641540EF01EF6001BC333 /* SPTableStructure.h */,
				17E641550EF01EF6001BC333 /* SPTableStructure.m */,
				17D38F6E1279E23A00672B13 /* SPTableFieldValidation.h */,
				17D38F6F1279E23A00672B13 /* SPTableFieldValidation.m */,
				8AEACD304316A0931DF8ED26 /* TableSortHelper.swift */,
			);
			name = "Table Structure";
			path = TableStructure;
			sourceTree = "<group>";
		};
		17D38FC2127B0C9500672B13 /* Connection View */ = {
			isa = PBXGroup;
			children = (
				5822C9B31000DB2400DCC3D6 /* SPConnectionController.h */,
				5822C9B41000DB2400DCC3D6 /* SPConnectionController.m */,
				17D38FC3127B0CFC00672B13 /* SPConnectionControllerDelegateProtocol.h */,
				1798F1801550172A004B0AB8 /* Import & Export */,
			);
			name = "Connection View";
			path = ConnectionView;
			sourceTree = "<group>";
		};
		17D390A6127B54F300672B13 /* Preferences */ = {
			isa = PBXGroup;
			children = (
				17D390C9127B6BF800672B13 /* SPPreferencesUpgrade.h */,
				17D390CA127B6BF800672B13 /* SPPreferencesUpgrade.m */,
				B57747D50F7A8978003B34F9 /* SPPreferenceController.h */,
				B57747D30F7A8974003B34F9 /* SPPreferenceController.m */,
				17D390A8127B556F00672B13 /* SPPreferencePaneProtocol.h */,
				17D390A7127B551400672B13 /* Panes */,
			);
			path = Preferences;
			sourceTree = "<group>";
		};
		17D390A7127B551400672B13 /* Panes */ = {
			isa = PBXGroup;
			children = (
				1785E9F5127D8C7500F468C8 /* SPPreferencePane.h */,
				1785E9F6127D8C7500F468C8 /* SPPreferencePane.m */,
				17D390C6127B65AF00672B13 /* SPGeneralPreferencePane.h */,
				17D390C7127B65AF00672B13 /* SPGeneralPreferencePane.m */,
				1785EA21127DAF3300F468C8 /* SPTablesPreferencePane.h */,
				1785EA22127DAF3300F468C8 /* SPTablesPreferencePane.m */,
				1785EB5E127DD5A800F468C8 /* SPNotificationsPreferencePane.h */,
				1785EB5F127DD5A800F468C8 /* SPNotificationsPreferencePane.m */,
				1785EB68127DD79300F468C8 /* SPEditorPreferencePane.h */,
				1785EB69127DD79300F468C8 /* SPEditorPreferencePane.m */,
				1785EB64127DD5EA00F468C8 /* SPNetworkPreferencePane.h */,
				1785EB65127DD5EA00F468C8 /* SPNetworkPreferencePane.m */,
				65F52CCC24AE21F200FED3CB /* SPFilePreferencePane.h */,
				65F52CC724AE21D600FED3CB /* SPFilePreferencePane.m */,
			);
			path = Panes;
			sourceTree = "<group>";
		};
		17DC8825126B222D00E9AAEC /* Third Party */ = {
			isa = PBXGroup;
			children = (
				1A2711CD2539D9B10066ED58 /* SPReachability.h */,
				1A2711CE2539D9B10066ED58 /* SPReachability.m */,
				584D88A71515034200F24774 /* NSNotificationCenterThreadingAdditions.h */,
				584D88A81515034200F24774 /* NSNotificationCenterThreadingAdditions.m */,
				5841929F101E57BB0089807F /* NSMutableArray-MultipleSort.h */,
				584192A0101E57BB0089807F /* NSMutableArray-MultipleSort.m */,
				17DC8826126B22F200E9AAEC /* Views */,
				58B909A111C3B8EC000826E5 /* Localization */,
				177486451528EE820036121C /* RegexKitLite */,
				296DC8A40F90914B002A3258 /* MGTemplateEngine */,
			);
			name = "Third Party";
			path = ThirdParty;
			sourceTree = "<group>";
		};
		17DC8826126B22F200E9AAEC /* Views */ = {
			isa = PBXGroup;
			children = (
				5841423D0F97E11000A34B47 /* NoodleLineNumberView.h */,
				5841423E0F97E11000A34B47 /* NoodleLineNumberView.m */,
				BC05F1C3101241DF008A97F8 /* YRKSpinningProgressIndicator.h */,
				BC05F1C4101241DF008A97F8 /* YRKSpinningProgressIndicator.m */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		17DC8827126B32F300E9AAEC /* Table Views */ = {
			isa = PBXGroup;
			children = (
				BC8C8530100E0A8000D7A129 /* SPTableView.h */,
				BC8C8531100E0A8000D7A129 /* SPTableView.m */,
				BC398A2B121D526200BE3EF4 /* SPCopyTable.h */,
				BC398A2C121D526200BE3EF4 /* SPCopyTable.m */,
				171C398D16BD634600209EC6 /* SPDatabaseContentViewDelegate.h */,
				FD0E72E92C38DC0E007EF348 /* SATableHeaderView.swift */,
			);
			name = "Table Views";
			path = TableViews;
			sourceTree = "<group>";
		};
		17DC8828126B332F00E9AAEC /* Accessory Views */ = {
			isa = PBXGroup;
			children = (
				29A1B7E30FD1293A000B88E8 /* SPPrintAccessory.h */,
				29A1B7E40FD1293A000B88E8 /* SPPrintAccessory.m */,
				D35577F42728C6CF002B3989 /* SPWindowTabAccessory.swift */,
				BC01BCCD104024BE006BDEE7 /* SPEncodingPopupAccessory.h */,
				BC01BCCE104024BE006BDEE7 /* SPEncodingPopupAccessory.m */,
			);
			name = "Accessory Views";
			path = AccessoryViews;
			sourceTree = "<group>";
		};
		17DC8829126B337900E9AAEC /* Text Views */ = {
			isa = PBXGroup;
			children = (
				17E641800EF01FA8001BC333 /* SPTextView.h */,
				17E641810EF01FA8001BC333 /* SPTextView.m */,
				BC1847E80FE6EC8400094BFB /* SPEditSheetTextView.h */,
				BC1847E90FE6EC8400094BFB /* SPEditSheetTextView.m */,
				BC1944CE1297291800A236CD /* SPBundleCommandTextView.h */,
				BC1944CF1297291800A236CD /* SPBundleCommandTextView.m */,
			);
			name = "Text Views";
			path = TextViews;
			sourceTree = "<group>";
		};
		17DC886A126B378A00E9AAEC /* Category Additions */ = {
			isa = PBXGroup;
			children = (
				380F4EF40FC0B68F00B0BFD7 /* SPStringAdditionsTests.m */,
				1798F1C2155018D4004B0AB8 /* SPMutableArrayAdditionsTests.m */,
				502D21F51BA50710000D4CE7 /* SPDataAdditionsTests.m */,
				1ADEA5A224BF1C4800D2140B /* SPDateAdditionsTests.m */,
				1A8B53672584552F00526DED /* SPURLAdditionsTests.m */,
				1A2DD55025939B6400616E7E /* SPArrayAdditions.m */,
				1A2DD55925939BEE00616E7E /* SPTestingUtils.m */,
				1A2DD55F25939C1500616E7E /* SPTestingUtils.h */,
				1AF0DA83259F631000961974 /* SPPointerArrayAdditionsTests.m */,
			);
			name = "Category Additions";
			sourceTree = "<group>";
		};
		17DF51241163C68600E3F396 /* Outline Views */ = {
			isa = PBXGroup;
			children = (
				173C44D61044A6AF001F3A30 /* SPOutlineView.h */,
				173C44D71044A6B0001F3A30 /* SPOutlineView.m */,
				17D3C6D1128B1C900047709F /* SPFavoritesOutlineView.h */,
				17D3C6D2128B1C900047709F /* SPFavoritesOutlineView.m */,
				BC4DF1961158FB280059FABD /* SPNavigatorOutlineView.h */,
				BC4DF1971158FB280059FABD /* SPNavigatorOutlineView.m */,
			);
			name = "Outline Views";
			path = OutlineViews;
			sourceTree = "<group>";
		};
		17E5954F14F304000054EE08 /* Products */ = {
			isa = PBXGroup;
			children = (
				17E5955314F304000054EE08 /* QueryKit.framework */,
				17E596A214F307CE0054EE08 /* Tests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		17E641420EF01E8A001BC333 /* Source */ = {
			isa = PBXGroup;
			children = (
				17E641470EF01EB8001BC333 /* Controllers */,
				17FC36AE141425D600AC3602 /* Frameworks */,
				17E642050EF020A3001BC333 /* Interfaces */,
				17E641440EF01EB5001BC333 /* main.m */,
				17E6415D0EF01EF9001BC333 /* Model */,
				17E6416E0EF01F3B001BC333 /* Other */,
				51F4AFBD24B26665006144D5 /* Sequel-Ace-Bridging-Header.h */,
				17E641450EF01EB5001BC333 /* Sequel-Ace.pch */,
				17DC8825126B222D00E9AAEC /* Third Party */,
				17E641670EF01F19001BC333 /* Views */,
			);
			path = Source;
			sourceTree = "<group>";
		};
		17E641430EF01E90001BC333 /* Resources */ = {
			isa = PBXGroup;
			children = (
				517E44F5257A906100ED333B /* Localization */,
				1740F8370FC306C900CF3699 /* Plists */,
				17E6418B0EF01FF7001BC333 /* Images */,
				96CA8F922783A3E40061C2D1 /* Fonts */,
				1740F8360FC306AE00CF3699 /* Scripting */,
				58D1006813A57FAE0092E019 /* Localization Strings Files */,
				1740F8350FC3069700CF3699 /* Templates */,
				96D494B8249C07940092F335 /* SSH Helpers */,
				1A479B15254375BD00D29E6E /* License.md */,
				517E44FB257A94C400ED333B /* Credits.rtf */,
				17CC993A10B4C9C80034CD7A /* License.rtf */,
				583A278D23F06F1000FBE97B /* Colors.xcassets */,
				51A709382565B99F001F1D2F /* Images.xcassets */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		17E641470EF01EB8001BC333 /* Controllers */ = {
			isa = PBXGroup;
			children = (
				173567BA12AC1306000DCCEF /* Bundle Support */,
				173E70D4107FF6E7008733C9 /* Data Controllers */,
				17F5B1491048C4C000FC794F /* Data Export */,
				17D38EFB12777B2300672B13 /* Data Import */,
				173E70A6107FF61D008733C9 /* Main View Controllers */,
				173E70D5107FF7AF008733C9 /* Other */,
				17D390A6127B54F300672B13 /* Preferences */,
				17E6414A0EF01EF6001BC333 /* SPAppController.h */,
				17E6414B0EF01EF6001BC333 /* SPAppController.m */,
				51BB391B25F82B060048CA69 /* SPAppController.swift */,
				173E70D2107FF687008733C9 /* Subview Controllers */,
				17D3583C1533766800A654D7 /* Window */,
			);
			path = Controllers;
			sourceTree = "<group>";
		};
		17E6415D0EF01EF9001BC333 /* Model */ = {
			isa = PBXGroup;
			children = (
				173E70A1107FF495008733C9 /* Core Data */,
				BC0ED3D812A9196C00088461 /* SPChooseMenuItemDialog.h */,
				BC0ED3D912A9196C00088461 /* SPChooseMenuItemDialog.m */,
				172A650F0F7BED7A001E861A /* SPConsoleMessage.h */,
				172A65100F7BED7A001E861A /* SPConsoleMessage.m */,
				17C058860FC9FC390077E9CF /* SPNarrowDownCompletion.h */,
				17C058870FC9FC390077E9CF /* SPNarrowDownCompletion.m */,
				17D3C66F128AD8160047709F /* SPSingleton.h */,
				17D3C670128AD8160047709F /* SPSingleton.m */,
				BCA6271A1031B9D40047E5D5 /* SPTooltip.h */,
				BCA6271B1031B9D40047E5D5 /* SPTooltip.m */,
				1798F192155017FB004B0AB8 /* Tree Nodes */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		17E641670EF01F19001BC333 /* Views */ = {
			isa = PBXGroup;
			children = (
				583CA21312EC8B2200C9E763 /* SPWindow.h */,
				583CA21412EC8B2200C9E763 /* SPWindow.m */,
				17E6417E0EF01FA8001BC333 /* SPImageView.h */,
				17E6417F0EF01FA8001BC333 /* SPImageView.m */,
				58DF9F7115AB8509003B4330 /* SPSplitView.h */,
				58DF9F7215AB8509003B4330 /* SPSplitView.m */,
				58C56EF30F438E120035701E /* SPDataCellFormatter.h */,
				58C56EF40F438E120035701E /* SPDataCellFormatter.m */,
				BC2898F1125F4488001B50E1 /* SPGeometryDataView.h */,
				BC2898F2125F4488001B50E1 /* SPGeometryDataView.m */,
				9BE768F3989033CEDDC2027E /* SPFillView.m */,
				9BE76CC0CCE3A4A74E3E8D5E /* SPFillView.h */,
				17FDB0AC1280938000DBBBC2 /* Controls */,
				171312CF109D23CA00FB465F /* Cells */,
				17DC8829126B337900E9AAEC /* Text Views */,
				17DC8827126B32F300E9AAEC /* Table Views */,
				17DF51241163C68600E3F396 /* Outline Views */,
				17DC8828126B332F00E9AAEC /* Accessory Views */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		17E6416E0EF01F3B001BC333 /* Other */ = {
			isa = PBXGroup;
			children = (
				FD2056032C3A7E64008DD271 /* Formatters */,
				51F4AFB924B26646006144D5 /* Extensions */,
				507FF10E1BBCC4A900104523 /* Utility */,
				1198F5B01174EDA700670590 /* Database Actions */,
				583CE39511722B70008F148E /* File Compression */,
				173284E51088FEC20062E892 /* Data */,
				17E6416F0EF01F4C001BC333 /* Keychain */,
				58FEF15E0F23D60A00518E8E /* Parsing */,
				17E641720EF01F6B001BC333 /* SSH Tunnel */,
				B57747D60F7A8990003B34F9 /* Category Additions */,
				58DA884E103E1597000B98DF /* Debugging & Support */,
			);
			path = Other;
			sourceTree = "<group>";
		};
		17E6416F0EF01F4C001BC333 /* Keychain */ = {
			isa = PBXGroup;
			children = (
				17E641730EF01F80001BC333 /* SPKeychain.h */,
				17E641740EF01F80001BC333 /* SPKeychain.m */,
			);
			path = Keychain;
			sourceTree = "<group>";
		};
		17E641720EF01F6B001BC333 /* SSH Tunnel */ = {
			isa = PBXGroup;
			children = (
				58CDB32E0FCE138D00F8ACA3 /* SPSSHTunnel.h */,
				58CDB32F0FCE138D00F8ACA3 /* SPSSHTunnel.m */,
				58CDB3310FCE139C00F8ACA3 /* SequelAceTunnelAssistant.m */,
			);
			name = "SSH Tunnel";
			path = SSHTunnel;
			sourceTree = "<group>";
		};
		17E6418B0EF01FF7001BC333 /* Images */ = {
			isa = PBXGroup;
			children = (
				8831EFA5224011B700D10172 /* button_addTemplate.pdf */,
				8831EFAB2240131400D10172 /* button_editTemplate.pdf */,
				8831EFAF224013AF00D10172 /* button_refreshTemplate.pdf */,
				8831EFB3224014C700D10172 /* button_clearTemplate.pdf */,
				8831EFA92240128600D10172 /* button_removeTemplate.pdf */,
				8831EFAD2240135300D10172 /* button_duplicateTemplate.pdf */,
				8831EFB52240150A00D10172 /* button_add_folderTemplate.pdf */,
				8831EFB72240154A00D10172 /* button_leftTemplate.pdf */,
				8831EFB82240154A00D10172 /* button_rightTemplate.pdf */,
				8831EFBB2240159D00D10172 /* button_pane_hideTemplate.pdf */,
				8831EFBC2240159E00D10172 /* button_pane_showTemplate.pdf */,
				8831EFBF2240166D00D10172 /* button_select_allTemplate.pdf */,
				8831EFC02240166D00D10172 /* button_select_noneTemplate.pdf */,
				8831EFC5224016E000D10172 /* button_edit_mode_selectedTemplate.pdf */,
				8831EFC6224016E000D10172 /* button_edit_modeTemplate.pdf */,
				8831EFC4224016DF00D10172 /* button_filter_activeTemplate.pdf */,
				8831EFC3224016DF00D10172 /* button_filterTemplate.pdf */,
				8831EFCB2240175300D10172 /* button_actionTemplate.pdf */,
				8831EFCC2240175300D10172 /* button_paginationTemplate.pdf */,
				8831EFB12240143600D10172 /* button_bar_handleTemplate.pdf */,
				3E242D4B20FEB44D0015470D /* button_bar_spacer_dark.png */,
				3E242D4E20FEB44D0015470D /* <EMAIL> */,
				C9C9943F1678A439001F5DA8 /* button_bar_spacer.png */,
				582E940D1682A2AD003459FD /* <EMAIL> */,
				BC09D7D812A786FB0030DB64 /* cancel-clicked-highlighted.png */,
				BC09D7D912A786FB0030DB64 /* cancel-clicked.png */,
				BC09D7DA12A786FB0030DB64 /* cancel-highlighted.png */,
				BC09D7DB12A786FB0030DB64 /* cancel-hovered-highlighted.png */,
				BC09D7DC12A786FB0030DB64 /* cancel-hovered.png */,
				BC09D7DD12A786FB0030DB64 /* cancel.png */,
				582E942D1683658A003459FD /* clearconsole.png */,
				177E792B0FCB54EC00E9E122 /* database-small.png */,
				C9AD7C771676138000234EEE /* <EMAIL> */,
				B577483A0F7A8B57003B34F9 /* database.png */,
				177E792C0FCB54EC00E9E122 /* dummy-small.png */,
				582E9449168374C1003459FD /* field-small-square.png */,
				384582C30FB95FF800DDACB6 /* func-small.png */,
				17E6419D0EF02036001BC333 /* grabber-horizontal.png */,
				17E6419E0EF02036001BC333 /* grabber-vertical.png */,
				582E944B16837986003459FD /* hideconsole.png */,
				5843DA68161FA35600EAA6D1 /* key-icon-alternate.png */,
				5843DA69161FA35600EAA6D1 /* <EMAIL> */,
				5843DA6A161FA35600EAA6D1 /* key-icon.png */,
				5843DA6B161FA35600EAA6D1 /* <EMAIL> */,
				58E205FB1234FE4F00A97059 /* KeyTemplate.pdf */,
				58D2E22B101222870063EF1D /* link-arrow-clicked.png */,
				582E9399168296F3003459FD /* <EMAIL> */,
				58D2E22C101222870063EF1D /* link-arrow-highlighted-clicked.png */,
				582E939A168296F3003459FD /* <EMAIL> */,
				581068B51015411B0068C6E2 /* link-arrow-highlighted.png */,
				582E939B168296F3003459FD /* <EMAIL> */,
				58D2E22D101222870063EF1D /* link-arrow.png */,
				582E939C168296F3003459FD /* <EMAIL> */,
				582E944F16837AA9003459FD /* network-small.png */,
				384582C60FB9603600DDACB6 /* proc-small.png */,
				58F48B2D161D08C0008536A1 /* quick-connect-icon-white.pdf */,
				58F48AA2161D03C6008536A1 /* quick-connect-icon.pdf */,
				50F530511ABCF66B002F2C1A /* resetTemplate.pdf */,
				582E946F16837DB2003459FD /* showconsole.png */,
				588B2CC50FE5641E00EC5FC0 /* ssh-connected.png */,
				588B2CC60FE5641E00EC5FC0 /* ssh-connecting.png */,
				588B2CC70FE5641E00EC5FC0 /* ssh-disconnected.png */,
				582E947D168380D6003459FD /* sync_arrows_01.png */,
				582E947E168380D6003459FD /* sync_arrows_02.png */,
				582E947F168380D6003459FD /* sync_arrows_03.png */,
				582E9480168380D6003459FD /* sync_arrows_04.png */,
				582E9481168380D6003459FD /* sync_arrows_05.png */,
				582E9482168380D6003459FD /* sync_arrows_06.png */,
				B5E2C5F90F2353B5007446E0 /* table-property.png */,
				C9C994471678B3E6001F5DA8 /* table-small-square.png */,
				C9C994481678B3E6001F5DA8 /* <EMAIL> */,
				C9C9944B1678BCFA001F5DA8 /* table-small.png */,
				C9C9944C1678BCFA001F5DA8 /* <EMAIL> */,
				582E948D168383F0003459FD /* table-view-small-square.png */,
				582E948E168383F0003459FD /* table-view-small.png */,
				582E94A716839AD5003459FD /* toolbar-preferences-autoupdate.png */,
				582E94A916839AEF003459FD /* toolbar-preferences-general.png */,
				582E94F716839E83003459FD /* toolbar-preferences-network.png */,
				582E94AD16839C4A003459FD /* toolbar-preferences-notifications.png */,
				582E94C616839D83003459FD /* toolbar-preferences-tables.png */,
				C9F92711162D39E60051CB2E /* toolbar-switch-to-browse.png */,
				C9F92713162D39FE0051CB2E /* <EMAIL> */,
				C9AD7C791676158C00234EEE /* toolbar-switch-to-sql.png */,
				C9AD7C7A1676158C00234EEE /* <EMAIL> */,
				17E641BE0EF02036001BC333 /* toolbar-switch-to-structure.png */,
				17E641BF0EF02036001BC333 /* toolbar-switch-to-table-info.png */,
				C9F9270F162D38D70051CB2E /* <EMAIL> */,
				B54F25E50FD909C400E2CF36 /* toolbar-switch-to-table-relations.png */,
				B51D6B9D114C310C0074704E /* toolbar-switch-to-table-triggers.png */,
			);
			path = Images;
			sourceTree = "<group>";
		};
		17E642050EF020A3001BC333 /* Interfaces */ = {
			isa = PBXGroup;
			children = (
				51F41FB625F56A5900594BA5 /* MainWindow.xib */,
				51CD0BD9258D06D8009E2484 /* AboutPanel.xib */,
				51CD0BD2258D06D8009E2484 /* BundleEditor.xib */,
				51CD0BDB258D06D8009E2484 /* BundleHTMLOutput.xib */,
				51CD0BCD258D06D8009E2484 /* ConnectionErrorDialog.xib */,
				51CD0BD8258D06D8009E2484 /* ConnectionView.xib */,
				51CD0BCB258D06D8009E2484 /* Console.xib */,
				51CD0BD3258D06D8009E2484 /* ContentFilterManager.xib */,
				51CD0BD5258D06D8009E2484 /* ContentPaginationView.xib */,
				51CD0BDA258D06D8009E2484 /* DatabaseProcessList.xib */,
				51CD0BCC258D06D8009E2484 /* DatabaseServerVariables.xib */,
				51CD0BBF258D06D7009E2484 /* DataMigrationDialog.xib */,
				51CD0BC5258D06D7009E2484 /* DBView.xib */,
				51CD0BD4258D06D8009E2484 /* EncodingPopupView.xib */,
				51CD0BD1258D06D8009E2484 /* ExportDialog.xib */,
				51CD0BC2258D06D7009E2484 /* FieldEditorSheet.xib */,
				51CD0BC1258D06D7009E2484 /* FilterTableWindow.xib */,
				51CD0BC3258D06D7009E2484 /* GotoDatabaseDialog.xib */,
				51CD0BC6258D06D7009E2484 /* HelpViewer.xib */,
				51CD0BC9258D06D8009E2484 /* ImportAccessory.xib */,
				51CD0BC4258D06D7009E2484 /* IndexesView.xib */,
				51CD0BCA258D06D8009E2484 /* MainMenu.xib */,
				51CD0BC7258D06D8009E2484 /* Navigator.xib */,
				51CD0BC0258D06D7009E2484 /* Preferences.xib */,
				51CD0BBE258D06D7009E2484 /* PrintAccessory.xib */,
				51CD0BCE258D06D8009E2484 /* ProgressIndicatorLayer.xib */,
				51CD0BD7258D06D8009E2484 /* QueryFavoriteManager.xib */,
				51CD0BCF258D06D8009E2484 /* SaveSPFAccessory.xib */,
				51CD0BD6258D06D8009E2484 /* SSHQuestionDialog.xib */,
				51CD0BD0258D06D8009E2484 /* UserManagerView.xib */,
			);
			path = Interfaces;
			sourceTree = "<group>";
		};
		17F2FB322172561400CA4358 /* Help Viewer */ = {
			isa = PBXGroup;
			children = (
				9BE760B3C4586EA3B1A48600 /* SPHelpViewerController.m */,
				9BE76C6E8377959C794385E5 /* SPHelpViewerController.h */,
				9BE764320CE8E86E8F63647B /* SPHelpViewerClient.m */,
				9BE761AE45D4F6B9B90E67DE /* SPHelpViewerClient.h */,
			);
			name = "Help Viewer";
			path = HelpViewer;
			sourceTree = "<group>";
		};
		17F5B1491048C4C000FC794F /* Data Export */ = {
			isa = PBXGroup;
			children = (
				B5E92F1A0F75B2E800012500 /* SPExportController.h */,
				B5E92F1B0F75B2E800012500 /* SPExportController.m */,
				173C836F11AAD26E00B8B084 /* SPExportUtilities.h */,
				173C837011AAD26E00B8B084 /* SPExportUtilities.m */,
				582F022F1370B52600B30621 /* SPExportFileNameTokenObject.h */,
				582F02301370B52600B30621 /* SPExportFileNameTokenObject.m */,
				17F90E451210B41100274C98 /* Model */,
				173C836C11AAD24300B8B084 /* Exporters */,
			);
			name = "Data Export";
			path = DataExport;
			sourceTree = "<group>";
		};
		17F90E451210B41100274C98 /* Model */ = {
			isa = PBXGroup;
			children = (
				17F90E461210B42700274C98 /* SPExportFile.h */,
				17F90E471210B42700274C98 /* SPExportFile.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		17FC36AE141425D600AC3602 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				17E5954E14F304000054EE08 /* QueryKit.xcodeproj */,
				584D876015140D3500F24774 /* SPMySQLFramework.xcodeproj */,
			);
			name = Frameworks;
			path = ..;
			sourceTree = "<group>";
		};
		17FDB0AC1280938000DBBBC2 /* Controls */ = {
			isa = PBXGroup;
			children = (
				50E217B118174246009D3580 /* SPColorSelectorView.h */,
				50E217B218174246009D3580 /* SPColorSelectorView.m */,
				17FDB04A1280778B00DBBBC2 /* SPFontPreviewTextField.h */,
				17FDB04B1280778B00DBBBC2 /* SPFontPreviewTextField.m */,
				58D2A6A516FBDEFF002EB401 /* SPComboPopupButton.h */,
				58D2A6A616FBDEFF002EB401 /* SPComboPopupButton.m */,
				50805B0B1BF2A068005F7A99 /* SPPopUpButtonCell.h */,
				50805B0C1BF2A068005F7A99 /* SPPopUpButtonCell.m */,
				3876E15B1CC0BA0300D85154 /* SPTableFooterPopUpButtonCell.h */,
				3876E15C1CC0BA0300D85154 /* SPTableFooterPopUpButtonCell.m */,
			);
			path = Controls;
			sourceTree = "<group>";
		};
		19C28FB0FE9D524F11CA2CBB /* Products */ = {
			isa = PBXGroup;
			children = (
				8D15AC370486D014006FF6A4 /* Sequel Ace.app */,
				380F4ED90FC0B50500B0BFD7 /* Unit Tests.xctest */,
				58CDB3360FCE13C900F8ACA3 /* SequelAceTunnelAssistant */,
				58B9096111C3A42B000826E5 /* xibLocalizationPostprocessor */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1AB0688D24A355B500E2AAC2 /* fixtures */ = {
			isa = PBXGroup;
			children = (
				1A445DA925BACBE5004E9A77 /* naughty_strings.txt */,
				1AB0688E24A355B500E2AAC2 /* certs */,
				1AB0689424A355B500E2AAC2 /* keys */,
			);
			path = fixtures;
			sourceTree = "<group>";
		};
		1AB0688E24A355B500E2AAC2 /* certs */ = {
			isa = PBXGroup;
			children = (
				1AB0688F24A355B500E2AAC2 /* client-cert-crlf.pem */,
				1AB0689024A355B500E2AAC2 /* client-cert-bad-end.pem */,
				1AB0689124A355B500E2AAC2 /* client-cert-lf.pem */,
				1AB0689224A355B500E2AAC2 /* client-cert-cr.pem */,
				1AB0689324A355B500E2AAC2 /* client-cert-bad-start.pem */,
			);
			path = certs;
			sourceTree = "<group>";
		};
		1AB0689424A355B500E2AAC2 /* keys */ = {
			isa = PBXGroup;
			children = (
				1AB0689524A355B500E2AAC2 /* client-key-bad-end.pem */,
				1AB0689624A355B500E2AAC2 /* client-key-cr.pem */,
				1AB0689724A355B500E2AAC2 /* client-key-bad-start.pem */,
				1AB0689824A355B500E2AAC2 /* client-key-lf.pem */,
				1AB0689924A355B500E2AAC2 /* client-key-crlf.pem */,
			);
			path = keys;
			sourceTree = "<group>";
		};
		296DC8A40F90914B002A3258 /* MGTemplateEngine */ = {
			isa = PBXGroup;
			children = (
				296DC8A50F909194002A3258 /* MGTemplateMarker.h */,
				296DC8A60F909194002A3258 /* MGTemplateFilter.h */,
				296DC8A80F909194002A3258 /* MGTemplateEngine.h */,
				296DC8A70F909194002A3258 /* MGTemplateEngine.m */,
				296DC8A90F909194002A3258 /* ICUTemplateMatcher.h */,
				296DC8AC0F909194002A3258 /* ICUTemplateMatcher.m */,
				296DC8AF0F909194002A3258 /* NSArray_DeepMutableCopy.h */,
				296DC8AE0F909194002A3258 /* NSArray_DeepMutableCopy.m */,
				296DC8B20F909194002A3258 /* NSDictionary_DeepMutableCopy.h */,
				296DC8B10F909194002A3258 /* NSDictionary_DeepMutableCopy.m */,
				296DC8B30F909194002A3258 /* MGTemplateStandardMarkers.h */,
				296DC8AD0F909194002A3258 /* MGTemplateStandardMarkers.m */,
				296DC8B50F909194002A3258 /* MGTemplateStandardFilters.h */,
				296DC8B40F909194002A3258 /* MGTemplateStandardFilters.m */,
			);
			path = MGTemplateEngine;
			sourceTree = "<group>";
		};
		2A37F4AAFDCFA73011CA2CEA /* sequel-pro */ = {
			isa = PBXGroup;
			children = (
				518E02F227B91FBC007973E3 /* Entitlements */,
				969178A324A5640D0012ED42 /* Default Bundles */,
				969178A424A5640D0012ED42 /* Default Themes */,
				2A37F4C3FDCFA73011CA2CEA /* Frameworks */,
				1798F17E1550171B004B0AB8 /* LICENSE */,
				19C28FB0FE9D524F11CA2CBB /* Products */,
				38F37194182F0621008EB031 /* readme.md */,
				17E641430EF01E90001BC333 /* Resources */,
				17E641420EF01E8A001BC333 /* Source */,
				380F4EF20FC0B67A00B0BFD7 /* Unit Tests */,
			);
			name = "sequel-pro";
			sourceTree = "<group>";
			usesTabs = 0;
		};
		2A37F4C3FDCFA73011CA2CEA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				96A5DDB42D63C8E70079105E /* libc++.tbd */,
				963DA1C22CACC6F000B5A544 /* QuickLookUI.framework */,
				38C613721C8977E600B3B6EF /* libz.tbd */,
				1058C7A6FEA54F5311CA2CBB /* Linked Frameworks */,
				1A1EE986255131560056FECD /* Quartz.framework */,
				517E44C2257A8F2C00ED333B /* AppleScriptObjC.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		380F4EF20FC0B67A00B0BFD7 /* Unit Tests */ = {
			isa = PBXGroup;
			children = (
				50D3C3591A771C2300B5429C /* Other */,
				1198F5B41174EDDE00670590 /* Database Actions */,
				17DC886A126B378A00E9AAEC /* Category Additions */,
			);
			name = "Unit Tests";
			path = UnitTests;
			sourceTree = "<group>";
		};
		507FF10E1BBCC4A900104523 /* Utility */ = {
			isa = PBXGroup;
			children = (
				1AD785E325B749760007E153 /* OSLog.swift */,
				507FF1101BBCC4C400104523 /* SPFunctions.h */,
				507FF1111BBCC57600104523 /* SPFunctions.m */,
				5089B0251BE714E300E226CD /* SPIdMenu.h */,
				5089B0261BE714E300E226CD /* SPIdMenu.m */,
				50A77DA61E8EB903007466BC /* SPCompatibility.h */,
				5174122E2573E10C00EB6935 /* SPPrintUtility.h */,
				5174122F2573E10C00EB6935 /* SPPrintUtility.m */,
				1A11E4FC25A32789001CB721 /* SPPanelOptions.h */,
				1A11E50325A327F1001CB721 /* SPPanelOptions.m */,
			);
			path = Utility;
			sourceTree = "<group>";
		};
		50D3C3591A771C2300B5429C /* Other */ = {
			isa = PBXGroup;
			children = (
				1AB0688D24A355B500E2AAC2 /* fixtures */,
				1A4152ED25AF530F00B17249 /* GeneralSwiftTests.swift */,
				50D3C35B1A771C4C00B5429C /* SPParserUtilsTest.m */,
				503B02CE1AE95C2C0060CAB1 /* SPTableFilterParserTest.m */,
				50837F731E50DCD4004FAE8A /* SPJSONFormatterTests.m */,
				1A85CB8C2493BC4A00B57B93 /* SPSyncTests.m */,
				1AB068B224A3575600E2AAC2 /* SPValidateKeyAndCertFiles.m */,
				1AE6C1CC25B07E9500880D73 /* SPFunctionsTests.m */,
				8AEACADD4B36D9819ADC93DD /* TableSortHelperTests.swift */,
				FD8099A62C59DCFA0084646F /* SAUuidFormatterTests.swift */,
			);
			name = Other;
			sourceTree = "<group>";
		};
		517E44F5257A906100ED333B /* Localization */ = {
			isa = PBXGroup;
			children = (
				17DD52C4115074CB007D8950 /* Localizable.strings */,
			);
			path = Localization;
			sourceTree = "<group>";
		};
		518E02F227B91FBC007973E3 /* Entitlements */ = {
			isa = PBXGroup;
			children = (
				9651261F24926C7900E65B53 /* Sequel Ace.entitlements */,
				9696538624931AD10003321E /* SequelAceTunnelAssistant.entitlements */,
				9696538724931AE10003321E /* xibLocalizationPostprocessor.entitlements */,
			);
			path = Entitlements;
			sourceTree = "<group>";
		};
		51F4AFB924B26646006144D5 /* Extensions */ = {
			isa = PBXGroup;
			children = (
				1A199629257A624200F5B0F1 /* BundleExtension.swift */,
				1A8B582925EEE15900DFC54A /* ByteCountFormatterExtension.swift */,
				5193502E2567D2FB001272B5 /* CollectionExtension.swift */,
				1A9D83A325514E740024B563 /* DateComponentsFormatterExtension.swift */,
				1A94988D25516057000BC793 /* DateExtension.swift */,
				1A1EE9492551185D0056FECD /* DateFormatterExtension.swift */,
				1ABC770025E3895300E8EE01 /* DispatchQueueExtension.swift */,
				1A70BC6A25EF439F004BB992 /* FileManagerExtension.swift */,
				51F4AFBE24B26665006144D5 /* NSAlertExtension.swift */,
				513515D1259354BB001E4533 /* NSImageExtensions.swift */,
				51BC150525BE13C400F1CDC9 /* NSViewExtension.swift */,
				1A1EE9572551249C0056FECD /* NumberFormatterExtension.swift */,
				1ACA0B6525BEBE18002FA618 /* PopupButtonExtensions.swift */,
				51C8597B24C8A31400A8C7C4 /* StringExtension.swift */,
				1A4CB03325923C4B00EDF804 /* StringRegexExtension.swift */,
				51C4626C254ED02500F63E70 /* UserDefaultsExtension.swift */,
				51384AC12903461000FEC501 /* NSDictionaryExtension.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		583CE39511722B70008F148E /* File Compression */ = {
			isa = PBXGroup;
			children = (
				5885CF48116A63B200A85ACB /* SPFileHandle.h */,
				5885CF49116A63B200A85ACB /* SPFileHandle.m */,
			);
			name = "File Compression";
			path = FileCompression;
			sourceTree = "<group>";
		};
		584D876115140D3500F24774 /* Products */ = {
			isa = PBXGroup;
			children = (
				584D876815140D3500F24774 /* SPMySQL.framework */,
				29B70BD61C805B2A00D1BE0C /* SPMySQL Unit Tests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		58B909A111C3B8EC000826E5 /* Localization */ = {
			isa = PBXGroup;
			children = (
				58B9095B11C3A3EC000826E5 /* xibLocalizationPostprocessor.m */,
			);
			path = Localization;
			sourceTree = "<group>";
		};
		58D1006813A57FAE0092E019 /* Localization Strings Files */ = {
			isa = PBXGroup;
			children = (
			);
			name = "Localization Strings Files";
			sourceTree = "<group>";
		};
		58DA884E103E1597000B98DF /* Debugging & Support */ = {
			isa = PBXGroup;
			children = (
				50E217B418174280009D3580 /* SPFavoriteColorSupport.h */,
				50E217B518174280009D3580 /* SPFavoriteColorSupport.m */,
				58DA8861103E15B5000B98DF /* SPLogger.h */,
				58DA8862103E15B5000B98DF /* SPLogger.m */,
			);
			name = "Debugging & Support";
			path = DebuggingSupport;
			sourceTree = "<group>";
		};
		58FEF15E0F23D60A00518E8E /* Parsing */ = {
			isa = PBXGroup;
			children = (
				58FEF16B0F23D66600518E8E /* SPSQLParser.h */,
				58FEF16C0F23D66600518E8E /* SPSQLParser.m */,
				5822D3071061833C00CE2157 /* SPCSVParser.h */,
				5822D3081061833C00CE2157 /* SPCSVParser.m */,
				179F15040F7C433C00579954 /* SPEditorTokens.h */,
				179F15050F7C433C00579954 /* SPEditorTokens.l */,
				BCD0AD4A0FBBFC480066EA5C /* SPSQLTokenizer.h */,
				BCD0AD480FBBFC340066EA5C /* SPSQLTokenizer.l */,
				1755A25C16B33BEA00B35787 /* SPSyntaxParser.h */,
				50D3C3501A77135F00B5429C /* SPParserUtils.c */,
				50D3C3511A77135F00B5429C /* SPParserUtils.h */,
				503B02C81AE82C5E0060CAB1 /* SPTableFilterParser.h */,
				503B02C91AE82C5E0060CAB1 /* SPTableFilterParser.m */,
				73F70A941E4E547500636550 /* SPJSONFormatter.h */,
				73F70A951E4E547500636550 /* SPJSONFormatter.m */,
			);
			path = Parsing;
			sourceTree = "<group>";
		};
		96CA8F922783A3E40061C2D1 /* Fonts */ = {
			isa = PBXGroup;
			children = (
				96CA8F9B2783A8FE0061C2D1 /* Menlo.ttc */,
			);
			name = Fonts;
			sourceTree = "<group>";
		};
		96D494B8249C07940092F335 /* SSH Helpers */ = {
			isa = PBXGroup;
			children = (
				964908C4249A77CF0052FC4A /* ssh_config */,
			);
			name = "SSH Helpers";
			sourceTree = "<group>";
		};
		B57747D60F7A8990003B34F9 /* Category Additions */ = {
			isa = PBXGroup;
			children = (
				175EC64C12733CDF009A7C0F /* SPCategoryAdditions.h */,
				B52460D30F8EF92300171639 /* SPArrayAdditions.h */,
				B52460D40F8EF92300171639 /* SPArrayAdditions.m */,
				1789343A0F30C1DD0097539A /* SPStringAdditions.h */,
				1789343B0F30C1DD0097539A /* SPStringAdditions.m */,
				584D878915140FEB00F24774 /* SPObjectAdditions.h */,
				584D878A15140FEB00F24774 /* SPObjectAdditions.m */,
				B52460D50F8EF92300171639 /* SPTextViewAdditions.h */,
				B52460D60F8EF92300171639 /* SPTextViewAdditions.m */,
				5843E245162B555B00EAA6D1 /* SPThreadAdditions.h */,
				5843E246162B555B00EAA6D1 /* SPThreadAdditions.m */,
				B57747D70F7A8990003B34F9 /* SPWindowAdditions.h */,
				B57747D80F7A8990003B34F9 /* SPWindowAdditions.m */,
				BC2C16D20FEBEDF10003993B /* SPDataAdditions.h */,
				BC2C16D30FEBEDF10003993B /* SPDataAdditions.m */,
				58DF9F3115AB26C2003B4330 /* SPDateAdditions.h */,
				58DF9F3215AB26C2003B4330 /* SPDateAdditions.m */,
				582A01E7107C0C170027D42B /* SPNotLoaded.h */,
				582A01E8107C0C170027D42B /* SPNotLoaded.m */,
				5870868210FA3E9C00D58E1C /* SPDataStorage.h */,
				5870868310FA3E9C00D58E1C /* SPDataStorage.m */,
				589582131154F8F400EDCC28 /* SPMainThreadTrampoline.h */,
				589582141154F8F400EDCC28 /* SPMainThreadTrampoline.m */,
				BC85F5CE12193B7D00E255B5 /* SPColorAdditions.h */,
				BC85F5CF12193B7D00E255B5 /* SPColorAdditions.m */,
				BC32F240121D66260067305E /* SPFileManagerAdditions.h */,
				BC32F241121D66260067305E /* SPFileManagerAdditions.m */,
				1798F19615501838004B0AB8 /* SPMutableArrayAdditions.h */,
				1798F19715501838004B0AB8 /* SPMutableArrayAdditions.m */,
				584D899B15162CBE00F24774 /* SPDataBase64EncodingAdditions.h */,
				584D899C15162CBE00F24774 /* SPDataBase64EncodingAdditions.m */,
				500DA4BA1BF0CD57000773FE /* SPScreenAdditions.h */,
				500DA4BB1BF0CD57000773FE /* SPScreenAdditions.m */,
				1A071B8B254D983700246912 /* SPNSMutableDictionaryAdditions.h */,
				1A071B8C254D983700246912 /* SPNSMutableDictionaryAdditions.m */,
				1A8B53552584520800526DED /* SPURLAdditions.h */,
				1A8B53562584520800526DED /* SPURLAdditions.m */,
				1AF0DA7D259F5D3100961974 /* SPPointerArrayAdditions.h */,
				1AF0DA7E259F5D8C00961974 /* SPPointerArrayAdditions.m */,
				1A11E51525A36C62001CB721 /* HyperlinkTextField.swift */,
				1A31FE3525F2132F000DD1D1 /* SPTaskAdditions.m */,
				1A0751E725EAF83300FFDF6B /* SPTaskAdditions.h */,
			);
			name = "Category Additions";
			path = CategoryAdditions;
			sourceTree = "<group>";
		};
		FD2056032C3A7E64008DD271 /* Formatters */ = {
			isa = PBXGroup;
			children = (
				FD2056042C3A7E90008DD271 /* SABaseFormatter.swift */,
				FD18C8982C3C9B2F002A5D57 /* SAUuidFormatter.swift */,
			);
			path = Formatters;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		380F4ED80FC0B50500B0BFD7 /* Unit Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 380F4EDE0FC0B50600B0BFD7 /* Build configuration list for PBXNativeTarget "Unit Tests" */;
			buildPhases = (
				17E20DEF12D6602F007F75A6 /* Copy Frameworks */,
				380F4ED50FC0B50500B0BFD7 /* Sources */,
				17E20DFF12D6609E007F75A6 /* Frameworks */,
				1AB068A724A355C200E2AAC2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				518402D924A3FF4F004693B0 /* PBXTargetDependency */,
			);
			name = "Unit Tests";
			packageProductDependencies = (
				513C8CE02BBC4132001CCE3A /* OCMock */,
			);
			productName = "Unit Tests";
			productReference = 380F4ED90FC0B50500B0BFD7 /* Unit Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		58B9096011C3A42B000826E5 /* xibLocalizationPostprocessor */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 58B9096A11C3A431000826E5 /* Build configuration list for PBXNativeTarget "xibLocalizationPostprocessor" */;
			buildPhases = (
				58B9095E11C3A42B000826E5 /* Sources */,
				9D68E8A3FC5FD471945E3BA0 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = xibLocalizationPostprocessor;
			productName = xibLocalizationPostprocessor;
			productReference = 58B9096111C3A42B000826E5 /* xibLocalizationPostprocessor */;
			productType = "com.apple.product-type.tool";
		};
		58CDB3350FCE13C900F8ACA3 /* SequelAceTunnelAssistant */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 58CDB33F0FCE13E300F8ACA3 /* Build configuration list for PBXNativeTarget "SequelAceTunnelAssistant" */;
			buildPhases = (
				58CDB3330FCE13C900F8ACA3 /* Sources */,
				58CDB3340FCE13C900F8ACA3 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				17D41AB22171157B00B1888D /* PBXTargetDependency */,
			);
			name = SequelAceTunnelAssistant;
			productName = TunnelPassphraseRequester;
			productReference = 58CDB3360FCE13C900F8ACA3 /* SequelAceTunnelAssistant */;
			productType = "com.apple.product-type.tool";
		};
		8D15AC270486D014006FF6A4 /* Sequel Ace */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = C05733C708A9546B00998B17 /* Build configuration list for PBXNativeTarget "Sequel Ace" */;
			buildPhases = (
				8D15AC300486D014006FF6A4 /* Sources */,
				8D15AC330486D014006FF6A4 /* Frameworks */,
				8D15AC2B0486D014006FF6A4 /* Resources */,
				96B0A162249416B1007BB270 /* Copy SequelAceTunnelAssistant and sign */,
				4DECC4940EC2B447008D359E /* Copy Frameworks */,
				9691789224A562330012ED42 /* Copy Default Themes and Default Bundles */,
				96CA8FA02783AA560061C2D1 /* Copy Fonts */,
				1A62C63425BE08F400DF903B /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
				58CDB34B0FCE144000F8ACA3 /* PBXTargetDependency */,
				58B9097011C3A462000826E5 /* PBXTargetDependency */,
			);
			name = "Sequel Ace";
			packageProductDependencies = (
				51D9527525AE2B5300574BEB /* FMDB */,
				515D303E25BD7DE60021CF1E /* AppCenterCrashes */,
				515D304025BD7DE60021CF1E /* AppCenterAnalytics */,
				51BC150025BE138700F1CDC9 /* SnapKit */,
				1A89556E25D6C8880060CE72 /* Alamofire */,
			);
			productInstallPath = "$(HOME)/Applications";
			productName = "sequel-pro";
			productReference = 8D15AC370486D014006FF6A4 /* Sequel Ace.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		2A37F4A9FDCFA73011CA2CEA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1420;
				ORGANIZATIONNAME = "Sequel-Ace";
				TargetAttributes = {
					8D15AC270486D014006FF6A4 = {
						LastSwiftMigration = 1200;
					};
				};
			};
			buildConfigurationList = C05733CB08A9546B00998B17 /* Build configuration list for PBXProject "sequel-ace" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 1;
			knownRegions = (
				en,
				es,
				Base,
				"zh-Hans",
				"zh-Hant",
				de,
				"pt-BR",
				pt,
				ja,
				vi,
				ru,
				tr,
				fr,
				it,
				ar,
				cs,
				eo,
			);
			mainGroup = 2A37F4AAFDCFA73011CA2CEA /* sequel-pro */;
			packageReferences = (
				51D9527425AE2B5300574BEB /* XCRemoteSwiftPackageReference "fmdb" */,
				515D303D25BD7DE60021CF1E /* XCRemoteSwiftPackageReference "appcenter-sdk-apple" */,
				51BC14FF25BE138700F1CDC9 /* XCRemoteSwiftPackageReference "SnapKit" */,
				1A89556D25D6C8880060CE72 /* XCRemoteSwiftPackageReference "Alamofire" */,
				513C8CDF2BBC4132001CCE3A /* XCRemoteSwiftPackageReference "ocmock" */,
			);
			productRefGroup = 19C28FB0FE9D524F11CA2CBB /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 17E5954F14F304000054EE08 /* Products */;
					ProjectRef = 17E5954E14F304000054EE08 /* QueryKit.xcodeproj */;
				},
				{
					ProductGroup = 584D876115140D3500F24774 /* Products */;
					ProjectRef = 584D876015140D3500F24774 /* SPMySQLFramework.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				8D15AC270486D014006FF6A4 /* Sequel Ace */,
				380F4ED80FC0B50500B0BFD7 /* Unit Tests */,
				58CDB3350FCE13C900F8ACA3 /* SequelAceTunnelAssistant */,
				58B9096011C3A42B000826E5 /* xibLocalizationPostprocessor */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		17E5955314F304000054EE08 /* QueryKit.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = QueryKit.framework;
			remoteRef = 17E5955214F304000054EE08 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		17E596A214F307CE0054EE08 /* Tests.xctest */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.cfbundle;
			path = Tests.xctest;
			remoteRef = 17E596A114F307CE0054EE08 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		29B70BD61C805B2A00D1BE0C /* SPMySQL Unit Tests.xctest */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.cfbundle;
			path = "SPMySQL Unit Tests.xctest";
			remoteRef = 29B70BD51C805B2A00D1BE0C /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		584D876815140D3500F24774 /* SPMySQL.framework */ = {
			isa = PBXReferenceProxy;
			fileType = wrapper.framework;
			path = SPMySQL.framework;
			remoteRef = 584D876715140D3500F24774 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		1AB068A724A355C200E2AAC2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1AB068A824A355CC00E2AAC2 /* client-cert-crlf.pem in Resources */,
				1AB068A924A355CC00E2AAC2 /* client-cert-bad-end.pem in Resources */,
				1A445DAA25BACBE5004E9A77 /* naughty_strings.txt in Resources */,
				1AB068AA24A355CC00E2AAC2 /* client-cert-lf.pem in Resources */,
				1AB068AB24A355CC00E2AAC2 /* client-cert-cr.pem in Resources */,
				1AB068AC24A355CC00E2AAC2 /* client-cert-bad-start.pem in Resources */,
				1AB068AD24A355CC00E2AAC2 /* client-key-bad-end.pem in Resources */,
				1AB068AE24A355CC00E2AAC2 /* client-key-cr.pem in Resources */,
				1AB068AF24A355CC00E2AAC2 /* client-key-bad-start.pem in Resources */,
				1AB068B024A355CC00E2AAC2 /* client-key-lf.pem in Resources */,
				1AB068B124A355CC00E2AAC2 /* client-key-crlf.pem in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D15AC2B0486D014006FF6A4 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				51CD0BF8258D06D8009E2484 /* DatabaseProcessList.xib in Resources */,
				17E641D10EF02036001BC333 /* grabber-horizontal.png in Resources */,
				17E641D20EF02036001BC333 /* grabber-vertical.png in Resources */,
				51CD0BE5258D06D8009E2484 /* Navigator.xib in Resources */,
				17E641F20EF02036001BC333 /* toolbar-switch-to-structure.png in Resources */,
				17E641F30EF02036001BC333 /* toolbar-switch-to-table-info.png in Resources */,
				1A6447992588CD9200927DB3 /* Credits.rtf in Resources */,
				8831EFC9224016E000D10172 /* button_edit_mode_selectedTemplate.pdf in Resources */,
				17E641FC0EF02088001BC333 /* sequel-pro.scriptSuite in Resources */,
				17E641FD0EF02088001BC333 /* sequel-pro.scriptTerminology in Resources */,
				51CD0BF9258D06D8009E2484 /* BundleHTMLOutput.xib in Resources */,
				51CD0BDC258D06D8009E2484 /* PrintAccessory.xib in Resources */,
				51CD0BEC258D06D8009E2484 /* ProgressIndicatorLayer.xib in Resources */,
				B5E2C5FA0F2353B5007446E0 /* table-property.png in Resources */,
				51CD0BDD258D06D8009E2484 /* DataMigrationDialog.xib in Resources */,
				B57748420F7A8B57003B34F9 /* database.png in Resources */,
				51CD0BED258D06D8009E2484 /* SaveSPFAccessory.xib in Resources */,
				B58731280F838C9E00087794 /* PreferenceDefaults.plist in Resources */,
				51CD0BF4258D06D8009E2484 /* SSHQuestionDialog.xib in Resources */,
				BC2C8E220FA8C2DB008468C7 /* SPMySQLHelpTemplate.html in Resources */,
				384582C40FB95FF800DDACB6 /* func-small.png in Resources */,
				384582C70FB9603600DDACB6 /* proc-small.png in Resources */,
				51CD0BEE258D06D8009E2484 /* UserManagerView.xib in Resources */,
				8831EFCE2240175400D10172 /* button_paginationTemplate.pdf in Resources */,
				177E792E0FCB54EC00E9E122 /* database-small.png in Resources */,
				8831EFC22240166D00D10172 /* button_select_noneTemplate.pdf in Resources */,
				51CD0BEB258D06D8009E2484 /* ConnectionErrorDialog.xib in Resources */,
				1A6D28FE25DD8018007509F1 /* ProgressWindowController.storyboard in Resources */,
				51CD0BF1258D06D8009E2484 /* ContentFilterManager.xib in Resources */,
				177E792F0FCB54EC00E9E122 /* dummy-small.png in Resources */,
				51CD0BE3258D06D8009E2484 /* DBView.xib in Resources */,
				B54F25E60FD909C400E2CF36 /* toolbar-switch-to-table-relations.png in Resources */,
				8831EFB0224013AF00D10172 /* button_refreshTemplate.pdf in Resources */,
				3E242D4F20FEB44D0015470D /* button_bar_spacer_dark.png in Resources */,
				588B2CC80FE5641E00EC5FC0 /* ssh-connected.png in Resources */,
				588B2CC90FE5641E00EC5FC0 /* ssh-connecting.png in Resources */,
				588B2CCA0FE5641E00EC5FC0 /* ssh-disconnected.png in Resources */,
				51CD0BDF258D06D8009E2484 /* FilterTableWindow.xib in Resources */,
				58D2E22E101222870063EF1D /* link-arrow-clicked.png in Resources */,
				58D2E22F101222870063EF1D /* link-arrow-highlighted-clicked.png in Resources */,
				58D2E230101222870063EF1D /* link-arrow.png in Resources */,
				581068B61015411B0068C6E2 /* link-arrow-highlighted.png in Resources */,
				1A6447912588CD8B00927DB3 /* License.rtf in Resources */,
				8831EFC7224016E000D10172 /* button_filterTemplate.pdf in Resources */,
				8831EFAA2240128700D10172 /* button_removeTemplate.pdf in Resources */,
				8831EFB92240154A00D10172 /* button_leftTemplate.pdf in Resources */,
				8831EFC8224016E000D10172 /* button_filter_activeTemplate.pdf in Resources */,
				BC962D661144EACA006170BD /* CompletionTokens.plist in Resources */,
				51CD0BF6258D06D8009E2484 /* ConnectionView.xib in Resources */,
				17E0937E114AE154007FC1B4 /* SPTableInfoPrintTemplate.html in Resources */,
				B51D6B9E114C310C0074704E /* toolbar-switch-to-table-triggers.png in Resources */,
				8831EFBA2240154A00D10172 /* button_rightTemplate.pdf in Resources */,
				51CD0BE0258D06D8009E2484 /* FieldEditorSheet.xib in Resources */,
				17DD52B7115071D0007D8950 /* SPPrintTemplate.html in Resources */,
				1A2711E82539E2BB0066ED58 /* local-connection.html in Resources */,
				8831EFCD2240175400D10172 /* button_actionTemplate.pdf in Resources */,
				17DD52C6115074CB007D8950 /* Localizable.strings in Resources */,
				58E205FC1234FE4F00A97059 /* KeyTemplate.pdf in Resources */,
				8831EFC12240166D00D10172 /* button_select_allTemplate.pdf in Resources */,
				51CD0BEF258D06D8009E2484 /* ExportDialog.xib in Resources */,
				51F41FBA25F56A5900594BA5 /* MainWindow.xib in Resources */,
				BC09D7DE12A786FB0030DB64 /* cancel-clicked-highlighted.png in Resources */,
				8831EFAC2240131500D10172 /* button_editTemplate.pdf in Resources */,
				BC09D7DF12A786FB0030DB64 /* cancel-clicked.png in Resources */,
				BC09D7E012A786FB0030DB64 /* cancel-highlighted.png in Resources */,
				BC09D7E112A786FB0030DB64 /* cancel-hovered-highlighted.png in Resources */,
				BC09D7E212A786FB0030DB64 /* cancel-hovered.png in Resources */,
				BC09D7E312A786FB0030DB64 /* cancel.png in Resources */,
				58F48AA3161D03C6008536A1 /* quick-connect-icon.pdf in Resources */,
				58F48B2E161D08C0008536A1 /* quick-connect-icon-white.pdf in Resources */,
				5843DA6C161FA35600EAA6D1 /* key-icon-alternate.png in Resources */,
				51CD0BF0258D06D8009E2484 /* BundleEditor.xib in Resources */,
				583A278E23F06F1000FBE97B /* Colors.xcassets in Resources */,
				5843DA6D161FA35600EAA6D1 /* <EMAIL> in Resources */,
				5843DA6E161FA35600EAA6D1 /* key-icon.png in Resources */,
				51CD0BF3258D06D8009E2484 /* ContentPaginationView.xib in Resources */,
				5843DA6F161FA35600EAA6D1 /* <EMAIL> in Resources */,
				51CD0BF5258D06D8009E2484 /* QueryFavoriteManager.xib in Resources */,
				8831EFBE2240159E00D10172 /* button_pane_showTemplate.pdf in Resources */,
				51CD0BE9258D06D8009E2484 /* Console.xib in Resources */,
				8831EFB22240143600D10172 /* button_bar_handleTemplate.pdf in Resources */,
				C9F92710162D38D70051CB2E /* <EMAIL> in Resources */,
				C9F92712162D39E60051CB2E /* toolbar-switch-to-browse.png in Resources */,
				51CD0BE2258D06D8009E2484 /* IndexesView.xib in Resources */,
				C9F92714162D39FE0051CB2E /* <EMAIL> in Resources */,
				51CD0BE8258D06D8009E2484 /* MainMenu.xib in Resources */,
				51A709392565B99F001F1D2F /* Images.xcassets in Resources */,
				51CD0BE7258D06D8009E2484 /* ImportAccessory.xib in Resources */,
				C9AD7C781676138000234EEE /* <EMAIL> in Resources */,
				C9AD7C7B1676158C00234EEE /* toolbar-switch-to-sql.png in Resources */,
				C9AD7C7C1676158C00234EEE /* <EMAIL> in Resources */,
				8831EFB4224014C700D10172 /* button_clearTemplate.pdf in Resources */,
				C9C994411678A439001F5DA8 /* button_bar_spacer.png in Resources */,
				8831EFAE2240135400D10172 /* button_duplicateTemplate.pdf in Resources */,
				C9C994491678B3E6001F5DA8 /* table-small-square.png in Resources */,
				C9C9944A1678B3E6001F5DA8 /* <EMAIL> in Resources */,
				964908C8249A77D00052FC4A /* ssh_config in Resources */,
				8831EFCA224016E000D10172 /* button_edit_modeTemplate.pdf in Resources */,
				C9C9944D1678BCFA001F5DA8 /* table-small.png in Resources */,
				C9C9944E1678BCFA001F5DA8 /* <EMAIL> in Resources */,
				582E939D168296F3003459FD /* <EMAIL> in Resources */,
				582E939E168296F3003459FD /* <EMAIL> in Resources */,
				3E242D5020FEB44D0015470D /* <EMAIL> in Resources */,
				51CD0BF7258D06D8009E2484 /* AboutPanel.xib in Resources */,
				582E939F168296F3003459FD /* <EMAIL> in Resources */,
				582E93A0168296F3003459FD /* <EMAIL> in Resources */,
				51CD0BDE258D06D8009E2484 /* Preferences.xib in Resources */,
				8831EFB62240150A00D10172 /* button_add_folderTemplate.pdf in Resources */,
				582E940E1682A2AD003459FD /* <EMAIL> in Resources */,
				50F530521ABCF66B002F2C1A /* resetTemplate.pdf in Resources */,
				51CD0BE1258D06D8009E2484 /* GotoDatabaseDialog.xib in Resources */,
				8831EFBD2240159E00D10172 /* button_pane_hideTemplate.pdf in Resources */,
				51CD0BE4258D06D8009E2484 /* HelpViewer.xib in Resources */,
				517412382573E8F900EB6935 /* EditorQuickLookTypes.plist in Resources */,
				8831EFA8224011B700D10172 /* button_addTemplate.pdf in Resources */,
				96CA8F9C2783A9010061C2D1 /* Menlo.ttc in Resources */,
				582E942E1683658A003459FD /* clearconsole.png in Resources */,
				582E944A168374C1003459FD /* field-small-square.png in Resources */,
				582E944C16837986003459FD /* hideconsole.png in Resources */,
				582E945016837AA9003459FD /* network-small.png in Resources */,
				582E947016837DB2003459FD /* showconsole.png in Resources */,
				582E9483168380D6003459FD /* sync_arrows_01.png in Resources */,
				582E9484168380D6003459FD /* sync_arrows_02.png in Resources */,
				582E9485168380D6003459FD /* sync_arrows_03.png in Resources */,
				582E9486168380D6003459FD /* sync_arrows_04.png in Resources */,
				51CD0BF2258D06D8009E2484 /* EncodingPopupView.xib in Resources */,
				51CD0BEA258D06D8009E2484 /* DatabaseServerVariables.xib in Resources */,
				582E9487168380D6003459FD /* sync_arrows_05.png in Resources */,
				582E9488168380D6003459FD /* sync_arrows_06.png in Resources */,
				582E948F168383F0003459FD /* table-view-small-square.png in Resources */,
				582E9490168383F0003459FD /* table-view-small.png in Resources */,
				582E94A816839AD5003459FD /* toolbar-preferences-autoupdate.png in Resources */,
				582E94AA16839AEF003459FD /* toolbar-preferences-general.png in Resources */,
				582E94AE16839C4A003459FD /* toolbar-preferences-notifications.png in Resources */,
				582E94C716839D83003459FD /* toolbar-preferences-tables.png in Resources */,
				582E94F816839E83003459FD /* toolbar-preferences-network.png in Resources */,
				517E4512257A954000ED333B /* ContentFilters.plist in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		1A62C63425BE08F400DF903B /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "if [ \"${CONFIGURATION}\" = \"Release\" ] || [ \"${CONFIGURATION}\" = \"Beta\" ] || [ \"${CONFIGURATION}\" = \"Distribution\" ]; then\n    echo \"Initializing upload symbols for \\\"${CONFIGURATION}\\\" build\"\n    ${PROJECT_DIR}/Scripts/upload_symbols.sh \"Sequel-Ace/Sequel-Ace\" \nelse\n    echo \"Not uploading symbols for \\\"${CONFIGURATION}\\\" build\"\n    return 0\nfi\n\n\n#!/usr/bin/env bash\n# shellcheck shell=bash\n\n# MS AppCenter upload-symbols wrapper for Xcode\n# By James Stout 25/01/2021\n\n# SEE: https://docs.microsoft.com/en-us/appcenter/diagnostics/iOS-symbolication#app-center-api\n# AND: https://github.com/microsoft/appcenter-cli#commands\n\necho \"[SA] Starting Upload Symbols Script\"\n\nSCRIPT_DIR=\"${PROJECT_DIR}\"\nAPP=\"Sequel-Ace/Sequel-Ace\"\n\ncd \"$SCRIPT_DIR\" || return 1\n\nme=$(basename \"$0\")\n\nexport SCRIPT_DIR\n\n# Common functions\nfunction sa_log () {\n    echo \"[SA] $1\";\n}\n\nfunction sa_fail () {\n    sa_log \"$1\";\n    exit 0; #TODO: change to error\n}\n\nfunction sa_usage ()\n{\n    sa_log \"You must invoke the script as follows:\"\n    echo \"    sh $me \\\"your/appname\\\"\"\n}\n\nfunction dir_exists() {\n    if [ -d \"$1\" ]; then\n        return 0\n    fi\n    return 1\n}\n\nfunction file_exists() {\n    if [ -e \"$1\" ]; then\n        return 0\n    fi\n    return 1\n}\n\nsafe_cd() {\n    cd \"$@\" >/dev/null || sa_fail \"Error: failed to cd to $*!\"\n}\n\nfunction is_variable\n{\n    compgen -A variable | grep ^\"${1}\"$ > /dev/null\n}\n\nfunction var_exists() {\n    if is_variable \"${1}\"\n    then\n        if [ -n \"$1\" ]; then\n            return 0\n        fi\n        sa_log \"ret here else 1\"\n        return 1\n    else\n        sa_log \"ret here else 2\"\n        return 1\n    fi\n}\n\n# try to add a path that contains appcenter\nfunction set_path() {\n    paths_to_add=(\n        # Private \"bin\"\n        \"$HOME/bin\"\n        # Homebrew (and various other distributions and local installations)\n        /usr/local/{,s}bin\n        /opt/homebrew/{,s}bin\n        \"$HOME/Homebrew\"\n        \"$HOME/.nvm/versions/node/\"\n        # XCode Cloud Paths\n        /Users/<USER>/bin\n        /Users/<USER>/Homebrew\n        /Users/<USER>/Homebrew/bin\n        \"/Users/<USER>/.npm-packages/bin\"\n        /Library/Apple/usr/{,s}bin\n        # System\n        /{,s}bin\n        /usr/{,s}bin\n        /usr/local/lib/node_modules\n        \"$HOME/.npm-packages/bin\"\n    );\n\n    # Create an array of directories currently in the PATH variable.\n    oldIFS=\"$IFS\";\n    IFS=:;\n    set -- $PATH;\n    IFS=\"$oldIFS\";\n    unset oldIFS;\n    old_paths=(\"$@\");\n\n    # Construct an array of the directories in the new PATH, preferring our paths\n    # to the predefined ones.\n    new_paths=();\n    for path_to_add in \"${paths_to_add[@]}\"; do\n        [ -d \"$path_to_add\" ] && new_paths+=(\"$path_to_add\");\n    done;\n    for old_path in \"${old_paths[@]}\"; do\n        [ -d \"$old_path\" ] || continue;\n        for new_path in \"${new_paths[@]}\"; do\n            [ \"${old_path%%/}\" = \"${new_path%%/}\" ] && continue 2;\n        done;\n        new_paths+=(\"$old_path\");\n    done;\n\n    # Now implode everything into the new PATH variable.\n    printf -v PATH \"%s:\" \"${new_paths[@]}\";\n    export PATH=\"${PATH%:}\";\n    unset {old,new}_path{,s} path{s,}_to_add;\n\n    # remove dupes\n    if hash perl 2> /dev/null; then\n        PATH=$(perl -e 'print join \":\", grep {!$h{$_}++} split \":\", $ENV{PATH}')\n    fi\n    export PATH\n\n    sa_log \"PATH = ${PATH}\"\n}\n\n\nfunction check_return_code () {\n\ncase \"$1\" in\n    0)\n    sa_log \"Symbols uploaded succesully.\"\n    ;;\n    1)\n    sa_fail \"Error: Unknown Error\"\n    ;;\n    2)\n    sa_fail \"Error: Invalid Options\"\n    ;;\n    3)\n    sa_fail \"Error: App File Not Found\"\n    ;;\n    [4-9])\n    sa_fail \"Error: Misc errors\"\n    ;;\n    10)\n    sa_fail \"Error: dSym Not Found Or Not Directory\"\n    ;;\n    11)\n    sa_fail \"Error: dSym Directory Wrong Extension\"\n    ;;\n    12)\n    sa_fail \"Error: dSym Contains More than One Dwarf\"\n    ;;\n    13)\n    sa_fail \"Error: Test Chunking Failed\"\n    ;;\n    14)\n    sa_fail \"Error: Upload Negotiation Failed\"\n    ;;\n    15)\n    sa_fail \"Error: Upload Failed\"\n    ;;\n    20)\n    sa_fail \"Error: Incompatible Versions\"\n    ;;\n    25)\n    sa_fail \"Error: Cancelled\"\n    ;;\n    *)\n    sa_fail \"Error: Unknown Error\"\n    ;;\nesac\n\n}\n\nif ! dir_exists \"${SOURCE_ROOT}\"; then\n    sa_fail \"SOURCE_ROOT path ${SOURCE_ROOT} does not exist!\"\nfi\n\nFASTLANE_DIR=\"$SOURCE_ROOT/fastlane\"\nFASTLANE_ENV_FILE=\"$FASTLANE_DIR/.env\"\n\n# Pre-checks\nif dir_exists \"$FASTLANE_DIR\"; then\n    sa_log \"FASTLANE_DIR exists: $FASTLANE_DIR\"\nelse\n    sa_fail \"FASTLANE_DIR does NOT exist: $FASTLANE_DIR\"\nfi\n\n# source the env file, which should export a var containing the token\n# var is: MS_APP_CENTER\nif file_exists \"$FASTLANE_ENV_FILE\"; then\n    sa_log \"FASTLANE_ENV_FILE exists: $FASTLANE_ENV_FILE\"\n    # shellcheck source=/dev/null\n    source \"$FASTLANE_ENV_FILE\"\nfi\n\n# It's okay if the value is instead in an env though\nif [[ -n $APPCENTER_ACCESS_TOKEN ]]; then\n    sa_log \"APPCENTER_ACCESS_TOKEN exists in ENV! Using this value\"\n    MS_APP_CENTER=\"$APPCENTER_ACCESS_TOKEN\"\nfi\n\nif var_exists MS_APP_CENTER; then\n    sa_log \"MS_APP_CENTER var exists: ******************\"\nelse\n    sa_fail \"MS_APP_CENTER var does NOT exist: $MS_APP_CENTER. Define the var in $FASTLANE_ENV_FILE\"\nfi\n\nif [[ -z \"$APP\" ]]; then\n    sa_usage\n    sa_fail \"App name not specified!\"\nfi\n\nif [ ! \"${DWARF_DSYM_FOLDER_PATH}\" ] || [ ! \"${DWARF_DSYM_FILE_NAME}\" ]; then\n    sa_fail \"Xcode Environment Variables are missing!: DWARF_DSYM_FOLDER_PATH and/or DWARF_DSYM_FILE_NAME\"\nfi\n\nDSYM_PATH=\"${DWARF_DSYM_FOLDER_PATH}/${DWARF_DSYM_FILE_NAME}\";\n\nif ! dir_exists \"${DSYM_PATH}\"; then\n    sa_fail \"dSYM path ${DSYM_PATH} does not exist!\"\nfi\n\nsa_log \"APP = ${APP}\"\nsa_log \"DSYM_PATH = ${DSYM_PATH}\"\n\nset_path\n\nmethod=\"\"\n\nif hash appcenter 2> /dev/null; then\n    method=\"cli\"\nelse\n    method=\"curl\"\n    sa_fail \"curl method DOES NOT WORK! Install appcenter: npm install -g appcenter-cli\"\nfi\n\nsa_log \"method = ${method}\"\n\n# these are our expected dSYMs\n# might need some work...\ndeclare -a dSYMArray=(\n    \"SequelAceTunnelAssistant.dSYM\"\n    \"Sequel Ace.app.dSYM\"\n    \"Sequel Ace Beta.app.dSYM\"\n    \"SPMySQL.framework.dSYM\"\n    \"xibLocalizationPostprocessor.dSYM\"\n    \"QueryKit.framework.dSYM\"\n)\n\nsa_log \"DWARF_DSYM_FOLDER_PATH = ${DWARF_DSYM_FOLDER_PATH}\"\n\nsafe_cd \"$DWARF_DSYM_FOLDER_PATH\"\n\ndSYM_ARCHIVE_NAME=\"${APP#*\\/}_dSYMs.zip\"\n\nsa_log \"dSYM_ARCHIVE_NAME: ${dSYM_ARCHIVE_NAME}\"\n\nif file_exists \"${dSYM_ARCHIVE_NAME}\"; then\n    sa_log \"dSYM_ARCHIVE exists, removing\"\n    rm -f \"${dSYM_ARCHIVE_NAME}\"\nfi\n\nfor dSYM in \"${dSYMArray[@]}\";\ndo\n   if ! dir_exists \"${dSYM}\"; then\n        sa_log \"path ${dSYM} does not exist, just skip?\"\n    else\n        sa_log \"path ${dSYM} exists, adding to zip archive.\"\n        zip -X -q -r -9 \"${dSYM_ARCHIVE_NAME}\" \"${dSYM}\"\n    fi\ndone\n\nif [ \"$method\" == \"cli\" ]\nthen\n    appcenter crashes upload-symbols --app \"${APP}\" --symbol \"${dSYM_ARCHIVE_NAME}\" --token \"${MS_APP_CENTER}\"\n    # should error check here: https://docs.microsoft.com/en-us/appcenter/test-cloud/troubleshooting/cli-exit-codes\n    rc=\"$?\"\n    check_return_code \"${rc}\"\nelse\n    if ! hash jq &>/dev/null; then\n        sa_log \"You need to install jq for this to work - jq is like sed for JSON data\"\n        sa_log \"Run: brew install jq\"\n        sa_fail \"Or build from source: https://github.com/stedolan/jq\"\n    fi\n\n    # per: https://docs.microsoft.com/en-us/appcenter/diagnostics/iOS-symbolication#app-center-api\n\n    #construct filename\n    file_name=\"${APP#*\\/}\"\n    file_name=\"${file_name}-\"$(date -u +\"%Y-%m-%dT%H-%M-%SZ\")\n    sa_log \"file_name = ${file_name}\"\n\n    #construct url\n    # This call allocates space on our backend for your file and returns a symbol_upload_id and an upload_url property.\n    URL=\"https://api.appcenter.ms/v0.1/apps/${APP}/symbol_uploads\"\n\n    response=\"$(curl -s -X POST \"${URL}\" -H  \"accept: application/json\" \\\n        -H  \"X-API-Token: ${TOKEN}\" \\\n        -H  \"Content-Type: application/json\" \\\n        -d \"{  \\\"symbol_type\\\": \\\"Apple\\\", \\\"file_name\\\": \\\"${file_name}\\\"}\")\"\n\n    sa_log \"response = ${response}\"\n\n    # check for errors\n    error_msg=$(echo \"$response\" | jq -r '.message')\n\n    if [[ \"$error_msg\" != \"null\" ]]; then\n        sa_fail \"Request failed. error_msg = ${error_msg}\"\n    fi\n\n    symbol_upload_id=$(echo \"$response\" | jq -r '.symbol_upload_id')\n    upload_url=$(echo \"$response\" | jq -r '.upload_url')\n\n    if [[ \"$symbol_upload_id\" == \"null\" ]]; then\n        sa_fail \"Request failed. symbol_upload_id = ${symbol_upload_id}\"\n    fi\n    if [[ \"$upload_url\" == \"null\" ]]; then\n        sa_fail \"Request failed. upload_url = ${upload_url}\"\n    fi\n\n    sa_log \"symbol_upload_id = ${symbol_upload_id}\"\n    sa_log \"upload_url = ${upload_url}\"\n\n    # Using the upload_url property returned from the first step, make a PUT request with the header: \"x-ms-blob-type: BlockBlob\" and supply the location of your file on disk.\n    # This call uploads the file to our backend storage accounts\n    # -----------\n    #   HOWEVER\n    # -----------\n    # this doesn't work. Reported to MS\n    # Transfer-Encoding: chunked is set by the call to curl\n    #\n    # https://docs.microsoft.com/en-us/rest/api/storageservices/put-blob#request-headers-all-blob-types says:\n    # \"For a page blob or an append blob, the value of this header must be set to zero\"\n    #\n    # but this is the response:\n    #<?xml version=\"1.0\" encoding=\"utf-8\"?><Error><Code>InvalidHeaderValue</Code><Message>The value for one of the HTTP headers is not in the correct format.\n    # RequestId:1485bd45-701e-0025-284b-9eb210000000\n    # Time:2020-10-09T14:47:04.2689302Z</Message><HeaderName>Content-Length</HeaderName><HeaderValue>-1</HeaderValue></Error>\n    curl -X PUT \"${upload_url}\" -H 'Content-Length: 0' -H 'x-ms-blob-type: BlockBlob' --upload-file \"${DSYM_PATH}\"\n\n    # this works, but nothing is committed\n    # Make a PATCH request to the symbol_uploads API using the symbol_upload_id property returned from the first step.\n    # In the body of the request, specify whether you want to set the status of the upload to committed (successfully completed) the upload process,\n    # or aborted (unsuccessfully completed).\n\n    #construct url\n    URL=\"https://api.appcenter.ms/v0.1/apps/${APP}/symbol_uploads/${symbol_upload_id}\"\n\n    curl -X PATCH \"${URL}\" \\\n    -H 'accept: application/json' \\\n    -H  \"X-API-Token: ${TOKEN}\" \\\n    -H 'Content-Type: application/json' \\\n    -d '{ \"status\": \"committed\" }'\n\nfi\n\nexit 0;\n\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		380F4ED50FC0B50500B0BFD7 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A9498C125517191000BC793 /* DateExtension.swift in Sources */,
				1ADEA5A424BF1FFB00D2140B /* SPDateAdditions.m in Sources */,
				50837F771E50E007004FAE8A /* SPJSONFormatter.m in Sources */,
				505F568F1BCEE485007467DD /* SPFunctions.m in Sources */,
				FD8099A82C59DDF70084646F /* SAUuidFormatter.swift in Sources */,
				502D21F81BA50966000D4CE7 /* SPDataAdditions.m in Sources */,
				1A9498DE2551776D000BC793 /* DateFormatterExtension.swift in Sources */,
				51C6288C24D196E8006491E9 /* StringExtension.swift in Sources */,
				1A8B53A52584650700526DED /* SPURLAdditions.m in Sources */,
				502D21F61BA50710000D4CE7 /* SPDataAdditionsTests.m in Sources */,
				1A0FA3EE258B758B00486D52 /* SPArrayAdditions.m in Sources */,
				1A70BC9225EF7EE9004BB992 /* FileManagerExtension.swift in Sources */,
				1A1667112584D94800A9686E /* SPURLAdditionsTests.m in Sources */,
				503B02D21AE95E010060CAB1 /* SPConstants.m in Sources */,
				1A4152F125AF531000B17249 /* GeneralSwiftTests.swift in Sources */,
				503B02D11AE95DD40060CAB1 /* SPTableFilterParser.m in Sources */,
				FDCF55E52788278500D30655 /* TableSortHelper.swift in Sources */,
				519350302567D2FB001272B5 /* CollectionExtension.swift in Sources */,
				1A3FA7962495FC2D00B7291A /* SPMainThreadTrampoline.m in Sources */,
				51C8598024C8A6D700A8C7C4 /* SPStringAdditions.m in Sources */,
				1AF0DA8B259F657200961974 /* SPPointerArrayAdditions.m in Sources */,
				503B02CF1AE95C2C0060CAB1 /* SPTableFilterParserTest.m in Sources */,
				50EA92681AB23EFC008D3C4F /* SPTableCopy.m in Sources */,
				1A85CB8D2493BC4A00B57B93 /* SPSyncTests.m in Sources */,
				1ADEA5A324BF1C4800D2140B /* SPDateAdditionsTests.m in Sources */,
				507FF1621BBF0D5000104523 /* SPTableCopyTest.m in Sources */,
				1A2DD55125939B6400616E7E /* SPArrayAdditions.m in Sources */,
				1AF0DA84259F631000961974 /* SPPointerArrayAdditionsTests.m in Sources */,
				50837F741E50DCD4004FAE8A /* SPJSONFormatterTests.m in Sources */,
				1AB925922551550200063446 /* NumberFormatterExtension.swift in Sources */,
				50EA926A1AB246B8008D3C4F /* SPDatabaseActionTest.m in Sources */,
				FD8099A92C59DE1D0084646F /* SABaseFormatter.swift in Sources */,
				50EA92651AB23EC8008D3C4F /* SPDatabaseAction.m in Sources */,
				1A94997A25518549000BC793 /* DateComponentsFormatterExtension.swift in Sources */,
				50EA92641AB23EAD008D3C4F /* SPDatabaseCopy.m in Sources */,
				1AB068B424A3577C00E2AAC2 /* SPValidateKeyAndCertFiles.m in Sources */,
				50D3C35D1A77217800B5429C /* SPParserUtils.c in Sources */,
				380F4EF50FC0B68F00B0BFD7 /* SPStringAdditionsTests.m in Sources */,
				1A2DD55A25939BEE00616E7E /* SPTestingUtils.m in Sources */,
				1A5A83532545DA8B00EDC196 /* SPObjectAdditions.m in Sources */,
				1798F1C4155018E2004B0AB8 /* SPMutableArrayAdditionsTests.m in Sources */,
				FD8099A72C59DCFA0084646F /* SAUuidFormatterTests.swift in Sources */,
				1A4CB06B25926D7C00EDF804 /* StringRegexExtension.swift in Sources */,
				1AEA768425EF05D500AC4DA6 /* ByteCountFormatterExtension.swift in Sources */,
				17DB5F441555CA300046834B /* SPMutableArrayAdditions.m in Sources */,
				1717FA401558313A0065C036 /* RegexKitLite.m in Sources */,
				50D3C35C1A771C4C00B5429C /* SPParserUtilsTest.m in Sources */,
				1AE6C1CD25B07E9500880D73 /* SPFunctionsTests.m in Sources */,
				1A9A40D525875EC9009F0E71 /* NSMutableArray-MultipleSort.m in Sources */,
				8AEACED018AABDD8CBB42877 /* TableSortHelperTests.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58B9095E11C3A42B000826E5 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				58B9097B11C3A4A2000826E5 /* xibLocalizationPostprocessor.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		58CDB3330FCE13C900F8ACA3 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				17D41A8221700F8200B1888D /* SPFunctions.m in Sources */,
				1A4CB04F2592535300EDF804 /* StringRegexExtension.swift in Sources */,
				58CDB3410FCE141900F8ACA3 /* SequelAceTunnelAssistant.m in Sources */,
				58CDB3420FCE142500F8ACA3 /* SPKeychain.m in Sources */,
				584D88AA1515034200F24774 /* NSNotificationCenterThreadingAdditions.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8D15AC300486D014006FF6A4 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				11C211301180EC9A00758039 /* SPDatabaseRename.m in Sources */,
				17E641460EF01EB5001BC333 /* main.m in Sources */,
				1A19962A257A624200F5B0F1 /* BundleExtension.swift in Sources */,
				17E641560EF01EF6001BC333 /* SPCustomQuery.m in Sources */,
				17E641570EF01EF6001BC333 /* SPAppController.m in Sources */,
				507FF1121BBCC57600104523 /* SPFunctions.m in Sources */,
				5193502F2567D2FB001272B5 /* CollectionExtension.swift in Sources */,
				17E641590EF01EF6001BC333 /* SPTableContent.m in Sources */,
				17E6415A0EF01EF6001BC333 /* SPDatabaseDocument.m in Sources */,
				17E6415B0EF01EF6001BC333 /* SPDataImport.m in Sources */,
				17E6415C0EF01EF6001BC333 /* SPTableStructure.m in Sources */,
				1A8B582A25EEE15900DFC54A /* ByteCountFormatterExtension.swift in Sources */,
				1A071B8D254D983700246912 /* SPNSMutableDictionaryAdditions.m in Sources */,
				50A9F8B119EAD4B90053E571 /* SPGotoDatabaseController.m in Sources */,
				17E641640EF01F15001BC333 /* SPTableInfo.m in Sources */,
				17E641650EF01F15001BC333 /* SPTablesList.m in Sources */,
				17E6416C0EF01F37001BC333 /* ImageAndTextCell.m in Sources */,
				17E641750EF01F80001BC333 /* SPKeychain.m in Sources */,
				17E641830EF01FA8001BC333 /* SPImageView.m in Sources */,
				17E641840EF01FA8001BC333 /* SPTextView.m in Sources */,
				58FEF16D0F23D66600518E8E /* SPSQLParser.m in Sources */,
				1789343C0F30C1DD0097539A /* SPStringAdditions.m in Sources */,
				1A1EE9582551249C0056FECD /* NumberFormatterExtension.swift in Sources */,
				1A4CB03425923C4B00EDF804 /* StringRegexExtension.swift in Sources */,
				58FEF57E0F3B4E9700518E8E /* SPTableData.m in Sources */,
				58C56EF50F438E120035701E /* SPDataCellFormatter.m in Sources */,
				1A2711CF2539D9B10066ED58 /* SPReachability.m in Sources */,
				172A65110F7BED7A001E861A /* SPConsoleMessage.m in Sources */,
				6F0EA8ED272F33B700514FF1 /* SPBundleManagerAdditions.swift in Sources */,
				FD0E72EA2C38DEA1007EF348 /* SATableHeaderView.swift in Sources */,
				1A24B627258A2E9A00541E88 /* SecureBookmarkData.swift in Sources */,
				179F15060F7C433C00579954 /* SPEditorTokens.l in Sources */,
				B5E92F1C0F75B2E800012500 /* SPExportController.m in Sources */,
				B57747D40F7A8974003B34F9 /* SPPreferenceController.m in Sources */,
				B57747D90F7A8990003B34F9 /* SPWindowAdditions.m in Sources */,
				B57747DC0F7A89D0003B34F9 /* SPFavoriteTextFieldCell.m in Sources */,
				B52460D70F8EF92300171639 /* SPArrayAdditions.m in Sources */,
				B52460D80F8EF92300171639 /* SPTextViewAdditions.m in Sources */,
				6F0EA9242734ABE200514FF1 /* SABundleRunner.m in Sources */,
				296DC8B60F909194002A3258 /* MGTemplateEngine.m in Sources */,
				1A11E50425A327F1001CB721 /* SPPanelOptions.m in Sources */,
				296DC8B70F909194002A3258 /* RegexKitLite.m in Sources */,
				17B548631E81FFA600175D5A /* SPCreateDatabaseInfo.m in Sources */,
				296DC8B80F909194002A3258 /* ICUTemplateMatcher.m in Sources */,
				296DC8B90F909194002A3258 /* MGTemplateStandardMarkers.m in Sources */,
				296DC8BA0F909194002A3258 /* NSArray_DeepMutableCopy.m in Sources */,
				296DC8BB0F909194002A3258 /* NSDictionary_DeepMutableCopy.m in Sources */,
				296DC8BC0F909194002A3258 /* MGTemplateStandardFilters.m in Sources */,
				5841423F0F97E11000A34B47 /* NoodleLineNumberView.m in Sources */,
				BCD0AD490FBBFC340066EA5C /* SPSQLTokenizer.l in Sources */,
				1A89558725D6E15D0060CE72 /* GitHub.swift in Sources */,
				1A11E51625A36C62001CB721 /* HyperlinkTextField.swift in Sources */,
				387BBBA80FBCB6CB00B31746 /* SPTableRelations.m in Sources */,
				1740FABB0FC4372F00CF3699 /* SPDatabaseData.m in Sources */,
				17C058880FC9FC390077E9CF /* SPNarrowDownCompletion.m in Sources */,
				177E7A230FCB6A2E00E9E122 /* SPExtendedTableInfo.m in Sources */,
				73F70A961E4E547500636550 /* SPJSONFormatter.m in Sources */,
				58CDB3300FCE138D00F8ACA3 /* SPSSHTunnel.m in Sources */,
				1A70BC6B25EF439F004BB992 /* FileManagerExtension.swift in Sources */,
				29A1B7E50FD1293A000B88E8 /* SPPrintAccessory.m in Sources */,
				BC1847EA0FE6EC8400094BFB /* SPEditSheetTextView.m in Sources */,
				BC2C16D40FEBEDF10003993B /* SPDataAdditions.m in Sources */,
				5822C9B51000DB2400DCC3D6 /* SPConnectionController.m in Sources */,
				BC8C8532100E0A8000D7A129 /* SPTableView.m in Sources */,
				51BB391C25F82B060048CA69 /* SPAppController.swift in Sources */,
				BC9F0881100FCF2C00A80D32 /* SPFieldEditorController.m in Sources */,
				58D2E229101222670063EF1D /* SPTextAndLinkCell.m in Sources */,
				1A96E4B72588F34C0055F5F5 /* SecureBookmarkManager.swift in Sources */,
				BC05F1C5101241DF008A97F8 /* YRKSpinningProgressIndicator.m in Sources */,
				967D875B24B67B4300BAE934 /* SPAutosizingTextView.swift in Sources */,
				503B02CA1AE82C5E0060CAB1 /* SPTableFilterParser.m in Sources */,
				1A9F343F257B0DBE0062EC87 /* SPBundleManager.m in Sources */,
				4D90B79A101E0CDF00D116A1 /* SPUserManager.m in Sources */,
				FD18C8992C3C9B2F002A5D57 /* SAUuidFormatter.swift in Sources */,
				4D90B79E101E0CF200D116A1 /* SPUserManager.xcdatamodel in Sources */,
				4D90B79F101E0CF200D116A1 /* SPUserMO.m in Sources */,
				584192A1101E57BB0089807F /* NSMutableArray-MultipleSort.m in Sources */,
				589235321020C1230011DE00 /* SPHistoryController.m in Sources */,
				1A31FE3925F2132F000DD1D1 /* SPTaskAdditions.m in Sources */,
				BCA6271C1031B9D40047E5D5 /* SPTooltip.m in Sources */,
				58DA8863103E15B5000B98DF /* SPLogger.m in Sources */,
				BC01BCCF104024BE006BDEE7 /* SPEncodingPopupAccessory.m in Sources */,
				3876E15D1CC0BA0300D85154 /* SPTableFooterPopUpButtonCell.m in Sources */,
				51BC14F825BE135500F1CDC9 /* SPWindowController.swift in Sources */,
				173C4366104455E0001F3A30 /* SPQueryFavoriteManager.m in Sources */,
				FD6DFF872ADB40630057B713 /* SPTableHistoryManager.swift in Sources */,
				173C44D81044A6B0001F3A30 /* SPOutlineView.m in Sources */,
				500C1F921BFB5F9F0095DC7F /* SPPrivilegesMO.m in Sources */,
				17F5B1511048C4E400FC794F /* SPCSVExporter.m in Sources */,
				17F5B1541048C50D00FC794F /* SPExporter.m in Sources */,
				17F5B39C1049B96A00FC794F /* SPSQLExporter.m in Sources */,
				BC29C37F10501EFD00DD6C6E /* SPQueryController.m in Sources */,
				1ABC770125E3895300E8EE01 /* DispatchQueueExtension.swift in Sources */,
				5822D3091061833C00CE2157 /* SPCSVParser.m in Sources */,
				1AD785E725B749760007E153 /* OSLog.swift in Sources */,
				5132930C25F586A900D803AD /* NotificationToken.swift in Sources */,
				BC675A141072039C00C5ACD4 /* SPContentFilterManager.m in Sources */,
				17292443107AC41000B21980 /* SPXMLExporter.m in Sources */,
				582A01E9107C0C170027D42B /* SPNotLoaded.m in Sources */,
				173284EA1088FEDE0062E892 /* SPConstants.m in Sources */,
				171312CE109D23C700FB465F /* SPTableTextFieldCell.m in Sources */,
				174CE14210AB9281008F892B /* SPProcessListController.m in Sources */,
				517412302573E10C00EB6935 /* SPPrintUtility.m in Sources */,
				1792C13710AD75C800ABE758 /* SPServerVariablesController.m in Sources */,
				1A70BC6625EF4243004BB992 /* ReportExceptionApplication.m in Sources */,
				5089B0271BE714E300E226CD /* SPIdMenu.m in Sources */,
				17CC97F310B4ABE90034CD7A /* SPAboutController.m in Sources */,
				51C4626D254ED02500F63E70 /* UserDefaultsExtension.swift in Sources */,
				5870868410FA3E9C00D58E1C /* SPDataStorage.m in Sources */,
				29FA88231114619E00D1AF3D /* SPTableTriggers.m in Sources */,
				BCE0025D11173D2A009DA533 /* SPFieldMapperController.m in Sources */,
				BC2777A011514B940034DF6A /* SPNavigatorController.m in Sources */,
				589582151154F8F400EDCC28 /* SPMainThreadTrampoline.m in Sources */,
				BC4DF1981158FB280059FABD /* SPNavigatorOutlineView.m in Sources */,
				FD2056052C3A7E90008DD271 /* SABaseFormatter.swift in Sources */,
				5885CF4A116A63B200A85ACB /* SPFileHandle.m in Sources */,
				1198F5B31174EDD500670590 /* SPDatabaseCopy.m in Sources */,
				1141A389117BBFF200126A28 /* SPTableCopy.m in Sources */,
				D35577F52728C6CF002B3989 /* SPWindowTabAccessory.swift in Sources */,
				11B55BFE1189E3B2009EF465 /* SPDatabaseAction.m in Sources */,
				1AF5A261250AC401009885DF /* SPBracketHighlighter.m in Sources */,
				5806B76411A991EC00813A88 /* SPDocumentController.m in Sources */,
				173C837211AAD26E00B8B084 /* SPExportUtilities.m in Sources */,
				51F4AFBF24B26665006144D5 /* NSAlertExtension.swift in Sources */,
				173C837911AAD2AE00B8B084 /* SPDotExporter.m in Sources */,
				173C837A11AAD2AE00B8B084 /* SPHTMLExporter.m in Sources */,
				173C837B11AAD2AE00B8B084 /* SPPDFExporter.m in Sources */,
				50805B0D1BF2A068005F7A99 /* SPPopUpButtonCell.m in Sources */,
				1A94988E25516057000BC793 /* DateExtension.swift in Sources */,
				17A7773411C52D8E001E27B4 /* SPIndexesController.m in Sources */,
				1ACA0B6625BEBE18002FA618 /* PopupButtonExtensions.swift in Sources */,
				17F90E481210B42700274C98 /* SPExportFile.m in Sources */,
				BC85F5D012193B7D00E255B5 /* SPColorAdditions.m in Sources */,
				1AF5A262250AC401009885DF /* SPBrackets.m in Sources */,
				BC878A71121A836F00AE5066 /* SPColorWellCell.m in Sources */,
				BC398A2D121D526200BE3EF4 /* SPCopyTable.m in Sources */,
				1AB28D8125DBD3B500E62BF5 /* ProgressViewController.swift in Sources */,
				BC32F242121D66260067305E /* SPFileManagerAdditions.m in Sources */,
				17A20AC6124F9B110095CEFB /* SPServerSupport.m in Sources */,
				BC2898F3125F4488001B50E1 /* SPGeometryDataView.m in Sources */,
				17D38F701279E23A00672B13 /* SPTableFieldValidation.m in Sources */,
				17D390C8127B65AF00672B13 /* SPGeneralPreferencePane.m in Sources */,
				1A9D83A425514E740024B563 /* DateComponentsFormatterExtension.swift in Sources */,
				17D390CB127B6BF800672B13 /* SPPreferencesUpgrade.m in Sources */,
				51C8597C24C8A31400A8C7C4 /* StringExtension.swift in Sources */,
				1785E9F7127D8C7500F468C8 /* SPPreferencePane.m in Sources */,
				1785EA23127DAF3300F468C8 /* SPTablesPreferencePane.m in Sources */,
				1785EB60127DD5A800F468C8 /* SPNotificationsPreferencePane.m in Sources */,
				1785EB66127DD5EA00F468C8 /* SPNetworkPreferencePane.m in Sources */,
				500DA4B71BEFF877000773FE /* SPComboBoxCell.m in Sources */,
				51384AC22903461000FEC501 /* NSDictionaryExtension.swift in Sources */,
				1785EB6A127DD79300F468C8 /* SPEditorPreferencePane.m in Sources */,
				17FDB04C1280778B00DBBBC2 /* SPFontPreviewTextField.m in Sources */,
				17D3C22212859E070047709F /* SPFavoriteNode.m in Sources */,
				506CE9311A311C6C0039F736 /* SPRuleFilterController.m in Sources */,
				17D3C66E128AD4710047709F /* SPFavoritesController.m in Sources */,
				17D3C671128AD8160047709F /* SPSingleton.m in Sources */,
				17D3C6D3128B1C900047709F /* SPFavoritesOutlineView.m in Sources */,
				50D3C3521A77135F00B5429C /* SPParserUtils.c in Sources */,
				1A8B53572584520800526DED /* SPURLAdditions.m in Sources */,
				FD0E72EC2C391F44007EF348 /* SQLiteDisplayFormatManager.swift in Sources */,
				513515D2259354BB001E4533 /* NSImageExtensions.swift in Sources */,
				BC68BFC7128D4EAE004907D9 /* SPBundleEditorController.m in Sources */,
				BC1944D01297291800A236CD /* SPBundleCommandTextView.m in Sources */,
				1AF0DA7F259F5D8C00961974 /* SPPointerArrayAdditions.m in Sources */,
				BC77C5E4129AA69E009AD832 /* SPBundleHTMLOutputController.m in Sources */,
				BC5750D512A6233900911BA2 /* SPActivityTextFieldCell.m in Sources */,
				BC0ED3DA12A9196C00088461 /* SPChooseMenuItemDialog.m in Sources */,
				51BC150625BE13C400F1CDC9 /* NSViewExtension.swift in Sources */,
				583CA21512EC8B2200C9E763 /* SPWindow.m in Sources */,
				1A9EB9AE25651F5000FE60FF /* SQLiteHistoryManager.swift in Sources */,
				582F02311370B52600B30621 /* SPExportFileNameTokenObject.m in Sources */,
				500DA4BC1BF0CD57000773FE /* SPScreenAdditions.m in Sources */,
				584D878B15140FEB00F24774 /* SPObjectAdditions.m in Sources */,
				584D87921514101E00F24774 /* SPDatabaseStructure.m in Sources */,
				1A4DC22D25DECEA000DA4FE1 /* ProgressWindowController.swift in Sources */,
				584D88A91515034200F24774 /* NSNotificationCenterThreadingAdditions.m in Sources */,
				584D899D15162CBE00F24774 /* SPDataBase64EncodingAdditions.m in Sources */,
				1798F1871550175B004B0AB8 /* SPFavoritesExporter.m in Sources */,
				1798F1881550175B004B0AB8 /* SPFavoritesImporter.m in Sources */,
				1A89556625D6BEED0060CE72 /* GitHubReleaseManager.swift in Sources */,
				514DD98925FACF1500EA3B3B /* SPDatabaseDocument.swift in Sources */,
				1798F1951550181B004B0AB8 /* SPGroupNode.m in Sources */,
				1798F19815501838004B0AB8 /* SPMutableArrayAdditions.m in Sources */,
				1A1EE94A2551185D0056FECD /* DateFormatterExtension.swift in Sources */,
				1798F19B1550185B004B0AB8 /* SPTreeNode.m in Sources */,
				17D5B49E1553059F00EF3BB3 /* SPViewCopy.m in Sources */,
				176E14D115570FE300FAF326 /* SPBundleCommandRunner.m in Sources */,
				58DF9F3315AB26C2003B4330 /* SPDateAdditions.m in Sources */,
				58DF9F7315AB8509003B4330 /* SPSplitView.m in Sources */,
				5843E247162B555B00EAA6D1 /* SPThreadAdditions.m in Sources */,
				58D2A6A716FBDEFF002EB401 /* SPComboPopupButton.m in Sources */,
				65F52CC824AE21D600FED3CB /* SPFilePreferencePane.m in Sources */,
				501B1D181728A3DA0017C92E /* SPCharsetCollationHelper.m in Sources */,
				50E217B318174246009D3580 /* SPColorSelectorView.m in Sources */,
				50E217B618174280009D3580 /* SPFavoriteColorSupport.m in Sources */,
				1A6377D4259B414400B1E96D /* SecureBookmark.swift in Sources */,
				1A564F74237E2E4958CA593A /* SPPillAttachmentCell.m in Sources */,
				5132930D25F586A900D803AD /* TabManager.swift in Sources */,
				9BE76F2886901784E4FD2321 /* SPFilterTableController.m in Sources */,
				9BE765EBBDFD2F121C13D274 /* SPFillView.m in Sources */,
				9BE76F2B943AFDBA6EDC52BE /* SPHelpViewerController.m in Sources */,
				9BE765682376A00C82FB93AA /* SPHelpViewerClient.m in Sources */,
				8AEAC27B5C5B866575ECDB62 /* TableSortHelper.swift in Sources */,
				44011FFBC7DF57126761313F /* SQLitePinnedTableManager.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		17D41AB22171157B00B1888D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			name = SPMySQL.framework;
			targetProxy = 17D41AB12171157B00B1888D /* PBXContainerItemProxy */;
		};
		518402D924A3FF4F004693B0 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8D15AC270486D014006FF6A4 /* Sequel Ace */;
			targetProxy = 518402D824A3FF4F004693B0 /* PBXContainerItemProxy */;
		};
		58B9097011C3A462000826E5 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 58B9096011C3A42B000826E5 /* xibLocalizationPostprocessor */;
			targetProxy = 58B9096F11C3A462000826E5 /* PBXContainerItemProxy */;
		};
		58CDB34B0FCE144000F8ACA3 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 58CDB3350FCE13C900F8ACA3 /* SequelAceTunnelAssistant */;
			targetProxy = 58CDB34A0FCE144000F8ACA3 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		17DD52C4115074CB007D8950 /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				965125F524926B6300E65B53 /* en */,
				511B309F25ABBC6500D010E3 /* es */,
				51BB3DFC25AEF020005AAE44 /* zh-Hans */,
				51BB3E0425AEF0D7005AAE44 /* zh-Hant */,
				519F2829261A5A1800370B14 /* de */,
				51C04C98261E4CF5001F5554 /* pt-BR */,
				51C04C9D261E4DB6001F5554 /* pt */,
				516A76C52653B4E800DC6501 /* ja */,
				51905622267DFF1700212442 /* vi */,
				511E8F6926EF72FD001FC731 /* ru */,
				5199783E27760147008DEB7B /* tr */,
				510EE15C27B6C35F008544E7 /* fr */,
				51731E7B27DFB99A00D81B98 /* it */,
				514D620D2B0278D900571694 /* ar */,
				5127EAF62B568DCC009CFC6A /* cs */,
				5127EAF72B568E2B009CFC6A /* eo */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		380F4EDB0FC0B50600B0BFD7 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/Frameworks",
					"$(inherited)",
					"$(PLATFORM_DIR)/Developer/Library/Frameworks/**",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Frameworks",
				);
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_MODEL_TUNING = G5;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				HEADER_SEARCH_PATHS = "$CONFIGURATION_TEMP_DIR/sequel-ace.build/DerivedSources";
				INFOPLIST_FILE = "Resources/Plists/Unit Tests-Info.plist";
				INSTALL_PATH = "$(USER_LIBRARY_DIR)/Bundles";
				LD_RUNPATH_SEARCH_PATHS = "$(SRCROOT)/Frameworks";
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-framework",
					Cocoa,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.sequel-ace.tests";
				PRODUCT_NAME = "Unit Tests";
				SKIP_INSTALL = YES;
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(PROJECT_NAME)-Swift.h";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		380F4EDC0FC0B50600B0BFD7 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/Frameworks",
					"$(inherited)",
					"$(PLATFORM_DIR)/Developer/Library/Frameworks/**",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Frameworks",
				);
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				HEADER_SEARCH_PATHS = "$CONFIGURATION_TEMP_DIR/sequel-ace.build/DerivedSources";
				INFOPLIST_FILE = "Resources/Plists/Unit Tests-Info.plist";
				INSTALL_PATH = "$(USER_LIBRARY_DIR)/Bundles";
				LD_RUNPATH_SEARCH_PATHS = "$(SRCROOT)/Frameworks";
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-framework",
					Cocoa,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.sequel-ace.tests";
				PRODUCT_NAME = "Unit Tests";
				SKIP_INSTALL = YES;
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(PROJECT_NAME)-Swift.h";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		380F4EDD0FC0B50600B0BFD7 /* Distribution */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/Frameworks",
					"$(inherited)",
					"$(PLATFORM_DIR)/Developer/Library/Frameworks/**",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Frameworks",
				);
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				HEADER_SEARCH_PATHS = "$CONFIGURATION_TEMP_DIR/sequel-ace.build/DerivedSources";
				INFOPLIST_FILE = "Resources/Plists/Unit Tests-Info.plist";
				INSTALL_PATH = "$(USER_LIBRARY_DIR)/Bundles";
				LD_RUNPATH_SEARCH_PATHS = "$(SRCROOT)/Frameworks";
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-framework",
					Foundation,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.sequel-ace.tests";
				PRODUCT_NAME = "Unit Tests";
				SKIP_INSTALL = YES;
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(PROJECT_NAME)-Swift.h";
				SWIFT_VERSION = 5.0;
			};
			name = Distribution;
		};
		588593F00F7AEB6900ED0E67 /* Distribution */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_LINK_OBJC_RUNTIME = NO;
				CLANG_STATIC_ANALYZER_MODE = deep;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 20095;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREFIX_HEADER = "Source/Sequel-Ace.pch";
				GCC_TREAT_IMPLICIT_FUNCTION_DECLARATIONS_AS_ERRORS = YES;
				GCC_TREAT_NONCONFORMANT_CODE_ERRORS_AS_WARNINGS = YES;
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_GLOBAL_CONSTRUCTORS = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_FOUR_CHARACTER_CONSTANTS = YES;
				GCC_WARN_HIDDEN_VIRTUAL_FUNCTIONS = YES;
				GCC_WARN_INHIBIT_ALL_WARNINGS = NO;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_PEDANTIC = NO;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_STRICT_SELECTOR_MATCH = NO;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNKNOWN_PRAGMAS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_LABEL = YES;
				GCC_WARN_UNUSED_PARAMETER = NO;
				GCC_WARN_UNUSED_VALUE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IBC_WARNINGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				SCRIPTS_DIR = "$(SRCROOT)/Scripts";
				SDKROOT = macosx;
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(PROJECT_NAME)-Swift.h";
				VERSIONING_SYSTEM = "apple-generic";
				WARNING_CFLAGS = "-Wmost";
			};
			name = Distribution;
		};
		588593F10F7AEB6900ED0E67 /* Distribution */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLY_RULES_IN_COPY_HEADERS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GENERATE_ASSET_SYMBOL_FRAMEWORKS = "UIKit AppKit";
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Entitlements/Sequel Ace.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMPRESS_PNG_FILES = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = NKQ4HJ66PX;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/Frameworks",
					"$(PROJECT_DIR)/Frameworks",
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
				);
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INFOPLIST_FILE = Resources/Plists/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INSTALL_PATH = /Applications;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/Frameworks/SPMySQLFramework/MySQL\\ Client\\ Libraries/lib",
					"$(inherited)",
				);
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.sequel-ace";
				PRODUCT_MODULE_NAME = Sequel_Ace;
				PRODUCT_NAME = "Sequel Ace";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SEPARATE_STRIP = NO;
				STRIP_PNG_TEXT = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Source/Sequel-Ace-Bridging-Header.h";
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(PROJECT_NAME)-Swift.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Distribution;
		};
		58B9096311C3A42C000826E5 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_ENTITLEMENTS = Entitlements/xibLocalizationPostprocessor.entitlements;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SYSTEM_LIBRARY_DIR)/Frameworks/AppKit.framework/Headers/AppKit.h";
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = /usr/local/bin;
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-framework",
					Foundation,
					"-framework",
					AppKit,
				);
				PRODUCT_NAME = xibLocalizationPostprocessor;
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		58B9096411C3A42C000826E5 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_ENTITLEMENTS = Entitlements/xibLocalizationPostprocessor.entitlements;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = YES;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SYSTEM_LIBRARY_DIR)/Frameworks/AppKit.framework/Headers/AppKit.h";
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = /usr/local/bin;
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-framework",
					Foundation,
					"-framework",
					AppKit,
				);
				PRODUCT_NAME = xibLocalizationPostprocessor;
				SKIP_INSTALL = YES;
				ZERO_LINK = NO;
			};
			name = Release;
		};
		58B9096511C3A42C000826E5 /* Distribution */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_ENTITLEMENTS = Entitlements/xibLocalizationPostprocessor.entitlements;
				CODE_SIGN_IDENTITY = "-";
				DEAD_CODE_STRIPPING = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SYSTEM_LIBRARY_DIR)/Frameworks/AppKit.framework/Headers/AppKit.h";
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = /usr/local/bin;
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-framework",
					Foundation,
					"-framework",
					AppKit,
				);
				PRODUCT_NAME = xibLocalizationPostprocessor;
				SKIP_INSTALL = YES;
			};
			name = Distribution;
		};
		58CDB3380FCE13CB00F8ACA3 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_ENTITLEMENTS = Entitlements/SequelAceTunnelAssistant.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_INJECT_BASE_ENTITLEMENTS = NO;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = NKQ4HJ66PX;
				ENABLE_HARDENED_RUNTIME = YES;
				FRAMEWORK_SEARCH_PATHS = "$(SRCROOT)/Frameworks";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GENERATE_PKGINFO_FILE = YES;
				INFOPLIST_FILE = "Resources/Plists/TunnelAssistant-Info.plist";
				INSTALL_PATH = "/Applications/Sequel Ace.app/Contents/MacOS";
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-framework",
					Foundation,
					"-framework",
					AppKit,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.sequel-ace.SequelAceTunnelAssistant";
				PRODUCT_NAME = SequelAceTunnelAssistant;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		58CDB3390FCE13CB00F8ACA3 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_ENTITLEMENTS = Entitlements/SequelAceTunnelAssistant.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_INJECT_BASE_ENTITLEMENTS = NO;
				CODE_SIGN_STYLE = Automatic;
				COPY_PHASE_STRIP = YES;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = NKQ4HJ66PX;
				ENABLE_HARDENED_RUNTIME = YES;
				FRAMEWORK_SEARCH_PATHS = "$(SRCROOT)/Frameworks";
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GENERATE_PKGINFO_FILE = YES;
				INFOPLIST_FILE = "Resources/Plists/TunnelAssistant-Info.plist";
				INSTALL_PATH = "/Applications/Sequel Ace.app/Contents/MacOS";
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-framework",
					Foundation,
					"-framework",
					AppKit,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.sequel-ace.SequelAceTunnelAssistant";
				PRODUCT_NAME = SequelAceTunnelAssistant;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
				ZERO_LINK = NO;
			};
			name = Release;
		};
		58CDB33A0FCE13CB00F8ACA3 /* Distribution */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_ENTITLEMENTS = Entitlements/SequelAceTunnelAssistant.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_INJECT_BASE_ENTITLEMENTS = NO;
				CODE_SIGN_STYLE = Automatic;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = NKQ4HJ66PX;
				ENABLE_HARDENED_RUNTIME = YES;
				FRAMEWORK_SEARCH_PATHS = "$(SRCROOT)/Frameworks";
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GENERATE_PKGINFO_FILE = YES;
				INFOPLIST_FILE = "Resources/Plists/TunnelAssistant-Info.plist";
				INSTALL_PATH = "/Applications/Sequel Ace.app/Contents/MacOS";
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-framework",
					Foundation,
					"-framework",
					AppKit,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.sequel-ace.SequelAceTunnelAssistant";
				PRODUCT_NAME = SequelAceTunnelAssistant;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Distribution;
		};
		96BC47C72500A84F003C8105 /* Beta */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_LINK_OBJC_RUNTIME = NO;
				CLANG_STATIC_ANALYZER_MODE = deep;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 20095;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREFIX_HEADER = "Source/Sequel-Ace.pch";
				GCC_TREAT_IMPLICIT_FUNCTION_DECLARATIONS_AS_ERRORS = YES;
				GCC_TREAT_NONCONFORMANT_CODE_ERRORS_AS_WARNINGS = YES;
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_GLOBAL_CONSTRUCTORS = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_FOUR_CHARACTER_CONSTANTS = YES;
				GCC_WARN_HIDDEN_VIRTUAL_FUNCTIONS = YES;
				GCC_WARN_INHIBIT_ALL_WARNINGS = NO;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_PEDANTIC = NO;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_STRICT_SELECTOR_MATCH = NO;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNKNOWN_PRAGMAS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_LABEL = YES;
				GCC_WARN_UNUSED_PARAMETER = NO;
				GCC_WARN_UNUSED_VALUE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IBC_WARNINGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				SCRIPTS_DIR = "$(SRCROOT)/Scripts";
				SDKROOT = macosx;
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(PROJECT_NAME)-Swift.h";
				VERSIONING_SYSTEM = "apple-generic";
				WARNING_CFLAGS = "-Wmost";
			};
			name = Beta;
		};
		96BC47C82500A84F003C8105 /* Beta */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLY_RULES_IN_COPY_HEADERS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GENERATE_ASSET_SYMBOL_FRAMEWORKS = "UIKit AppKit";
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Entitlements/Sequel Ace.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMPRESS_PNG_FILES = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = NKQ4HJ66PX;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/Frameworks",
					"$(PROJECT_DIR)/Frameworks",
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
				);
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INFOPLIST_FILE = Resources/Plists/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INSTALL_PATH = /Applications;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/Frameworks/SPMySQLFramework/MySQL\\ Client\\ Libraries/lib",
					"$(inherited)",
				);
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.sequel-ace-beta";
				PRODUCT_MODULE_NAME = Sequel_Ace;
				PRODUCT_NAME = "Sequel Ace Beta";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SEPARATE_STRIP = NO;
				STRIP_PNG_TEXT = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Source/Sequel-Ace-Bridging-Header.h";
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(PROJECT_NAME)-Swift.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Beta;
		};
		96BC47C92500A84F003C8105 /* Beta */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "-";
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/Frameworks",
					"$(inherited)",
					"$(PLATFORM_DIR)/Developer/Library/Frameworks/**",
					"$(PROJECT_DIR)",
					"$(PROJECT_DIR)/Frameworks",
				);
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				HEADER_SEARCH_PATHS = "$CONFIGURATION_TEMP_DIR/sequel-ace.build/DerivedSources";
				INFOPLIST_FILE = "Resources/Plists/Unit Tests-Info.plist";
				INSTALL_PATH = "$(USER_LIBRARY_DIR)/Bundles";
				LD_RUNPATH_SEARCH_PATHS = "$(SRCROOT)/Frameworks";
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-framework",
					Foundation,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.sequel-ace.tests";
				PRODUCT_NAME = "Unit Tests";
				SKIP_INSTALL = YES;
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(PROJECT_NAME)-Swift.h";
				SWIFT_VERSION = 5.0;
			};
			name = Beta;
		};
		96BC47CA2500A84F003C8105 /* Beta */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_ENTITLEMENTS = Entitlements/SequelAceTunnelAssistant.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_INJECT_BASE_ENTITLEMENTS = NO;
				CODE_SIGN_STYLE = Automatic;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = NKQ4HJ66PX;
				ENABLE_HARDENED_RUNTIME = YES;
				FRAMEWORK_SEARCH_PATHS = "$(SRCROOT)/Frameworks";
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GENERATE_PKGINFO_FILE = YES;
				INFOPLIST_FILE = "Resources/Plists/TunnelAssistant-Info.plist";
				INSTALL_PATH = "/Applications/Sequel Ace.app/Contents/MacOS";
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-framework",
					Foundation,
					"-framework",
					AppKit,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.sequel-ace.SequelAceTunnelAssistant";
				PRODUCT_NAME = SequelAceTunnelAssistant;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Beta;
		};
		96BC47CC2500A84F003C8105 /* Beta */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = NO;
				CLANG_ENABLE_OBJC_ARC = YES;
				CODE_SIGN_ENTITLEMENTS = Entitlements/xibLocalizationPostprocessor.entitlements;
				CODE_SIGN_IDENTITY = "-";
				DEAD_CODE_STRIPPING = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SYSTEM_LIBRARY_DIR)/Frameworks/AppKit.framework/Headers/AppKit.h";
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INSTALL_PATH = /usr/local/bin;
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = (
					"-framework",
					Foundation,
					"-framework",
					AppKit,
				);
				PRODUCT_NAME = xibLocalizationPostprocessor;
				SKIP_INSTALL = YES;
			};
			name = Beta;
		};
		C05733C808A9546B00998B17 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLY_RULES_IN_COPY_HEADERS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GENERATE_ASSET_SYMBOL_FRAMEWORKS = "UIKit AppKit";
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Entitlements/Sequel Ace.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMPRESS_PNG_FILES = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = NKQ4HJ66PX;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/Frameworks",
					"$(PROJECT_DIR)/Frameworks",
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
				);
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INFOPLIST_FILE = Resources/Plists/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INSTALL_PATH = /Applications;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/Frameworks/SPMySQLFramework/MySQL\\ Client\\ Libraries/lib",
					"$(inherited)",
				);
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.sequel-ace";
				PRODUCT_MODULE_NAME = Sequel_Ace;
				PRODUCT_NAME = "Sequel Ace";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SEPARATE_STRIP = NO;
				STRIP_PNG_TEXT = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Source/Sequel-Ace-Bridging-Header.h";
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(PROJECT_NAME)-Swift.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		C05733C908A9546B00998B17 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				APPLY_RULES_IN_COPY_HEADERS = NO;
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GENERATE_ASSET_SYMBOL_FRAMEWORKS = "UIKit AppKit";
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Entitlements/Sequel Ace.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMPRESS_PNG_FILES = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = NKQ4HJ66PX;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				FRAMEWORK_SEARCH_PATHS = (
					"$(SRCROOT)/Frameworks",
					"$(PROJECT_DIR)/Frameworks",
					"$(PLATFORM_DIR)/Developer/Library/Frameworks",
				);
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_MODEL_TUNING = G5;
				GCC_NO_COMMON_BLOCKS = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				INFOPLIST_FILE = Resources/Plists/Info.plist;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.utilities";
				INSTALL_PATH = /Applications;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				LIBRARY_SEARCH_PATHS = (
					"$(PROJECT_DIR)/Frameworks/SPMySQLFramework/MySQL\\ Client\\ Libraries/lib",
					"$(inherited)",
				);
				LOCALIZED_STRING_SWIFTUI_SUPPORT = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.sequel-ace";
				PRODUCT_MODULE_NAME = Sequel_Ace;
				PRODUCT_NAME = "Sequel Ace";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SEPARATE_STRIP = NO;
				STRIP_PNG_TEXT = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "Source/Sequel-Ace-Bridging-Header.h";
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(PROJECT_NAME)-Swift.h";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		C05733CC08A9546B00998B17 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_LINK_OBJC_RUNTIME = NO;
				CLANG_STATIC_ANALYZER_MODE = deep;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 20095;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREFIX_HEADER = "Source/Sequel-Ace.pch";
				GCC_PREPROCESSOR_DEFINITIONS = DEBUG;
				GCC_TREAT_IMPLICIT_FUNCTION_DECLARATIONS_AS_ERRORS = YES;
				GCC_TREAT_NONCONFORMANT_CODE_ERRORS_AS_WARNINGS = YES;
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_GLOBAL_CONSTRUCTORS = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_FOUR_CHARACTER_CONSTANTS = YES;
				GCC_WARN_HIDDEN_VIRTUAL_FUNCTIONS = YES;
				GCC_WARN_INHIBIT_ALL_WARNINGS = NO;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_PEDANTIC = NO;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_STRICT_SELECTOR_MATCH = NO;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNKNOWN_PRAGMAS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_LABEL = YES;
				GCC_WARN_UNUSED_PARAMETER = NO;
				GCC_WARN_UNUSED_VALUE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IBC_FLATTEN_NIBS = NO;
				IBC_WARNINGS = YES;
				LEX_INSERT_LINE_DIRECTIVES = YES;
				LEX_SUPPRESS_DEFAULT_RULE = NO;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_SWIFT_FLAGS = "-D DEBUG";
				SCRIPTS_DIR = "$(SRCROOT)/Scripts";
				SDKROOT = macosx;
				STRIP_INSTALLED_PRODUCT = NO;
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(PROJECT_NAME)-Swift.h";
				VERSIONING_SYSTEM = "apple-generic";
				WARNING_CFLAGS = "-Wmost";
			};
			name = Debug;
		};
		C05733CD08A9546B00998B17 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_LINK_OBJC_RUNTIME = NO;
				CLANG_STATIC_ANALYZER_MODE = deep;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 20095;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = c99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREFIX_HEADER = "Source/Sequel-Ace.pch";
				GCC_TREAT_IMPLICIT_FUNCTION_DECLARATIONS_AS_ERRORS = YES;
				GCC_TREAT_NONCONFORMANT_CODE_ERRORS_AS_WARNINGS = YES;
				GCC_TREAT_WARNINGS_AS_ERRORS = NO;
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_GLOBAL_CONSTRUCTORS = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_FOUR_CHARACTER_CONSTANTS = YES;
				GCC_WARN_HIDDEN_VIRTUAL_FUNCTIONS = YES;
				GCC_WARN_INHIBIT_ALL_WARNINGS = NO;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_PEDANTIC = NO;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_STRICT_SELECTOR_MATCH = NO;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNKNOWN_PRAGMAS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_LABEL = YES;
				GCC_WARN_UNUSED_PARAMETER = NO;
				GCC_WARN_UNUSED_VALUE = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IBC_WARNINGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				SCRIPTS_DIR = "$(SRCROOT)/Scripts";
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OBJC_INTERFACE_HEADER_NAME = "$(PROJECT_NAME)-Swift.h";
				VERSIONING_SYSTEM = "apple-generic";
				WARNING_CFLAGS = "-Wmost";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		380F4EDE0FC0B50600B0BFD7 /* Build configuration list for PBXNativeTarget "Unit Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				380F4EDB0FC0B50600B0BFD7 /* Debug */,
				380F4EDC0FC0B50600B0BFD7 /* Release */,
				380F4EDD0FC0B50600B0BFD7 /* Distribution */,
				96BC47C92500A84F003C8105 /* Beta */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		58B9096A11C3A431000826E5 /* Build configuration list for PBXNativeTarget "xibLocalizationPostprocessor" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58B9096311C3A42C000826E5 /* Debug */,
				58B9096411C3A42C000826E5 /* Release */,
				58B9096511C3A42C000826E5 /* Distribution */,
				96BC47CC2500A84F003C8105 /* Beta */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		58CDB33F0FCE13E300F8ACA3 /* Build configuration list for PBXNativeTarget "SequelAceTunnelAssistant" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58CDB3380FCE13CB00F8ACA3 /* Debug */,
				58CDB3390FCE13CB00F8ACA3 /* Release */,
				58CDB33A0FCE13CB00F8ACA3 /* Distribution */,
				96BC47CA2500A84F003C8105 /* Beta */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		C05733C708A9546B00998B17 /* Build configuration list for PBXNativeTarget "Sequel Ace" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C05733C808A9546B00998B17 /* Debug */,
				C05733C908A9546B00998B17 /* Release */,
				588593F10F7AEB6900ED0E67 /* Distribution */,
				96BC47C82500A84F003C8105 /* Beta */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
		C05733CB08A9546B00998B17 /* Build configuration list for PBXProject "sequel-ace" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				C05733CC08A9546B00998B17 /* Debug */,
				C05733CD08A9546B00998B17 /* Release */,
				588593F00F7AEB6900ED0E67 /* Distribution */,
				96BC47C72500A84F003C8105 /* Beta */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Debug;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		1A89556D25D6C8880060CE72 /* XCRemoteSwiftPackageReference "Alamofire" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/Alamofire/Alamofire.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.9.0;
			};
		};
		513C8CDF2BBC4132001CCE3A /* XCRemoteSwiftPackageReference "ocmock" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/paulb777/ocmock";
			requirement = {
				branch = "patch-2";
				kind = branch;
			};
		};
		515D303D25BD7DE60021CF1E /* XCRemoteSwiftPackageReference "appcenter-sdk-apple" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/microsoft/appcenter-sdk-apple";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.0.4;
			};
		};
		51BC14FF25BE138700F1CDC9 /* XCRemoteSwiftPackageReference "SnapKit" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/SnapKit/SnapKit";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 5.6.0;
			};
		};
		51D9527425AE2B5300574BEB /* XCRemoteSwiftPackageReference "fmdb" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/ccgus/fmdb";
			requirement = {
				kind = exactVersion;
				version = 2.7.9;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		1A89556E25D6C8880060CE72 /* Alamofire */ = {
			isa = XCSwiftPackageProductDependency;
			package = 1A89556D25D6C8880060CE72 /* XCRemoteSwiftPackageReference "Alamofire" */;
			productName = Alamofire;
		};
		513C8CE02BBC4132001CCE3A /* OCMock */ = {
			isa = XCSwiftPackageProductDependency;
			package = 513C8CDF2BBC4132001CCE3A /* XCRemoteSwiftPackageReference "ocmock" */;
			productName = OCMock;
		};
		515D303E25BD7DE60021CF1E /* AppCenterCrashes */ = {
			isa = XCSwiftPackageProductDependency;
			package = 515D303D25BD7DE60021CF1E /* XCRemoteSwiftPackageReference "appcenter-sdk-apple" */;
			productName = AppCenterCrashes;
		};
		515D304025BD7DE60021CF1E /* AppCenterAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 515D303D25BD7DE60021CF1E /* XCRemoteSwiftPackageReference "appcenter-sdk-apple" */;
			productName = AppCenterAnalytics;
		};
		51BC150025BE138700F1CDC9 /* SnapKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = 51BC14FF25BE138700F1CDC9 /* XCRemoteSwiftPackageReference "SnapKit" */;
			productName = SnapKit;
		};
		51D9527525AE2B5300574BEB /* FMDB */ = {
			isa = XCSwiftPackageProductDependency;
			package = 51D9527425AE2B5300574BEB /* XCRemoteSwiftPackageReference "fmdb" */;
			productName = FMDB;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 2A37F4A9FDCFA73011CA2CEA /* Project object */;
}
