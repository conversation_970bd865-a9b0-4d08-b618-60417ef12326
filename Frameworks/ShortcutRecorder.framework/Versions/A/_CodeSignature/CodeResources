<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/Info.plist</key>
		<data>
		G7G+/X/SyZ24N/EnFQgURlDt8yU=
		</data>
		<key>Resources/SRRemoveShortcut.tif</key>
		<data>
		3KyBKD4lJJ0DFkSxeu591bveWYg=
		</data>
		<key>Resources/SRRemoveShortcutPressed.tif</key>
		<data>
		+BRi/8OvavtPHT3rO/GObqd1mCU=
		</data>
		<key>Resources/SRRemoveShortcutRollover.tif</key>
		<data>
		cVH4mWqfmMH+G7j/OG/3AeZScKo=
		</data>
		<key>Resources/SRSnapback.tiff</key>
		<data>
		7dh75aviacDC9SXZtO7jLVldfY0=
		</data>
		<key>Resources/en.lproj/ShortcutRecorder.strings</key>
		<dict>
			<key>hash</key>
			<data>
			02/vEmNVsc5H/LsbGvkckQToPCw=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/SRCommon.h</key>
		<dict>
			<key>hash2</key>
			<data>
			rkWkgUZjTz1zgu417G7opuSgHVMii800l1V944lBSEI=
			</data>
		</dict>
		<key>Headers/SRKeyCodeTransformer.h</key>
		<dict>
			<key>hash2</key>
			<data>
			0gNN1MmX1bvv7Cajax8nYXX3NJQsMRKTpGGhB/eVHwU=
			</data>
		</dict>
		<key>Headers/SRRecorderCell.h</key>
		<dict>
			<key>hash2</key>
			<data>
			QA8WHbcBk939ok1TKIW95JNcZx4T3mCIvd4Rh/xZ3tY=
			</data>
		</dict>
		<key>Headers/SRRecorderControl.h</key>
		<dict>
			<key>hash2</key>
			<data>
			5fxbBKa5Mk6T0zgHGO8uLuquDqNWQxw3uBdUqNAu3xA=
			</data>
		</dict>
		<key>Headers/SRValidator.h</key>
		<dict>
			<key>hash2</key>
			<data>
			EyKpmV3c1krkbgg/IbacvJMmSZhm/3l9paxhmJ91WkE=
			</data>
		</dict>
		<key>Headers/ShortcutRecorder.h</key>
		<dict>
			<key>hash2</key>
			<data>
			GiKVkZLHH29bjM4XeP7Vfigi5bSeU9IpNTmhsYFs0WA=
			</data>
		</dict>
		<key>Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			mNupcEM6JkBXpfejKqL49N63kE83hzCLR52hcF6dlqQ=
			</data>
		</dict>
		<key>Resources/SRRemoveShortcut.tif</key>
		<dict>
			<key>hash2</key>
			<data>
			Ds/ykCmI5Ts4WWZbpA38YGxP4tuwIqkkUSBYkaPa8pM=
			</data>
		</dict>
		<key>Resources/SRRemoveShortcutPressed.tif</key>
		<dict>
			<key>hash2</key>
			<data>
			HAOtWeSPJdCAUY+O1UDJOOy9TjXqHsEuIwPR9XdaScI=
			</data>
		</dict>
		<key>Resources/SRRemoveShortcutRollover.tif</key>
		<dict>
			<key>hash2</key>
			<data>
			a41jY+1obryOlkcILehoNzJ1L/ewTVUTtm5P8elZgi4=
			</data>
		</dict>
		<key>Resources/SRSnapback.tiff</key>
		<dict>
			<key>hash2</key>
			<data>
			pO4357Maimxyhcef+0LCTROHp4Zwsr3jl33BHJOxGbg=
			</data>
		</dict>
		<key>Resources/en.lproj/ShortcutRecorder.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			w+gQm8Bcdx7aOsQ0yNDYRBUzgHhTG1g9v3Yf9bgZAfM=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
