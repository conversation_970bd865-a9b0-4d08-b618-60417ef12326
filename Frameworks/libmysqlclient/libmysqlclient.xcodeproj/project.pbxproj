// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXFileReference section */
		9069D28C0FCE659A0042E34C /* libmysqlclient.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; name = libmysqlclient.a; path = liblibmysqlclient.a; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXGroup section */
		9069D12D0FCE340B0042E34C = {
			isa = PBXGroup;
			children = (
				9069D25C0FCE62420042E34C /* Products */,
			);
			sourceTree = "<group>";
		};
		9069D25C0FCE62420042E34C /* Products */ = {
			isa = PBXGroup;
			children = (
				9069D28C0FCE659A0042E34C /* libmysqlclient.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		9069D28B0FCE659A0042E34C /* libmysqlclient */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 9069D2980FCE65C10042E34C /* Build configuration list for PBXNativeTarget "libmysqlclient" */;
			buildPhases = (
				9038ACD812DCAC96004FA0D0 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = libmysqlclient;
			productName = libmysqlclient;
			productReference = 9069D28C0FCE659A0042E34C /* libmysqlclient.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		9069D12F0FCE340B0042E34C /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1220;
			};
			buildConfigurationList = 9069D1320FCE340B0042E34C /* Build configuration list for PBXProject "libmysqlclient" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 9069D12D0FCE340B0042E34C;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				9069D28B0FCE659A0042E34C /* libmysqlclient */,
			);
		};
/* End PBXProject section */

/* Begin PBXShellScriptBuildPhase section */
		9038ACD812DCAC96004FA0D0 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PROJECT_DIR}/build-libmysqlclient.sh\"\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin XCBuildConfiguration section */
		9069D1300FCE340B0042E34C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				"ARCHS[sdk=iphoneos*]" = "$(ARCHS_STANDARD)";
				"ARCHS[sdk=macosx*]" = "$(ARCHS_STANDARD)";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = "$(PLATFORM_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = macosx;
				VALID_ARCHS = "arm64 x86_64";
			};
			name = Debug;
		};
		9069D1310FCE340B0042E34C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				"ARCHS[sdk=iphoneos*]" = "$(ARCHS_STANDARD)";
				"ARCHS[sdk=macosx*]" = "$(ARCHS_STANDARD)";
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				SDKROOT = "$(PLATFORM_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = macosx;
				VALID_ARCHS = "arm64 x86_64";
			};
			name = Release;
		};
		9069D28D0FCE659B0042E34C /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				DYLIB_INSTALL_NAME_BASE = "$(INSTALL_PATH)";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_MODEL_TUNING = G5;
				GCC_OPTIMIZATION_LEVEL = 0;
				INSTALL_PATH = "@executable_path";
				LD_DYLIB_INSTALL_NAME = "$(DYLIB_INSTALL_NAME_BASE:standardizepath)/$(EXECUTABLE_PATH)";
				PRODUCT_NAME = libmysqlclient;
				VALID_ARCHS = "arm64 x86_64";
			};
			name = Debug;
		};
		9069D28E0FCE659B0042E34C /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				BUILD_LIBRARY_FOR_DISTRIBUTION = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DYLIB_INSTALL_NAME_BASE = "$(INSTALL_PATH)";
				GCC_MODEL_TUNING = G5;
				INSTALL_PATH = "@executable_path";
				LD_DYLIB_INSTALL_NAME = "$(DYLIB_INSTALL_NAME_BASE:standardizepath)/$(EXECUTABLE_PATH)";
				PRODUCT_NAME = libmysqlclient;
				VALID_ARCHS = "arm64 x86_64";
				ZERO_LINK = NO;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		9069D1320FCE340B0042E34C /* Build configuration list for PBXProject "libmysqlclient" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9069D1300FCE340B0042E34C /* Debug */,
				9069D1310FCE340B0042E34C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		9069D2980FCE65C10042E34C /* Build configuration list for PBXNativeTarget "libmysqlclient" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				9069D28D0FCE659B0042E34C /* Debug */,
				9069D28E0FCE659B0042E34C /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 9069D12F0FCE340B0042E34C /* Project object */;
}
