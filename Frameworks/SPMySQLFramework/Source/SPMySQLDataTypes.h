//
//  SPMySQLDataTypes.h
//  SPMySQLFramework
//
//  Created by <PERSON> (stuconnolly.com) on January 14, 2014.
//  Copyright (c) 2014 <PERSON>. All rights reserved.
//
//  Permission is hereby granted, free of charge, to any person
//  obtaining a copy of this software and associated documentation
//  files (the "Software"), to deal in the Software without
//  restriction, including without limitation the rights to use,
//  copy, modify, merge, publish, distribute, sublicense, and/or sell
//  copies of the Software, and to permit persons to whom the
//  Software is furnished to do so, subject to the following
//  conditions:
//
//  The above copyright notice and this permission notice shall be
//  included in all copies or substantial portions of the Software.
//
//  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
//  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
//  OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
//  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
//  HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
//  WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
//  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
//  OTHER DEALINGS IN THE SOFTWARE.
//
//  More info at <https://github.com/sequelpro/sequelpro>

extern NSString * const SPMySQLTinyIntType;
extern NSString * const SPMySQLSmallIntType;
extern NSString * const SPMySQLMediumIntType;
extern NSString * const SPMySQLIntType;
extern NSString * const SPMySQLBigIntType;
extern NSString * const SPMySQLFloatType;
extern NSString * const SPMySQLDoubleType;
extern NSString * const SPMySQLDoublePrecisionType;
extern NSString * const SPMySQLRealType;
extern NSString * const SPMySQLDecimalType;
extern NSString * const SPMySQLBitType;
extern NSString * const SPMySQLSerialType;
extern NSString * const SPMySQLBoolType;
extern NSString * const SPMySQLBoolean;
extern NSString * const SPMySQLDecType;
extern NSString * const SPMySQLFixedType;
extern NSString * const SPMySQLNumericType;
extern NSString * const SPMySQLCharType;
extern NSString * const SPMySQLVarCharType;
extern NSString * const SPMySQLTinyTextType;
extern NSString * const SPMySQLTextType;
extern NSString * const SPMySQLMediumTextType;
extern NSString * const SPMySQLLongTextType;
extern NSString * const SPMySQLTinyBlobType;
extern NSString * const SPMySQLMediumBlobType;
extern NSString * const SPMySQLBlobType;
extern NSString * const SPMySQLLongBlobType;
extern NSString * const SPMySQLBinaryType;
extern NSString * const SPMySQLVarBinaryType;
extern NSString * const SPMySQLEnumType;
extern NSString * const SPMySQLSetType;
extern NSString * const SPMySQLDateType;
extern NSString * const SPMySQLDatetimeType;
extern NSString * const SPMySQLTimestampType;
extern NSString * const SPMySQLTimeType;
extern NSString * const SPMySQLYearType;
extern NSString * const SPMySQLGeometryType;
extern NSString * const SPMySQLPointType;
extern NSString * const SPMySQLLineStringType;
extern NSString * const SPMySQLPolygonType;
extern NSString * const SPMySQLMultiPointType;
extern NSString * const SPMySQLMultiLineStringType;
extern NSString * const SPMySQLMultiPolygonType;
extern NSString * const SPMySQLGeometryCollectionType;
extern NSString * const SPMySQLJsonType;
