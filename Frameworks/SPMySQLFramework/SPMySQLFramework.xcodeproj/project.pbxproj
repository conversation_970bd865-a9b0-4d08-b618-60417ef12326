// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		177916A21E88733000EE3043 /* LICENSE in Resources */ = {isa = PBXBuildFile; fileRef = 177916A01E88733000EE3043 /* LICENSE */; };
		17E3A57B1885A286009CF372 /* SPMySQLDataTypes.h in Headers */ = {isa = PBXBuildFile; fileRef = 17E3A5791885A286009CF372 /* SPMySQLDataTypes.h */; settings = {ATTRIBUTES = (Public, ); }; };
		17E3A57C1885A286009CF372 /* SPMySQLDataTypes.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E3A57A1885A286009CF372 /* SPMySQLDataTypes.m */; };
		1A96314625B9CE6600BF2E91 /* SPMySQLArrayAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 1A96314425B9CE6600BF2E91 /* SPMySQLArrayAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1A96314725B9CE6600BF2E91 /* SPMySQLArrayAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A96314525B9CE6600BF2E91 /* SPMySQLArrayAdditions.m */; };
		1A96314E25B9CE9900BF2E91 /* SPMySQLMutableDictionaryAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1A96314C25B9CE9900BF2E91 /* SPMySQLMutableDictionaryAdditions.m */; };
		1A96314F25B9CE9900BF2E91 /* SPMySQLMutableDictionaryAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 1A96314D25B9CE9900BF2E91 /* SPMySQLMutableDictionaryAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		507FF1E51BC0D82300104523 /* DataConversion_Tests.m in Sources */ = {isa = PBXBuildFile; fileRef = 507FF1811BC0C64100104523 /* DataConversion_Tests.m */; };
		507FF23B1BC0E8CA00104523 /* SPMySQL.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8DC2EF5B0486A6940098B216 /* SPMySQL.framework */; };
		507FF23D1BC157B500104523 /* SPMySQLStringAdditions_Tests.m in Sources */ = {isa = PBXBuildFile; fileRef = 507FF23C1BC157B500104523 /* SPMySQLStringAdditions_Tests.m */; };
		580A331E14D75CF7000D6933 /* SPMySQLGeometryData.h in Headers */ = {isa = PBXBuildFile; fileRef = 580A331C14D75CF7000D6933 /* SPMySQLGeometryData.h */; settings = {ATTRIBUTES = (Public, ); }; };
		580A331F14D75CF7000D6933 /* SPMySQLGeometryData.m in Sources */ = {isa = PBXBuildFile; fileRef = 580A331D14D75CF7000D6933 /* SPMySQLGeometryData.m */; };
		583C734A17A489CC0056B284 /* SPMySQLStreamingResultStoreDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 583C734917A489CC0056B284 /* SPMySQLStreamingResultStoreDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		583C734D17B0778A0056B284 /* Data Conversion.h in Headers */ = {isa = PBXBuildFile; fileRef = 583C734B17B0778A0056B284 /* Data Conversion.h */; };
		583C734E17B0778A0056B284 /* Data Conversion.m in Sources */ = {isa = PBXBuildFile; fileRef = 583C734C17B0778A0056B284 /* Data Conversion.m */; };
		58428E0014BA5FAE000F8438 /* SPMySQLConnection.h in Headers */ = {isa = PBXBuildFile; fileRef = 58428DFE14BA5FAE000F8438 /* SPMySQLConnection.h */; settings = {ATTRIBUTES = (Public, ); }; };
		58428E0114BA5FAE000F8438 /* SPMySQLConnection.m in Sources */ = {isa = PBXBuildFile; fileRef = 58428DFF14BA5FAE000F8438 /* SPMySQLConnection.m */; };
		584294E414CB8002000F8438 /* SPMySQLConstants.h in Headers */ = {isa = PBXBuildFile; fileRef = 584294E314CB8002000F8438 /* SPMySQLConstants.h */; settings = {ATTRIBUTES = (Public, ); }; };
		584294F014CB8002000F8438 /* Ping & KeepAlive.h in Headers */ = {isa = PBXBuildFile; fileRef = 584294EE14CB8002000F8438 /* Ping & KeepAlive.h */; };
		584294F114CB8002000F8438 /* Ping & KeepAlive.m in Sources */ = {isa = PBXBuildFile; fileRef = 584294EF14CB8002000F8438 /* Ping & KeepAlive.m */; };
		584294F614CB8002000F8438 /* Querying & Preparation.h in Headers */ = {isa = PBXBuildFile; fileRef = 584294F414CB8002000F8438 /* Querying & Preparation.h */; settings = {ATTRIBUTES = (Public, ); }; };
		584294FA14CB8002000F8438 /* Encoding.h in Headers */ = {isa = PBXBuildFile; fileRef = 584294F814CB8002000F8438 /* Encoding.h */; settings = {ATTRIBUTES = (Public, ); }; };
		584294FB14CB8002000F8438 /* Encoding.m in Sources */ = {isa = PBXBuildFile; fileRef = 584294F914CB8002000F8438 /* Encoding.m */; };
		584294FE14CB8002000F8438 /* Server Info.h in Headers */ = {isa = PBXBuildFile; fileRef = 584294FC14CB8002000F8438 /* Server Info.h */; settings = {ATTRIBUTES = (Public, ); }; };
		584294FF14CB8002000F8438 /* Server Info.m in Sources */ = {isa = PBXBuildFile; fileRef = 584294FD14CB8002000F8438 /* Server Info.m */; };
		584D812E15057ECD00F24774 /* SPMySQLKeepAliveTimer.h in Headers */ = {isa = PBXBuildFile; fileRef = 584D812C15057ECD00F24774 /* SPMySQLKeepAliveTimer.h */; };
		584D812F15057ECD00F24774 /* SPMySQLKeepAliveTimer.m in Sources */ = {isa = PBXBuildFile; fileRef = 584D812D15057ECD00F24774 /* SPMySQLKeepAliveTimer.m */; };
		584D82551509775000F24774 /* Copying.h in Headers */ = {isa = PBXBuildFile; fileRef = 584D82531509775000F24774 /* Copying.h */; };
		584D82561509775000F24774 /* Copying.m in Sources */ = {isa = PBXBuildFile; fileRef = 584D82541509775000F24774 /* Copying.m */; };
		584F16A81752911200D150A6 /* SPMySQLStreamingResultStore.h in Headers */ = {isa = PBXBuildFile; fileRef = 584F16A61752911100D150A6 /* SPMySQLStreamingResultStore.h */; settings = {ATTRIBUTES = (Public, ); }; };
		584F16A91752911200D150A6 /* SPMySQLStreamingResultStore.m in Sources */ = {isa = PBXBuildFile; fileRef = 584F16A71752911100D150A6 /* SPMySQLStreamingResultStore.m */; };
		586A99FB14F02E21007F82BF /* SPMySQLStreamingResult.h in Headers */ = {isa = PBXBuildFile; fileRef = 586A99F914F02E21007F82BF /* SPMySQLStreamingResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		586A99FC14F02E21007F82BF /* SPMySQLStreamingResult.m in Sources */ = {isa = PBXBuildFile; fileRef = 586A99FA14F02E21007F82BF /* SPMySQLStreamingResult.m */; };
		586AA16714F30C5F007F82BF /* Convenience Methods.h in Headers */ = {isa = PBXBuildFile; fileRef = 586AA16514F30C5F007F82BF /* Convenience Methods.h */; settings = {ATTRIBUTES = (Public, ); }; };
		586AA16814F30C5F007F82BF /* Convenience Methods.m in Sources */ = {isa = PBXBuildFile; fileRef = 586AA16614F30C5F007F82BF /* Convenience Methods.m */; };
		5884127714CC63830078027F /* SPMySQL.h in Headers */ = {isa = PBXBuildFile; fileRef = 5884127614CC63830078027F /* SPMySQL.h */; settings = {ATTRIBUTES = (Public, ); }; };
		588412A814CC7A4D0078027F /* Locking.h in Headers */ = {isa = PBXBuildFile; fileRef = 588412A614CC7A4D0078027F /* Locking.h */; };
		5884133C14CCEC6B0078027F /* libz.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 5884133B14CCEC6B0078027F /* libz.dylib */; };
		5884142714CCF5190078027F /* Conversion.h in Headers */ = {isa = PBXBuildFile; fileRef = 5884142514CCF5190078027F /* Conversion.h */; };
		5884142814CCF5190078027F /* Conversion.m in Sources */ = {isa = PBXBuildFile; fileRef = 5884142614CCF5190078027F /* Conversion.m */; };
		588414BD14CE3B110078027F /* SPMySQLConnectionDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 588414BC14CE3B110078027F /* SPMySQLConnectionDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5884159414D1A6760078027F /* Locking.m in Sources */ = {isa = PBXBuildFile; fileRef = 588412A714CC7A4D0078027F /* Locking.m */; };
		5884159514D1A6880078027F /* Querying & Preparation.m in Sources */ = {isa = PBXBuildFile; fileRef = 584294F514CB8002000F8438 /* Querying & Preparation.m */; };
		5884165514D2306A0078027F /* SPMySQLResult.h in Headers */ = {isa = PBXBuildFile; fileRef = 5884165314D2306A0078027F /* SPMySQLResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		5884165614D2306A0078027F /* SPMySQLResult.m in Sources */ = {isa = PBXBuildFile; fileRef = 5884165414D2306A0078027F /* SPMySQLResult.m */; };
		58C006C814E0B18A00AC489A /* SPMySQLUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = 58C006C714E0B18A00AC489A /* SPMySQLUtilities.h */; };
		58C008CD14E2AC7D00AC489A /* SPMySQLConnectionProxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 58C008CC14E2AC7D00AC489A /* SPMySQLConnectionProxy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		58C009D514E31D3800AC489A /* SPMySQLStringAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 58C009D314E31D3800AC489A /* SPMySQLStringAdditions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		58C009D614E31D3800AC489A /* SPMySQLStringAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 58C009D414E31D3800AC489A /* SPMySQLStringAdditions.m */; };
		58C00AA914E4869C00AC489A /* Max Packet Size.h in Headers */ = {isa = PBXBuildFile; fileRef = 58C00AA714E4869C00AC489A /* Max Packet Size.h */; settings = {ATTRIBUTES = (Public, ); }; };
		58C00AAA14E4869C00AC489A /* Max Packet Size.m in Sources */ = {isa = PBXBuildFile; fileRef = 58C00AA814E4869C00AC489A /* Max Packet Size.m */; };
		58C00AB514E4892E00AC489A /* Delegate & Proxy.h in Headers */ = {isa = PBXBuildFile; fileRef = 58C00AB314E4892E00AC489A /* Delegate & Proxy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		58C00AB614E4892E00AC489A /* Delegate & Proxy.m in Sources */ = {isa = PBXBuildFile; fileRef = 58C00AB414E4892E00AC489A /* Delegate & Proxy.m */; };
		58C00ADA14E4959A00AC489A /* SystemConfiguration.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 58C00AD914E4959A00AC489A /* SystemConfiguration.framework */; };
		58C00BD114E7459600AC489A /* Databases & Tables.h in Headers */ = {isa = PBXBuildFile; fileRef = 58C00BCF14E7459600AC489A /* Databases & Tables.h */; settings = {ATTRIBUTES = (Public, ); }; };
		58C00BD214E7459600AC489A /* Databases & Tables.m in Sources */ = {isa = PBXBuildFile; fileRef = 58C00BD014E7459600AC489A /* Databases & Tables.m */; };
		58C00CA514E845D800AC489A /* SPMySQL Private APIs.h in Headers */ = {isa = PBXBuildFile; fileRef = 58C00CA414E845D800AC489A /* SPMySQL Private APIs.h */; };
		58C7C1E414DB6E4C00436315 /* SPMySQLFastStreamingResult.h in Headers */ = {isa = PBXBuildFile; fileRef = 58C7C1E214DB6E4C00436315 /* SPMySQLFastStreamingResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		58C7C1E514DB6E4C00436315 /* SPMySQLFastStreamingResult.m in Sources */ = {isa = PBXBuildFile; fileRef = 58C7C1E314DB6E4C00436315 /* SPMySQLFastStreamingResult.m */; };
		58C7C1E814DB6E8600436315 /* Field Definitions.h in Headers */ = {isa = PBXBuildFile; fileRef = 58C7C1E614DB6E8600436315 /* Field Definitions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		58C7C1E914DB6E8600436315 /* Field Definitions.m in Sources */ = {isa = PBXBuildFile; fileRef = 58C7C1E714DB6E8600436315 /* Field Definitions.m */; };
		58D2A4D116EDF1C6002EB401 /* SPMySQLEmptyResult.h in Headers */ = {isa = PBXBuildFile; fileRef = 58D2A4CF16EDF1C6002EB401 /* SPMySQLEmptyResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		58D2A4D216EDF1C6002EB401 /* SPMySQLEmptyResult.m in Sources */ = {isa = PBXBuildFile; fileRef = 58D2A4D016EDF1C6002EB401 /* SPMySQLEmptyResult.m */; };
		8DC2EF570486A6940098B216 /* Cocoa.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1058C7B1FEA5585E11CA2CBB /* Cocoa.framework */; };
		9615D1592D4C18CB0095F55A /* libmysqlclient.24.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 9615D1582D4C18CB0095F55A /* libmysqlclient.24.dylib */; };
		9615D15A2D4C18F80095F55A /* libmysqlclient.24.dylib in CopyFiles */ = {isa = PBXBuildFile; fileRef = 9615D1582D4C18CB0095F55A /* libmysqlclient.24.dylib */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
		9615D15D2D4C26DD0095F55A /* libcrypto.3.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 9615D15B2D4C26DD0095F55A /* libcrypto.3.dylib */; };
		9615D15E2D4C26DD0095F55A /* libssl.3.dylib in Frameworks */ = {isa = PBXBuildFile; fileRef = 9615D15C2D4C26DD0095F55A /* libssl.3.dylib */; };
		9615D15F2D4C26F80095F55A /* libcrypto.3.dylib in CopyFiles */ = {isa = PBXBuildFile; fileRef = 9615D15B2D4C26DD0095F55A /* libcrypto.3.dylib */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
		9615D3922D4D7DA00095F55A /* libssl.3.dylib in CopyFiles */ = {isa = PBXBuildFile; fileRef = 9615D15C2D4C26DD0095F55A /* libssl.3.dylib */; settings = {ATTRIBUTES = (CodeSignOnCopy, ); }; };
		9615D84D2D5EDF530095F55A /* psi_base.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D8362D5EDF530095F55A /* psi_base.h */; };
		9615D84E2D5EDF530095F55A /* psi_memory.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D8372D5EDF530095F55A /* psi_memory.h */; };
		9615D84F2D5EDF530095F55A /* client_plugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D8392D5EDF530095F55A /* client_plugin.h */; };
		9615D8502D5EDF530095F55A /* plugin_auth_common.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D83A2D5EDF530095F55A /* plugin_auth_common.h */; };
		9615D8512D5EDF530095F55A /* udf_registration_types.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D83B2D5EDF530095F55A /* udf_registration_types.h */; };
		9615D8522D5EDF530095F55A /* errmsg.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D83D2D5EDF530095F55A /* errmsg.h */; };
		9615D8532D5EDF530095F55A /* field_types.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D83E2D5EDF530095F55A /* field_types.h */; };
		9615D8542D5EDF530095F55A /* my_alloc.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D83F2D5EDF530095F55A /* my_alloc.h */; };
		9615D8552D5EDF530095F55A /* my_command.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D8402D5EDF530095F55A /* my_command.h */; };
		9615D8562D5EDF530095F55A /* my_compress.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D8412D5EDF530095F55A /* my_compress.h */; };
		9615D8572D5EDF530095F55A /* my_list.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D8422D5EDF530095F55A /* my_list.h */; };
		9615D8582D5EDF530095F55A /* mysql.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D8432D5EDF530095F55A /* mysql.h */; };
		9615D8592D5EDF530095F55A /* mysql_com.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D8442D5EDF530095F55A /* mysql_com.h */; };
		9615D85A2D5EDF530095F55A /* mysql_time.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D8452D5EDF530095F55A /* mysql_time.h */; };
		9615D85B2D5EDF530095F55A /* mysql_version.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D8462D5EDF530095F55A /* mysql_version.h */; };
		9615D85C2D5EDF530095F55A /* mysqld_error.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D8472D5EDF530095F55A /* mysqld_error.h */; };
		9615D85D2D5EDF530095F55A /* mysqlx_ername.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D8482D5EDF530095F55A /* mysqlx_ername.h */; };
		9615D85E2D5EDF530095F55A /* mysqlx_error.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D8492D5EDF530095F55A /* mysqlx_error.h */; };
		9615D85F2D5EDF530095F55A /* mysqlx_version.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D84A2D5EDF530095F55A /* mysqlx_version.h */; };
		9615D8602D5EDF530095F55A /* typelib.h in Headers */ = {isa = PBXBuildFile; fileRef = 9615D84B2D5EDF530095F55A /* typelib.h */; };
		96A5DDB32D63C8AE0079105E /* libc++.tbd in Frameworks */ = {isa = PBXBuildFile; fileRef = 96A5DDB22D63C89A0079105E /* libc++.tbd */; };
		FD4211952918779400941BFE /* SPMySQLGeometryDataTests.m in Sources */ = {isa = PBXBuildFile; fileRef = FD4211942918779400941BFE /* SPMySQLGeometryDataTests.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		507FF2391BC0E8AF00104523 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0867D690FE84028FC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8DC2EF4F0486A6940098B216;
			remoteInfo = SPMySQL.framework;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		26F0BF1024A0052100A43B20 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 6;
			files = (
				9615D3922D4D7DA00095F55A /* libssl.3.dylib in CopyFiles */,
				9615D15F2D4C26F80095F55A /* libcrypto.3.dylib in CopyFiles */,
				9615D15A2D4C18F80095F55A /* libmysqlclient.24.dylib in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0867D69BFE84028FC02AAC07 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = /System/Library/Frameworks/Foundation.framework; sourceTree = "<absolute>"; };
		0867D6A5FE840307C02AAC07 /* AppKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = AppKit.framework; path = /System/Library/Frameworks/AppKit.framework; sourceTree = "<absolute>"; };
		1058C7B1FEA5585E11CA2CBB /* Cocoa.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Cocoa.framework; path = /System/Library/Frameworks/Cocoa.framework; sourceTree = "<absolute>"; };
		177916A01E88733000EE3043 /* LICENSE */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = LICENSE; sourceTree = "<group>"; };
		177916A11E88733000EE3043 /* README.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = README.md; sourceTree = "<group>"; };
		17E3A5791885A286009CF372 /* SPMySQLDataTypes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLDataTypes.h; path = Source/SPMySQLDataTypes.h; sourceTree = "<group>"; };
		17E3A57A1885A286009CF372 /* SPMySQLDataTypes.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SPMySQLDataTypes.m; path = Source/SPMySQLDataTypes.m; sourceTree = "<group>"; };
		1A96314425B9CE6600BF2E91 /* SPMySQLArrayAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLArrayAdditions.h; path = Source/SPMySQLArrayAdditions.h; sourceTree = "<group>"; };
		1A96314525B9CE6600BF2E91 /* SPMySQLArrayAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SPMySQLArrayAdditions.m; path = Source/SPMySQLArrayAdditions.m; sourceTree = "<group>"; };
		1A96314C25B9CE9900BF2E91 /* SPMySQLMutableDictionaryAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SPMySQLMutableDictionaryAdditions.m; path = Source/SPMySQLMutableDictionaryAdditions.m; sourceTree = "<group>"; };
		1A96314D25B9CE9900BF2E91 /* SPMySQLMutableDictionaryAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLMutableDictionaryAdditions.h; path = Source/SPMySQLMutableDictionaryAdditions.h; sourceTree = "<group>"; };
		32DBCF5E0370ADEE00C91783 /* SPMySQLFramework_Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLFramework_Prefix.pch; path = Source/SPMySQLFramework_Prefix.pch; sourceTree = "<group>"; };
		507FF1811BC0C64100104523 /* DataConversion_Tests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = DataConversion_Tests.m; sourceTree = "<group>"; };
		507FF1D51BC0D7D300104523 /* SPMySQL Unit Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = "SPMySQL Unit Tests.xctest"; sourceTree = BUILT_PRODUCTS_DIR; };
		507FF1D81BC0D7D300104523 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		507FF23C1BC157B500104523 /* SPMySQLStringAdditions_Tests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SPMySQLStringAdditions_Tests.m; sourceTree = "<group>"; };
		580A331C14D75CF7000D6933 /* SPMySQLGeometryData.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLGeometryData.h; path = Source/SPMySQLGeometryData.h; sourceTree = "<group>"; };
		580A331D14D75CF7000D6933 /* SPMySQLGeometryData.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SPMySQLGeometryData.m; path = Source/SPMySQLGeometryData.m; sourceTree = "<group>"; };
		583C734917A489CC0056B284 /* SPMySQLStreamingResultStoreDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLStreamingResultStoreDelegate.h; path = Source/SPMySQLStreamingResultStoreDelegate.h; sourceTree = "<group>"; };
		583C734B17B0778A0056B284 /* Data Conversion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "Data Conversion.h"; path = "Source/SPMySQLResult Categories/Data Conversion.h"; sourceTree = "<group>"; };
		583C734C17B0778A0056B284 /* Data Conversion.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = "Data Conversion.m"; path = "Source/SPMySQLResult Categories/Data Conversion.m"; sourceTree = "<group>"; };
		58428DF614BA5A13000F8438 /* build-mysql-client.sh */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.script.sh; path = "build-mysql-client.sh"; sourceTree = "<group>"; };
		58428DFE14BA5FAE000F8438 /* SPMySQLConnection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLConnection.h; path = Source/SPMySQLConnection.h; sourceTree = "<group>"; };
		58428DFF14BA5FAE000F8438 /* SPMySQLConnection.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SPMySQLConnection.m; path = Source/SPMySQLConnection.m; sourceTree = "<group>"; };
		584294E314CB8002000F8438 /* SPMySQLConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLConstants.h; path = Source/SPMySQLConstants.h; sourceTree = "<group>"; };
		584294EE14CB8002000F8438 /* Ping & KeepAlive.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "Ping & KeepAlive.h"; path = "Source/SPMySQLConnection Categories/Ping & KeepAlive.h"; sourceTree = "<group>"; };
		584294EF14CB8002000F8438 /* Ping & KeepAlive.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = "Ping & KeepAlive.m"; path = "Source/SPMySQLConnection Categories/Ping & KeepAlive.m"; sourceTree = "<group>"; };
		584294F414CB8002000F8438 /* Querying & Preparation.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "Querying & Preparation.h"; path = "Source/SPMySQLConnection Categories/Querying & Preparation.h"; sourceTree = "<group>"; };
		584294F514CB8002000F8438 /* Querying & Preparation.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = "Querying & Preparation.m"; path = "Source/SPMySQLConnection Categories/Querying & Preparation.m"; sourceTree = "<group>"; };
		584294F814CB8002000F8438 /* Encoding.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Encoding.h; path = "Source/SPMySQLConnection Categories/Encoding.h"; sourceTree = "<group>"; };
		584294F914CB8002000F8438 /* Encoding.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = Encoding.m; path = "Source/SPMySQLConnection Categories/Encoding.m"; sourceTree = "<group>"; };
		584294FC14CB8002000F8438 /* Server Info.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "Server Info.h"; path = "Source/SPMySQLConnection Categories/Server Info.h"; sourceTree = "<group>"; };
		584294FD14CB8002000F8438 /* Server Info.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = "Server Info.m"; path = "Source/SPMySQLConnection Categories/Server Info.m"; sourceTree = "<group>"; };
		584D812C15057ECD00F24774 /* SPMySQLKeepAliveTimer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLKeepAliveTimer.h; path = Source/SPMySQLKeepAliveTimer.h; sourceTree = "<group>"; };
		584D812D15057ECD00F24774 /* SPMySQLKeepAliveTimer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SPMySQLKeepAliveTimer.m; path = Source/SPMySQLKeepAliveTimer.m; sourceTree = "<group>"; };
		584D82531509775000F24774 /* Copying.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Copying.h; path = "Source/SPMySQLConnection Categories/Copying.h"; sourceTree = "<group>"; };
		584D82541509775000F24774 /* Copying.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = Copying.m; path = "Source/SPMySQLConnection Categories/Copying.m"; sourceTree = "<group>"; };
		584F16A61752911100D150A6 /* SPMySQLStreamingResultStore.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLStreamingResultStore.h; path = Source/SPMySQLStreamingResultStore.h; sourceTree = "<group>"; };
		584F16A71752911100D150A6 /* SPMySQLStreamingResultStore.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SPMySQLStreamingResultStore.m; path = Source/SPMySQLStreamingResultStore.m; sourceTree = "<group>"; };
		586A99F914F02E21007F82BF /* SPMySQLStreamingResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLStreamingResult.h; path = Source/SPMySQLStreamingResult.h; sourceTree = "<group>"; };
		586A99FA14F02E21007F82BF /* SPMySQLStreamingResult.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SPMySQLStreamingResult.m; path = Source/SPMySQLStreamingResult.m; sourceTree = "<group>"; };
		586AA16514F30C5F007F82BF /* Convenience Methods.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "Convenience Methods.h"; path = "Source/SPMySQLResult Categories/Convenience Methods.h"; sourceTree = "<group>"; };
		586AA16614F30C5F007F82BF /* Convenience Methods.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = "Convenience Methods.m"; path = "Source/SPMySQLResult Categories/Convenience Methods.m"; sourceTree = "<group>"; };
		5884127614CC63830078027F /* SPMySQL.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQL.h; path = Source/SPMySQL.h; sourceTree = "<group>"; };
		588412A614CC7A4D0078027F /* Locking.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Locking.h; path = "Source/SPMySQLConnection Categories/Locking.h"; sourceTree = "<group>"; };
		588412A714CC7A4D0078027F /* Locking.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = Locking.m; path = "Source/SPMySQLConnection Categories/Locking.m"; sourceTree = "<group>"; };
		5884133B14CCEC6B0078027F /* libz.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; name = libz.dylib; path = usr/lib/libz.dylib; sourceTree = SDKROOT; };
		5884142514CCF5190078027F /* Conversion.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = Conversion.h; path = "Source/SPMySQLConnection Categories/Conversion.h"; sourceTree = "<group>"; };
		5884142614CCF5190078027F /* Conversion.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = Conversion.m; path = "Source/SPMySQLConnection Categories/Conversion.m"; sourceTree = "<group>"; };
		588414BC14CE3B110078027F /* SPMySQLConnectionDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLConnectionDelegate.h; path = Source/SPMySQLConnectionDelegate.h; sourceTree = "<group>"; };
		5884165314D2306A0078027F /* SPMySQLResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLResult.h; path = Source/SPMySQLResult.h; sourceTree = "<group>"; };
		5884165414D2306A0078027F /* SPMySQLResult.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SPMySQLResult.m; path = Source/SPMySQLResult.m; sourceTree = "<group>"; };
		58C006C714E0B18A00AC489A /* SPMySQLUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLUtilities.h; path = Source/SPMySQLUtilities.h; sourceTree = "<group>"; };
		58C008CC14E2AC7D00AC489A /* SPMySQLConnectionProxy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLConnectionProxy.h; path = Source/SPMySQLConnectionProxy.h; sourceTree = "<group>"; };
		58C009D314E31D3800AC489A /* SPMySQLStringAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLStringAdditions.h; path = Source/SPMySQLStringAdditions.h; sourceTree = "<group>"; };
		58C009D414E31D3800AC489A /* SPMySQLStringAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SPMySQLStringAdditions.m; path = Source/SPMySQLStringAdditions.m; sourceTree = "<group>"; };
		58C00AA714E4869C00AC489A /* Max Packet Size.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "Max Packet Size.h"; path = "Source/SPMySQLConnection Categories/Max Packet Size.h"; sourceTree = "<group>"; };
		58C00AA814E4869C00AC489A /* Max Packet Size.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = "Max Packet Size.m"; path = "Source/SPMySQLConnection Categories/Max Packet Size.m"; sourceTree = "<group>"; };
		58C00AB314E4892E00AC489A /* Delegate & Proxy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "Delegate & Proxy.h"; path = "Source/SPMySQLConnection Categories/Delegate & Proxy.h"; sourceTree = "<group>"; };
		58C00AB414E4892E00AC489A /* Delegate & Proxy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = "Delegate & Proxy.m"; path = "Source/SPMySQLConnection Categories/Delegate & Proxy.m"; sourceTree = "<group>"; };
		58C00AD914E4959A00AC489A /* SystemConfiguration.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SystemConfiguration.framework; path = System/Library/Frameworks/SystemConfiguration.framework; sourceTree = SDKROOT; };
		58C00BCF14E7459600AC489A /* Databases & Tables.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "Databases & Tables.h"; path = "Source/SPMySQLConnection Categories/Databases & Tables.h"; sourceTree = "<group>"; };
		58C00BD014E7459600AC489A /* Databases & Tables.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = "Databases & Tables.m"; path = "Source/SPMySQLConnection Categories/Databases & Tables.m"; sourceTree = "<group>"; };
		58C00CA414E845D800AC489A /* SPMySQL Private APIs.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "SPMySQL Private APIs.h"; path = "Source/SPMySQL Private APIs.h"; sourceTree = "<group>"; };
		58C7C1E214DB6E4C00436315 /* SPMySQLFastStreamingResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLFastStreamingResult.h; path = Source/SPMySQLFastStreamingResult.h; sourceTree = "<group>"; };
		58C7C1E314DB6E4C00436315 /* SPMySQLFastStreamingResult.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SPMySQLFastStreamingResult.m; path = Source/SPMySQLFastStreamingResult.m; sourceTree = "<group>"; };
		58C7C1E614DB6E8600436315 /* Field Definitions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "Field Definitions.h"; path = "Source/SPMySQLResult Categories/Field Definitions.h"; sourceTree = "<group>"; };
		58C7C1E714DB6E8600436315 /* Field Definitions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = "Field Definitions.m"; path = "Source/SPMySQLResult Categories/Field Definitions.m"; sourceTree = "<group>"; };
		58D2A4CF16EDF1C6002EB401 /* SPMySQLEmptyResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = SPMySQLEmptyResult.h; path = Source/SPMySQLEmptyResult.h; sourceTree = "<group>"; };
		58D2A4D016EDF1C6002EB401 /* SPMySQLEmptyResult.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = SPMySQLEmptyResult.m; path = Source/SPMySQLEmptyResult.m; sourceTree = "<group>"; };
		8DC2EF5A0486A6940098B216 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = Resources/Info.plist; sourceTree = "<group>"; };
		8DC2EF5B0486A6940098B216 /* SPMySQL.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = SPMySQL.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		9615D1582D4C18CB0095F55A /* libmysqlclient.24.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; path = libmysqlclient.24.dylib; sourceTree = "<group>"; };
		9615D15B2D4C26DD0095F55A /* libcrypto.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; path = libcrypto.3.dylib; sourceTree = "<group>"; };
		9615D15C2D4C26DD0095F55A /* libssl.3.dylib */ = {isa = PBXFileReference; lastKnownFileType = "compiled.mach-o.dylib"; path = libssl.3.dylib; sourceTree = "<group>"; };
		9615D8362D5EDF530095F55A /* psi_base.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = psi_base.h; sourceTree = "<group>"; };
		9615D8372D5EDF530095F55A /* psi_memory.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = psi_memory.h; sourceTree = "<group>"; };
		9615D8392D5EDF530095F55A /* client_plugin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = client_plugin.h; sourceTree = "<group>"; };
		9615D83A2D5EDF530095F55A /* plugin_auth_common.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = plugin_auth_common.h; sourceTree = "<group>"; };
		9615D83B2D5EDF530095F55A /* udf_registration_types.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = udf_registration_types.h; sourceTree = "<group>"; };
		9615D83D2D5EDF530095F55A /* errmsg.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = errmsg.h; sourceTree = "<group>"; };
		9615D83E2D5EDF530095F55A /* field_types.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = field_types.h; sourceTree = "<group>"; };
		9615D83F2D5EDF530095F55A /* my_alloc.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = my_alloc.h; sourceTree = "<group>"; };
		9615D8402D5EDF530095F55A /* my_command.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = my_command.h; sourceTree = "<group>"; };
		9615D8412D5EDF530095F55A /* my_compress.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = my_compress.h; sourceTree = "<group>"; };
		9615D8422D5EDF530095F55A /* my_list.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = my_list.h; sourceTree = "<group>"; };
		9615D8432D5EDF530095F55A /* mysql.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = mysql.h; sourceTree = "<group>"; };
		9615D8442D5EDF530095F55A /* mysql_com.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = mysql_com.h; sourceTree = "<group>"; };
		9615D8452D5EDF530095F55A /* mysql_time.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = mysql_time.h; sourceTree = "<group>"; };
		9615D8462D5EDF530095F55A /* mysql_version.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = mysql_version.h; sourceTree = "<group>"; };
		9615D8472D5EDF530095F55A /* mysqld_error.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = mysqld_error.h; sourceTree = "<group>"; };
		9615D8482D5EDF530095F55A /* mysqlx_ername.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = mysqlx_ername.h; sourceTree = "<group>"; };
		9615D8492D5EDF530095F55A /* mysqlx_error.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = mysqlx_error.h; sourceTree = "<group>"; };
		9615D84A2D5EDF530095F55A /* mysqlx_version.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = mysqlx_version.h; sourceTree = "<group>"; };
		9615D84B2D5EDF530095F55A /* typelib.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = typelib.h; sourceTree = "<group>"; };
		96A5DDB22D63C89A0079105E /* libc++.tbd */ = {isa = PBXFileReference; lastKnownFileType = "sourcecode.text-based-dylib-definition"; name = "libc++.tbd"; path = "usr/lib/libc++.tbd"; sourceTree = SDKROOT; };
		D2F7E79907B2D74100F64583 /* CoreData.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = CoreData.framework; path = /System/Library/Frameworks/CoreData.framework; sourceTree = "<absolute>"; };
		FD4211942918779400941BFE /* SPMySQLGeometryDataTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SPMySQLGeometryDataTests.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		507FF1D21BC0D7D300104523 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				507FF23B1BC0E8CA00104523 /* SPMySQL.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8DC2EF560486A6940098B216 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				8DC2EF570486A6940098B216 /* Cocoa.framework in Frameworks */,
				96A5DDB32D63C8AE0079105E /* libc++.tbd in Frameworks */,
				9615D15D2D4C26DD0095F55A /* libcrypto.3.dylib in Frameworks */,
				9615D15E2D4C26DD0095F55A /* libssl.3.dylib in Frameworks */,
				9615D1592D4C18CB0095F55A /* libmysqlclient.24.dylib in Frameworks */,
				5884133C14CCEC6B0078027F /* libz.dylib in Frameworks */,
				58C00ADA14E4959A00AC489A /* SystemConfiguration.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		034768DFFF38A50411DB9C8B /* Products */ = {
			isa = PBXGroup;
			children = (
				8DC2EF5B0486A6940098B216 /* SPMySQL.framework */,
				507FF1D51BC0D7D300104523 /* SPMySQL Unit Tests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0867D691FE84028FC02AAC07 /* SPMySQLFramework */ = {
			isa = PBXGroup;
			children = (
				177916A01E88733000EE3043 /* LICENSE */,
				177916A11E88733000EE3043 /* README.md */,
				5884127614CC63830078027F /* SPMySQL.h */,
				58C0077714E1DFFF00AC489A /* Protocols */,
				08FB77AEFE84172EC02AAC07 /* Classes */,
				58C009D214E31D1300AC489A /* Category Additions */,
				32C88DFF0371C24200C91783 /* Other Sources */,
				507FF1801BC0C64100104523 /* Unit Tests */,
				089C1665FE841158C02AAC07 /* Resources */,
				58428DF514BA5A03000F8438 /* Scripts */,
				0867D69AFE84028FC02AAC07 /* Linked Frameworks */,
				96A5DDB12D63C8990079105E /* Frameworks */,
				034768DFFF38A50411DB9C8B /* Products */,
			);
			name = SPMySQLFramework;
			sourceTree = "<group>";
		};
		0867D69AFE84028FC02AAC07 /* Linked Frameworks */ = {
			isa = PBXGroup;
			children = (
				1058C7B1FEA5585E11CA2CBB /* Cocoa.framework */,
				0867D6A5FE840307C02AAC07 /* AppKit.framework */,
				D2F7E79907B2D74100F64583 /* CoreData.framework */,
				0867D69BFE84028FC02AAC07 /* Foundation.framework */,
				58C00AD914E4959A00AC489A /* SystemConfiguration.framework */,
				5884133B14CCEC6B0078027F /* libz.dylib */,
			);
			name = "Linked Frameworks";
			sourceTree = "<group>";
		};
		089C1665FE841158C02AAC07 /* Resources */ = {
			isa = PBXGroup;
			children = (
				5842929214C34B36000F8438 /* MySQL Client Libraries */,
				8DC2EF5A0486A6940098B216 /* Info.plist */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		08FB77AEFE84172EC02AAC07 /* Classes */ = {
			isa = PBXGroup;
			children = (
				58C00CA414E845D800AC489A /* SPMySQL Private APIs.h */,
				58428DFE14BA5FAE000F8438 /* SPMySQLConnection.h */,
				58428DFF14BA5FAE000F8438 /* SPMySQLConnection.m */,
				584294EB14CB8002000F8438 /* Connection Categories */,
				5884165314D2306A0078027F /* SPMySQLResult.h */,
				5884165414D2306A0078027F /* SPMySQLResult.m */,
				58D2A4CF16EDF1C6002EB401 /* SPMySQLEmptyResult.h */,
				58D2A4D016EDF1C6002EB401 /* SPMySQLEmptyResult.m */,
				586A99F914F02E21007F82BF /* SPMySQLStreamingResult.h */,
				586A99FA14F02E21007F82BF /* SPMySQLStreamingResult.m */,
				58C7C1E214DB6E4C00436315 /* SPMySQLFastStreamingResult.h */,
				58C7C1E314DB6E4C00436315 /* SPMySQLFastStreamingResult.m */,
				584F16A61752911100D150A6 /* SPMySQLStreamingResultStore.h */,
				584F16A71752911100D150A6 /* SPMySQLStreamingResultStore.m */,
				58C7C1E114DB6E3000436315 /* Result Categories */,
				580A331B14D75CCF000D6933 /* Result types */,
				584D812C15057ECD00F24774 /* SPMySQLKeepAliveTimer.h */,
				584D812D15057ECD00F24774 /* SPMySQLKeepAliveTimer.m */,
			);
			name = Classes;
			sourceTree = "<group>";
		};
		32C88DFF0371C24200C91783 /* Other Sources */ = {
			isa = PBXGroup;
			children = (
				584294E314CB8002000F8438 /* SPMySQLConstants.h */,
				58C006C714E0B18A00AC489A /* SPMySQLUtilities.h */,
				32DBCF5E0370ADEE00C91783 /* SPMySQLFramework_Prefix.pch */,
				17E3A5791885A286009CF372 /* SPMySQLDataTypes.h */,
				17E3A57A1885A286009CF372 /* SPMySQLDataTypes.m */,
			);
			name = "Other Sources";
			sourceTree = "<group>";
		};
		507FF1801BC0C64100104523 /* Unit Tests */ = {
			isa = PBXGroup;
			children = (
				507FF1D81BC0D7D300104523 /* Info.plist */,
				507FF1811BC0C64100104523 /* DataConversion_Tests.m */,
				507FF23C1BC157B500104523 /* SPMySQLStringAdditions_Tests.m */,
				FD4211942918779400941BFE /* SPMySQLGeometryDataTests.m */,
			);
			name = "Unit Tests";
			path = "SPMySQL Unit Tests";
			sourceTree = "<group>";
		};
		580A331B14D75CCF000D6933 /* Result types */ = {
			isa = PBXGroup;
			children = (
				580A331C14D75CF7000D6933 /* SPMySQLGeometryData.h */,
				580A331D14D75CF7000D6933 /* SPMySQLGeometryData.m */,
			);
			name = "Result types";
			sourceTree = "<group>";
		};
		58428DF514BA5A03000F8438 /* Scripts */ = {
			isa = PBXGroup;
			children = (
				58428DF614BA5A13000F8438 /* build-mysql-client.sh */,
			);
			name = Scripts;
			sourceTree = "<group>";
		};
		5842929214C34B36000F8438 /* MySQL Client Libraries */ = {
			isa = PBXGroup;
			children = (
				5842929C14C34B36000F8438 /* lib */,
				9615D84C2D5EDF530095F55A /* include */,
			);
			path = "MySQL Client Libraries";
			sourceTree = "<group>";
		};
		5842929C14C34B36000F8438 /* lib */ = {
			isa = PBXGroup;
			children = (
				9615D1582D4C18CB0095F55A /* libmysqlclient.24.dylib */,
				9615D15B2D4C26DD0095F55A /* libcrypto.3.dylib */,
				9615D15C2D4C26DD0095F55A /* libssl.3.dylib */,
			);
			path = lib;
			sourceTree = "<group>";
		};
		584294EB14CB8002000F8438 /* Connection Categories */ = {
			isa = PBXGroup;
			children = (
				584D82531509775000F24774 /* Copying.h */,
				584D82541509775000F24774 /* Copying.m */,
				58C00AB314E4892E00AC489A /* Delegate & Proxy.h */,
				58C00AB414E4892E00AC489A /* Delegate & Proxy.m */,
				58C00BCF14E7459600AC489A /* Databases & Tables.h */,
				58C00BD014E7459600AC489A /* Databases & Tables.m */,
				584294F414CB8002000F8438 /* Querying & Preparation.h */,
				584294F514CB8002000F8438 /* Querying & Preparation.m */,
				584294F814CB8002000F8438 /* Encoding.h */,
				584294F914CB8002000F8438 /* Encoding.m */,
				584294FC14CB8002000F8438 /* Server Info.h */,
				584294FD14CB8002000F8438 /* Server Info.m */,
				58C00AA714E4869C00AC489A /* Max Packet Size.h */,
				58C00AA814E4869C00AC489A /* Max Packet Size.m */,
				5884142414CCF4E60078027F /* Private */,
			);
			name = "Connection Categories";
			sourceTree = "<group>";
		};
		5884142414CCF4E60078027F /* Private */ = {
			isa = PBXGroup;
			children = (
				584294EE14CB8002000F8438 /* Ping & KeepAlive.h */,
				584294EF14CB8002000F8438 /* Ping & KeepAlive.m */,
				588412A614CC7A4D0078027F /* Locking.h */,
				588412A714CC7A4D0078027F /* Locking.m */,
				5884142514CCF5190078027F /* Conversion.h */,
				5884142614CCF5190078027F /* Conversion.m */,
			);
			name = Private;
			sourceTree = "<group>";
		};
		58C0077714E1DFFF00AC489A /* Protocols */ = {
			isa = PBXGroup;
			children = (
				588414BC14CE3B110078027F /* SPMySQLConnectionDelegate.h */,
				583C734917A489CC0056B284 /* SPMySQLStreamingResultStoreDelegate.h */,
				58C008CC14E2AC7D00AC489A /* SPMySQLConnectionProxy.h */,
			);
			name = Protocols;
			sourceTree = "<group>";
		};
		58C009D214E31D1300AC489A /* Category Additions */ = {
			isa = PBXGroup;
			children = (
				1A96314D25B9CE9900BF2E91 /* SPMySQLMutableDictionaryAdditions.h */,
				1A96314C25B9CE9900BF2E91 /* SPMySQLMutableDictionaryAdditions.m */,
				1A96314425B9CE6600BF2E91 /* SPMySQLArrayAdditions.h */,
				1A96314525B9CE6600BF2E91 /* SPMySQLArrayAdditions.m */,
				58C009D314E31D3800AC489A /* SPMySQLStringAdditions.h */,
				58C009D414E31D3800AC489A /* SPMySQLStringAdditions.m */,
			);
			name = "Category Additions";
			sourceTree = "<group>";
		};
		58C7C1E114DB6E3000436315 /* Result Categories */ = {
			isa = PBXGroup;
			children = (
				583C734B17B0778A0056B284 /* Data Conversion.h */,
				583C734C17B0778A0056B284 /* Data Conversion.m */,
				58C7C1E614DB6E8600436315 /* Field Definitions.h */,
				58C7C1E714DB6E8600436315 /* Field Definitions.m */,
				586AA16514F30C5F007F82BF /* Convenience Methods.h */,
				586AA16614F30C5F007F82BF /* Convenience Methods.m */,
			);
			name = "Result Categories";
			sourceTree = "<group>";
		};
		9615D8382D5EDF530095F55A /* psi */ = {
			isa = PBXGroup;
			children = (
				9615D8362D5EDF530095F55A /* psi_base.h */,
				9615D8372D5EDF530095F55A /* psi_memory.h */,
			);
			path = psi;
			sourceTree = "<group>";
		};
		9615D83C2D5EDF530095F55A /* mysql */ = {
			isa = PBXGroup;
			children = (
				9615D8382D5EDF530095F55A /* psi */,
				9615D8392D5EDF530095F55A /* client_plugin.h */,
				9615D83A2D5EDF530095F55A /* plugin_auth_common.h */,
				9615D83B2D5EDF530095F55A /* udf_registration_types.h */,
			);
			path = mysql;
			sourceTree = "<group>";
		};
		9615D84C2D5EDF530095F55A /* include */ = {
			isa = PBXGroup;
			children = (
				9615D83C2D5EDF530095F55A /* mysql */,
				9615D83D2D5EDF530095F55A /* errmsg.h */,
				9615D83E2D5EDF530095F55A /* field_types.h */,
				9615D83F2D5EDF530095F55A /* my_alloc.h */,
				9615D8402D5EDF530095F55A /* my_command.h */,
				9615D8412D5EDF530095F55A /* my_compress.h */,
				9615D8422D5EDF530095F55A /* my_list.h */,
				9615D8432D5EDF530095F55A /* mysql.h */,
				9615D8442D5EDF530095F55A /* mysql_com.h */,
				9615D8452D5EDF530095F55A /* mysql_time.h */,
				9615D8462D5EDF530095F55A /* mysql_version.h */,
				9615D8472D5EDF530095F55A /* mysqld_error.h */,
				9615D8482D5EDF530095F55A /* mysqlx_ername.h */,
				9615D8492D5EDF530095F55A /* mysqlx_error.h */,
				9615D84A2D5EDF530095F55A /* mysqlx_version.h */,
				9615D84B2D5EDF530095F55A /* typelib.h */,
			);
			path = include;
			sourceTree = "<group>";
		};
		96A5DDB12D63C8990079105E /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				96A5DDB22D63C89A0079105E /* libc++.tbd */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		8DC2EF500486A6940098B216 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = **********;
			files = (
				58428E0014BA5FAE000F8438 /* SPMySQLConnection.h in Headers */,
				584294E414CB8002000F8438 /* SPMySQLConstants.h in Headers */,
				1A96314625B9CE6600BF2E91 /* SPMySQLArrayAdditions.h in Headers */,
				584294F614CB8002000F8438 /* Querying & Preparation.h in Headers */,
				9615D84D2D5EDF530095F55A /* psi_base.h in Headers */,
				9615D84E2D5EDF530095F55A /* psi_memory.h in Headers */,
				9615D84F2D5EDF530095F55A /* client_plugin.h in Headers */,
				9615D8502D5EDF530095F55A /* plugin_auth_common.h in Headers */,
				9615D8512D5EDF530095F55A /* udf_registration_types.h in Headers */,
				9615D8522D5EDF530095F55A /* errmsg.h in Headers */,
				9615D8532D5EDF530095F55A /* field_types.h in Headers */,
				9615D8542D5EDF530095F55A /* my_alloc.h in Headers */,
				9615D8552D5EDF530095F55A /* my_command.h in Headers */,
				9615D8562D5EDF530095F55A /* my_compress.h in Headers */,
				9615D8572D5EDF530095F55A /* my_list.h in Headers */,
				9615D8582D5EDF530095F55A /* mysql.h in Headers */,
				9615D8592D5EDF530095F55A /* mysql_com.h in Headers */,
				9615D85A2D5EDF530095F55A /* mysql_time.h in Headers */,
				9615D85B2D5EDF530095F55A /* mysql_version.h in Headers */,
				9615D85C2D5EDF530095F55A /* mysqld_error.h in Headers */,
				9615D85D2D5EDF530095F55A /* mysqlx_ername.h in Headers */,
				9615D85E2D5EDF530095F55A /* mysqlx_error.h in Headers */,
				9615D85F2D5EDF530095F55A /* mysqlx_version.h in Headers */,
				9615D8602D5EDF530095F55A /* typelib.h in Headers */,
				584294F014CB8002000F8438 /* Ping & KeepAlive.h in Headers */,
				584294FA14CB8002000F8438 /* Encoding.h in Headers */,
				58C7C1E414DB6E4C00436315 /* SPMySQLFastStreamingResult.h in Headers */,
				584294FE14CB8002000F8438 /* Server Info.h in Headers */,
				583C734A17A489CC0056B284 /* SPMySQLStreamingResultStoreDelegate.h in Headers */,
				584F16A81752911200D150A6 /* SPMySQLStreamingResultStore.h in Headers */,
				1A96314F25B9CE9900BF2E91 /* SPMySQLMutableDictionaryAdditions.h in Headers */,
				5884127714CC63830078027F /* SPMySQL.h in Headers */,
				588412A814CC7A4D0078027F /* Locking.h in Headers */,
				5884142714CCF5190078027F /* Conversion.h in Headers */,
				588414BD14CE3B110078027F /* SPMySQLConnectionDelegate.h in Headers */,
				17E3A57B1885A286009CF372 /* SPMySQLDataTypes.h in Headers */,
				5884165514D2306A0078027F /* SPMySQLResult.h in Headers */,
				580A331E14D75CF7000D6933 /* SPMySQLGeometryData.h in Headers */,
				58C7C1E814DB6E8600436315 /* Field Definitions.h in Headers */,
				58C006C814E0B18A00AC489A /* SPMySQLUtilities.h in Headers */,
				58C008CD14E2AC7D00AC489A /* SPMySQLConnectionProxy.h in Headers */,
				58C009D514E31D3800AC489A /* SPMySQLStringAdditions.h in Headers */,
				58C00AA914E4869C00AC489A /* Max Packet Size.h in Headers */,
				58C00AB514E4892E00AC489A /* Delegate & Proxy.h in Headers */,
				58C00BD114E7459600AC489A /* Databases & Tables.h in Headers */,
				58C00CA514E845D800AC489A /* SPMySQL Private APIs.h in Headers */,
				586A99FB14F02E21007F82BF /* SPMySQLStreamingResult.h in Headers */,
				586AA16714F30C5F007F82BF /* Convenience Methods.h in Headers */,
				584D812E15057ECD00F24774 /* SPMySQLKeepAliveTimer.h in Headers */,
				584D82551509775000F24774 /* Copying.h in Headers */,
				58D2A4D116EDF1C6002EB401 /* SPMySQLEmptyResult.h in Headers */,
				583C734D17B0778A0056B284 /* Data Conversion.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		507FF1D41BC0D7D300104523 /* SPMySQL Unit Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 507FF1DE1BC0D7D300104523 /* Build configuration list for PBXNativeTarget "SPMySQL Unit Tests" */;
			buildPhases = (
				507FF1D11BC0D7D300104523 /* Sources */,
				507FF1D21BC0D7D300104523 /* Frameworks */,
				507FF1D31BC0D7D300104523 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				507FF23A1BC0E8AF00104523 /* PBXTargetDependency */,
			);
			name = "SPMySQL Unit Tests";
			productName = "SPMySQL Unit Tests";
			productReference = 507FF1D51BC0D7D300104523 /* SPMySQL Unit Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		8DC2EF4F0486A6940098B216 /* SPMySQL.framework */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1DEB91AD08733DA50010E9CD /* Build configuration list for PBXNativeTarget "SPMySQL.framework" */;
			buildPhases = (
				8DC2EF500486A6940098B216 /* Headers */,
				8DC2EF520486A6940098B216 /* Resources */,
				8DC2EF540486A6940098B216 /* Sources */,
				26F0BF1024A0052100A43B20 /* CopyFiles */,
				26F0BF1424A0069300A43B20 /* ShellScript */,
				8DC2EF560486A6940098B216 /* Frameworks */,
				26F0BF1324A0053E00A43B20 /* ShellScript */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SPMySQL.framework;
			productInstallPath = "$(HOME)/Library/Frameworks";
			productName = SPMySQLFramework;
			productReference = 8DC2EF5B0486A6940098B216 /* SPMySQL.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0867D690FE84028FC02AAC07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1320;
				TargetAttributes = {
					507FF1D41BC0D7D300104523 = {
						CreatedOnToolsVersion = 6.2;
					};
					8DC2EF4F0486A6940098B216 = {
						DevelopmentTeam = NKQ4HJ66PX;
						ProvisioningStyle = Automatic;
					};
				};
			};
			buildConfigurationList = 1DEB91B108733DA50010E9CD /* Build configuration list for PBXProject "SPMySQLFramework" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 1;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 0867D691FE84028FC02AAC07 /* SPMySQLFramework */;
			productRefGroup = 034768DFFF38A50411DB9C8B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8DC2EF4F0486A6940098B216 /* SPMySQL.framework */,
				507FF1D41BC0D7D300104523 /* SPMySQL Unit Tests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		507FF1D31BC0D7D300104523 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8DC2EF520486A6940098B216 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				177916A21E88733000EE3043 /* LICENSE in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		26F0BF1324A0053E00A43B20 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "# Create symlinks for all re-exported dylibs, at the framework's top level. They must be also relative to the framework directory.\ncd \"$CONFIGURATION_BUILD_DIR/$WRAPPER_NAME\"\n# ln -Ffsv Versions/Current/lib*.dylib .\n# ln -Ffsv Versions/Current/mysqlplugins .\n";
		};
		26F0BF1424A0069300A43B20 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			alwaysOutOfDate = 1;
			buildActionMask = 12;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "#!/usr/bin/env bash\n\n# always need to do this\n# Make it so the SPMySQL executable can find the dylibs it was compiled with\necho \"running install_name_tool\"\npwd\ncd \"$CONFIGURATION_BUILD_DIR/$WRAPPER_NAME/Versions/Current\" || exit 1;\npwd\notool -L SPMySQL\n\nCRYPTO=$(otool -L SPMySQL | grep libcrypto.3.dylib | cut -d' ' -f1 | head -1)\nCRYPTO=\"${CRYPTO#\"${CRYPTO%%[![:space:]]*}\"}\"  # remove leading whitespace characters\nCRYPTO=\"${CRYPTO%\"${CRYPTO##*[![:space:]]}\"}\"   # remove trailing whitespace characters\necho \"CRYPTO: $CRYPTO\"\necho \"Setting @loader_path/libcrypto.3.dylib in SPMySQL\"\ninstall_name_tool -change \"$CRYPTO\" @loader_path/libcrypto.3.dylib SPMySQL\notool -L SPMySQL\n\nSSL=$(otool -L SPMySQL | grep libssl.3.dylib | cut -d' ' -f1 | head -1)\nSSL=\"${SSL#\"${SSL%%[![:space:]]*}\"}\"  \nSSL=\"${SSL%\"${SSL##*[![:space:]]}\"}\"  \necho \"SSL: $SSL\"\necho \"Setting @loader_path/libssl.3.dylib in SPMySQL\"\ninstall_name_tool -change \"$SSL\" @loader_path/libssl.3.dylib SPMySQL\notool -L SPMySQL\n\nMSL=$(otool -L SPMySQL | grep libmysqlclient.24.dylib | cut -d' ' -f1 | head -1)\nMSL=\"${MSL#\"${MSL%%[![:space:]]*}\"}\"  \nMSL=\"${MSL%\"${MSL##*[![:space:]]}\"}\"  \necho \"MSL: $MSL\"\necho \"Setting @loader_path/libmysqlclient.24.dylib in SPMySQL\"\ninstall_name_tool -change \"$MSL\" @loader_path/libmysqlclient.24.dylib SPMySQL\notool -L SPMySQL\n\necho \"checking for new dylibs\"\ncd \"$SRCROOT\" || exit 1;\npwd\n\nNEW_LIBS=$(git diff --name-only --diff-filter=ACM -- *.dylib)\n\nif  [ -z \"$NEW_LIBS\" ]; then\n     echo \"no new dylibs, stopping\"\n     exit 0;\n fi\n\ncd \"$CONFIGURATION_BUILD_DIR/$WRAPPER_NAME/Versions/A\" || exit 1;\necho \"Setting libcrypto.3.dylib in libcrypto.3.dylib\"\ninstall_name_tool -id \"libcrypto.3.dylib\" libcrypto.3.dylib\ninstall_name_tool -id \"libssl.3.dylib\" libssl.3.dylib\ninstall_name_tool -id \"libmysqlclient.24.dylib\" libmysqlclient.24.dylib\n\nwhile true; do\n    CRYPTO=$(otool -L libssl.3.dylib | grep -v \"@loader_path\" | grep libcrypto.3.dylib | cut -d' ' -f1 | head -1)\n    CRYPTO=\"${CRYPTO#\"${CRYPTO%%[![:space:]]*}\"}\"  \n    CRYPTO=\"${CRYPTO%\"${CRYPTO##*[![:space:]]}\"}\"     \n\n    # Exit the loop if CRYPTO is empty\n    if [[ -z \"$CRYPTO\" ]]; then\n        break\n    fi\n\n    echo \"CRYPTO: $CRYPTO\"\n    echo \"Setting @loader_path/libcrypto.3.dylib in libssl.3.dylib (replacing $CRYPTO)\"\n    install_name_tool -change \"$CRYPTO\" @loader_path/libcrypto.3.dylib libssl.3.dylib\ndone;\n\notool -L lib*\n\nwhile true; do\n    SSL=$(otool -L libmysqlclient.24.dylib | grep -v \"@loader_path\" | grep libssl.3.dylib | cut -d' ' -f1 | head -1)\n    SSL=\"${SSL#\"${SSL%%[![:space:]]*}\"}\"  \n    SSL=\"${SSL%\"${SSL##*[![:space:]]}\"}\"   \n\n    # Exit the loop if CRYPTO is empty\n    if [[ -z \"$SSL\" ]]; then\n        break\n    fi\n\n    echo \"Setting @loader_path/libssl.3.dylib in libmysqlclient.24.dylib (replacing $SSL)\"\n    install_name_tool -change \"$SSL\" @loader_path/libssl.3.dylib libmysqlclient.24.dylib\ndone;\n\nwhile true; do\n    CRYPTO=$(otool -L libmysqlclient.24.dylib | grep -v \"@loader_path\" | grep libcrypto.3.dylib | cut -d' ' -f1 | head -1)\n    CRYPTO=\"${CRYPTO#\"${CRYPTO%%[![:space:]]*}\"}\"  \n    CRYPTO=\"${CRYPTO%\"${CRYPTO##*[![:space:]]}\"}\" \n\n    # Exit the loop if CRYPTO is empty\n    if [[ -z \"$CRYPTO\" ]]; then\n        break\n    fi\n\n    echo \"Setting @loader_path/libcrypto.3.dylib in libmysqlclient.24.dylib (replacing $CRYPTO)\"\n    install_name_tool -change \"$CRYPTO\" @loader_path/libcrypto.3.dylib libmysqlclient.24.dylib  \ndone\n\n# no need for exit, if there are no new libs, we've already exited.\ncd \"$CONFIGURATION_BUILD_DIR/$WRAPPER_NAME/Versions/Current\" || exit 1;\n\necho \"Codesigning libcrypto.3.dylib, libssl.3.dylib, and libmysqlclient.24.dylib\"\n/usr/bin/codesign --force --sign \"Apple Development\" -o runtime --timestamp=none --preserve-metadata=identifier,entitlements,flags \"libcrypto.3.dylib\"\n/usr/bin/codesign --force --sign \"Apple Development\" -o runtime --timestamp=none --preserve-metadata=identifier,entitlements,flags \"libssl.3.dylib\"\n/usr/bin/codesign --force --sign \"Apple Development\" -o runtime --timestamp=none --preserve-metadata=identifier,entitlements,flags \"libmysqlclient.24.dylib\"\n\necho \"Copying libcrypto.3.dylib, libssl.3.dylib, and libmysqlclient.24.dylib to $SRCROOT/MySQL Client Libraries/lib\"\ncp libcrypto.3.dylib \"$SRCROOT/MySQL Client Libraries/lib\" || exit 1;\ncp libssl.3.dylib \"$SRCROOT/MySQL Client Libraries/lib\" || exit 1;\ncp libmysqlclient.24.dylib \"$SRCROOT/MySQL Client Libraries/lib\" || exit 1;\n";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		507FF1D11BC0D7D300104523 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				507FF23D1BC157B500104523 /* SPMySQLStringAdditions_Tests.m in Sources */,
				FD4211952918779400941BFE /* SPMySQLGeometryDataTests.m in Sources */,
				507FF1E51BC0D82300104523 /* DataConversion_Tests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8DC2EF540486A6940098B216 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				58428E0114BA5FAE000F8438 /* SPMySQLConnection.m in Sources */,
				584294F114CB8002000F8438 /* Ping & KeepAlive.m in Sources */,
				584294FB14CB8002000F8438 /* Encoding.m in Sources */,
				584294FF14CB8002000F8438 /* Server Info.m in Sources */,
				5884142814CCF5190078027F /* Conversion.m in Sources */,
				5884159514D1A6880078027F /* Querying & Preparation.m in Sources */,
				5884159414D1A6760078027F /* Locking.m in Sources */,
				1A96314E25B9CE9900BF2E91 /* SPMySQLMutableDictionaryAdditions.m in Sources */,
				17E3A57C1885A286009CF372 /* SPMySQLDataTypes.m in Sources */,
				5884165614D2306A0078027F /* SPMySQLResult.m in Sources */,
				580A331F14D75CF7000D6933 /* SPMySQLGeometryData.m in Sources */,
				58C7C1E514DB6E4C00436315 /* SPMySQLFastStreamingResult.m in Sources */,
				58C7C1E914DB6E8600436315 /* Field Definitions.m in Sources */,
				58C009D614E31D3800AC489A /* SPMySQLStringAdditions.m in Sources */,
				58C00AAA14E4869C00AC489A /* Max Packet Size.m in Sources */,
				58C00AB614E4892E00AC489A /* Delegate & Proxy.m in Sources */,
				58C00BD214E7459600AC489A /* Databases & Tables.m in Sources */,
				586A99FC14F02E21007F82BF /* SPMySQLStreamingResult.m in Sources */,
				1A96314725B9CE6600BF2E91 /* SPMySQLArrayAdditions.m in Sources */,
				586AA16814F30C5F007F82BF /* Convenience Methods.m in Sources */,
				584D812F15057ECD00F24774 /* SPMySQLKeepAliveTimer.m in Sources */,
				584D82561509775000F24774 /* Copying.m in Sources */,
				58D2A4D216EDF1C6002EB401 /* SPMySQLEmptyResult.m in Sources */,
				584F16A91752911200D150A6 /* SPMySQLStreamingResultStore.m in Sources */,
				583C734E17B0778A0056B284 /* Data Conversion.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		507FF23A1BC0E8AF00104523 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8DC2EF4F0486A6940098B216 /* SPMySQL.framework */;
			targetProxy = 507FF2391BC0E8AF00104523 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		1DEB91AE08733DA50010E9CD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = NKQ4HJ66PX;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 20095;
				FRAMEWORK_VERSION = A;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Source/SPMySQLFramework_Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = "DEBUG=1";
				GENERATE_PKGINFO_FILE = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INSTALL_PATH = "@executable_path/../Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SRCROOT)/MySQL Client Libraries/lib\"",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = "-lc++";
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.spmysql";
				PRODUCT_NAME = SPMySQL;
				PROVISIONING_PROFILE_SPECIFIER = "";
				REEXPORTED_LIBRARY_NAMES = "crypto.3 ssl.3";
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = framework;
			};
			name = Debug;
		};
		1DEB91AF08733DA50010E9CD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = NKQ4HJ66PX;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 20095;
				FRAMEWORK_VERSION = A;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Source/SPMySQLFramework_Prefix.pch;
				INFOPLIST_FILE = Resources/Info.plist;
				INSTALL_PATH = "@executable_path/../Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SRCROOT)/MySQL Client Libraries/lib\"",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = "-lc++";
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.spmysql";
				PRODUCT_NAME = SPMySQL;
				PROVISIONING_PROFILE_SPECIFIER = "";
				REEXPORTED_LIBRARY_NAMES = "crypto.3 ssl.3";
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = framework;
			};
			name = Release;
		};
		1DEB91B208733DA50010E9CD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_LINK_OBJC_RUNTIME = NO;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CURRENT_PROJECT_VERSION = 20095;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_STRICT_SELECTOR_MATCH = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACH_O_TYPE = mh_dylib;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		1DEB91B308733DA50010E9CD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_LINK_OBJC_RUNTIME = NO;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CURRENT_PROJECT_VERSION = 20095;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_STRICT_SELECTOR_MATCH = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACH_O_TYPE = mh_dylib;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				SDKROOT = macosx;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		507FF1DF1BC0D7D300104523 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(DEVELOPER_FRAMEWORKS_DIR)",
					"$(inherited)",
				);
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				INFOPLIST_FILE = "SPMySQL Unit Tests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.spmysql-unittests";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Debug;
		};
		507FF1E11BC0D7D300104523 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(DEVELOPER_FRAMEWORKS_DIR)",
					"$(inherited)",
				);
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				INFOPLIST_FILE = "SPMySQL Unit Tests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.spmysql-unittests";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Release;
		};
		507FF1E21BC0D7D300104523 /* Distribution */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(DEVELOPER_FRAMEWORKS_DIR)",
					"$(inherited)",
				);
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				INFOPLIST_FILE = "SPMySQL Unit Tests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.spmysql-unittests";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Distribution;
		};
		507FF2361BC0E0A800104523 /* Unit Testing */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_LINK_OBJC_RUNTIME = NO;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CURRENT_PROJECT_VERSION = 20095;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = "";
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_STRICT_SELECTOR_MATCH = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACH_O_TYPE = mh_dylib;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = "Unit Testing";
		};
		507FF2371BC0E0A800104523 /* Unit Testing */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = NKQ4HJ66PX;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 20095;
				FRAMEWORK_VERSION = A;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Source/SPMySQLFramework_Prefix.pch;
				GCC_PREPROCESSOR_DEFINITIONS = SPMYSQL_FOR_UNIT_TESTING;
				GENERATE_PKGINFO_FILE = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INSTALL_PATH = "@executable_path/../Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SRCROOT)/MySQL Client Libraries/lib\"",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = "-lc++";
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.spmysql";
				PRODUCT_NAME = SPMySQL;
				PROVISIONING_PROFILE_SPECIFIER = "";
				REEXPORTED_LIBRARY_NAMES = "crypto.3 ssl.3";
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = framework;
			};
			name = "Unit Testing";
		};
		507FF2381BC0E0A800104523 /* Unit Testing */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(DEVELOPER_FRAMEWORKS_DIR)",
					"$(inherited)",
				);
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				INFOPLIST_FILE = "SPMySQL Unit Tests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.spmysql-unittests";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = "Unit Testing";
		};
		586AA55214F5D599007F82BF /* Distribution */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_LINK_OBJC_RUNTIME = NO;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CURRENT_PROJECT_VERSION = 20095;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_STRICT_SELECTOR_MATCH = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACH_O_TYPE = mh_dylib;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				SDKROOT = macosx;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Distribution;
		};
		586AA55314F5D599007F82BF /* Distribution */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = NKQ4HJ66PX;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 20095;
				FRAMEWORK_VERSION = A;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Source/SPMySQLFramework_Prefix.pch;
				INFOPLIST_FILE = Resources/Info.plist;
				INSTALL_PATH = "@executable_path/../Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SRCROOT)/MySQL Client Libraries/lib\"",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = "-lc++";
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.spmysql";
				PRODUCT_NAME = SPMySQL;
				PROVISIONING_PROFILE_SPECIFIER = "";
				REEXPORTED_LIBRARY_NAMES = "crypto.3 ssl.3";
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = framework;
			};
			name = Distribution;
		};
		96BC47D62500ACD0003C8105 /* Beta */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_LINK_OBJC_RUNTIME = NO;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CURRENT_PROJECT_VERSION = 20095;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = NO;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_STRICT_SELECTOR_MATCH = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACH_O_TYPE = mh_dylib;
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				SDKROOT = macosx;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Beta;
		};
		96BC47D72500ACD0003C8105 /* Beta */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				DEAD_CODE_STRIPPING = YES;
				DEVELOPMENT_TEAM = NKQ4HJ66PX;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 20095;
				FRAMEWORK_VERSION = A;
				GCC_GENERATE_DEBUGGING_SYMBOLS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = Source/SPMySQLFramework_Prefix.pch;
				INFOPLIST_FILE = Resources/Info.plist;
				INSTALL_PATH = "@executable_path/../Frameworks";
				LD_RUNPATH_SEARCH_PATHS = "";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"\"$(SRCROOT)/MySQL Client Libraries/lib\"",
				);
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				OTHER_LDFLAGS = "-lc++";
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.spmysql";
				PRODUCT_NAME = SPMySQL;
				PROVISIONING_PROFILE_SPECIFIER = "";
				REEXPORTED_LIBRARY_NAMES = "crypto.3 ssl.3";
				SKIP_INSTALL = YES;
				WRAPPER_EXTENSION = framework;
			};
			name = Beta;
		};
		96BC47D82500ACD0003C8105 /* Beta */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				FRAMEWORK_SEARCH_PATHS = (
					"$(DEVELOPER_FRAMEWORKS_DIR)",
					"$(inherited)",
				);
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				INFOPLIST_FILE = "SPMySQL Unit Tests/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
					"@loader_path/../Frameworks",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.spmysql-unittests";
				PRODUCT_NAME = "$(TARGET_NAME)";
			};
			name = Beta;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1DEB91AD08733DA50010E9CD /* Build configuration list for PBXNativeTarget "SPMySQL.framework" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1DEB91AE08733DA50010E9CD /* Debug */,
				507FF2371BC0E0A800104523 /* Unit Testing */,
				1DEB91AF08733DA50010E9CD /* Release */,
				586AA55314F5D599007F82BF /* Distribution */,
				96BC47D72500ACD0003C8105 /* Beta */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1DEB91B108733DA50010E9CD /* Build configuration list for PBXProject "SPMySQLFramework" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1DEB91B208733DA50010E9CD /* Debug */,
				507FF2361BC0E0A800104523 /* Unit Testing */,
				1DEB91B308733DA50010E9CD /* Release */,
				586AA55214F5D599007F82BF /* Distribution */,
				96BC47D62500ACD0003C8105 /* Beta */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		507FF1DE1BC0D7D300104523 /* Build configuration list for PBXNativeTarget "SPMySQL Unit Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				507FF1DF1BC0D7D300104523 /* Debug */,
				507FF2381BC0E0A800104523 /* Unit Testing */,
				507FF1E11BC0D7D300104523 /* Release */,
				507FF1E21BC0D7D300104523 /* Distribution */,
				96BC47D82500ACD0003C8105 /* Beta */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0867D690FE84028FC02AAC07 /* Project object */;
}
