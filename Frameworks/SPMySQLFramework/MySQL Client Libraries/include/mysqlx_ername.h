/*
 * Copyright (c) 2016, 2024, Oracle and/or its affiliates.
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License, version 2.0,
 * as published by the Free Software Foundation.
 *
 * This program is designed to work with certain software (including
 * but not limited to OpenSSL) that is licensed under separate terms,
 * as designated in a particular file or component or in included license
 * documentation.  The authors of MySQL hereby grant you an additional
 * permission to link the program and your derivative works with the
 * separately licensed software that they have either included with
 * the program or referenced in the documentation.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License, version 2.0, for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, write to the Free Software
 * Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301  USA
 */

/* Autogenerated file, please don't edit */

#include "mysqlx_error.h"

  {"ER_X_BAD_MESSAGE", ER_X_BAD_MESSAGE, "", nullptr, nullptr, 0 },
  {"ER_X_CAPABILITIES_PREPARE_FAILED", ER_X_CAPABILITIES_PREPARE_FAILED, "", nullptr, nullptr, 0 },
  {"ER_X_CAPABILITY_NOT_FOUND", ER_X_CAPABILITY_NOT_FOUND, "", nullptr, nullptr, 0 },
  {"ER_X_INVALID_PROTOCOL_DATA", ER_X_INVALID_PROTOCOL_DATA, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_VALUE_LENGTH", ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_VALUE_LENGTH, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_KEY_LENGTH", ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_KEY_LENGTH, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_EMPTY_KEY", ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_EMPTY_KEY, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_LENGTH", ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_LENGTH, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_TYPE", ER_X_BAD_CONNECTION_SESSION_ATTRIBUTE_TYPE, "", nullptr, nullptr, 0 },
  {"ER_X_CAPABILITY_SET_NOT_ALLOWED", ER_X_CAPABILITY_SET_NOT_ALLOWED, "", nullptr, nullptr, 0 },
  {"ER_X_SERVICE_ERROR", ER_X_SERVICE_ERROR, "", nullptr, nullptr, 0 },
  {"ER_X_SESSION", ER_X_SESSION, "", nullptr, nullptr, 0 },
  {"ER_X_INVALID_ARGUMENT", ER_X_INVALID_ARGUMENT, "", nullptr, nullptr, 0 },
  {"ER_X_MISSING_ARGUMENT", ER_X_MISSING_ARGUMENT, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_INSERT_DATA", ER_X_BAD_INSERT_DATA, "", nullptr, nullptr, 0 },
  {"ER_X_CMD_NUM_ARGUMENTS", ER_X_CMD_NUM_ARGUMENTS, "", nullptr, nullptr, 0 },
  {"ER_X_CMD_ARGUMENT_TYPE", ER_X_CMD_ARGUMENT_TYPE, "", nullptr, nullptr, 0 },
  {"ER_X_CMD_ARGUMENT_VALUE", ER_X_CMD_ARGUMENT_VALUE, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_UPSERT_DATA", ER_X_BAD_UPSERT_DATA, "", nullptr, nullptr, 0 },
  {"ER_X_DUPLICATED_CAPABILITIES", ER_X_DUPLICATED_CAPABILITIES, "", nullptr, nullptr, 0 },
  {"ER_X_CMD_ARGUMENT_OBJECT_EMPTY", ER_X_CMD_ARGUMENT_OBJECT_EMPTY, "", nullptr, nullptr, 0 },
  {"ER_X_CMD_INVALID_ARGUMENT", ER_X_CMD_INVALID_ARGUMENT, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_UPDATE_DATA", ER_X_BAD_UPDATE_DATA, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_TYPE_OF_UPDATE", ER_X_BAD_TYPE_OF_UPDATE, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_COLUMN_TO_UPDATE", ER_X_BAD_COLUMN_TO_UPDATE, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_MEMBER_TO_UPDATE", ER_X_BAD_MEMBER_TO_UPDATE, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_STATEMENT_ID", ER_X_BAD_STATEMENT_ID, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_CURSOR_ID", ER_X_BAD_CURSOR_ID, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_SCHEMA", ER_X_BAD_SCHEMA, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_TABLE", ER_X_BAD_TABLE, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_PROJECTION", ER_X_BAD_PROJECTION, "", nullptr, nullptr, 0 },
  {"ER_X_DOC_ID_MISSING", ER_X_DOC_ID_MISSING, "", nullptr, nullptr, 0 },
  {"ER_X_DUPLICATE_ENTRY", ER_X_DUPLICATE_ENTRY, "", nullptr, nullptr, 0 },
  {"ER_X_DOC_REQUIRED_FIELD_MISSING", ER_X_DOC_REQUIRED_FIELD_MISSING, "", nullptr, nullptr, 0 },
  {"ER_X_PROJ_BAD_KEY_NAME", ER_X_PROJ_BAD_KEY_NAME, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_DOC_PATH", ER_X_BAD_DOC_PATH, "", nullptr, nullptr, 0 },
  {"ER_X_CURSOR_EXISTS", ER_X_CURSOR_EXISTS, "", nullptr, nullptr, 0 },
  {"ER_X_CURSOR_REACHED_EOF", ER_X_CURSOR_REACHED_EOF, "", nullptr, nullptr, 0 },
  {"ER_X_PREPARED_STATMENT_CAN_HAVE_ONE_CURSOR", ER_X_PREPARED_STATMENT_CAN_HAVE_ONE_CURSOR, "", nullptr, nullptr, 0 },
  {"ER_X_PREPARED_EXECUTE_ARGUMENT_NOT_SUPPORTED", ER_X_PREPARED_EXECUTE_ARGUMENT_NOT_SUPPORTED, "", nullptr, nullptr, 0 },
  {"ER_X_PREPARED_EXECUTE_ARGUMENT_CONSISTENCY", ER_X_PREPARED_EXECUTE_ARGUMENT_CONSISTENCY, "", nullptr, nullptr, 0 },
  {"ER_X_EXPR_BAD_OPERATOR", ER_X_EXPR_BAD_OPERATOR, "", nullptr, nullptr, 0 },
  {"ER_X_EXPR_BAD_NUM_ARGS", ER_X_EXPR_BAD_NUM_ARGS, "", nullptr, nullptr, 0 },
  {"ER_X_EXPR_MISSING_ARG", ER_X_EXPR_MISSING_ARG, "", nullptr, nullptr, 0 },
  {"ER_X_EXPR_BAD_TYPE_VALUE", ER_X_EXPR_BAD_TYPE_VALUE, "", nullptr, nullptr, 0 },
  {"ER_X_EXPR_BAD_VALUE", ER_X_EXPR_BAD_VALUE, "", nullptr, nullptr, 0 },
  {"ER_X_INVALID_COLLECTION", ER_X_INVALID_COLLECTION, "", nullptr, nullptr, 0 },
  {"ER_X_INVALID_ADMIN_COMMAND", ER_X_INVALID_ADMIN_COMMAND, "", nullptr, nullptr, 0 },
  {"ER_X_EXPECT_NOT_OPEN", ER_X_EXPECT_NOT_OPEN, "", nullptr, nullptr, 0 },
  {"ER_X_EXPECT_NO_ERROR_FAILED", ER_X_EXPECT_NO_ERROR_FAILED, "", nullptr, nullptr, 0 },
  {"ER_X_EXPECT_BAD_CONDITION", ER_X_EXPECT_BAD_CONDITION, "", nullptr, nullptr, 0 },
  {"ER_X_EXPECT_BAD_CONDITION_VALUE", ER_X_EXPECT_BAD_CONDITION_VALUE, "", nullptr, nullptr, 0 },
  {"ER_X_INVALID_NAMESPACE", ER_X_INVALID_NAMESPACE, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_NOTICE", ER_X_BAD_NOTICE, "", nullptr, nullptr, 0 },
  {"ER_X_CANNOT_DISABLE_NOTICE", ER_X_CANNOT_DISABLE_NOTICE, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_CONFIGURATION", ER_X_BAD_CONFIGURATION, "", nullptr, nullptr, 0 },
  {"ER_X_MYSQLX_ACCOUNT_MISSING_PERMISSIONS", ER_X_MYSQLX_ACCOUNT_MISSING_PERMISSIONS, "", nullptr, nullptr, 0 },
  {"ER_X_EXPECT_FIELD_EXISTS_FAILED", ER_X_EXPECT_FIELD_EXISTS_FAILED, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_LOCKING", ER_X_BAD_LOCKING, "", nullptr, nullptr, 0 },
  {"ER_X_FRAME_COMPRESSION_DISABLED", ER_X_FRAME_COMPRESSION_DISABLED, "", nullptr, nullptr, 0 },
  {"ER_X_DECOMPRESSION_FAILED", ER_X_DECOMPRESSION_FAILED, "", nullptr, nullptr, 0 },
  {"ER_X_BAD_COMPRESSED_FRAME", ER_X_BAD_COMPRESSED_FRAME, "", nullptr, nullptr, 0 },
  {"ER_X_CAPABILITY_COMPRESSION_INVALID_ALGORITHM", ER_X_CAPABILITY_COMPRESSION_INVALID_ALGORITHM, "", nullptr, nullptr, 0 },
  {"ER_X_CAPABILITY_COMPRESSION_INVALID_SERVER_STYLE", ER_X_CAPABILITY_COMPRESSION_INVALID_SERVER_STYLE, "", nullptr, nullptr, 0 },
  {"ER_X_CAPABILITY_COMPRESSION_INVALID_CLIENT_STYLE", ER_X_CAPABILITY_COMPRESSION_INVALID_CLIENT_STYLE, "", nullptr, nullptr, 0 },
  {"ER_X_CAPABILITY_COMPRESSION_INVALID_OPTION", ER_X_CAPABILITY_COMPRESSION_INVALID_OPTION, "", nullptr, nullptr, 0 },
  {"ER_X_CAPABILITY_COMPRESSION_MISSING_REQUIRED_FIELDS", ER_X_CAPABILITY_COMPRESSION_MISSING_REQUIRED_FIELDS, "", nullptr, nullptr, 0 },
  {"ER_X_DOCUMENT_DOESNT_MATCH_EXPECTED_SCHEMA", ER_X_DOCUMENT_DOESNT_MATCH_EXPECTED_SCHEMA, "", nullptr, nullptr, 0 },
  {"ER_X_COLLECTION_OPTION_DOESNT_EXISTS", ER_X_COLLECTION_OPTION_DOESNT_EXISTS, "", nullptr, nullptr, 0 },
  {"ER_X_INVALID_VALIDATION_SCHEMA", ER_X_INVALID_VALIDATION_SCHEMA, "", nullptr, nullptr, 0 },

