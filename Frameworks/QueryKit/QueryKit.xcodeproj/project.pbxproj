// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		1713ECB014F96A5C0013C4F0 /* QKSelectQueryOrderByTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 1713ECAF14F96A5C0013C4F0 /* QKSelectQueryOrderByTests.m */; };
		1713ECD814F970BB0013C4F0 /* QKSelectQueryGroupByTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 1713ECD714F970BB0013C4F0 /* QKSelectQueryGroupByTests.m */; };
		1719E47D151E8CA7003F98C5 /* QKQueryUpdateParameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 1719E47B151E8CA7003F98C5 /* QKQueryUpdateParameter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1719E47E151E8CA7003F98C5 /* QKQueryUpdateParameter.m in Sources */ = {isa = PBXBuildFile; fileRef = 1719E47C151E8CA7003F98C5 /* QKQueryUpdateParameter.m */; };
		1719E4BD151F51F1003F98C5 /* QKUpdateQueryTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 1719E4BA151F51EA003F98C5 /* QKUpdateQueryTests.m */; };
		1726972915AAF6CE009586E1 /* QKQueryTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 1726972815AAF6CE009586E1 /* QKQueryTests.m */; };
		1726976715AC3DD2009586E1 /* QKQueryDatabases.h in Headers */ = {isa = PBXBuildFile; fileRef = 1726976515AC3DD2009586E1 /* QKQueryDatabases.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1726979515AEE939009586E1 /* QKQueryStringAdditions.h in Headers */ = {isa = PBXBuildFile; fileRef = 1726979315AEE939009586E1 /* QKQueryStringAdditions.h */; };
		1726979615AEE939009586E1 /* QKQueryStringAdditions.m in Sources */ = {isa = PBXBuildFile; fileRef = 1726979415AEE939009586E1 /* QKQueryStringAdditions.m */; };
		173F094A15B5720A00371974 /* QKQueryConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = 173F094915B5720A00371974 /* QKQueryConstants.m */; };
		17577F6715A98FEA00CDF67A /* QKTestConstants.m in Sources */ = {isa = PBXBuildFile; fileRef = 17577F6615A98FEA00CDF67A /* QKTestConstants.m */; };
		17577FC615A99AC000CDF67A /* QKQueryConstants.h in Headers */ = {isa = PBXBuildFile; fileRef = 17577FC415A99AC000CDF67A /* QKQueryConstants.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1757801E15A9A14400CDF67A /* QKQueryGenericParameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 1757801C15A9A14400CDF67A /* QKQueryGenericParameter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1757801F15A9A14400CDF67A /* QKQueryGenericParameter.m in Sources */ = {isa = PBXBuildFile; fileRef = 1757801D15A9A14400CDF67A /* QKQueryGenericParameter.m */; };
		179FEECA15B6CE50009B34F0 /* QKTestCase.m in Sources */ = {isa = PBXBuildFile; fileRef = 179FEEC915B6CE50009B34F0 /* QKTestCase.m */; };
		179FEF8E15BA7EB0009B34F0 /* QKDeleteQueryTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 179FEF8D15BA7EB0009B34F0 /* QKDeleteQueryTests.m */; };
		17E5951F14F301DF0054EE08 /* QKQuery.h in Headers */ = {isa = PBXBuildFile; fileRef = 17E5951614F301DF0054EE08 /* QKQuery.h */; settings = {ATTRIBUTES = (Public, ); }; };
		17E5952014F301DF0054EE08 /* QKQuery.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E5951714F301DF0054EE08 /* QKQuery.m */; };
		17E5952114F301DF0054EE08 /* QKQueryOperators.h in Headers */ = {isa = PBXBuildFile; fileRef = 17E5951814F301DF0054EE08 /* QKQueryOperators.h */; settings = {ATTRIBUTES = (Public, ); }; };
		17E5952214F301DF0054EE08 /* QKQueryParameter.h in Headers */ = {isa = PBXBuildFile; fileRef = 17E5951914F301DF0054EE08 /* QKQueryParameter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		17E5952314F301DF0054EE08 /* QKQueryParameter.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E5951A14F301DF0054EE08 /* QKQueryParameter.m */; };
		17E5952414F301DF0054EE08 /* QKQueryTypes.h in Headers */ = {isa = PBXBuildFile; fileRef = 17E5951B14F301DF0054EE08 /* QKQueryTypes.h */; settings = {ATTRIBUTES = (Public, ); }; };
		17E5952514F301DF0054EE08 /* QKQueryUtilities.h in Headers */ = {isa = PBXBuildFile; fileRef = 17E5951C14F301DF0054EE08 /* QKQueryUtilities.h */; settings = {ATTRIBUTES = (Public, ); }; };
		17E5952614F301DF0054EE08 /* QKQueryUtilities.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E5951D14F301DF0054EE08 /* QKQueryUtilities.m */; };
		17E5952714F301DF0054EE08 /* QueryKit.h in Headers */ = {isa = PBXBuildFile; fileRef = 17E5951E14F301DF0054EE08 /* QueryKit.h */; settings = {ATTRIBUTES = (Public, ); }; };
		17E595F214F3058F0054EE08 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 17E595F114F3058F0054EE08 /* Foundation.framework */; };
		17E596A814F308160054EE08 /* QKSelectQueryTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 17E5969814F307B70054EE08 /* QKSelectQueryTests.m */; };
		17E596A914F308250054EE08 /* Foundation.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 17E595F114F3058F0054EE08 /* Foundation.framework */; };
		17F48BA815B27F6400C6455B /* QKQueryOrderBy.h in Headers */ = {isa = PBXBuildFile; fileRef = 17F48BA615B27F6400C6455B /* QKQueryOrderBy.h */; settings = {ATTRIBUTES = (Public, ); }; };
		17F48BA915B27F6500C6455B /* QKQueryOrderBy.m in Sources */ = {isa = PBXBuildFile; fileRef = 17F48BA715B27F6400C6455B /* QKQueryOrderBy.m */; };
		17F48BC315B289C100C6455B /* QKQueryConstruct.h in Headers */ = {isa = PBXBuildFile; fileRef = 17F48BC115B289C100C6455B /* QKQueryConstruct.h */; settings = {ATTRIBUTES = (Public, ); }; };
		17F48BC415B289C100C6455B /* QKQueryConstruct.m in Sources */ = {isa = PBXBuildFile; fileRef = 17F48BC215B289C100C6455B /* QKQueryConstruct.m */; };
		17F620BE14F961C1003E7290 /* QueryKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 8DC2EF5B0486A6940098B216 /* QueryKit.framework */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		17E596BE14F916B40054EE08 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 0867D690FE84028FC02AAC07 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8DC2EF4F0486A6940098B216;
			remoteInfo = QueryKit;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		1713ECAF14F96A5C0013C4F0 /* QKSelectQueryOrderByTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKSelectQueryOrderByTests.m; sourceTree = "<group>"; };
		1713ECD714F970BB0013C4F0 /* QKSelectQueryGroupByTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKSelectQueryGroupByTests.m; sourceTree = "<group>"; };
		1719E47B151E8CA7003F98C5 /* QKQueryUpdateParameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKQueryUpdateParameter.h; sourceTree = "<group>"; };
		1719E47C151E8CA7003F98C5 /* QKQueryUpdateParameter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKQueryUpdateParameter.m; sourceTree = "<group>"; };
		1719E4BA151F51EA003F98C5 /* QKUpdateQueryTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKUpdateQueryTests.m; sourceTree = "<group>"; };
		1726972815AAF6CE009586E1 /* QKQueryTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKQueryTests.m; sourceTree = "<group>"; };
		1726976515AC3DD2009586E1 /* QKQueryDatabases.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKQueryDatabases.h; sourceTree = "<group>"; };
		1726979315AEE939009586E1 /* QKQueryStringAdditions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKQueryStringAdditions.h; sourceTree = "<group>"; };
		1726979415AEE939009586E1 /* QKQueryStringAdditions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKQueryStringAdditions.m; sourceTree = "<group>"; };
		173F093115B56E3400371974 /* QKSelectQueryTests.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKSelectQueryTests.h; sourceTree = "<group>"; };
		173F094915B5720A00371974 /* QKQueryConstants.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKQueryConstants.m; sourceTree = "<group>"; };
		173F096315B5774400371974 /* QKUpdateQueryTests.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKUpdateQueryTests.h; sourceTree = "<group>"; };
		17577F6515A98FEA00CDF67A /* QKTestConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKTestConstants.h; sourceTree = "<group>"; };
		17577F6615A98FEA00CDF67A /* QKTestConstants.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKTestConstants.m; sourceTree = "<group>"; };
		17577FC415A99AC000CDF67A /* QKQueryConstants.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKQueryConstants.h; sourceTree = "<group>"; };
		1757801C15A9A14400CDF67A /* QKQueryGenericParameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKQueryGenericParameter.h; sourceTree = "<group>"; };
		1757801D15A9A14400CDF67A /* QKQueryGenericParameter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKQueryGenericParameter.m; sourceTree = "<group>"; };
		179FEEB915B6CC0C009B34F0 /* QKSelectQueryOrderByTests.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKSelectQueryOrderByTests.h; sourceTree = "<group>"; };
		179FEEC515B6CD55009B34F0 /* QKSelectQueryGroupByTests.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKSelectQueryGroupByTests.h; sourceTree = "<group>"; };
		179FEEC815B6CE50009B34F0 /* QKTestCase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKTestCase.h; sourceTree = "<group>"; };
		179FEEC915B6CE50009B34F0 /* QKTestCase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKTestCase.m; sourceTree = "<group>"; };
		179FEF8C15BA7EB0009B34F0 /* QKDeleteQueryTests.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKDeleteQueryTests.h; sourceTree = "<group>"; };
		179FEF8D15BA7EB0009B34F0 /* QKDeleteQueryTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKDeleteQueryTests.m; sourceTree = "<group>"; };
		17E5951614F301DF0054EE08 /* QKQuery.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKQuery.h; sourceTree = "<group>"; };
		17E5951714F301DF0054EE08 /* QKQuery.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKQuery.m; sourceTree = "<group>"; };
		17E5951814F301DF0054EE08 /* QKQueryOperators.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKQueryOperators.h; sourceTree = "<group>"; };
		17E5951914F301DF0054EE08 /* QKQueryParameter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKQueryParameter.h; sourceTree = "<group>"; };
		17E5951A14F301DF0054EE08 /* QKQueryParameter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKQueryParameter.m; sourceTree = "<group>"; };
		17E5951B14F301DF0054EE08 /* QKQueryTypes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKQueryTypes.h; sourceTree = "<group>"; };
		17E5951C14F301DF0054EE08 /* QKQueryUtilities.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKQueryUtilities.h; sourceTree = "<group>"; };
		17E5951D14F301DF0054EE08 /* QKQueryUtilities.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKQueryUtilities.m; sourceTree = "<group>"; };
		17E5951E14F301DF0054EE08 /* QueryKit.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QueryKit.h; sourceTree = "<group>"; };
		17E595F114F3058F0054EE08 /* Foundation.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Foundation.framework; path = System/Library/Frameworks/Foundation.framework; sourceTree = SDKROOT; };
		17E5969814F307B70054EE08 /* QKSelectQueryTests.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKSelectQueryTests.m; sourceTree = "<group>"; };
		17E5969E14F307CE0054EE08 /* Tests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = Tests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		17E5969F14F307CE0054EE08 /* Tests-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; name = "Tests-Info.plist"; path = "Resources/Tests-Info.plist"; sourceTree = "<group>"; };
		17F48BA615B27F6400C6455B /* QKQueryOrderBy.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKQueryOrderBy.h; sourceTree = "<group>"; };
		17F48BA715B27F6400C6455B /* QKQueryOrderBy.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKQueryOrderBy.m; sourceTree = "<group>"; };
		17F48BC115B289C100C6455B /* QKQueryConstruct.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = QKQueryConstruct.h; sourceTree = "<group>"; };
		17F48BC215B289C100C6455B /* QKQueryConstruct.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = QKQueryConstruct.m; sourceTree = "<group>"; };
		32DBCF5E0370ADEE00C91783 /* QueryKit-Prefix.pch */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = "QueryKit-Prefix.pch"; path = "Source/QueryKit-Prefix.pch"; sourceTree = "<group>"; };
		8DC2EF5A0486A6940098B216 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = Resources/Info.plist; sourceTree = "<group>"; };
		8DC2EF5B0486A6940098B216 /* QueryKit.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = QueryKit.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		17E5969B14F307CE0054EE08 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				17E596A914F308250054EE08 /* Foundation.framework in Frameworks */,
				17F620BE14F961C1003E7290 /* QueryKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8DC2EF560486A6940098B216 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				17E595F214F3058F0054EE08 /* Foundation.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		034768DFFF38A50411DB9C8B /* Products */ = {
			isa = PBXGroup;
			children = (
				8DC2EF5B0486A6940098B216 /* QueryKit.framework */,
				17E5969E14F307CE0054EE08 /* Tests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		0867D691FE84028FC02AAC07 /* QueryKit */ = {
			isa = PBXGroup;
			children = (
				32DBCF5E0370ADEE00C91783 /* QueryKit-Prefix.pch */,
				08FB77AEFE84172EC02AAC07 /* Source */,
				17E5969614F3079E0054EE08 /* Tests */,
				089C1665FE841158C02AAC07 /* Resources */,
				0867D69AFE84028FC02AAC07 /* Frameworks */,
				034768DFFF38A50411DB9C8B /* Products */,
			);
			name = QueryKit;
			sourceTree = "<group>";
		};
		0867D69AFE84028FC02AAC07 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				17E595F114F3058F0054EE08 /* Foundation.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		089C1665FE841158C02AAC07 /* Resources */ = {
			isa = PBXGroup;
			children = (
				8DC2EF5A0486A6940098B216 /* Info.plist */,
				17E5969F14F307CE0054EE08 /* Tests-Info.plist */,
			);
			name = Resources;
			sourceTree = "<group>";
		};
		08FB77AEFE84172EC02AAC07 /* Source */ = {
			isa = PBXGroup;
			children = (
				17E5951E14F301DF0054EE08 /* QueryKit.h */,
				17E5951614F301DF0054EE08 /* QKQuery.h */,
				17E5951714F301DF0054EE08 /* QKQuery.m */,
				17E5951C14F301DF0054EE08 /* QKQueryUtilities.h */,
				17E5951D14F301DF0054EE08 /* QKQueryUtilities.m */,
				1719E47A151E8C87003F98C5 /* Model */,
				17E5952814F301F40054EE08 /* Constants */,
				17577FC315A99AA500CDF67A /* Other */,
			);
			path = Source;
			sourceTree = "<group>";
		};
		1719E47A151E8C87003F98C5 /* Model */ = {
			isa = PBXGroup;
			children = (
				17F48BC515B28A4D00C6455B /* Common */,
				17F48BA615B27F6400C6455B /* QKQueryOrderBy.h */,
				17F48BA715B27F6400C6455B /* QKQueryOrderBy.m */,
				17E5951914F301DF0054EE08 /* QKQueryParameter.h */,
				17E5951A14F301DF0054EE08 /* QKQueryParameter.m */,
				1719E47B151E8CA7003F98C5 /* QKQueryUpdateParameter.h */,
				1719E47C151E8CA7003F98C5 /* QKQueryUpdateParameter.m */,
			);
			name = Model;
			sourceTree = "<group>";
		};
		17322A7214FA645300F0CF9B /* SELECT Tests */ = {
			isa = PBXGroup;
			children = (
				173F093115B56E3400371974 /* QKSelectQueryTests.h */,
				17E5969814F307B70054EE08 /* QKSelectQueryTests.m */,
				179FEEB915B6CC0C009B34F0 /* QKSelectQueryOrderByTests.h */,
				1713ECAF14F96A5C0013C4F0 /* QKSelectQueryOrderByTests.m */,
				179FEEC515B6CD55009B34F0 /* QKSelectQueryGroupByTests.h */,
				1713ECD714F970BB0013C4F0 /* QKSelectQueryGroupByTests.m */,
			);
			name = "SELECT Tests";
			sourceTree = "<group>";
		};
		17322A7414FA646000F0CF9B /* UPDATE Tests */ = {
			isa = PBXGroup;
			children = (
				173F096315B5774400371974 /* QKUpdateQueryTests.h */,
				1719E4BA151F51EA003F98C5 /* QKUpdateQueryTests.m */,
			);
			name = "UPDATE Tests";
			sourceTree = "<group>";
		};
		17322A7514FA647200F0CF9B /* DELETE Tests */ = {
			isa = PBXGroup;
			children = (
				179FEF8C15BA7EB0009B34F0 /* QKDeleteQueryTests.h */,
				179FEF8D15BA7EB0009B34F0 /* QKDeleteQueryTests.m */,
			);
			name = "DELETE Tests";
			sourceTree = "<group>";
		};
		17322A7614FA648100F0CF9B /* INSERT Tests */ = {
			isa = PBXGroup;
			children = (
			);
			name = "INSERT Tests";
			sourceTree = "<group>";
		};
		17577FA015A994EB00CDF67A /* Common */ = {
			isa = PBXGroup;
			children = (
				1726972815AAF6CE009586E1 /* QKQueryTests.m */,
			);
			name = Common;
			sourceTree = "<group>";
		};
		17577FC315A99AA500CDF67A /* Other */ = {
			isa = PBXGroup;
			children = (
				1726979315AEE939009586E1 /* QKQueryStringAdditions.h */,
				1726979415AEE939009586E1 /* QKQueryStringAdditions.m */,
			);
			name = Other;
			sourceTree = "<group>";
		};
		17E5952814F301F40054EE08 /* Constants */ = {
			isa = PBXGroup;
			children = (
				17E5951B14F301DF0054EE08 /* QKQueryTypes.h */,
				17E5951814F301DF0054EE08 /* QKQueryOperators.h */,
				1726976515AC3DD2009586E1 /* QKQueryDatabases.h */,
				17577FC415A99AC000CDF67A /* QKQueryConstants.h */,
				173F094915B5720A00371974 /* QKQueryConstants.m */,
			);
			name = Constants;
			sourceTree = "<group>";
		};
		17E5969614F3079E0054EE08 /* Tests */ = {
			isa = PBXGroup;
			children = (
				17F48D1415B2DCA900C6455B /* Other */,
				17577FA015A994EB00CDF67A /* Common */,
				17322A7414FA646000F0CF9B /* UPDATE Tests */,
				17322A7214FA645300F0CF9B /* SELECT Tests */,
				17322A7514FA647200F0CF9B /* DELETE Tests */,
				17322A7614FA648100F0CF9B /* INSERT Tests */,
			);
			path = Tests;
			sourceTree = "<group>";
		};
		17F48BC515B28A4D00C6455B /* Common */ = {
			isa = PBXGroup;
			children = (
				17F48BC115B289C100C6455B /* QKQueryConstruct.h */,
				17F48BC215B289C100C6455B /* QKQueryConstruct.m */,
				1757801C15A9A14400CDF67A /* QKQueryGenericParameter.h */,
				1757801D15A9A14400CDF67A /* QKQueryGenericParameter.m */,
			);
			name = Common;
			sourceTree = "<group>";
		};
		17F48D1415B2DCA900C6455B /* Other */ = {
			isa = PBXGroup;
			children = (
				179FEEC815B6CE50009B34F0 /* QKTestCase.h */,
				179FEEC915B6CE50009B34F0 /* QKTestCase.m */,
				17577F6515A98FEA00CDF67A /* QKTestConstants.h */,
				17577F6615A98FEA00CDF67A /* QKTestConstants.m */,
			);
			name = Other;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		8DC2EF500486A6940098B216 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				17E5951F14F301DF0054EE08 /* QKQuery.h in Headers */,
				17E5952114F301DF0054EE08 /* QKQueryOperators.h in Headers */,
				17E5952214F301DF0054EE08 /* QKQueryParameter.h in Headers */,
				17E5952414F301DF0054EE08 /* QKQueryTypes.h in Headers */,
				17E5952514F301DF0054EE08 /* QKQueryUtilities.h in Headers */,
				17E5952714F301DF0054EE08 /* QueryKit.h in Headers */,
				1719E47D151E8CA7003F98C5 /* QKQueryUpdateParameter.h in Headers */,
				17577FC615A99AC000CDF67A /* QKQueryConstants.h in Headers */,
				1757801E15A9A14400CDF67A /* QKQueryGenericParameter.h in Headers */,
				1726976715AC3DD2009586E1 /* QKQueryDatabases.h in Headers */,
				1726979515AEE939009586E1 /* QKQueryStringAdditions.h in Headers */,
				17F48BA815B27F6400C6455B /* QKQueryOrderBy.h in Headers */,
				17F48BC315B289C100C6455B /* QKQueryConstruct.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		17E5969D14F307CE0054EE08 /* Tests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 17E596A614F307CE0054EE08 /* Build configuration list for PBXNativeTarget "Tests" */;
			buildPhases = (
				17E5969A14F307CE0054EE08 /* Sources */,
				17E5969B14F307CE0054EE08 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
				17E596BF14F916B40054EE08 /* PBXTargetDependency */,
			);
			name = Tests;
			productName = Tests;
			productReference = 17E5969E14F307CE0054EE08 /* Tests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		8DC2EF4F0486A6940098B216 /* QueryKit */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1DEB91AD08733DA50010E9CD /* Build configuration list for PBXNativeTarget "QueryKit" */;
			buildPhases = (
				8DC2EF500486A6940098B216 /* Headers */,
				8DC2EF520486A6940098B216 /* Resources */,
				8DC2EF540486A6940098B216 /* Sources */,
				8DC2EF560486A6940098B216 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = QueryKit;
			productInstallPath = "$(HOME)/Library/Frameworks";
			productName = QueryKit;
			productReference = 8DC2EF5B0486A6940098B216 /* QueryKit.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		0867D690FE84028FC02AAC07 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1620;
			};
			buildConfigurationList = 1DEB91B108733DA50010E9CD /* Build configuration list for PBXProject "QueryKit" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = en;
			hasScannedForEncodings = 1;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 0867D691FE84028FC02AAC07 /* QueryKit */;
			productRefGroup = 034768DFFF38A50411DB9C8B /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8DC2EF4F0486A6940098B216 /* QueryKit */,
				17E5969D14F307CE0054EE08 /* Tests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8DC2EF520486A6940098B216 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		17E5969A14F307CE0054EE08 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				17E596A814F308160054EE08 /* QKSelectQueryTests.m in Sources */,
				1713ECB014F96A5C0013C4F0 /* QKSelectQueryOrderByTests.m in Sources */,
				1713ECD814F970BB0013C4F0 /* QKSelectQueryGroupByTests.m in Sources */,
				1719E4BD151F51F1003F98C5 /* QKUpdateQueryTests.m in Sources */,
				17577F6715A98FEA00CDF67A /* QKTestConstants.m in Sources */,
				1726972915AAF6CE009586E1 /* QKQueryTests.m in Sources */,
				179FEECA15B6CE50009B34F0 /* QKTestCase.m in Sources */,
				179FEF8E15BA7EB0009B34F0 /* QKDeleteQueryTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8DC2EF540486A6940098B216 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				17E5952014F301DF0054EE08 /* QKQuery.m in Sources */,
				17E5952314F301DF0054EE08 /* QKQueryParameter.m in Sources */,
				17E5952614F301DF0054EE08 /* QKQueryUtilities.m in Sources */,
				1719E47E151E8CA7003F98C5 /* QKQueryUpdateParameter.m in Sources */,
				1757801F15A9A14400CDF67A /* QKQueryGenericParameter.m in Sources */,
				1726979615AEE939009586E1 /* QKQueryStringAdditions.m in Sources */,
				17F48BA915B27F6500C6455B /* QKQueryOrderBy.m in Sources */,
				17F48BC415B289C100C6455B /* QKQueryConstruct.m in Sources */,
				173F094A15B5720A00371974 /* QKQueryConstants.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		17E596BF14F916B40054EE08 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8DC2EF4F0486A6940098B216 /* QueryKit */;
			targetProxy = 17E596BE14F916B40054EE08 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		17E5952D14F302740054EE08 /* Distribution */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_LINK_OBJC_RUNTIME = NO;
				CLANG_STATIC_ANALYZER_MODE = deep;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CURRENT_PROJECT_VERSION = 20095;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Source/QueryKit-Prefix.pch";
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INFOPLIST_PREFIX_HEADER = "";
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				SDKROOT = macosx;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Distribution;
		};
		17E5952E14F302740054EE08 /* Distribution */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				FRAMEWORK_VERSION = A;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Source/QueryKit-Prefix.pch";
				GCC_TREAT_IMPLICIT_FUNCTION_DECLARATIONS_AS_ERRORS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_INVALID_OFFSETOF_MACRO = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_FOUR_CHARACTER_CONSTANTS = YES;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_MULTIPLE_DEFINITION_TYPES_FOR_SELECTOR = NO;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_PROTOTYPE_CONVERSION = NO;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_STRICT_SELECTOR_MATCH = NO;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNKNOWN_PRAGMAS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_LABEL = YES;
				GCC_WARN_UNUSED_PARAMETER = YES;
				GCC_WARN_UNUSED_VALUE = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INSTALL_PATH = "@executable_path/../Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.querykit";
				PRODUCT_NAME = QueryKit;
				SKIP_INSTALL = YES;
				WARNING_CFLAGS = "-Wmost";
				WRAPPER_EXTENSION = framework;
			};
			name = Distribution;
		};
		17E596A314F307CE0054EE08 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_MODEL_TUNING = G5;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SYSTEM_LIBRARY_DIR)/Frameworks/Cocoa.framework/Headers/Cocoa.h";
				INFOPLIST_FILE = "Resources/Tests-Info.plist";
				INSTALL_PATH = "$(USER_LIBRARY_DIR)/Bundles";
				OTHER_LDFLAGS = (
					"-framework",
					Cocoa,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.querykit.tests";
				PRODUCT_NAME = Tests;
			};
			name = Debug;
		};
		17E596A414F307CE0054EE08 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SYSTEM_LIBRARY_DIR)/Frameworks/Cocoa.framework/Headers/Cocoa.h";
				INFOPLIST_FILE = "Resources/Tests-Info.plist";
				INSTALL_PATH = "$(USER_LIBRARY_DIR)/Bundles";
				OTHER_LDFLAGS = (
					"-framework",
					Cocoa,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.querykit.tests";
				PRODUCT_NAME = Tests;
				ZERO_LINK = NO;
			};
			name = Release;
		};
		17E596A514F307CE0054EE08 /* Distribution */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				COMBINE_HIDPI_IMAGES = YES;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SYSTEM_LIBRARY_DIR)/Frameworks/Cocoa.framework/Headers/Cocoa.h";
				INFOPLIST_FILE = "Resources/Tests-Info.plist";
				INSTALL_PATH = "$(USER_LIBRARY_DIR)/Bundles";
				OTHER_LDFLAGS = (
					"-framework",
					Cocoa,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.querykit.tests";
				PRODUCT_NAME = Tests;
			};
			name = Distribution;
		};
		1DEB91AE08733DA50010E9CD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				COMBINE_HIDPI_IMAGES = YES;
				COPY_PHASE_STRIP = NO;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 20095;
				FRAMEWORK_VERSION = A;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_MODEL_TUNING = G5;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Source/QueryKit-Prefix.pch";
				GCC_TREAT_IMPLICIT_FUNCTION_DECLARATIONS_AS_ERRORS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_INVALID_OFFSETOF_MACRO = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_FOUR_CHARACTER_CONSTANTS = YES;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_MULTIPLE_DEFINITION_TYPES_FOR_SELECTOR = YES;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_PROTOTYPE_CONVERSION = NO;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_STRICT_SELECTOR_MATCH = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNKNOWN_PRAGMAS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_LABEL = YES;
				GCC_WARN_UNUSED_PARAMETER = YES;
				GCC_WARN_UNUSED_VALUE = YES;
				GENERATE_PKGINFO_FILE = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INSTALL_PATH = "@executable_path/../Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.querykit";
				PRODUCT_NAME = QueryKit;
				SKIP_INSTALL = YES;
				WARNING_CFLAGS = "-Wmost";
				WRAPPER_EXTENSION = framework;
			};
			name = Debug;
		};
		1DEB91AF08733DA50010E9CD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				FRAMEWORK_VERSION = A;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Source/QueryKit-Prefix.pch";
				GCC_TREAT_IMPLICIT_FUNCTION_DECLARATIONS_AS_ERRORS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_INVALID_OFFSETOF_MACRO = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_FOUR_CHARACTER_CONSTANTS = YES;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_MULTIPLE_DEFINITION_TYPES_FOR_SELECTOR = NO;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_PROTOTYPE_CONVERSION = NO;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_STRICT_SELECTOR_MATCH = NO;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNKNOWN_PRAGMAS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_LABEL = YES;
				GCC_WARN_UNUSED_PARAMETER = YES;
				GCC_WARN_UNUSED_VALUE = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INSTALL_PATH = "@executable_path/../Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.querykit";
				PRODUCT_NAME = QueryKit;
				SKIP_INSTALL = YES;
				WARNING_CFLAGS = "-Wmost";
				WRAPPER_EXTENSION = framework;
			};
			name = Release;
		};
		1DEB91B208733DA50010E9CD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_LINK_OBJC_RUNTIME = NO;
				CLANG_STATIC_ANALYZER_MODE = deep;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CURRENT_PROJECT_VERSION = 20095;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Source/QueryKit-Prefix.pch";
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INFOPLIST_PREFIX_HEADER = "";
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		1DEB91B308733DA50010E9CD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_LINK_OBJC_RUNTIME = NO;
				CLANG_STATIC_ANALYZER_MODE = deep;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CURRENT_PROJECT_VERSION = 20095;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Source/QueryKit-Prefix.pch";
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INFOPLIST_PREFIX_HEADER = "";
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				SDKROOT = macosx;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		96BC47D92500ADD1003C8105 /* Beta */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_LINK_OBJC_RUNTIME = NO;
				CLANG_STATIC_ANALYZER_MODE = deep;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CURRENT_PROJECT_VERSION = 20095;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Source/QueryKit-Prefix.pch";
				GCC_VERSION = com.apple.compilers.llvm.clang.1_0;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INFOPLIST_PREFIX_HEADER = "";
				MACOSX_DEPLOYMENT_TARGET = 12.0;
				SDKROOT = macosx;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Beta;
		};
		96BC47DA2500ADD1003C8105 /* Beta */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				COMBINE_HIDPI_IMAGES = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				FRAMEWORK_VERSION = A;
				GCC_GENERATE_DEBUGGING_SYMBOLS = NO;
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "Source/QueryKit-Prefix.pch";
				GCC_TREAT_IMPLICIT_FUNCTION_DECLARATIONS_AS_ERRORS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_INVALID_OFFSETOF_MACRO = YES;
				GCC_WARN_ABOUT_MISSING_FIELD_INITIALIZERS = YES;
				GCC_WARN_ABOUT_MISSING_NEWLINE = YES;
				GCC_WARN_ABOUT_MISSING_PROTOTYPES = YES;
				GCC_WARN_CHECK_SWITCH_STATEMENTS = YES;
				GCC_WARN_FOUR_CHARACTER_CONSTANTS = YES;
				GCC_WARN_INITIALIZER_NOT_FULLY_BRACKETED = YES;
				GCC_WARN_MISSING_PARENTHESES = YES;
				GCC_WARN_MULTIPLE_DEFINITION_TYPES_FOR_SELECTOR = NO;
				GCC_WARN_NON_VIRTUAL_DESTRUCTOR = YES;
				GCC_WARN_PROTOTYPE_CONVERSION = NO;
				GCC_WARN_SHADOW = YES;
				GCC_WARN_SIGN_COMPARE = YES;
				GCC_WARN_STRICT_SELECTOR_MATCH = NO;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES;
				GCC_WARN_UNKNOWN_PRAGMAS = YES;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_LABEL = YES;
				GCC_WARN_UNUSED_PARAMETER = YES;
				GCC_WARN_UNUSED_VALUE = YES;
				INFOPLIST_FILE = Resources/Info.plist;
				INSTALL_PATH = "@executable_path/../Frameworks";
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.querykit";
				PRODUCT_NAME = QueryKit;
				SKIP_INSTALL = YES;
				WARNING_CFLAGS = "-Wmost";
				WRAPPER_EXTENSION = framework;
			};
			name = Beta;
		};
		96BC47DB2500ADD1003C8105 /* Beta */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				COMBINE_HIDPI_IMAGES = YES;
				FRAMEWORK_SEARCH_PATHS = "$(inherited)";
				GCC_ENABLE_OBJC_EXCEPTIONS = YES;
				GCC_MODEL_TUNING = G5;
				GCC_PRECOMPILE_PREFIX_HEADER = YES;
				GCC_PREFIX_HEADER = "$(SYSTEM_LIBRARY_DIR)/Frameworks/Cocoa.framework/Headers/Cocoa.h";
				INFOPLIST_FILE = "Resources/Tests-Info.plist";
				INSTALL_PATH = "$(USER_LIBRARY_DIR)/Bundles";
				OTHER_LDFLAGS = (
					"-framework",
					Cocoa,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "com.sequel-ace.querykit.tests";
				PRODUCT_NAME = Tests;
			};
			name = Beta;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		17E596A614F307CE0054EE08 /* Build configuration list for PBXNativeTarget "Tests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				17E596A314F307CE0054EE08 /* Debug */,
				17E596A414F307CE0054EE08 /* Release */,
				17E596A514F307CE0054EE08 /* Distribution */,
				96BC47DB2500ADD1003C8105 /* Beta */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1DEB91AD08733DA50010E9CD /* Build configuration list for PBXNativeTarget "QueryKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1DEB91AE08733DA50010E9CD /* Debug */,
				1DEB91AF08733DA50010E9CD /* Release */,
				17E5952E14F302740054EE08 /* Distribution */,
				96BC47DA2500ADD1003C8105 /* Beta */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1DEB91B108733DA50010E9CD /* Build configuration list for PBXProject "QueryKit" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1DEB91B208733DA50010E9CD /* Debug */,
				1DEB91B308733DA50010E9CD /* Release */,
				17E5952D14F302740054EE08 /* Distribution */,
				96BC47D92500ADD1003C8105 /* Beta */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 0867D690FE84028FC02AAC07 /* Project object */;
}
