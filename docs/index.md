# Sequel Ace

## Installation

### Mac AppStore <a href="https://apps.apple.com/us/app/sequel-ace/id1518036000?ls=1"><img alt="Download on the Mac AppStore" src="https://sequel-ace.com/images/download_on_mas.png" align="right" height="60"></a>

Download Sequel Ace today from the [macOS App Store](https://apps.apple.com/us/app/sequel-ace/id1518036000?ls=1)!

### MAS CLI

To install via `mas` [MAS CLI](https://github.com/mas-cli/mas) use Sequel Ace id `1518036000`

```sh
mas install 1518036000 # Sequel Ace
```

### Homebrew

To install an unofficial community maintained [Homebrew](https://brew.sh) [Cask](https://github.com/Homebrew/homebrew-cask) of the [GitHub Release](https://github.com/sequel-ace/sequel-ace/releases)

```sh
brew install sequel-ace
```

## Get started

Get started using Sequel Ace quickly by browsing through this online documentation to learn how to connect with your MySQL or MariaDB database server.

- [Getting Connected](get-started/)
- [Moving Saved Connections from Sequel Pro](get-started/migrating-from-sequel-pro.html)
- [Making Queries](queries.html)
- [Keyboard Shortcuts](shortcuts.html)
- [Reference](ref/)
- [Bundles](bundles/)
- [Contribute](contribute/)

---

Edit these pages at https://github.com/Sequel-Ace/Sequel-Ace/tree/main/docs
