-   [Getting Connected](get-started/)
-   [Making Queries](queries.html)
-   [Keyboard Shortcuts](shortcuts.html)
-   [Reference](ref/)
-   [Bundles](bundles/)
-   [Contribute](contribute/)

<hr>

### Query View

It looks like a simple text area, but the Sequel Ace Query view is as powerful as your favorite text editor. Write queries like a boss!

**Shortcut** `⌘ 5`

#### Query Favourites

The Query Favourites are a powerful feature that makes it easy to rerun saved queries or to write dynamic and customizable reusable queries. [Learn more about Query Favorites …](favorites.html)

#### Tab Trigger

A **tab trigger** is used as keyboard shortcut to insert the corresponding query favorite into the Custom Query Editor. It can contain any alphanumeric character but no spaces or punctuation signs.

#### Tab Snippet

A **tab snippet** represents an insertion point of the cursor within a [query favorite](favorites.html "Query View"). To navigate through defined tab snippets use `⇥` or `⇧⇥`.
