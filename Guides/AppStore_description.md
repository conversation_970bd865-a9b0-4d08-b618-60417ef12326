Sequel Ace is the "sequel" to longtime macOS tool Sequel Pro. Sequel Ace is a fast, easy-to-use Mac database management application for working with MySQL & MariaDB databases.

With Sequel Ace, you can write custom queries, or utilize the built-in filtering system to scope down to just what you're looking for. SSH, socket, and local connections are all supported, making all your databases accessible with ease.

Sequel Ace is open source! If you experience issues or have suggestions, check out the GitHub repository to get involved - https://github.com/Sequel-Ace/Sequel-Ace
