# How to contribute

We are really glad you're reading this, because we need volunteer developers to help this project be successful long-term.

We make a commitment to review *all* pull requests that are submitted by members of the community. We will provide feedback and recommendations with the effort of incorporating contributions from the community whenever possible.

We have a lot of work to do, but we're here to provide, with your help, an always-free, macOS first SQL database tool for everyone.

## Communication

We use Slack for communication - feel free to join our [Slack workspace](https://join.slack.com/t/sequel-ace/shared_invite/zt-g9bg1q6o-gDWyGCzauwPdg8BjmBCqKg).

## Testing

If you add a new feature or fix a bug, it's generally a good idea to write a test to accompany it! Tests help us ensure that our users can depend on Sequel Ace.

## Submitting changes

Please send a [GitHub Pull Request to Sequel Ace](https://github.com/Sequel-Ace/Sequel-Ace/pull/new/dev), pointed at the *main* branch, with a clear list of what you've done (read more about [pull requests](http://help.github.com/pull-requests/)). Please include detailed explanations of what changed (screenshots of changes are preferred). Please avoid committing changes that are unrelated to the pull request (if you accidentally edited an .xib file, it's all good, just revert changes to that unrelated file).

Always write a clear log message for your commits. One-line messages are fine for small changes, but bigger changes should look like this:

    $ git commit -m "A brief summary of the commit
    > 
    > A paragraph describing what changed and its impact."

## Coding conventions

Start reading our code and you'll get the hang of it. We optimize for readability!

Thanks,
The Sequel Ace Team
