<!DOCTYPE html>
<html>
<head>
	<title><PERSON>quel Ace</title>
	
	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">

	<style type="text/css" media="all">
		html {
			font-size: 12px;
		}
		
		.nowrap {
			white-space: nowrap;
		}
		
		div.nowrap {
			margin: 0;
			padding: 0;
		}
		
		body, table, th, td {
			background-color: #FFFFFF;
			color: #000000;
		}
		
		h1 {
			font-size: 16px;
		}
		
		h2 {
			font-size: 14px;
		}

		table {
			width: 100%;
			border: 1px solid #CCCCCC;
			border-collapse: collapse;
			border-spacing: 0;
		}

		th, td {
			padding: 0.2em;
		}

		th {
			text-align: left;
			font-weight: bold;
			background-color: #E5E5E5;
			border: 1px solid #CCCCCC;
		}
		
		td {
			font-family: {{font}};
			border: {{gridlines}};
		}

		tr > td {
			text-align: left;
		}

		tr.even > td {
			background-color: #EDF3FE;
		}

		tr.odd > td {
			background-color: #FFFFFF;
		}
		
		code {
			font-family: Courier;
		}
	</style>
</head>

<body>
	<h1>{{c.table}}</h1>

	<p>
		<strong>Connection:</strong> {{c.username}}{% if c.username %}@{% /if %}{{c.hostname}}{% if c.port %}:{% /if %}{{c.port}}/{{c.database}}<br />
		<strong>Generated on:</strong> {% now | date_format: "dd MMM yyyy 'at' HH:mm:ss" %} by {{c.version}}<br />
		{% if c.query %}<br /><strong>SQL query:</strong> <code>{{c.query}}</code>{% /if %}
		<br />
	</p>
	
	<h2>{{heading}}</h2>
	
	<table class="data">
		<thead>
			<tr>
				{% for column in columns %}<th>{{ column }}</th>{% /for %}
			</tr>
		</thead>

		<tbody>
			{% for row in rows %}
			<tr {% cycle 'class="odd" ' 'class="even" ' %}>
				{% for cell in row %}<td>{{ cell }}</td>{% /for %}
			</tr>
			{% /for %}
		</tbody>
	</table><br />
	
	{% if hasIndexes && indexes %}
	<h2>Table Indexes</h2>
	
	<table class="data">
		 <thead>
			<tr>
				{% for indexColumn in indexColumns %}<th>{{ indexColumn }}</th>{% /for %}
			</tr>
		</thead>

		<tbody>
			{% for index in indexes %}
			<tr {% cycle 'class="odd" ' 'class="even" ' %}>
				{% for cell in index %}<td>{{ cell }}</td>{% /for %}
			</tr>
			{% /for %}
		</tbody>
	</table>
	{% /if %}
</body>
</html>
