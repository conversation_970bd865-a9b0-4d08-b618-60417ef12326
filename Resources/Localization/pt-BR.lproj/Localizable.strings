/* Table Content : Remove Row : Result : Too Few : Part 3 : Row not deleted when using primary key for DELETE statement. */
" Check the Console for possible errors inside the primary key(s) of this table!" = " Verifique o console por possíveis erros na chave(s) primária(s) desta tabela!";

/* Table Content : Remove Row : Result : Too Many : Part 2 : Generic text */
" Please check the Console and inform the Sequel Ace team!" = " Por favor, verifique o console e informe à equipe do Sequel Ace!";

/* Table Content : Remove Row : Result : Too Few : Part 2 : Generic help message */
" Reload the table to be sure that the contents have not changed in the meantime." = " Recarregue a tabela pra ter certeza de que o conteúdo não foi alterado neste meio-tempo.";

/* Table Content : Remove Row : Result : Too Few : Part 3 : no primary key in table generic message */
" You should also add a primary key to this table!" = " Você precisa adicionar uma chave primária nesta tabela!";

/* Query snippet shell command syntax and placeholder */
"$(shell_command)" = "$(shell_command)";

/* text showing how many rows are selected */
"%@ %@ selected" = "%1$@ %2$@ selecionado";

/* History item filtered by values label */
"%@ (Filtered by %@)" = "%1$@ (Filtrado por %2$@)";

/* History item with page number label */
"%@ (Page %lu)" = "%1$@ (Página %2$lu)";

/* Content Filter Manager : Initial name of copied filter
 Initial favourite name after duplicating a previous favourite */
"%@ Copy" = "%@ Copiar";

/* text showing a single row a partially loaded result */
"%@ row in partial load" = "%@ linha carregada parcialmente";

/* text showing a single row in the result */
"%@ row in table" = "%@ linha na tabela";

/* text showing how a single rows matched filter */
"%@ row of %@%@ matches filter" = "%1$@ linha de %2$@%3$@ encontradas pelo filtro";

/* text showing how many rows are in a partially loaded result */
"%@ rows in partial load" = "%@ linhas carregadas parcialmente";

/* text showing how many rows are in the result */
"%@ rows in table" = "%@ linhas na tabela";

/* text showing how many rows matched filter */
"%@ rows of %@%@ match filter" = "%1$@ linhas de %2$@%3$@ encontradas pelo filtro";

/* text showing how many rows have been affected by a single query */
"%@; %ld rows affected" = "%1$@; %2$ld linhas afetadas";

/* text showing how many rows have been affected by multiple queries */
"%@; %ld rows affected in total, by %ld queries taking %@" = "%1$@; %2$ld linhas afetadas no total, de %3$ld queries demorando %4$@";

/* text showing one row has been affected by a single query */
"%@; 1 row affected" = "%@; 1 linha afetada";

/* text showing one row has been affected by multiple queries */
"%@; 1 row affected in total, by %ld queries taking %@" = "%1$@; 1 linha afetada no total, de %2$ld queries demorando %3$@";

/* text showing a query was cancelled */
"%@; Cancelled after %@" = "%1$@; Cancelado depois de %2$@";

/* text showing multiple queries were cancelled */
"%@; Cancelled in query %ld, after %@" = "%1$@; Cancelado na query %2$ld, depois de %3$@";

/* Error display text, showing original MySQL error */
"%@\n\nMySQL said: %@" = "%1$@\n\nMensagem do MySQL: %2$@";

/* favorite singular label (%d == 1) */
"%lu favorite" = "%lu favorito";

/* favorites plural label (%d != 1) */
"%lu favorites" = "%lu favoritos";

/* favorite group singular label (%d == 1) */
"%lu group" = "%lu grupo";

/* favorite groups plural label (%d != 1) */
"%lu groups" = "%lu grupos";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+y (y!=1) rows instead of n selected were deleted. */
"%ld additional rows were removed!" = "%ld linhas adicionais foram removidas!";

/* Label showing the index of the selected CSV row */
"%ld of %lu record(s)" = "%1$ld de %2$lu registro(s)";

/* Label showing the index of the selected CSV row (csv partially loaded) */
"%ld of first %lu record(s)" = "%1$ld dos primeiros %2$lu registro(s)";

/* Table Content : Remove Row : Result : Too Few : Part 1 : n-x (x!=1) of n selected rows were deleted. */
"%ld rows were not removed." = "%ld linhas não foram removidas.";

/* Export file already exists message */
"%lu files already exist. Do you want to replace them?" = "%lu arquivos já existem. Deseja substituí-los?";

/* Export files creation error title */
"%lu files could not be created" = "%lu arquivos não puderam ser criados";

/* Some export files already exist explanatory text */
"%lu files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "%lu arquivos com os mesmos nomes já existem na pasta de destino. Substituí-los irá substituir seus conteúdos atuais.";

/* filtered item count */
"%lu of %lu" = "%1$lu de %2$lu";

/* Export folder not writable for some files explanatory text */
"%lu of the export files could not be created because their target export folder is not writable; please select a new export location and try again." = "%lu dos arquivos de exportação não puderam ser criados porque sua pasta de exportação de destino não é gravável; por favor selecione um novo local de exportação e tente novamente.";

/* Export folder missing for some files explanatory text */
"%lu of the export files could not be created because their target export folder no longer exists; please select a new export location and try again." = "%lu dos arquivos de exportação não puderam ser criados porque a pasta de exportação de destino já não existe; por favor selecione um novo local de exportação e tente novamente.";

/* History item title with nothing selected */
"(no selection)" = "(sem seleção)";

/* value shown for hidden blob and text fields */
"(not loaded)" = "(não carregado)";

/* Explanation for MySQL server has gone away error */
"(This usually indicates that the connection has been closed by the server after inactivity, but can also occur due to other conditions.  The connection has been restored; please try again if the query is safe to re-run.)" = "(Isto geralmente indica que a conexão foi fechada pelo servidor após a inatividade, mas também pode ocorrer devido a outras condições. A conexão foi restaurada; tente novamente se a consulta for segura para executar novamente.)";

/* Custom Query : text appended to the “x row(s) affected” messages. $1 is a time interval */
", first row available after %1$@" = ", primeira linha disponível após %1$@";

/* Custom Query : text appended to the “x row(s) affected” messages (for update/delete queries). $1 is a time interval */
", taking %1$@" = ", levando %1$@";

/* warning shown in the console when no rows have been affected after writing to the db */
"/* WARNING: No rows have been affected */\n" = "/* AVISO: Nenhuma linha foi afetada */\n";

/* error text when multiple custom query failed */
"[ERROR in query %ld] %@\n" = "[ERRO na query %1$ld] %2$@\n";

/* error text when reading of csv file gave errors */
"[ERROR in row %ld] %@\n" = "[ERRO na linha %1$ld] %2$@\n";

/* [multiple selection] */
"[multiple selection]" = "[múltipla seleção]";

/* displayed when new content filter has empty Name field (ContentFilterManager) */
"[name required]" = "[nome necessário]";

/* [no selection] */
"[no selection]" = "[sem seleção]";

/* Additional export file errors */
"\n\n(In addition, one or more errors occurred while attempting to create the export files: %lu could not be created. These files will be ignored.)" = "\n\n(Além disso, um ou mais erros ocorreram ao tentar criar os arquivos de exportação: %lu não pôde ser criado. Estes arquivos serão ignorados.)";

/* \n\npress shift for binary search tooltip message */
"\n\nPress ⇧ for binary search (case-sensitive)." = "\n\nPressione ⇧ para pesquisa binária (diferencia maiúsculas de minúsculas).";

/* description of bit */
"A bit-field type. M specifies the number of bits. If shorter values are inserted, they will be aligned on the least significant bit. See the SET type if you want to explicitly name each bit." = "Um tipo de campo-bit. M especifica o número de bits. Se valores mais curtos forem inseridos, eles serão alinhados no ponto menos significativo. Veja o tipo de parâmetro se você quiser nomear explicitamente cada parte.";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog message */
"A Bundle ‘%@’ is already installed. Do you want to update it?" = "Um pacote %@ já está instalado. Você quer atualizá-lo?";

/* description of binary */
"A byte array with fixed length. Shorter values will always be padded to the right with 0x00 until they fit M." = "Uma matriz de bytes com comprimento fixo. Valores menores serão sempre preenchidos à direita com 0x00 até que eles encaixem.";

/* description of varbinary */
"A byte array with variable length. The actual number of bytes is further limited by the values of other fields in the row." = "Um array de bytes com comprimento variável. O número real de bytes é ainda limitado pelos valores de outros campos na linha.";

/* description of blob
 description of longblob
 description of mediumblob
 description of tinyblob */
"A byte array with variable length. Unlike VARBINARY this type does not count towards the maximum row length." = "Um array de bytes com comprimento variável. Ao contrário do VARBINARY este tipo não conta para o comprimento máximo da linha.";

/* description of tinytext */
"A character string that can store up to 255 bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Uma string de caracteres que pode armazenar até 255 bytes, mas requer menos espaço para valores mais curtos. O número real de caracteres é limitado pela codificação usada. Ao contrário deste tipo VARCHAR não conta para o comprimento máximo de linha.";

/* description of varchar */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding and the values of other fields in the row." = "Uma string de caracteres que pode armazenar até M bytes, mas requer menos espaço para valores mais curtos. O número real de caracteres é mais limitado pela codificação usada e os valores de outros campos na linha.";

/* description of text */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Uma string de caracteres que pode armazenar até M bytes, mas requer menos espaço para valores mais curtos. O número real de caracteres é limitado pela codificação usada. Ao contrário deste tipo VARCHAR não conta para o comprimento máximo de linha.";

/* description of char */
"A character string that will require M×w bytes per row, independent of the actual content length. w is the maximum number of bytes a single character can occupy in the given encoding." = "Uma string de caracteres que irá exigir MxW bytes por linha, independente do tamanho real do conteúdo. w é o número máximo de bytes que um único caractere pode ocupar na codificação fornecida.";

/* description of longtext
 description of mediumtext */
"A character string with variable length. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Uma string de caracteres com comprimento variável. O número real de caracteres é mais limitado pela codificação usada. Ao contrário do VARCHAR este tipo não conta para o comprimento máximo da linha.";

/* description of json */
"A data type that validates JSON data on INSERT and internally stores it in a binary format that is both, more compact and faster to access than textual JSON.\nAvailable from MySQL 5.7.8." = "Um tipo de dado que valida dados JSON no INSERT e armazena internamente em um formato binário que é ambos, mais compacto e mais rápido para acessar do que JSON textual.\nDisponível a partir do MySQL 5.7.8.";

/* Export file already exists explanatory text */
"A file with the same name already exists in the target folder. Replacing it will overwrite its current contents." = "Um arquivo com o mesmo nome já existe na pasta de destino. Substituí-lo irá substituir seu conteúdo atual.";

/* description of decimal */
"A fixed-point, exact decimal value. M is the maxium number of digits, of which D may be after the decimal point. When rounding, 0-4 is always rounded down, 5-9 up (“round towards nearest”)." = "Um ponto fixo, valor decimal exato. M é o número máximo de dígitos, dos quais D pode ser após o ponto decimal. Quando arredondar, 0-4 é sempre arredondado para baixo, 5-9 para cima (arredonda para o mais próximo).";

/* table structure : indexes : delete index : error 1553 : title
 table structure : indexes : delete index : error 1553, no FK found : title */
"A foreign key needs this index" = "Uma chave estrangeira precisa deste índice";

/* description of set */
"A SET can define up to 64 members (as strings) of which a field can use one or more using a comma-separated list. Upon insertion the order of members is automatically normalized and duplicate members will be eliminated. Assignment of numbers is supported using the same semantics as for BIT types." = "Um SET pode definir até 64 membros (como strings) dos quais um campo pode usar uma ou mais usando uma lista separada por vírgulas. Após a inserção, a ordem dos membros é automaticamente normalizada e os membros duplicados serão eliminados. A atribuição de números é suportada usando a mesma semântica que para tipos de BIT.";

/* SSH key not found message */
"A SSH key location was specified, but no file was found in the specified location.  Please re-select the key and try again." = "Um local da chave SSH foi especificado, mas nenhum arquivo foi encontrado no local especificado. Por favor, selecione novamente a chave e tente novamente.";

/* SSL CA certificate file not found message */
"A SSL Certificate Authority certificate location was specified, but no file was found in the specified location.  Please re-select the Certificate Authority certificate and try again." = "Um local de certificado da Autoridade de Certificados SSL foi especificado, mas nenhum arquivo foi encontrado no local especificado. Por favor, selecione novamente o certificado da Autoridade de Certificado e tente novamente.";

/* SSL certificate file not found message */
"A SSL certificate location was specified, but no file was found in the specified location.  Please re-select the certificate and try again." = "Um local de certificado SSL foi especificado, mas nenhum arquivo foi encontrado no local especificado. Por favor, selecione novamente o certificado e tente novamente.";

/* SSL key file not found message */
"A SSL key file location was specified, but no file was found in the specified location.  Please re-select the key file and try again." = "Um local do arquivo de chave SSL foi especificado, mas nenhum arquivo foi encontrado no local especificado. Por favor, selecione novamente o arquivo de chave e tente novamente.";

/* duplicate host informative message */
"A user with the host '%@' already exists" = "Um usuário com o host '%@' já existe";

/* duplicate user informative message */
"A user with the name '%@' already exists" = "Um usuário com o nome '%@' já existe";

/* table content : editing : error message description when parsing as hex string failed */
"A valid hex string may only contain the numbers 0-9 and letters A-F (a-f). It can optionally begin with „0x“ and spaces will be ignored.\nAlternatively the syntax X'val' is supported, too." = "Um texto hexadecimal válido pode conter apenas números de 0-9 e letras A-F (a-f). Ele pode opcionalmente começar com \"0x\" e espaços serão ignorados.\nComo alternativa, a sintaxe X'val' também é suportada.";

/* connection failed due to access denied title */
"Access denied!" = "Acesso negado!";

/* range of double */
"Accurate to approx. 15 decimal places" = "É preciso aproximadamente 15 casas decimais";

/* range of float */
"Accurate to approx. 7 decimal places" = "É preciso aproximadamente 7 casas decimais";

/* active connection window is busy. please wait and try again. tooltip */
"Active connection window is busy. Please wait and try again." = "Janela de conexão ativa ocupada. Por favor aguarde e tente novamente.";

/* header for activities pane */
"ACTIVITIES" = "ATIVIDADES";

/* Add trigger button label */
"Add" = "Adicionar";

/* menu item to add db */
"Add Database..." = "Adicionar banco de dados...";

/* Add Host */
"Add Host" = "Adicionar Host";

/* add global value or expression menu item */
"Add Value or Expression…" = "Adicionar Valor ou Expressão…";

/* adding index task status message */
"Adding index..." = "Adicionando índice...";

/* Advanced options short title */
"Advanced" = "Avançado";

/* notifications preference pane name */
"Alerts & Logs" = "Alertas e Logs";

/* notifications preference pane tooltip */
"Alerts & Logs Preferences" = "Preferências de alertas e logs";

/* All databases placeholder */
"All Databases" = "Todos os Bancos";

/* All databases (%) placeholder */
"All Databases (%)" = "Todos os Bancos de Dados (%)";

/* All export files already exist message */
"All the export files already exist. Do you want to replace them?" = "Todos os arquivos de exportação já existem. Deseja substituí-los?";

/* An error for sequelace URL scheme command occurred. Probably no corresponding connection window found. */
"An error for sequelace URL scheme command occurred. Probably no corresponding connection window found." = "Ocorreu um erro para o comando do Sequel-ACE. Provavelmente nenhuma janela de conexão correspondente foi encontrada.";

/* no connection available informatie message */
"An error has occurred and there doesn't seem to be a connection available." = "Ocorreu um erro e parece que não há uma conexão disponível.";

/* an error occur while executing a scheme command. if the scheme command was invoked by a bundle command, it could be that the command still runs. you can try to terminate it by pressing ⌘+. or via the activities pane. */
"An error occur while executing a scheme command. If the scheme command was invoked by a Bundle command, it could be that the command still runs. You can try to terminate it by pressing ⌘+. or via the Activities pane." = "Ocorreu um erro ao executar um comando de esquema. Se o comando do esquema foi chamado por um comando Bundle, pode ser que o comando ainda seja executado. Você pode tentar terminá-la pressionando ⌘+. ou através do painel de Atividades.";

/* error killing query informative message */
"An error occurred while attempting to kill connection %lld.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar encerrar a conexão %1$lld.\n\nMensagem do MySQL: %2$@";

/* error killing query informative message */
"An error occurred while attempting to kill the query associated with connection %lld.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar encerrar a consulta associada à conexão %1$lld.\n\nMensagem do MySQL: %2$@";

/* Error shown when unable to show create table syntax */
"An error occurred while creating table syntax.\n\n: %@" = "Ocorreu um erro ao criar a sintaxe da tabela.\n\n: %@";

/* rename table error - no temporary name found */
"An error occurred while renaming '%@'. No temporary name could be found. Please try renaming to something else first." = "Ocorreu um erro ao renomear '%@'. Nenhum nome temporário foi encontrado. Por favor, tente renomear para outro nome primeiro.";

/* rename table error informative message */
"An error occurred while renaming '%@'.\n\nMySQL said: %@" = "Ocorreu um erro ao renomear '%1$@'.\n\nMensagem do MySQL: %2$@";

/* rename error - don't know what type the renamed thing is */
"An error occurred while renaming. '%@' is of an unknown type." = "Ocorreu um erro ao renomear. '%@' é de um tipo desconhecido.";

/* rename precedure/function error - can't delete old procedure */
"An error occurred while renaming. I couldn't delete '%@'.\n\nMySQL said: %@" = "Ocorreu um erro durante a renomeação. Eu não pude excluir '%1$@'.\n\nMensagem do MySQL: %2$@";

/* rename precedure/function error - can't recreate procedure */
"An error occurred while renaming. I couldn't recreate '%@'.\n\nMySQL said: %@" = "Ocorreu um erro ao renomear. Não pude recriar '%1$@'.\n\nMensagem do MySQL disse: %2$@";

/* rename precedure/function error - can't retrieve syntax */
"An error occurred while renaming. I couldn't retrieve the syntax for '%@'.\n\nMySQL said: %@" = "Ocorreu um erro durante a renomeação. Não consegui recuperar a sintaxe de '%1$@'.\n\nMensagem do MySQL: %2$@";

/* rename error - invalid create syntax */
"An error occurred while renaming. The CREATE syntax of '%@' could not be parsed." = "Ocorreu um erro durante a renomeação. A sintaxe CREATE de '%@' não pôde ser analisada.";

/* message of panel when retrieving view information failed */
"An error occurred while retrieving status data.\n\nMySQL said: %@" = "Ocorreu um erro ao recuperar dados de status.\n\nMensagem do MySQL: %@";

/* message of panel when create syntax cannot be retrieved */
"An error occurred while retrieving the create syntax for '%@'.\nMySQL said: %@" = "Ocorreu um erro ao recuperar a sintaxe de criação para '%1$@'.\nMensagem do MySQL: %2$@";

/* add index error informative message */
"An error occurred while trying to add the index.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar adicionar o índice.\n\nMensagem do MySQL: %@";

/* error adding password to keychain informative message */
"An error occurred while trying to add the password to your Keychain. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Ocorreu um erro ao tentar adicionar a senha à sua chave. Reparar seu Keychain pode resolver isso, mas se não resolver, por favor reporte-o à equipe Sequel Ace, fornecendo o código de erro %i.";

/* unable to copy database message informative message */
"An error occurred while trying to copy the database '%@' to '%@'." = "Ocorreu um erro ao tentar copiar o banco de dados '%1$@' para '%2$@'.";

/* error deleting index informative message */
"An error occurred while trying to delete the index.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar excluir o índice.\n\nMensagem do MySQL: %@";

/* table status : row count query failed : error message */
"An error occurred while trying to determine the number of rows for %@.\nMySQL said: %@ (%lu)" = "Ocorreu um erro ao tentar determinar o número de linhas para “%1$@”.\nMensagem do MySQL: %2$@ (%3$lu)";

/* unable to rename database message informative message */
"An error occurred while trying to rename the database '%@' to '%@'." = "Ocorreu um erro ao tentar renomear o banco de dados '%1$@' para '%2$@'.";

/* error finding keychain item to edit informative message */
"An error occurred while trying to retrieve the Keychain item you're trying to edit. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Ocorreu um erro ao tentar recuperar o item Keychain que você está tentando editar. Reparar seu Keychain pode resolver isso, mas se não resolver, por favor reporte-o à equipe Sequel Ace, fornecendo o código de erro %i.";

/* error updating keychain item informative message */
"An error occurred while trying to update the Keychain item. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Ocorreu um erro ao tentar atualizar o item do Keychain. Reparar seu Keychain pode resolver isso, mas se não resolver, por favor reporte-o à equipe Sequel Ace, fornecendo o código de erro %i.";

/* mysql error occurred message */
"An error occurred" = "Ocorreu um erro";

/* MySQL table info retrieval error message */
"An error occurred retrieving table information.  MySQL said: %@" = "Ocorreu um erro ao recuperar informações da tabela. MySQL: %@";

/* SQL encoding read error */
"An error occurred when reading the file, as it could not be read in the encoding you selected (%@).\n\nOnly %ld queries were executed." = "Ocorreu um erro ao ler o arquivo, pois ele não pôde ser lido na codificação selecionada (%1$@).\n\nApenas %2$ld consultas foram executadas.";

/* CSV encoding read error */
"An error occurred when reading the file, as it could not be read using the encoding you selected (%@).\n\nOnly %ld rows were imported." = "Ocorreu um erro ao ler o arquivo, pois ele não pôde ser lido usando a codificação selecionada (%1$@).\n\nApenas %2$ld linhas foram importadas.";

/* SQL read error, including detail from system */
"An error occurred when reading the file.\n\nOnly %ld queries were executed.\n\n(%@)" = "Ocorreu um erro ao ler o arquivo.\n\nApenas %1$ld queries foram executadas.\n\n(%2$@)";

/* CSV read error, including detail string from system */
"An error occurred when reading the file.\n\nOnly %ld rows were imported.\n\n(%@)" = "Ocorreu um erro ao ler o arquivo.\n\nApenas %1$ld linhas foram importadas.\n\n(%2$@)";

/* error adding field informative message */
"An error occurred when trying to add the field '%@' via\n\n%@\n\nMySQL said: %@" = "Ocorreu um erro ao tentar adicionar o campo '%1$@via\n\n%2$@\n\nMensagem do MySQL: %3$@";

/* error changing field informative message */
"An error occurred when trying to change the field '%@' via\n\n%@\n\nMySQL said: %@" = "Ocorreu um erro ao tentar alterar o campo '%1$@via\n\n%2$@\n\nMensagem do MySQL: %3$@";

/* error changing table collation informative message */
"An error occurred when trying to change the table collation to '%@'.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar alterar o agrupamento da tabela para '%1$@'.\n\nMensagem do MySQL: %2$@";

/* error changing table encoding informative message */
"An error occurred when trying to change the table encoding to '%@'.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar alterar a codificação da tabela para '%1$@'.\n\nMensagem do MySQL: %2$@";

/* error changing table type informative message */
"An error occurred when trying to change the table type to '%@'.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar alterar o tipo de tabela para '%1$@'.\n\nMensagem do MySQL: %2$@";

/* error changing table comment informative message */
"An error occurred when trying to change the table's comment to '%@'.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar alterar o comentário da tabela para '%1$@'.\n\nMensagem do MySQL: %2$@";

/* an error occurred while analyzing the %@.\n\nMySQL said:%@ */
"An error occurred while analyzing the %@.\n\nMySQL said:%@" = "Ocorreu um erro ao analisar o %1$@.\n\nMensagem do MySQL:%2$@";

/* an error occurred while fetching the optimized field type.\n\nMySQL said:%@ */
"An error occurred while fetching the optimized field type.\n\nMySQL said:%@" = "Ocorreu um erro ao obter o tipo de campo otimizado.\n\nMensagem do MySQL:%@";

/* an error occurred while trying to flush the %@.\n\nMySQL said:%@ */
"An error occurred while flushing the %@.\n\nMySQL said:%@" = "Ocorreu um erro ao enviar o %1$@.\n\nMensagem do MySQL:%2$@";

/* sql import error message */
"An error occurred while importing SQL" = "Ocorreu um erro ao importar SQL";

/* an error occurred while trying to optimze the %@.\n\nMySQL said:%@ */
"An error occurred while optimzing the %@.\n\nMySQL said:%@" = "Ocorreu um erro ao otimizar o %1$@.\n\nMensagem do MySQL:%2$@";

/* an error occurred while performing the checksum on the %@.\n\nMySQL said:%@ */
"An error occurred while performing the checksum on %@.\n\nMySQL said:%@" = "Ocorreu um erro ao executar a checksum em %1$@.\n\nMensagem do MySQL:%2$@";

/* an error occurred while trying to repair the %@.\n\nMySQL said:%@ */
"An error occurred while repairing the %@.\n\nMySQL said:%@" = "Ocorreu um erro ao reparar o %1$@.\n\nMensagem do MySQL:%2$@";

/* message of panel when retrieving information failed */
"An error occurred while retrieving information.\nMySQL said: %@" = "Ocorreu um erro ao recuperar informações.\nMensagem do MySQL: %@";

/* error retrieving table information informative message */
"An error occurred while retrieving the information for table '%@'. Please try again.\n\nMySQL said: %@" = "Ocorreu um erro ao recuperar as informações para a tabela '%1$@'. Por favor, tente novamente.\n\nMensagem do MySQL: %2$@";

/* error retrieving table information informative message */
"An error occurred while retrieving the trigger information for table '%@'. Please try again.\n\nMySQL said: %@" = "Ocorreu um erro ao recuperar as informações da trigger da tabela '%1$@'. Por favor, tente novamente.\n\nMensagem do MySQL: %2$@";

/* error adding new column informative message */
"An error occurred while trying to add the new column '%@' by\n\n%@.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar adicionar a nova coluna '%1$@' por\n\n%2$@.\n\nMensagem do MySQL: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@' by\n\n%@.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar adicionar a nova tabela '%1$@' por\n\n%2$@.\n\nMensagem do MySQL: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@'.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar adicionar a nova tabela '%1$@'.\n\nMensagem do MySQL: %2$@";

/* error while trying to alter table message */
"An error occurred while trying to alter table '%@'.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar alterar a tabela '%1$@'.\n\nMensagem do MySQL: %2$@";

/* an error occurred while trying to check the %@.\n\nMySQL said:%@ */
"An error occurred while trying to check the %@.\n\nMySQL said:%@" = "Ocorreu um erro ao tentar verificar o %1$@.\n\nMensagem do MySQL:%2$@";

/* error deleting relation informative message */
"An error occurred while trying to delete the relation '%@'.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar excluir o relacionamento '%1$@'.\n\nMensagem do MySQL: %2$@";

/* unable to get list of users informative message */
"An error occurred while trying to get the list of users. Please make sure you have the necessary privileges to perform user management, including access to the mysql.user table." = "Ocorreu um erro ao tentar obter a lista de usuários. Por favor, certifique-se de que você tem os privilégios necessários para executar o gerenciamento de usuários, incluindo o acesso à tabela mysql.user";

/* error importing table informative message */
"An error occurred while trying to import a table via: \n%@\n\n\nMySQL said: %@" = "Ocorreu um erro ao tentar importar uma tabela via: \n%1$@\n\n\nMensagem do MySQL: %2$@";

/* error moving field informative message */
"An error occurred while trying to move the field.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar mover o campo.\n\nMensagem do MySQL: %@";

/* error resetting auto_increment informative message */
"An error occurred while trying to reset AUTO_INCREMENT of table '%@'.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar redefinir AUTO_INCREMENT da tabela '%1$@'.\n\nMensagem do MySQL: %2$@";

/* error truncating table informative message */
"An error occurred while trying to truncate the table '%@'.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar truncar a tabela '%1$@'.\n\nMensagem do MySQL: %2$@";

/* mysql error occurred informative message */
"An error occurred whilst trying to perform the operation.\n\nMySQL said: %@" = "Ocorreu um erro ao tentar executar a operação.\n\nMensagem do MySQL: %@";

/* mysql resource limits unsupported message */
"Resource Limits are not supported for your version of MySQL. Any Resouce Limits you specified have been discarded and not saved. MySQL said: %@" = "Os Limites de Recursos não são suportados para sua versão do MySQL. Quaisquer Limites de recurso especificados foram descartados e não salvos. MySQL disse: %@";

/* Export files creation error explanatory text */
"An unhandled error occurred when attempting to create %lu of the export files.  Please check the details and try again." = "Ocorreu um erro não tratado ao tentar criar %lu dos arquivos de exportação. Por favor, verifique os detalhes e tente novamente.";

/* All export files creation error explanatory text */
"An unhandled error occurred when attempting to create each of the export files.  Please check the details and try again." = "Um erro não tratado ocorreu ao tentar criar cada um dos arquivos de exportação. Por favor, verifique os detalhes e tente novamente.";

/* Export file creation error explanatory text */
"An unhandled error occurred when attempting to create the export file.  Please check the details and try again." = "Um erro não tratado ocorreu ao tentar criar o arquivo de exportação. Por favor, verifique os detalhes e tente novamente.";

/* ANALYZE one or more tables - result title */
"Analyze %@" = "Analisar %@";

/* analyze selected items menu item */
"Analyze Selected Items" = "Analisar Itens Selecionados";

/* analyze table menu item */
"Analyze Table" = "Analisar Tabela";

/* analyze table failed message */
"Analyze table failed." = "Falha ao analisar a tabela.";

/* change table type informative message */
"Are you sure you want to change this table's type to %@?\n\nPlease be aware that changing a table's type has the potential to cause the loss of some or all of its data. This action cannot be undone." = "Tem certeza de que deseja alterar o tipo desta tabela para %@?\n\nPor favor, esteja ciente de que alterar o tipo de uma tabela tem o potencial de causar a perda de alguns ou todos os seus dados. Esta ação não pode ser desfeita.";

/* clear global history list informative message */
"Are you sure you want to clear the global history list? This action cannot be undone." = "Tem certeza que deseja limpar a lista de histórico global? Esta ação não pode ser desfeita.";

/* clear history list for %@ informative message */
"Are you sure you want to clear the history list for %@? This action cannot be undone." = "Tem certeza que deseja limpar a lista de histórico para %@? Esta ação não pode ser desfeita.";

/* truncate tables informative message */
"Are you sure you want to delete ALL records in the selected tables? This operation cannot be undone." = "Tem certeza de que deseja excluir TODOS os registros nas tabelas selecionadas? Esta operação não pode ser desfeita.";

/* truncate table informative message */
"Are you sure you want to delete ALL records in the table '%@'? This operation cannot be undone." = "Tem certeza que deseja apagar TODOS os registros na tabela%@'? Esta operação não pode ser desfeita.";

/* delete all rows informative message */
"Are you sure you want to delete all the rows from this table? This action cannot be undone." = "Tem certeza de que deseja excluir todas as linhas desta tabela? Esta ação não pode ser desfeita.";

/* delete table/view informative message */
"Are you sure you want to delete the %@ '%@'? This operation cannot be undone." = "Tem certeza que deseja excluir %1$@ '%2$@'? Esta operação não pode ser desfeita.";

/* delete database informative message */
"Are you sure you want to delete the database '%@'? This operation cannot be undone." = "Tem certeza que deseja apagar o banco de dados '%@? Esta operação não pode ser desfeita.";

/* delete database informative message */
"Are you sure you want to delete the favorite '%@'? This operation cannot be undone." = "Tem certeza que deseja excluir o favorito %@? Esta operação não pode ser desfeita.";

/* delete field informative message */
"Are you sure you want to delete the field '%@'? This action cannot be undone." = "Tem certeza que deseja excluir o campo '%@'? Esta ação não pode ser desfeita.";

/* delete database informative message */
"Are you sure you want to delete the group '%@'? All groups and favorites within this group will also be deleted. This operation cannot be undone." = "Tem certeza que deseja excluir o grupo '%@'? Todos os grupos e favoritos desse grupo também serão excluídos. Esta operação não pode ser desfeita.";

/* delete index informative message */
"Are you sure you want to delete the index '%@'? This action cannot be undone." = "Tem certeza que deseja excluir o índice '%@'? Esta ação não pode ser desfeita.";

/* delete tables/views informative message */
"Are you sure you want to delete the selected %@? This operation cannot be undone." = "Tem certeza que deseja excluir o %@? Esta operação não pode ser desfeita.";

/* delete rows informative message */
"Are you sure you want to delete the selected %ld rows from this table? This action cannot be undone." = "Tem certeza que deseja excluir as %ld linhas selecionadas desta tabela? Esta ação não pode ser desfeita.";

/* delete selected relation informative message */
"Are you sure you want to delete the selected relations? This action cannot be undone." = "Tem certeza que deseja apagar as relações selecionadas? Esta ação não pode ser desfeita.";

/* delete selected row informative message */
"Are you sure you want to delete the selected row from this table? This action cannot be undone." = "Tem certeza que deseja excluir a linha selecionada desta tabela? Esta ação não pode ser desfeita.";

/* delete selected trigger informative message */
"Are you sure you want to delete the selected triggers? This action cannot be undone." = "Tem certeza que deseja excluir os gatilhos selecionados? Essa ação não pode ser desfeita.";

/* kill connection informative message */
"Are you sure you want to kill connection ID %lld?\n\nPlease be aware that continuing to kill this connection may result in data corruption. Please proceed with caution." = "Tem certeza de que deseja matar a conexão ID %lld?\n\nPor favor, esteja ciente de que continuar matando esta conexão pode resultar em corrupção de dados. Por favor, prossiga com cuidado.";

/* kill query informative message */
"Are you sure you want to kill the current query executing on connection ID %lld?\n\nPlease be aware that continuing to kill this query may result in data corruption. Please proceed with caution." = "Tem certeza que deseja encerrar a consulta atual executando a ID de conexão %lld?\n\nPor favor, esteja ciente de que continuar matando esta consulta pode resultar em corrupção de dados. Por favor, prossiga com cuidado.";

/* Bundle Editor : Remove-Bundle: remove dialog message */
"Are you sure you want to move the selected Bundle to the Trash and remove them respectively?" = "Tem certeza que deseja mover o Pacote selecionado para a Lixeira e removê-lo respectivamente?";

/* continue to print informative message */
"Are you sure you want to print the current content view of the table '%@'?\n\nIt currently contains %@ rows, which may take a significant amount of time to print." = "Você tem certeza que deseja imprimir o modo de exibição de conteúdo atual da tabela '%1$@'?\n\nAtualmente contém %2$@ linhas, que podem levar um tempo significativo para imprimir.";

/* remove all query favorites informative message */
"Are you sure you want to remove all of your saved query favorites? This action cannot be undone." = "Tem certeza que deseja remover todos os favoritos da sua consulta salva? Esta ação não pode ser desfeita.";

/* remove all selected content filters informative message */
"Are you sure you want to remove all selected content filters? This action cannot be undone." = "Você tem certeza que deseja remover todos os filtros de conteúdo selecionado? Esta ação não pode ser desfeita.";

/* remove all selected query favorites informative message */
"Are you sure you want to remove all selected query favorites? This action cannot be undone." = "Tem certeza que deseja remover todos os favoritos da consulta selecionados? Esta ação não pode ser desfeita.";

/* Table Info Section : current value of auto_increment */
"auto_increment: %@" = "auto_increment: %@";

/* Encoding autodetect menu item */
"Autodetect" = "Autodetectar";

/* background label for color table (Prefs > Editor) */
"Background" = "Fundo";

/* backtick quote label for color table (Prefs > Editor) */
"Backtick Quote" = "Citação";

/* bash error */
"BASH Error" = "Erro no BASH";

/* toolbar item label for switching to the Table Content tab */
"Browse & Edit Table Content" = "Navegar e editar conteúdo da tabela";

/* build label */
"build" = "construir";

/* build label */
"Build" = "Construir";

/* bundle editor menu item label */
"Bundle Editor" = "Editor de pacote";

/* bundle error */
"Bundle Error" = "Erro do Pacote";

/* bundles menu item label */
"Bundles" = "Pacotes";

/* Bundle Editor : Outline View : 'BUNDLES' item */
"BUNDLES" = "PACOTES";

/* Bundle Editor : Outline View : Menu Category item : tooltip */
"Bundles in category %@" = "Pacotes na categoria %@";

/* bundles installation error */
"Bundles Installation Error" = "Erro de instalação dos pacotes";

/* bzip2 compression export summary - within a sentence */
"bzip2 compression" = "compressão bzip2";

/* description of geometry */
"Can store a single spatial value of types POINT, LINESTRING or POLYGON. Spatial support in MySQL is based on the OpenGIS Geometry Model." = "Pode armazenar um único valor espacial dos tipos POINT, LINESTRING ou POLYGON. O suporte espacial no MySQL é baseado no Modelo de Geometria OpenGIS.";

/* Bundle Editor : Remove-Bundle: cancel button
 cancel
 cancel button
 Open Files : Bundle : Already-Installed : Cancel button
 Preferences : Network : Custom SSH client : warning dialog : cancel button
 shutdown server : confirmation dialog : cancel button */
"Cancel" = "Cancelar";

/* sql import : charset error alert : cancel button */
"Cancel Import" = "Cancelar Importação";

/* cancelling task status message */
"Cancelling..." = "Cancelando...";

/* empty query informative message */
"Cannot save an empty query." = "Não é possível salvar uma consulta vazia.";

/* caret label for color table (Prefs > Editor) */
"Caret" = "Acento circunflexo";

/* change button */
"Change" = "Mudar";

/* change focus to table list menu item */
"Change Focus to Table List" = "Mudar Foco para Lista de Tabelas";

/* change table type message */
"Change table type" = "Alterar tipo de tabela";

/* unsaved changes informative message */
"Changes have been made, which will be lost if this window is closed. Are you sure you want to continue" = "Alterações foram feitas, que serão perdidas se esta janela for fechada. Tem certeza que deseja continuar";

/* quitting app informal alert title */
"Close the app?" = "Close the app?";

/* quitting app informal alert body */
"Are you sure you want to quit the app?" = "Are you sure you want to quit the app?";

/* character set client: %@ */
"character set client: %@" = "definir o conjunto de caracteres cliente: %@";

/* CHECK one or more tables - result title */
"Check %@" = "Verificar %@";

/* check of all selected items successfully passed message */
"Check of all selected items successfully passed." = "Verificação se todos os itens selecionados passaram com sucesso.";

/* check option: %@ */
"check option: %@" = "opção de verificação: %@";

/* check selected items menu item */
"Check Selected Items" = "Verificar Itens Selecionados";

/* check table menu item */
"Check Table" = "Verificar Tabela";

/* check table failed message */
"Check table failed." = "Verificação da tabela falhou.";

/* check table successfully passed message */
"Check table successfully passed." = "Tabela verificada com sucesso.";

/* check view menu item */
"Check View" = "Ver Verificação";

/* checking field data for editing task description */
"Checking field data for editing..." = "Verificando dados do campo para edição...";

/* checksum %@ message */
"Checksum %@" = "Verificação de checksum %@";

/* checksum selected items menu item */
"Checksum Selected Items" = "Checksim de itens selecionados";

/* checksum table menu item */
"Checksum Table" = "Checksum de Tabela";

/* Checksums of %@ message */
"Checksums of %@" = "Verificações de checksum de %@";

/* menu item for choose db */
"Choose Database..." = "Escolher banco de dados...";

/* cancelling export cleaning up message */
"Cleaning up..." = "Limpando...";

/* clear button */
"Clear" = "Limpar";

/* toolbar item for clear console */
"Clear Console" = "Limpar o Console";

/* clear global history menu item title */
"Clear Global History" = "Limpar o Histórico Global";

/* clear history for %@ menu title */
"Clear History for %@" = "Limpar Histórico de %@";

/* clear history message */
"Clear History?" = "Limpar histórico?";

/* tooltip for toolbar item for clear console */
"Clear the console which shows all MySQL commands performed by Sequel Ace" = "Limpar o console que mostra todos os comandos MySQL executados por Sequel Ace";

/* clear the document-based history list tooltip message */
"Clear the document-based history list" = "Limpar o histórico baseado em documentos";

/* clear the global history list tooltip message */
"Clear the global history list" = "Limpar a lista de históricos globais";

/* Close menu item */
"Close" = "Fechar";

/* close tab context menu item */
"Close Tab" = "Fechar Aba";

/* Close Window menu item */
"Close Window" = "Fechar Janela";

/* collation label (Navigator) */
"Collation" = "Colação";

/* collation connection: %@ */
"collation connection: %@" = "conexão de ordenação: %@";

/* comment label */
"Comment" = "Comentar";

/* Title of action menu item to comment line */
"Comment Line" = "Linha de Comentários";

/* Title of action menu item to comment selection */
"Comment Selection" = "Comentar Seleção";

/* connect button */
"Connect" = "Conectar";

/* Connect via socket button */
"Connect via socket" = "Conectar via socket";

/* connection established message */
"Connected" = "Conectado";

/* description for connected notification */
"Connected to %@" = "Conectado a %@";

/* message of panel when connection to db failed */
"Connected to host, but unable to connect to database %@.\n\nBe sure that the database exists and that you have the necessary privileges.\n\nMySQL said: %@" = "Conectado ao servidor, mas não foi possível conectar ao banco de dados %1$@.\n\nCertifique-se de que o banco de dados existe e que você tem os privilégios necessários.\n\nMensagem do MySQL: %2$@";

/* Generic connecting very short status message */
"Connecting..." = "Conectando...";

/* window title string indicating that sp is connecting */
"Connecting…" = "Conectando…";

/* error while reading connection data file */
"Connection data file couldn't be read." = "Não foi possível ler o arquivo de dados da conexão.";

/* message error while reading connection data file and suggesting to save it under a differnet name */
"Connection data file %@ couldn't be read. Please try to save the document under a different name." = "O arquivo de dados de conexão %@ não pôde ser lido. Por favor, tente salvar o documento com um nome diferente.";

/* connection failed title */
"Connection failed!" = "Falha na conexão!";

/* Connection file is encrypted */
"Connection file is encrypted" = "Arquivo de conexão está criptografado";

/* Connection success very short status message */
"Connection succeeded" = "Conexão bem sucedida";

/* Console */
"Console" = "Console";

/* Console : Save as : Initial filename */
"ConsoleLog" = "ConsoleLog";

/* toolbar item label for switching to the Table Content tab */
"Content" = "Conteúdo";

/* content filter clause is empty tooltip. */
"Content Filter clause is empty." = "A cláusula do Filtro de Conteúdo está vazia.";

/* continue button
 Continue button title */
"Continue" = "Continuar";

/* continue to print message */
"Continue to print?" = "Continuar imprimindo?";

/* Copy as RTF */
"Copy as RTF" = "Copiar como RTF";

/* copy create func syntax menu item
 Table List : Context Menu : copy CREATE FUNCTION syntax */
"Copy Create Function Syntax" = "Copiar Sintaxe de CREATE FUNCTION";

/* copy create proc syntax menu item
 Table List : Context Menu : Copy CREATE PROCEDURE syntax
 Table List : Gear Menu : Copy CREATE PROCEDURE syntax */
"Copy Create Procedure Syntax" = "Copiar Sintaxe de CREATE FUNCTION";

/* copy create syntaxes menu item
 Table List : Context Menu : Copy CREATE syntax (multiple selection)
 Table List : Gear Menu : Copy CREATE syntax (multiple selection) */
"Copy Create Syntaxes" = "Copiar Sintaxes Criar";

/* copy create table syntax menu item
 Table List : Context Menu : Copy CREATE syntax (single table)
 Table List : Gear Menu : Copy CREATE syntax (single table) */
"Copy Create Table Syntax" = "Copiar Sintaxe de CREATE TABLE";

/* copy create view syntax menu item
 Table List : Context Menu : Copy CREATE view statement
 Table List : Gear Menu : Copy CREATE view statement */
"Copy Create View Syntax" = "Copiar Sintaxe de CREATE VIEW";

/* copy server variable name menu item */
"Copy Variable Name" = "Copiar Nome da Variável";

/* copy server variable names menu item */
"Copy Variable Names" = "Copiar Nomes de Variáveis";

/* copy server variable value menu item */
"Copy Variable Value" = "Copiar Valor da Variável";

/* copy server variable values menu item */
"Copy Variable Values" = "Copiar Valores das Variáveis";

/* Procedure/function export permission error */
"Could not export the %@ '%@' because of a permissions error.\n" = "Não foi possível exportar o %1$@ '%2$@' devido a um erro de permissão.\n";

/* Error when we can't parse/split file as CSV */
"Could not parse file as CSV" = "Não foi possível analisar o arquivo como CSV";

/* message when database selection failed */
"Could not select database" = "Não foi possível selecionar o banco de dados";

/* Alter Database : Query Failed ($1 = mysql error message) */
"Couldn't alter database.\nMySQL said: %@" = "Não foi possível alterar o banco de dados.\nMensagem do MySQL: %@";

/* Couldn't copy default themes to Application Support Theme folder!\nError: %@ */
"Couldn't copy default themes to Application Support Theme folder!\nError: %@" = "Não foi possível copiar os temas padrão para a pasta do Tema de Suporte de Aplicativos!\nErro: %@";

/* message of panel when table cannot be created */
"Couldn't create '%@'.\nMySQL said: %@" = "Não foi possível criar '%1$@'.\nMensagem do MySQL: %2$@";

/* Couldn't create Application Support Bundle folder!\nError: %@ */
"Couldn't create Application Support Bundle folder!\nError: %@" = "Não foi possível criar a pasta Pacotes de Suporte do Aplicativo!\nErro: %@";

/* Couldn't create Application Support Theme folder!\nError: %@ */
"Couldn't create Application Support Theme folder!\nError: %@" = "Não foi possível criar pasta Suporte de Temas do Aplicativo!\nErro: %@";

/* message of panel when creation of db failed */
"Couldn't create database.\nMySQL said: %@" = "Não foi possível criar o banco de dados.\nMensagem do MySQL: %@";

/* message of panel when an item cannot be deleted */
"Couldn't delete '%@'.\n\nMySQL said: %@" = "Não foi possível excluir '%1$@'.\n\nMensagem do MySQL: %2$@";

/* message of panel when an item cannot be deleted including informative message about using force deletion */
"Couldn't delete '%@'.\n\nSelecting the 'Force delete' option may prevent this issue, but may leave the database in an inconsistent state.\n\nMySQL said: %@" = "Não foi possível excluir '%1$@'\n\nSelecionar a opção 'Forçar eliminação' pode evitar esse problema, mas pode deixar o banco de dados em um estado inconsistente.\n\nMensagem do MySQL: %2$@";

/* message of panel when field cannot be deleted */
"Couldn't delete field %@.\nMySQL said: %@" = "Não foi possível excluir o campo %1$@.\nMensagem do MySQL: %2$@";

/* message when deleteing all rows failed */
"Couldn't delete rows.\n\nMySQL said: %@" = "Não foi possível excluir as linhas.\n\nMensagem do MySQL: %@";

/* message of panel when deleting db failed */
"Couldn't delete the database.\nMySQL said: %@" = "Não foi possível excluir o banco de dados.\nMensagem do MySQL: %@";

/* message of panel when an item cannot be renamed */
"Couldn't duplicate '%@'.\nMySQL said: %@" = "Não foi possível duplicar '%1$@'.\nMensagem do MySQL: %2$@";

/* message of panel when flushing privs failed */
"Couldn't flush privileges.\nMySQL said: %@" = "Não foi possível liberar privilégios.\nMensagem do MySQL: %@";

/* message of panel when table information cannot be retrieved */
"Couldn't get create syntax.\nMySQL said: %@" = "Não foi possível obter sintaxe de criação.\nMensagem do MySQL: %@";

/* Custom Query result editing error - could not identify a corresponding column */
"Couldn't identify field origin unambiguously. The column '%@' contains data from more than one table." = "Não foi possível identificar a origem do campo sem ambiguidades. A coluna '%@' contém dados de mais de uma tabela.";

/* message of panel when loading of row failed */
"Couldn't load the row. Reload the table to be sure that the row exists and use a primary key for your table." = "Não foi possível carregar a linha. Recarregue a tabela para ter certeza de que a linha existe e use uma chave primária para sua tabela.";

/* Couldn't read the file content of */
"Couldn't read the file content of" = "Não foi possível ler o conteúdo do arquivo de";

/* text shown if an error occurred while sorting the result table */
"Couldn't sort column." = "Não foi possível ordenar a coluna.";

/* message of panel when sorting of table failed */
"Couldn't sort table. MySQL said: %@" = "Não foi possível ordenar. Mensagem do MySQL: %@";

/* message of panel when error while updating field to db */
"Couldn't write field.\nMySQL said: %@" = "Não foi possível escrever o campo.\nMensagem do MySQL: %@";

/* create syntax for table comment */
"Create syntax for" = "Criar sintaxe para";

/* Create syntax label */
"Create syntax for %@ '%@'" = "Criar sintaxe para %1$@ '%2$@'";

/* Create syntaxes for selected items label */
"Create syntaxes for selected items" = "Criar sintaxe para itens selecionados";

/* Table Info Section : table create options */
"create_options: %@" = "create_options: %@";

/* created: %@
 Table Info Section : time+date table was created at */
"created: %@" = "criado: %@";

/* description of polygon */
"Creates a surface by combining one LinearRing (ie. a LineString that is closed and simple) as the outside boundary with zero or more inner LinearRings acting as \"holes\"." = "Cria uma superfície ao combinar um LinearRing (ex. uma LineString que é fechada e simples) como o limite externo com zero ou mais anéis internos LinearRings atuando como \"buracos\".";

/* Creating table task string */
"Creating %@..." = "Criando %@...";

/* Bundle Editor : Fallback Input source dropdown : 'current line' item */
"Current Line" = "Linha Atual";

/* Bundle Editor : Fallback Input source dropdown : 'current query' item */
"Current Query" = "Consulta atual";

/* header for current selection in filtered list */
"CURRENT SELECTION" = "SELEÇÃO ATUAL";

/* Bundle Editor : Fallback Input source dropdown : 'current word' item */
"Current Word" = "Palavra Atual";

/* SSH connection : debug header with user-defined ssh binary */
"Custom SSH binary enabled. Disable in Preferences to rule out incompatibilities!" = "O binário de SSH personalizado está habilitado. Desative nas Preferências para excluir incompatibilidades!";

/* customize file name label */
"Customize Filename (%@)" = "Personalizar nome do arquivo (%@)";

/* data access: %@ (%@) */
"data access: %@ (%@)" = "acesso aos dados: %1$@ (%2$@)";

/* Bundle Editor : Scope dropdown : 'data table' item
 data table menu item label */
"Data Table" = "Tabela de dados";

/* Bundle Editor : Outline View : 'Data Table' item : tooltip */
"Data Table Scope\ncommands will run on the Content and Query data tables" = "Escopo da Tabela de Dados\nComandos serão executados no Conteúdo e na Query das tabelas de dados";

/* export filename database token
 export header database label
 export source
 Table Structure : Collation dropdown : 'item is the same as the collation of database' marker
 Table Structure : Encoding dropdown : 'item is database default' marker */
"Database" = "Banco de Dados";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'database changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'database changed' item */
"Database changed" = "Banco de dados alterado";

/* message of panel when no db name is given */
"Database must have a name." = "O banco de dados deve ter um nome.";

/* databsse rename unsupported message */
"Database Rename Unsupported" = "Renomear banco de dados não suportado";

/* export filename date token */
"Date" = "Data";

/* export filename date token */
"Day" = "Dia";

/* Add Table Sheet : Collation : Default (unknown)
 Collation Dropdown : Default (unknown)
 default label
 Table Structure : Collation dropdown : 'item is the same as the default collation of the row's charset' marker */
"Default" = "Padrão";

/* Add Table : Collation : Default ($1 = collation name)
 Charset Dropdown : Default item ($1 = charset name)
 Collation Dropdown : Default ($1 = collation name)
 Collation Dropdown : Default collation for given charset ($1 = collation name)
 New Table Sheet : Table Engine Dropdown : Default */
"Default (%@)" = "Padrão (%@)";

/* default bundles update */
"Default Bundles Update" = "Atualização Padrão dos Pacotes";

/* import : csv field mapping : field default value */
"Default: %@" = "Padrão: %@";

/* Query snippet default value placeholder */
"default_value" = "default_value";

/* definer label (Navigator) */
"Definer" = "Definidor";

/* definer: %@ */
"definer: %@" = "definidor: %@";

/* description of enum */
"Defines a list of members, of which every field can use at most one. Values are sorted by their index number (starting at 0 for the first member)." = "Define uma lista de membros que podem ser utilizados por cada campo. Os valores são classificados pelo seu número de índice (começando em 0 para o primeiro membro).";

/* delete button */
"Delete" = "Excluir";

/* delete table/view message */
"Delete %@ '%@'?" = "Excluir %1$@ de%2$@?";

/* table structure : indexes : delete index : error 1553 : delete index and FK button */
"Delete Both" = "Excluir ambos";

/* delete database message */
"Delete database '%@'?" = "Excluir banco de dados '%@'?";

/* delete database message */
"Delete favorite '%@'?" = "Excluir favorito %@?";

/* delete field message */
"Delete field '%@'?" = "Excluir o campo '%@' '?";

/* delete func menu title */
"Delete Function" = "Excluir Função";

/* delete functions menu title */
"Delete Functions" = "Excluir Funções";

/* delete database message */
"Delete group '%@'?" = "Excluir grupo '%@'?";

/* delete index message */
"Delete index '%@'?" = "Excluir o índice%@'?";

/* delete items menu title */
"Delete Items" = "Excluir Itens";

/* delete proc menu title */
"Delete Procedure" = "Excluir Procedimento";

/* delete procedures menu title */
"Delete Procedures" = "Excluir procedimentos";

/* delete relation menu item */
"Delete Relation" = "Excluir Relacionamento";

/* delete relation message */
"Delete relation" = "Excluir relacionamento";

/* delete relations menu item */
"Delete Relations" = "Excluir relacionamentos";

/* delete row menu item singular */
"Delete Row" = "Excluir linha";

/* delete rows menu item plural */
"Delete Rows" = "Excluir Linhas";

/* delete rows message */
"Delete rows?" = "Excluir linhas?";

/* delete tables/views message */
"Delete selected %@?" = "Excluir %@?";

/* delete selected row message */
"Delete selected row?" = "Excluir linha selecionada?";

/* delete table menu title */
"Delete Table..." = "Excluir Tabela...";

/* delete tables menu title */
"Delete Tables" = "Excluir Tabelas";

/* delete trigger menu item */
"Delete Trigger" = "Excluir Trigger";

/* delete trigger message */
"Delete trigger" = "Excluir trigger";

/* delete triggers menu item */
"Delete Triggers" = "Excluir Triggers";

/* delete view menu title */
"Delete View" = "Excluir View";

/* delete views menu title */
"Delete Views" = "Excluir Views";

/* Preferences : Network : SSL Chiper suites : List seperator */
"Disabled Cipher Suites" = "Desabilitar Cipher Suites";

/* force table deltion button text tooltip */
"Disables foreign key checks (FOREIGN_KEY_CHECKS) before deletion and re-enables them afterwards." = "Desabilita verificações de chaves estrangeiras (FOREIGN_KEY_CHECKS) antes da exclusão e reativa-las posteriormente.";

/* discard changes button */
"Discard changes" = "Descartar alterações";

/* description for disconnected notification */
"Disconnected from %@" = "Desconectado de %@";

/* do update operator tooltip */
"Do UPDATE where field contents match" = "Faça UPDATE onde o conteúdo do campo corresponder";

/* message of panel asking for confirmation for loading large text into the query editor */
"Do you really want to load a SQL file with %@ of data into the Query Editor?" = "Você realmente deseja carregar um arquivo SQL com %@ de dados no Editor de Query?";

/* message of panel asking for confirmation for inserting large text from dragging action */
"Do you really want to proceed with %@ of data?" = "Você realmente deseja prosseguir com %@ de dados?";

/* shutdown server : confirmation dialog : title */
"Do you really want to shutdown the server?" = "Você realmente quer desligar o servidor?";

/* dtd identifier label (Navigator) */
"DTD Identifier" = "Identificador DTD";

/* sql export dump of table label */
"Dump of table" = "Dump de tabela";

/* sql export dump of view label */
"Dump of view" = "Dump da vista";

/* text showing that app is writing dump */
"Dumping..." = "Executando o dump...";

/* duplicate object message */
"Duplicate %@ '%@' to:" = "Duplicar %1$@ '%2$@' para:";

/* duplicate func menu title */
"Duplicate Function..." = "Função duplicada...";

/* duplicate host message */
"Duplicate Host" = "Servidor duplicado";

/* duplicate proc menu title */
"Duplicate Procedure..." = "Duplicar Procedimento...";

/* duplicate table menu title */
"Duplicate Table..." = "Duplicar Tabela...";

/* duplicate user message */
"Duplicate User" = "Duplicar Usuário";

/* duplicate view menu title */
"Duplicate View..." = "Duplicar View...";

/* partial copy database support informative message */
"Duplicating the database '%@' is only partially supported as it contains objects other than tables (i.e. views, procedures, functions, etc.), which will not be copied.\n\nWould you like to continue?" = "A duplicação do banco de dados '%@' é apenas parcialmente suportada, já que contém objetos para além de tabelas (ou seja, visualizações, procedimentos, funções, etc.), que não serão copiados.\n\nVocê gostaria de continuar?";

/* edit filter */
"Edit Filters…" = "Editar filtros…";

/* Edit row button */
"Edit row" = "Editar linha";

/* toolbar item label for switching to the Table Structure tab */
"Edit Table Structure" = "Editar estrutura da tabela";

/* edit theme list label */
"Edit Theme List…" = "Editar lista de temas…";

/* edit user-defined filter */
"Edit user-defined Filters…" = "Editar Filtros definidos pelo usuário…";

/* empty query message */
"Empty query" = "Query vazia";

/* MySQL search language code - eg in http://search.mysql.com/search?q=select&site=refman-50&lr=lang_en */
"en" = "en";

/* encoding label (Navigator) */
"Encoding" = "Codificação";

/* Table Info Section : $1 = table charset */
"encoding: %1$@" = "codificação: %1$@";

/* Table Info Section : $1 = table charset, $2 = table collation */
"encoding: %1$@ (%2$@)" = "codificação: %1$@ (%2$@)";

/* Table Info Section : Table Engine */
"engine: %@" = "engine: %@";

/* enter connection details label */
"Enter connection details below, or choose a favorite" = "Insira os detalhes de conexão abaixo ou escolha um favorito";

/* SSH key password prompt */
"Enter your password for the SSH key\n\"%@\"" = "Digite sua senha para a chave SSH\n%@";

/* Bundle Editor : Fallback Input source dropdown : 'entire content' item
 Bundle Editor : Scope=Field : Input source dropdown: 'entire content' item */
"Entire Content" = "Todo o conteúdo";

/* Bundle Editor : Copy-Command-Error : error dialog title
 error */
"Error" = "Erro";

/* error adding field message */
"Error adding field" = "Erro ao adicionar campo";

/* error adding new column message */
"Error adding new column" = "Erro ao adicionar nova coluna";

/* error adding new table message */
"Error adding new table" = "Erro ao adicionar nova tabela";

/* error adding password to keychain message */
"Error adding password to Keychain" = "Erro ao adicionar senha à Keychain";

/* error changing field message */
"Error changing field" = "Erro ao alterar campo";

/* error changing table collation message */
"Error changing table collation" = "Erro ao alterar ordenação da tabela";

/* error changing table comment message */
"Error changing table comment" = "Erro ao alterar comentário da tabela";

/* error changing table encoding message */
"Error changing table encoding" = "Erro ao alterar codificação da tabela";

/* error changing table type message */
"Error changing table type" = "Erro ao alterar tipo de tabela";

/* error creating relation message */
"Error creating relation" = "Erro ao criar relação";

/* error creating trigger message */
"Error creating trigger" = "Erro ao criar trigger";

/* error for message */
"Error for" = "Erro para";

/* error for bash command ($1), $2=message */
"Error for “%1$@”:\n%2$@" = "Erro para “%1$@”:\n%2$@";

/* error moving field message */
"Error moving field" = "Erro ao mover campo";

/* error occurred */
"error occurred" = "ocorreu um erro";

/* error reading import file */
"Error reading import file." = "Erro ao ler arquivo de importação.";

/* error finding keychain item to edit message */
"Error retrieving Keychain item to edit" = "Erro ao recuperar item do Keychain para editar";

/* error retrieving table information message */
"Error retrieving table information" = "Erro ao recuperar informações da tabela";

/* error retrieving trigger information message */
"Error retrieving trigger information" = "Erro ao recuperar informações da trigger";

/* error truncating table message */
"Error truncating table" = "Erro ao truncar a tabela";

/* error updating keychain item message */
"Error updating Keychain item" = "Erro ao atualizar item Keychain";

/* error while analyzing selected items message */
"Error while analyzing selected items" = "Erro ao analisar os itens selecionados";

/* error while checking selected items message */
"Error while checking selected items" = "Erro ao verificar os itens selecionados";

/* error while converting color scheme data */
"Error while converting color scheme data" = "Erro ao converter dados do esquema de cores";

/* error while converting connection data */
"Error while converting connection data" = "Erro ao converter dados da conexão";

/* Content filters could not be converted to plist upon export - message title (ContentFilterManager) */
"Error while converting content filter data" = "Erro ao converter dados de filtro de conteúdo";

/* error while converting query favorite data */
"Error while converting query favorite data" = "Erro ao converter dados favoritos da query";

/* error while converting session data */
"Error while converting session data" = "Erro ao converter dados da sessão";

/* Error while deleting field */
"Error while deleting field" = "Erro ao excluir campo";

/* Bundle Editor : Copy-Command-Error : Copying failed error message */
"Error while duplicating Bundle content." = "Erro ao duplicar o conteúdo do pacote.";

/* error while executing javascript bash command */
"Error while executing JavaScript BASH command" = "Erro ao executar o comando BASH do JavaScript";

/* error while fetching the optimized field type message */
"Error while fetching the optimized field type" = "Erro ao obter o tipo otimizado de campo";

/* error while flushing selected items message */
"Error while flushing selected items" = "Erro ao liberar os itens selecionados";

/* error while importing table message */
"Error while importing table" = "Erro ao importar tabela";

/* Open Files : Bundle : Install-Error : error dialog title
 Open Files : Bundle : UUID : Error dialog title */
"Error while installing Bundle" = "Erro ao instalar o Pacote";

/* error while installing color theme file */
"Error while installing color theme file" = "Erro ao instalar o arquivo de tema de cor";

/* Bundle Editor : Trash-Bundle(s)-Error : error dialog title
 error while moving %@ to trash
 Open Files : Bundle : Already-Installed : Delete-Old-Error : Could not delete old bundle before installing new version. */
"Error while moving %@ to Trash." = "Erro ao mover %@ para a Lixeira.";

/* error while optimizing selected items message */
"Error while optimizing selected items" = "Erro ao otimizar os itens selecionados";

/* error while parsing CREATE TABLE syntax */
"Error while parsing CREATE TABLE syntax" = "Erro ao analisar a sintaxe CREATE TABLE";

/* error while reading connection data file */
"Error while reading connection data file" = "Erro ao ler o arquivo de dados da conexão";

/* error while reading data file */
"Error while reading data file" = "Erro ao ler o arquivo de dados";

/* error while repairing selected items message */
"Error while repairing selected items" = "Erro ao reparar itens selecionados";

/* Bundle Editor : Save-Bundle-Error : error dialog title */
"Error while saving the Bundle." = "Erro ao salvar o Pacote.";

/* Bundle Editor : Save-and-Close-Error : error dialog title */
"Error while saving %@." = "Erro ao salvar %@.";

/* Errors title */
"Errors" = "Erros";

/* Mysql Help Viewer : Help Topic: Example section title */
"Example:" = "Exemplo:";

/* Bundle Editor : BLOB dropdown : 'exclude BLOB' item */
"exclude BLOB" = "excluir BLOB";

/* execution privilege label (Navigator) */
"Execution Privilege" = "Privilégio de Execução";

/* execution privilege: %@ */
"execution privilege: %@" = "privilégio de execução: %@";

/* execution stopped message */
"Execution stopped!\n" = "Execução interrompida!\n";

/* export selected favorites menu item */
"Export Selected..." = "Exportar Selecionados...";

/* text showing that the application is importing a supplied format */
"Exporting %@" = "Exportando %@";

/* text showing that the application is exporting a Dot file */
"Exporting Dot File" = "Exportando arquivo Dot";

/* text showing that the application is exporting SQL */
"Exporting SQL" = "Exportando SQL";

/* extra label (Navigator) */
"Extra" = "Extra";

/* table structure : indexes : delete index : no columns error : title */
"Failed to remove index '%@'" = "Falha ao remover índice '%@'";

/* fatal error */
"Fatal Error" = "Erro fatal";

/* export filename favorite name token */
"Favorite" = "Favorito";

/* favorites label */
"Favorites" = "Favoritos";

/* favorites export error message */
"Favorites export error" = "Erro ao exportar Favoritos";

/* favorites import error message */
"Favorites import error" = "Erro ao importar favoritos";

/* export label showing that the app is fetching data */
"Fetching data..." = "Bsucando dados...";

/* fetching database structure data in progress */
"fetching database structure data in progress" = "buscando dados de estrutura de banco de dados em andamento";

/* fetching database structure in progress */
"fetching database structure in progress" = "buscando a estrutura do banco de dados em andamento";

/* fetching table data for completion in progress message */
"fetching table data…" = "buscando dados de tabela…";

/* popup menuitem for field (showing only if disabled) */
"field" = "campo";

/* Field */
"Field" = "Campo";

/* Query result editing error - could not match row being edited uniquely */
"Field is not editable. Couldn't identify field origin unambiguously (%ld matches)." = "O campo não é editável. Não foi possível identificar a origem do campo de forma inequívoca (%ld correspondências).";

/* field is not editable due to no table/database */
"Field is not editable. Field has no or multiple table or database origin(s)." = "O campo não é editável. O campo não possui ou múltiplas origens da tabela ou do banco de dados.";

/* Custom Query result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload data, check encoding, or try to add\na primary key field or more fields\nin your SELECT statement for table '%@'\nto identify field origin unambiguously." = "Campo não é editável, nenhum registro correspondente encontrado.\nRecarregar dados, verificar codificação, ou tente adicionar\num campo de chave primária ou mais campos\ndo seu comando SELECT para a tabela '%@'\npara identificar a origem do campo de forma inequívoca.";

/* Table Content result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload table, check the encoding, or try to add\na primary key field or more fields\nin the view declaration of '%@' to identify\nfield origin unambiguously." = "Campo não é editável, nenhum registro correspondente encontrado.\nRecarregar tabela, verificar a codificação, ou tente adicionar\num campo chave primária ou mais campos\nna declaração de exibição do '%@' para identificar\na origem do campo \n de forma inequívoca.";

/* error while reading data file */
"File couldn't be read." = "O arquivo não pôde ser lido.";
"File couldn't be read: %@\n\nIt will be deleted." = "O arquivo não pode ser lido: %@\n\nEle será excluído.";

/* File read error title (Import Dialog) */
"File read error" = "Erro de leitura";

/* All export files already exist explanatory text */
"Files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "Arquivos com os mesmos nomes já existem na pasta de destino. Substituí-los irá substituir seus conteúdos atuais.";

/* filter label */
"Filter" = "Filtro";

/* apply filter label */
"Apply Filter(s)" = "Aplicar Filtro(s)";

/* filter tables menu item */
"Filter Tables" = "Filtrar Tabelas";

/* export source */
"Filtered table content" = "Conteúdo da tabela filtrada";

/* filtering failed. please try again. tooltip */
"Filtering failed. Please try again." = "A filtragem falhou. Por favor, tente novamente.";

/* Filtering table task description */
"Filtering table..." = "Filtrando tabela...";

/* description for finished exporting notification */
"Finished exporting to %@" = "Exportação para %@ concluída";

/* description for finished importing notification */
"Finished importing %@" = "Importação %@ concluída";

/* FLUSH one or more tables - result title */
"Flush %@" = "Liberar %@";

/* flush selected items menu item */
"Flush Selected Items" = "Liberar itens selecionados";

/* flush table menu item */
"Flush Table" = "Liberar Tabela";

/* flush table failed message */
"Flush table failed." = "Liberação de tabela falhou.";

/* flush view menu item */
"Flush View" = "Liberar View";

/* title of panel when successfully flushed privs */
"Flushed Privileges" = "Privilégios liberados";

/* For BIT fields only “1” or “0” are allowed. */
"For BIT fields only “1” or “0” are allowed." = "Para os campos BIT, apenas são permitidos \"1\" ou \"0\".";

/* force table deletion button text */
"Force delete (disables integrity checks)" = "Forçar exclusão (desativa verificações de integridade)";

/* full text index menu item title */
"FULLTEXT" = "FULLTEXT";

/* function */
"function" = "função";

/* header for function info pane */
"FUNCTION INFORMATION" = "INFORMAÇÃO DA FUNÇÃO";

/* functions */
"functions" = "funções";

/* Bundle Editor : Scope dropdown : 'general' item
 general menu item label
 general preference pane name */
"General" = "Geral";

/* general preference pane tooltip */
"General Preferences" = "Preferências Gerais";

/* Bundle Editor : Outline View : 'General' item : tooltip */
"General Scope\ncommands will run application-wide" = "Os comandos gerais do escopo\nserão executados em todo o aplicativo";

/* generating print document status message */
"Generating print document..." = "Gerando documento de impressão...";

/* export header generation time label */
"Generation Time" = "Tempo de geração";

/* Content Filter Manager : Filter Entry List: 'Global' Header
 Query Favorites : List : Section Heading : global query favorites */
"Global" = "Global";

/* Query Favorites : List : Section Heading : global : tooltip */
"Globally stored favorites" = "Favoritos armazenados globalmente";

/* Preferences : Notifications : status text : notification setup */
"Notifications are delivered via the Notification Center." = "As notificações são entregues através do Centro de Notificações.";

/* Gzip compression export summary - within a sentence */
"Gzip compression" = "Compressão Gzip";

/* MySQL Help Viewer : Results list : Page title */
"Help topics for %@" = "Tópicos de ajuda para %@";

/* hide console */
"Hide Console" = "Ocultar Console";

/* hide navigator */
"Hide Navigator" = "Esconder a barra de navegação";

/* hide tab bar */
"Hide Tab Bar" = "Ocultar Barra de Abas";

/* Hide Toolbar menu item */
"Hide Toolbar" = "Ocultar barra de ferramentas";

/* export filename host token
 export header host label */
"Host" = "Servidor";

/* description of double */
"IEEE 754 double-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 valor de ponto flutuante de precisão duplo. O M é o número máximo de dígitos, dos quais D pode estar depois do ponto decimal. Nota: Muitos números decimais só podem ser aproximados por valores de ponto flutuante. Veja DECIMAL se você precisar de resultados exatos.";

/* description of float */
"IEEE 754 single-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 valor flutuante de precisão única. O M é o número máximo de dígitos, dos quais D pode estar depois do ponto decimal. Nota: Muitos números decimais só podem ser aproximados por valores de ponto flutuante. Veja DECIMAL se você precisar de resultados exatos.";

/* ignore button */
"Ignore" = "Ignorar";

/* ignore errors button */
"Ignore All Errors" = "Ignorar todos os erros";

/* ignore all fields menu item */
"Ignore all Fields" = "Ignorar todos os campos";

/* ignore field label */
"Ignore field" = "Ignorar campo";

/* ignore field label */
"Ignore Field" = "Ignorar campo";

/* import button */
"Import" = "Importar";

/* import all fields menu item */
"Import all Fields" = "Importar todos os campos";

/* sql import : charset error alert : continue button */
"Import Anyway" = "Importar mesmo assim";

/* import cancelled message */
"Import cancelled!\n" = "Importação cancelada!\n";

/* Import Error title */
"Import Error" = "Erro ao importar";

/* import field operator tooltip */
"Import field" = "Campo de importação";

/* import file does not exist message */
"Import file does not exist." = "Arquivo de importação não existe.";

/* Export file format cannot be imported warning */
"Import of the selected data is currently not supported." = "A importação dos dados selecionados não é suportada atualmente.";

/* SQL import progress text */
"Imported %@ of %@" = "Importado %1$@ de %2$@";

/* CSV import progress text where total size is unknown */
"Imported %@ of CSV data" = "Importado %@ de dados CSV";

/* SQL import progress text where total size is unknown */
"Imported %@ of SQL" = "Importado %@ de SQL";

/* text showing that the application is importing CSV */
"Importing CSV" = "Importando CSV";

/* text showing that the application is importing SQL */
"Importing SQL" = "Importando SQL";

/* Bundle Editor : BLOB dropdown : 'include BLOB' item */
"include BLOB" = "incluir BLOB";

/* include content table column tooltip */
"Include content" = "Incluir conteúdo";

/* sql import error message */
"Incompatible encoding in SQL file" = "Codificação incompatível no arquivo SQL";

/* header for blank info pane */
"INFORMATION" = "INFORMAÇÕES";

/* New Table Sheet : Table Collation Dropdown : Default inherited from database
 New Table Sheet : Table Encoding Dropdown : Default inherited from database */
"Inherit from database (%@)" = "Herdar do banco de dados (%@)";

/* initializing export label */
"Initializing..." = "Inicializando...";

/* Bundle Editor : Scope dropdown : 'input field' item
 input field menu item label */
"Input Field" = "Campo de entrada";

/* input field  doesn't support insertion of snippets. */
"Input Field doesn't support insertion of snippets." = "Campo de entrada não suporta inserção de snippets.";

/* input field is not editable. */
"Input Field is not editable." = "O campo de entrada não é editável.";

/* Bundle Editor : Outline View : 'Input Field' item : tooltip */
"Input Field Scope\ncommands will run on each text input field" = "O Escopo do Campo de Entrada\ncomandos serão executados em cada campo de entrada de texto";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as snippet' item */
"Insert as Snippet" = "Inserir como Snippet";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as text' item */
"Insert as Text" = "Inserir como Texto";

/* Bundle Editor : Outline View : 'BUNDLES' item : tooltip */
"Installed Bundles" = "Pacotes instalados";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog title */
"Installing Bundle" = "Instalando o Pacote";

/* insufficient details message */
"Insufficient connection details" = "Detalhes de conexão insuficientes";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please enter at least the hostname." = "Detalhes insuficientes fornecidos para estabelecer uma conexão. Por favor, insira pelo menos o nome do servidor.";

/* insufficient SSH tunnel details informative message */
"Insufficient details provided to establish a connection. Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Detalhes insuficientes fornecidos para estabelecer uma conexão. Por favor, digite o nome de servidor para o túnel SSH, ou desative o túnel SSH.";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please provide at least a host." = "Detalhes insuficientes fornecidos para estabelecer uma conexão. Por favor, forneça pelo menos um servidor.";

/* Interpret data as: */
"Interpret data as:" = "Interpretar dados como:";

/* Invalid database very short status message */
"Invalid database" = "Banco de dados inválido";

/* export : import settings : file error title */
"Invalid file supplied!" = "Arquivo fornecido inválido!";

/* table content : editing : error message title when parsing as hex string failed */
"Invalid hexadecimal value" = "Valor hexadecimal inválido";

/* is deterministic label (Navigator) */
"Is Deterministic" = "É Determinístico";

/* is nullable label (Navigator) */
"Is Nullable" = "É nulável";

/* is updatable: %@ */
"is updatable: %@" = "é atualizável: %@";

/* items */
"items" = "itens";

/* javascript exception */
"JavaScript Exception" = "Exceção de JavaScript";

/* javascript parsing error */
"JavaScript Parsing Error" = "Erro ao analisar JavaScript";

/* key label (Navigator) */
"Key" = "Chave";

/* keyword label for color table (Prefs > Editor) */
"Keyword" = "Palavra-chave";

/* kill button */
"Kill" = "Matar";

/* kill connection message */
"Kill connection?" = "Matar a conexão?";

/* kill query message */
"Kill query?" = "Matar query?";

/* Last Error Message */
"Last Error Message" = "Última mensagem de erro";

/* Last Used entry in favorites menu */
"Last Used" = "Usado pela última vez";

/* range for json type */
"Limited to @@max_allowed_packet" = "Limitado a @@max_allowed_packet";

/* Loading table task string */
"Loading %@..." = "Carregando %@...";

/* Loading database task string */
"Loading database '%@'..." = "Carregando banco de dados '%@'...";

/* Loading history entry task desc */
"Loading history entry..." = "Carregando entrada no histórico...";

/* Loading table page task string */
"Loading page %lu..." = "Carregando página %lu...";

/* Loading referece task string */
"Loading reference..." = "Carregando referência...";

/* Loading table data string */
"Loading table data..." = "Carregando dados de tabela...";

/* Low memory export summary */
"Low memory" = "Pouca memória";

/* range of decimal */
"M (precision): Up to 65 digits\nD (scale): 0 to 30 digits" = "M (precisão): Até 65 dígitos\nD (escala): 0 a 30 dígitos";

/* range for blob type
 range for varbinary type */
"M: %@ to %@ bytes" = "M: %1$@ a %2$@ bytes";

/* range for text type
 range for varchar type */
"M: %@ to %@ characters" = "M: %1$@ a %2$@ caracteres";

/* range for longtext type */
"M: %@ to %@ characters (4 GiB)" = "M: %1$@ a %2$@ caracteres (4 GiB)";

/* range for binary type */
"M: 0 to 255 bytes" = "M: 0 a 255 bytes";

/* range for char type */
"M: 0 to 255 characters" = "M: 0 a 255 caracteres";

/* range for bit type */
"M: 1 (default) to 64" = "M: 1 (padrão) a 64";

/* connection view : ssl : key file picker : wrong format error description */
"Make sure the file contains a RSA private key and is using PEM encoding." = "Certifique-se de que o arquivo contém uma chave RSA privada e está usando a codificação PEM.";

/* connection view : ssl : client cert picker : wrong format error description */
"Make sure the file contains a X.509 client certificate and is using PEM encoding." = "Certifique-se de que o arquivo contém um certificado de cliente X.509 e está usando a codificação PEM.";

/* match field menu item */
"Match Field" = "Campo de Correspondência";

/* Shown when user inserts too many arguments (ContentFilterManager) */
"Maximum number of arguments is 2!" = "O número máximo de argumentos é 2!";

/* Maximum text length is set to %ld. */
"Maximum text length is set to %ld." = "Comprimento máximo do texto é definido como %ld.";

/* Maximum text length is set to %ld. Inserted text was truncated. */
"Maximum text length is set to %ld. Inserted text was truncated." = "O comprimento máximo do texto é definido para %ld. O texto inserido foi truncado.";

/* Maximum text length is set to %llu. */
"Maximum text length is set to %llu." = "Comprimento máximo do texto é definido como %llu.";

/* Maximum text length is set to %llu. Inserted text was truncated. */
"Maximum text length is set to %llu. Inserted text was truncated." = "O comprimento máximo do texto é definido para %llu. O texto inserido foi truncado.";

/* message column title */
"Message" = "Mensagem";

/* mgtemplateengine error */
"MGTemplateEngine Error" = "MGTemplateEngine Error";

/* export filename date token */
"Month" = "Mês";

/* multiple selection */
"multiple selection" = "múltipla seleção";

/* MySQL connecting very short status message */
"MySQL connecting..." = "Conectando ao MySQL...";

/* mysql error message */
"MySQL Error" = "Erro no MySQL";

/* mysql help */
"MySQL Help" = "Ajuda MySQL";

/* MySQL Help for Selection */
"MySQL Help for Selection" = "Ajuda do MySQL para seleção";

/* MySQL Help for Word */
"MySQL Help for Word" = "Ajuda do MySQL para Word";

/* mysql help categories */
"MySQL Help – Categories" = "Ajuda do MySQL – Categorias";

/* mysql said message */
"MySQL said:" = "Mensagem do MySQL:";

/* shutdown server : error dialog : message */
"MySQL said:\n%@" = "Mensagem do MySQL:\n%@";

/* message of panel when error while adding row to db */
"MySQL said:\n\n%@" = "Mensagem do MySQL:\n\n%@";

/* Preferences : Themes : Initial filename for 'Export' */
"MyTheme" = "MeuTema";

/* network preference pane name */
"Network" = "Rede";

/* network preference pane tooltip */
"Network Preferences" = "Preferências de rede";

/* file preference pane name */
"Files" = "Arquivos";

/* file preference pane tooltip */
"File Preferences" = "Preferências do arquivo";

/* Bundle Editor : Default name for new bundle in the list on the left */
"New Bundle" = "Novo Pacote";

/* new column name placeholder string */
"New Column Name" = "Nova Coluna";

/* new favorite name */
"New Favorite" = "Novo Favorito";

/* Content Filter Manager : Initial name for new filter */
"New Filter" = "Novo Filtro";

/* new folder placeholder name */
"New Folder" = "Nova Pasta";

/* Bundle Editor : Default name for a new bundle in the menu */
"New Name" = "Novo Nome";

/* new table menu item */
"New Table" = "Nova Tabela";

/* error that no color theme found */
"No color theme data found." = "Nenhum dado de cor do tema encontrado.";

/* No compression export summary - within a sentence */
"no compression" = "sem compressão";

/* no connection available message */
"No connection available" = "Nenhuma conexão disponível";

/* no connection data found */
"No connection data found." = "Nenhum dado de conexão encontrado.";

/* No content filters were found in file to import (ContentFilterManager) */
"No content filters found." = "Nenhum filtro de conteúdo encontrado.";

/* no data found */
"No data found." = "Nenhum dado encontrado.";

/* No errors title */
"No errors" = "Sem erros";

/* No favorites entry in favorites menu */
"No Favorties" = "Sem Favoritos";

/* All export files creation error title */
"No files could be created" = "Nenhum arquivo pôde ser criado";

/* no item found message */
"No item found" = "Nenhum item encontrado";

/* SSH tunnel could not be created because no local port could be allocated */
"No local port could be allocated for the SSH Tunnel." = "Nenhuma porta local pode ser alocada para o túnel SSH.";

/* header for no matches in filtered list */
"NO MATCHES" = "SEM OCORRÊNCIAS";

/* no optimized field type found. message */
"No optimized field type found." = "Nenhum tipo otimizado de campo encontrado.";

/* error that no query favorites found */
"No query favorites found." = "Nenhuma query favorita encontrada.";

/* Mysql Help Viewer : Search : No results */
"No results found." = "Nenhum resultado encontrado.";

/* Bundle Editor : Fallback Input source dropdown : 'none' item
 Bundle Editor : Scope=? : ? dropdown: 'None' item
 Bundle Editor : Scope=Data-Table : Input source dropdown: 'none' item
 Bundle Editor : Scope=Data-Table : Output dropdown : 'none' item
 Bundle Editor : Scope=Data-Table : Trigger dropdown : 'none' item
 Bundle Editor : Scope=Field : Input source dropdown: 'None' item
 Bundle Editor : Scope=Field : Output dropdown : 'none' item
 Bundle Editor : Scope=Field : Trigger dropdown : 'none' item
 Bundle Editor : Scope=General : Input source dropdown: 'None' item
 Bundle Editor : Scope=General : Output dropdown : 'none' item
 Bundle Editor : Scope=General : Trigger dropdown : 'none' item
 compression: none */
"None" = "Nada";

/* not available label */
"Not available" = "Não disponível";

/* Argument count (ContentFilterManager) */
"Number of arguments: %lu" = "Número de argumentos: %lu";

/* numeric label for color table (Prefs > Editor) */
"Numeric" = "Numérico";

/* Bundle Editor : Copy-Command-Error : OK button
 Bundle Editor : Save-and-Close-Error : OK button
 Bundle Editor : Save-Bundle-Error : OK button
 Bundle Editor : Trash-Bundle(s)-Error : OK button
 OK
 OK button
 Open Files : Bundle : Already-Installed : Delete-Old-Error : OK button
 Open Files : Bundle : Install-Error : OK button
 Open Files : Bundle : UUID : OK button
 Preferences : Network : Custom SSH client : warning dialog : accept button */
"OK" = "Ok";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+1 rows instead of n selected were deleted. */
"One additional row was removed!" = "Uma linha adicional foi removida!";

/* Table Content : Remove Row : Result : Too Few : Part 1 : Only n-1 of n selected rows were deleted. */
"One row was not removed." = "Uma linha não foi removida.";

/* Only one dragged item allowed. */
"Only one dragged item allowed." = "Apenas um item arrastado é permitido.";

/* partial copy database support message */
"Only Partially Supported" = "Apenas parcialmente suportada";

/* open function in new table title */
"Open Function in New Tab" = "Abrir Função em Nova Aba";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Function in New Window" = "Abrir Função em Nova Janela";

/* open connection in new tab context menu item */
"Open in New Tab" = "Abrir em Nova Aba";

/* menu item open in new window */
"Open in New Window" = "Abrir em Nova Janela";

/* open procedure in new table title */
"Open Procedure in New Tab" = "Abrir procedimento em nova aba";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Procedure in New Window" = "Abrir procedimento em nova janela";

/* open table in new tab title
 open table in new table title */
"Open Table in New Tab" = "Abrir Tabela em Nova Aba";

/* Table List : Context Menu : Duplicate connection to new window
 Table List : Gear Menu : Duplicate connection to new window */
"Open Table in New Window" = "Abrir Tabela em Nova Janela";

/* open view in new tab title
 open view in new table title */
"Open View in New Tab" = "Abrir View em Nova Aba";

/* Tables List : Context Menu : Duplicate connection to new window
 Tables List : Gear Menu : Duplicate connection to new window */
"Open View in New Window" = "Abrir View em Nova Janela";

/* menu item open %@ in new window */
"Open %@ in New Window" = "Abrir o %@ em uma nova janela";

/* OPTIMIZE one or more tables - result title */
"Optimize %@" = "Otimizar %@";

/* optimize selected items menu item */
"Optimize Selected Items" = "Otimizar itens selecionados";

/* optimize table menu item */
"Optimize Table" = "Otimizar Tabela";

/* optimize table failed message */
"Optimize table failed." = "Otimizar tabela falhou.";

/* Optimized type for field %@ */
"Optimized type for field '%@'" = "Tipo otimizado para o campo '%@'";

/* optional placeholder string */
"optional" = "opcional";

/* Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed. */
"Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed." = "O parâmetro informado não pôde ser interpretado. Apenas sequências de caracteres ou matriz (com 2 elementos) são permitidos.";

/* Permission Denied */
"Permission Denied" = "Permissão Negada";

/* please choose a favorite connection view label */
"Please choose a favorite" = "Por favor, escolha um favorito";

/* message of panel when ssh details are incomplete */
"Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Por favor, digite o nome de servidor para o túnel SSH, ou desative o túnel SSH.";

/* Please enter the password */
"Please enter the password for ‘%@’:" = "Por favor digite a senha para ‘%@’:";

/* print button */
"Print" = "Impressão";

/* print page menu item title */
"Print Page…" = "Imprimir Página…";

/* privileges label (Navigator) */
"Privileges" = "Privilégios";

/* procedure */
"procedure" = "procedimento";

/* header for procedure info pane */
"PROCEDURE INFORMATION" = "INFORMAÇÕES DA PROCEDURE";

/* procedures */
"procedures" = "procedimentos";

/* proceed button */
"Proceed" = "Continuar";

/* header for procs & funcs list */
"PROCS & FUNCS" = "PROCS & FUNCS";

/* toolbar item label for switching to the Run Query tab */
"Query" = "Query";

/* query background label for color table (Prefs > Editor) */
"Query Background" = "Fundo da Query";

/* Query cancelled error */
"Query cancelled." = "Query cancelada.";

/* Query cancel by resetting connection error */
"Query cancelled.  Please note that to cancel the query the connection had to be reset; transactions and connection variables were reset." = "Query cancelada. Por favor, note que para cancelar a query, a conexão teve que ser redefinida; as variáveis de conexão e transações foram redefinidas.";

/* query editor preference pane name */
"Query Editor" = "Editor de Query";

/* query editor preference pane tooltip */
"Query Editor Preferences" = "Preferências do Editor de Query";

/* query logging currently disabled label
 query logging disabled label */
"Query logging is currently disabled" = "O Log de queries está atualmente desativado";

/* query result print heading */
"Query Result" = "Resultado da query";

/* export source */
"Query results" = "Resultados da query";

/* Query Status */
"Query Status" = "Estado da query";

/* table status : row count query failed : error title */
"Querying row count failed" = "Falha ao obter contagem de linhas";

/* Quick connect item label */
"Quick Connect" = "Conexão Rápida";

/* quote label for color table (Prefs > Editor) */
"Quote" = "Citação";

/* databsse rename unsupported informative message */
"Renaming the database '%@' is currently unsupported as it contains objects other than tables (i.e. views, procedures, functions, etc.).\n\nIf you would like to rename a database please use the 'Duplicate Database', move any non-table objects manually then drop the old database." = "Renomear o banco de dados '%@' não é suportado atualmente, já que contém objetos diferentes de tabelas (ou seja, views, procedimentos, funções, etc.).\n\nSe você gostaria de renomear um banco de dados, por favor use 'Duplicar banco de dados', mova todos os objetos não-tabela manualmente, em seguida, solte o banco de dados antigo.";

/* range for serial type */
"Range: %@ to %@" = "Alcance: %1$@ a %2$@";

/* range for time type */
"Range: -838:59:59.0 to 838:59:59.0\nF (precision): 0 (1s) to 6 (1µs)" = "Intervalo: -838:59:59:59,0 a 838:59:59,0\nF (precisão): 0 (1s) a 6 (1➲ s)";

/* range for year type */
"Range: 0000, 1901 to 2155" = "Intervalo: 0000, 1901 a 2155";

/* range for set type */
"Range: 1 to 64 members\n1, 2, 3, 4 or 8 bytes storage" = "Intervalo: entre 1 e 64 membros\n1, 2, 3, 4 ou 8 bytes de armazenamento";

/* range for datetime type */
"Range: 1000-01-01 00:00:00.0 to 9999-12-31 23:59:59.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Intervalo: 1000-01-01 00:00.0 a 9999-12-31 23:59:59.999999\nF (precisão): 0 (1s) a 6 (1µs)";

/* range for date type */
"Range: 1000-01-01 to 9999-12-31" = "Intervalo: 1000-01-01 a 9999-12-31";

/* range for timestamp type */
"Range: 1970-01-01 00:00:01.0 to 2038-01-19 03:14:07.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Intervalo: 1970-01-01 00:00:01.0 a 2038-01-19 03:14:07,999999\nF (precisão): 0 (1s) a 6 (1µs)";

/* text showing that app is reading dump */
"Reading..." = "Lendo...";

/* menu item to refresh databases */
"Refresh Databases" = "Atualizar Bancos de Dados";

/* refresh list menu item */
"Refresh List" = "Atualizar Lista";

/* toolbar item label for switching to the Table Relations tab */
"Relations" = "Relacionamentos";

/* Relations tab subtitle showing table name */
"Relations for table: %@" = "Relacionamentos para a tabela: %@";

/* reload bundles menu item label */
"Reload Bundles" = "Recarregar Pacotes";

/* Reloading data task description */
"Reloading data..." = "Recarregando dados...";

/* Reloading table task string */
"Reloading..." = "Recarregando...";

/* remote error */
"Remote Error" = "Erro Remoto";

/* Bundle Editor : Remove-Bundle: remove button
 remove button */
"Remove" = "Remover";

/* remove all button */
"Remove All" = "Remover Todos";

/* remove all query favorites message */
"Remove all query favorites?" = "Remover todas as queries favoritas?";

/* Bundle Editor : Remove-Bundle: remove dialog title */
"Remove selected Bundle?" = "Remover Pacote selecionado?";

/* remove selected content filters message */
"Remove selected content filters?" = "Remover filtros de conteúdo selecionados?";

/* remove selected query favorites message */
"Remove selected query favorites?" = "Remover queries selecionadas favoritas?";

/* removing field task status message */
"Removing field..." = "Removendo campo...";

/* removing index task status message */
"Removing index..." = "Removendo índice...";

/* rename database message */
"Rename database '%@' to:" = "Renomear banco de dados '%@' para:";

/* rename func menu title */
"Rename Function..." = "Renomear Função...";

/* rename proc menu title */
"Rename Procedure..." = "Renomear Procedimento...";

/* rename table menu title */
"Rename Table..." = "Renomear Tabela...";

/* rename view menu title */
"Rename View..." = "Renomear View...";

/* REPAIR one or more tables - result title */
"Repair %@" = "Reparar %@";

/* repair selected items menu item */
"Repair Selected Items" = "Reparar itens selecionados";

/* repair table menu item */
"Repair Table" = "Reparar Tabela";

/* repair table failed message */
"Repair table failed." = "A reparação da tabela falhou.";

/* Replace button */
"Replace" = "Substituir";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace entire content' item */
"Replace Entire Content" = "Substituir todo o conteúdo";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace selection' item */
"Replace Selection" = "Substituir seleção";

/* description of year */
"Represents a 4 digit year value, stored as 1 byte. Invalid values are converted to 0000 and two digit values 0 to 69 will be converted to years 2000 to 2069, resp. values 70 to 99 to years 1970 to 1999.\nThe YEAR(2) type was removed in MySQL 5.7.5." = "Representa um valor de ano de 4 dígitos, armazenado como 1 byte. Valores inválidos são convertidos para 0000 e valores de dois dígitos 0 a 69 serão convertidos nos anos 2000 a 2069, resp. valores de 70 a 99 aos anos de 1970 a 1999.\nO tipo de YEAR (2) foi removido no MySQL 5.7.5.";

/* description of multilinestring */
"Represents a collection of LineStrings." = "Representa uma coleção de LineStrings.";

/* description of geometrycollection */
"Represents a collection of objects of any other single- or multi-valued spatial type. The only restriction being, that all objects must share a common coordinate system." = "Representa uma coleção de objetos de qualquer outro tipo espacial singular ou multi-valorizado. A única restrição, ou seja, que todos os objectos devem partilhar um sistema de coordenação comum.";

/* description of multipolygon */
"Represents a collection of Polygons. The Polygons making up the MultiPolygon must not intersect." = "Representa uma coleção de polígonos. Os polígonos que compõem o Multipolígono não devem se cruzar.";

/* description of multipoint */
"Represents a set of Points without specifying any kind of relation and/or order between them." = "Representa um conjunto de Pontos sem especificar qualquer tipo de relação e/ou ordem entre eles.";

/* description of point */
"Represents a single location in coordinate space using X and Y coordinates. The point is zero-dimensional." = "Representa um único local no espaço das coordenadas usando as coordenadas X e Y. O ponto é de zero dimensão.";

/* description of linestring */
"Represents an ordered set of coordinates where each consecutive pair of two points is connected by a straight line." = "Representa um conjunto de coordenadas ordenado em que cada par consecutivo de dois pontos está conectado por uma linha reta.";

/* required placeholder string */
"required" = "obrigatório";

/* description of smallint */
"Requires 2 bytes storage space. M is the optional display width and does not affect the possible value range." = "Requer espaço de armazenamento de 2 bytes. M é a largura de exibição opcional e não afeta o possível intervalo de valor.";

/* description of mediumint */
"Requires 3 bytes storage space. M is the optional display width and does not affect the possible value range." = "Requer espaço de armazenamento de 3 bytes. M é a largura de exibição opcional e não afeta o possível intervalo do valor.";

/* description of int */
"Requires 4 bytes storage space. M is the optional display width and does not affect the possible value range. INTEGER is an alias to this type." = "Requer espaço de armazenamento de 4 bytes. M é a largura de exibição opcional e não afeta o possível intervalo do valor. INTEGER é um apelido para este tipo.";

/* description of bigint */
"Requires 8 bytes storage space. M is the optional display width and does not affect the possible value range. Note: Arithmetic operations might fail for large numbers." = "Requer espaço de armazenamento de 8 bytes. M é a largura de exibição opcional e não afeta o possível intervalo do valor. Nota: Operações aritméticas podem falhar para números grandes.";

/* reset auto_increment after deletion of all rows message */
"Reset AUTO_INCREMENT after deletion\n(only for Delete ALL ROWS IN TABLE)?" = "Redefinir AUTO_INCREMENT após a exclusão\n(somente para Delete ALL ROWS IN TABLE)?";

/* delete selected row button */
"Delete Selected Row" = "Excluir linha selecionada";

/* delete selected rows button */
"Delete Selected Rows" = "Excluir linhas selecionadas";

/* delete all rows in table button */
"Delete ALL ROWS IN TABLE" = "Delete ALL ROWS IN TABLE";

/* Restoring session task description */
"Restoring session..." = "Restaurando sessão...";

/* return type label (Navigator) */
"Return Type" = "Tipo de retorno";

/* return type: %@ */
"return type: %@" = "tipo de retorno: %@";

/* singular word for row */
"row" = "linha";

/* plural word for rows */
"rows" = "linhas";

/* text showing how many rows are in the limited filter match */
"Rows %@ - %@ from filtered matches" = "Linhas %1$@ - %2$@ das filtradas";

/* text showing how many rows are in the limited result */
"Rows %@ - %@ of %@%@ from table" = "Linhas %1$@ - %2$@ de %3$@%4$@ da tabela";

/* Table Info Section : number of rows (exact value) */
"rows: %@" = "linhas: %@";

/* Table Info Section : number of rows (estimated value) */
"rows: ~%@" = "linhas: ~%@";

/* run all button */
"Run All" = "Executar todos";

/* Run All menu item title */
"Run All Queries" = "Executar todas as queries";

/* Title of button to run current query in custom query view */
"Run Current" = "Executar atual";

/* Title of action menu item to run current query in custom query view */
"Run Current Query" = "Executar Query Atual";

/* toolbar item label for switching to the Run Query tab */
"Run Custom Query" = "Executar query personalizada";

/* Title of button to run query just before text caret in custom query view */
"Run Previous" = "Executar Anterior";

/* Title of action menu item to run query just before text caret in custom query view */
"Run Previous Query" = "Executar query anterior";

/* Title of action menu item to run selected text in custom query view */
"Run Selected Text" = "Executar Texto Selecionado";

/* Title of button to run selected text in custom query view */
"Run Selection" = "Executar seleção";

/* Running multiple queries string */
"Running query %i of %lu..." = "Executando query %1$i de %2$lu...";

/* Running multiple queries string */
"Running query %ld of %lu..." = "Executando query %1$ld de %2$lu...";

/* Running single query string */
"Running query..." = "Executando query...";

/* Save trigger button label */
"Save" = "Salvar";

/* Save All to Favorites */
"Save All to Favorites" = "Salvar Tudo nos Favoritos";

/* save as button title */
"Save As..." = "Salvar Como...";

/* Bundle Editor : BLOB dropdown : 'save BLOB as dat file' item */
"save BLOB as dat file" = "salvar BLOB como arquivo dat";

/* Bundle Editor : BLOB dropdown : 'save BLOB as image file' item */
"save BLOB as image file" = "salvar BLOB como arquivo de imagem";

/* Save Current Query to Favorites */
"Save Current Query to Favorites" = "Salvar a Query Atual nos Favoritos";

/* save page as menu item title */
"Save Page As…" = "Salvar Página Como…";

/* Save Queries… */
"Save Queries…" = "Salvar Queries…";

/* Save Query… */
"Save Query…" = "Salvar Query…";

/* Save Selection to Favorites */
"Save Selection to Favorites" = "Salvar Seleção nos Favoritos";

/* save view as button title */
"Save View As..." = "Salvar View Como...";

/* schema path header for completion tooltip */
"Schema path:" = "Caminho do esquema:";

/* Search in MySQL Documentation */
"Search in MySQL Documentation" = "Buscar na Documentação do MySQL";

/* Search in MySQL Help */
"Search in MySQL Help" = "Procurar na ajuda do MySQL";

/* Select Active Query */
"Select Active Query" = "Selecionar Query Ativa";

/* toolbar item for selecting a db */
"Select Database" = "Selecionar base de dados";

/* selected items */
"selected items" = "itens selecionados";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as comma-separated' item */
"Selected Rows (CSV)" = "Linhas selecionadas (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as SQL' item */
"Selected Rows (SQL)" = "Linhas selecionadas (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as tab-separated' item */
"Selected Rows (TSV)" = "Linhas Selecionadas (TSV)";

/* Bundle Editor : Scope=Field : Input source dropdown: 'selected text' item */
"Selected Text" = "Texto Selecionado";

/* selection label for color table (Prefs > Editor) */
"Selection" = "Seleção";

/* table structure : indexes : delete index : no columns error : description */
"Sequel Ace could not find any columns belonging to this index. Maybe it has been removed already?" = "Sequel Ace não conseguiu encontrar colunas pertencentes a este índice. Será que já foi removido?";

/* Preferences : Network : Custom SSH client : warning dialog message */
"Sequel Ace only supports and is tested with the default OpenSSH client versions included with macOS. Using different clients might cause connection issues, security risks or not work at all.\n\nPlease be aware, that we cannot provide support for such configurations." = "O Sequel Ace só suporta e é testado com as versões padrão do cliente OpenSSH incluídas no macOS. Usar diferentes clientes pode causar problemas de conexão, riscos de segurança ou nem sequer funcionar.\n\nPor favor, saiba que não podemos fornecer suporte para tais configurações.";

/* sequelace URL scheme command not supported. */
"sequelace URL scheme command not supported." = "comando Sequel Ace não suportado.";

/* sequelace url Scheme Error */
"sequelace URL Scheme Error" = "erro de esquema de URL Sequel Ace";

/* Table Structure : Collation dropdown : 'item is the same as the collation of server' marker
 Table Structure : Encoding dropdown : 'item is server default' marker */
"Server" = "Servidor";

/* Add Database : Charset dropdown : default item ($1 = charset name)
 Add Database : Collation dropdown : default item ($1 = collation name) */
"Server Default (%@)" = "Servidor Padrão (%@)";

/* server processes window title (var = hostname) */
"Server Processes on %@" = "Processos de Servidor em %@";

/* Initial filename for 'Save session' file */
"Session" = "Sessão";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html' item */
"Show as HTML" = "Ver como HTML";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html tooltip' item */
"Show as HTML Tooltip" = "Mostrar como dica HTML";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as text tooltip' item */
"Show as Text Tooltip" = "Mostrar como dica de Texto";

/* show console */
"Show Console" = "Ver console";

/* show create func syntax menu item */
"Show Create Function Syntax..." = "Mostrar sintaxe CREATE FUNCTION";

/* show create proc syntax menu item */
"Show Create Procedure Syntax..." = "Mostrar sintaxe CREATE PROCEDURE...";

/* show create syntaxes menu item */
"Show Create Syntaxes..." = "Mostrar Sintaxe de Criação...";

/* show create table syntax menu item */
"Show Create Table Syntax..." = "Mostrar Sintaxe de Criar Tabela...";

/* show create view syntax menu item */
"Show Create View Syntax..." = "Mostrar Sintaxe de Criar View...";

/* Show detail button */
"Show Detail" = "Mostrar Detalhes";

/* MySQL Help Viewer : Results list : Link tooltip */
"Show MySQL help for %@" = "Mostrar ajuda do MySQL para %@";

/* show navigator */
"Show Navigator" = "Mostrar barra de navegação";

/* show tab bar */
"Show Tab Bar" = "Mostrar barra de abas";

/* tooltip for toolbar item for show console */
"Show the console which shows all MySQL commands performed by Sequel Ace" = "Mostrar o console que mostra todos os comandos MySQL executados pela Sequel Ace";

/* Show Toolbar menu item */
"Show Toolbar" = "Mostrar Barra de Ferramentas";

/* filtered item count */
"Showing %lu of %lu processes" = "Mostrando %1$lu de %2$lu processos";

/* shutdown server : confirmation dialog : shutdown button */
"Shutdown" = "Desligar";

/* shutdown server : error dialog : title */
"Shutdown failed!" = "Desligar falhou!";

/* range of integer types */
"Signed: %@ to %@\nUnsigned: %@ to %@" = "Assinado: %1$@ para %2$@\nSem assinatura: %3$@ para %4$@";

/* Table Info Section : table size on disk */
"size: %@" = "tamanho: %@";

/* skip existing button */
"Skip existing" = "Pular existente";

/* skip problems button */
"Skip problems" = "Pular problemas";

/* beta build label */
"Beta Build" = "Versão Beta";

/* socket connection failed title */
"Socket connection failed!" = "A conexão do socket falhou!";

/* socket not found title */
"Socket not found!" = "Socket não encontrado!";

/* Some export folders not writable explanatory text */
"Some of the target export folders are not writable.  Please select a new export location and try again." = "Algumas das pastas de exportação de destino não são graváveis. Por favor, selecione um novo local de exportação e tente novamente.";

/* Some export folders missing explanatory text */
"Some of the target export folders no longer exist.  Please select a new export location and try again." = "Algumas das pastas de exportação de destino não existem mais. Por favor, selecione um novo local de exportação e tente novamente.";

/* Sorting table task description */
"Sorting table..." = "Ordenando a tabela...";

/* spatial index menu item title */
"SPATIAL" = "SPATIAL";

/* sql data access label (Navigator) */
"SQL Data Access" = "Acesso aos dados SQL";


/* Connection Secured via SSL information text */
"Connection Secured via SSL" = "Conexão segura via SSL";

/* SSH connected titlebar marker */
"SSH Connected" = "SSH conectado";

/* SSH connecting very short status message */
"SSH connecting..." = "SSH conectando...";

/* SSH connecting titlebar marker */
"SSH Connecting…" = "Conectando SSH…";

/* SSH connection failed title */
"SSH connection failed!" = "A conexão SSH falhou!";

/* SSH disconnected titlebar marker */
"SSH Disconnected" = "SSH desconectado";

/* SSH key check error */
"SSH Key not found" = "Chave SSH não encontrada";

/* title when ssh tunnel port forwarding failed */
"SSH port forwarding failed" = "Encaminhamento de porta SSH falhou";

/* SSL certificate authority file check error */
"SSL Certificate Authority File not found" = "Arquivo de autoridade certificadora SSL não encontrado";

/* SSL certificate file check error */
"SSL Certificate File not found" = "Arquivo de certificado SSL não encontrado";

/* SSL requested but not used title */
"SSL connection not established" = "Conexão SSL não estabelecida";

/* SSL key file check error */
"SSL Key File not found" = "Arquivo de chave SSL não encontrado";

/* Standard memory export summary */
"Standard memory" = "Memória padrão";

/* started */
"started" = "iniciado";

/* status file for sequelace url scheme command couldn't be written error message */
"Status file for sequelace url scheme command couldn't be written!" = "Arquivo de status para o comando Sequel Ace não pôde ser escrito!";

/* stop button */
"Stop" = "Parar";

/* Stop queries string */
"Stop queries" = "Parar queries";

/* Stop query string */
"Stop query" = "Parar query";

/* description of timestamp */
"Stores a date and time of day as seconds since the beginning of the UNIX epoch (1970-01-01 00:00:00). The values displayed/stored are affected by the connection's @@time_zone setting.\nThe representation is the same as for DATETIME. Invalid values, as well as \"second zero\", are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F. Some additional rules may apply." = "Armazena uma data e hora do dia em segundos desde o início da época do UNIX (1970-01-01 00:00). Os valores exibidos/armazenados são afetados pela configuração @@time_zone da conexão.\nA representação é a mesma que para DATETIME. Valores inválidos, assim como \"segundo zero\", são convertidos para 0000-00-00 00:00:00.0. Segundos fracionários foram adicionados no MySQL 5.6.4 com uma precisão para microsegundos (6), especificada por F. Algumas regras adicionais podem ser aplicadas.";

/* description of datetime */
"Stores a date and time of day. The representation is YYYY-MM-DD HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Armazena uma data e hora do dia. A representação é AAAA-MM-DD HH:MM:SS[. *], Eu sou um segundo fracionário. O valor não é afetado por nenhuma configuração de fuso horário. Valores inválidos são convertidos para 0000-00-00 00:00:00.0. Os segundos fracionários foram adicionados no MySQL 5.6.4 com uma precisão para microsegundos (6), especificada por F.";

/* description of date */
"Stores a date without time information. The representation is YYYY-MM-DD. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00." = "Armazena uma data sem informação de horário. A representação é AAAA-MM-DD O valor não é afetado por qualquer configuração de fuso horário. Valores inválidos são convertidos para 0000-00-00-00.";

/* description of time */
"Stores a time of day, duration or time interval. The representation is HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 00:00:00. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Armazena um horário do dia, duração ou intervalo de tempo. A representação é HH:MM:SS[. *], Eu sou um segundo fracionário. O valor não é afetado por nenhuma configuração de fuso horário. Valores inválidos são convertidos para 00:00:00. Os segundos fracionários foram adicionados no MySQL 5.6.4 com uma precisão para microsegundos (6), especificado por F.";

/* toolbar item label for switching to the Table Structure tab */
"Structure" = "Estrutura";

/* successfully analyzed all selected items message */
"Successfully analyzed all selected items." = "Todos os itens selecionados foram analisados com sucesso.";

/* analyze table successfully passed message */
"Successfully analyzed table." = "Tabela analisada com sucesso.";

/* successfully flushed all selected items message */
"Successfully flushed all selected items." = "Todos os itens selecionados foram liberados com sucesso.";

/* message of panel when successfully flushed privs */
"Successfully flushed privileges." = "Privilégios liberados com sucesso.";

/* flush table successfully passed message */
"Successfully flushed table." = "Tabela limpa com sucesso.";

/* successfully optimized all selected items message */
"Successfully optimized all selected items." = "Todos os itens selecionados foram otimizados com sucesso.";

/* optimize table successfully passed message */
"Successfully optimized table." = "Tabela otimizada com sucesso.";

/* successfully repaired all selected items message */
"Successfully repaired all selected items." = "Todos os itens selecionados foram reparados com sucesso.";

/* repair table successfully passed message */
"Successfully repaired table." = "Tabela reparada com sucesso.";

/* tooltip for toolbar item for switching to the Run Query tab */
"Switch to the Run Query tab" = "Mudar para a aba Executar Query";

/* tooltip for toolbar item for switching to the Table Content tab */
"Switch to the Table Content tab" = "Mudar para a aba Conteúdo da Tabela";

/* tooltip for toolbar item for switching to the Table Info tab */
"Switch to the Table Info tab" = "Mudar para a aba Informações da Tabela";

/* tooltip for toolbar item for switching to the Table Relations tab */
"Switch to the Table Relations tab" = "Mudar para a guia Tabela de Relacionamentos";

/* tooltip for toolbar item for switching to the Table Structure tab */
"Switch to the Table Structure tab" = "Mudar para a aba de Estrutura da Tabela";

/* tooltip for toolbar item for switching to the Table Triggers tab */
"Switch to the Table Triggers tab" = "Alternar para a guia Triggers da Tabela";

/* tooltip for toolbar item for switching to the User Manager tab */
"Switch to the User Manager tab" = "Mudar para a guia Gerenciador de Usuários";

/* description for table syntax copied notification */
"Syntax for %@ table copied" = "Sintaxe para a tabela %@ copiada";

/* table */
"table" = "tabela";

/* csv export table heading
 table
 Table Structure : Collation dropdown : 'item is the same as the collation of table' marker
 Table Structure : Encoding dropdown : 'item is table default' marker */
"Table" = "Tabela";

/* export label showing that the app is fetching data for a specific table */
"Table %lu of %lu (%@): Fetching data..." = "Tabela %1$lu de %2$lu (%3$@): Buscando dados...";

/* export label showing app is fetching relations data for a specific table */
"Table %lu of %lu (%@): Fetching relations data..." = "Tabela %1$lu de %2$lu (%3$@): Buscando dados dos relacionamentos...";

/* export label showing app if writing data for a specific table */
"Table %lu of %lu (%@): Writing data..." = "Tabela %1$lu de %2$lu (%3$@): Gravando dados...";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'table changed' item */
"Table changed" = "Tabela alterada";

/* table checksum message */
"Table checksum" = "Verificação da tabela";

/* table checksum: %@ */
"Table checksum: %@" = "Verificação da tabela: %@";

/* table content print heading */
"Table Content" = "Conteúdo da Tabela";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as comma-separated' item */
"Table Content (CSV)" = "Conteúdo da Tabela (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as SQL' item */
"Table Content (SQL)" = "Conteúdo da Tabela (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as tab-separated' item */
"Table Content (TSV)" = "Conteúdo da Tabela (TSV)";

/* toolbar item for navigation history */
"Table History" = "Histórico da tabela";

/* toolbar item label for switching to the Table Info tab */
"Table Info" = "Informação da tabela";

/* header for table info pane */
"TABLE INFORMATION" = "INFORMAÇÕES DA TABELA";

/* table information print heading */
"Table Information" = "Informações da tabela";

/* message of panel when no name is given for table */
"Table must have a name." = "A tabela deve ter um nome.";

/* general preference pane tooltip */
"Table Preferences" = "Preferências da tabela";

/* toolbar item label for switching to the Table Relations tab */
"Table Relations" = "Relacionamentos da Tabela";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table row changed' item */
"Table Row changed" = "Linha da tabela alterada";

/* table structure print heading */
"Table Structure" = "Estrutura da Tabela";

/* toolbar item label for switching to the Table Triggers tab */
"Table Triggers" = "Triggers da tabela";

/* Add Relation sheet title, showing table name */
"Table: %@" = "Tabela: %@";

/* tables preference pane name */
"Tables" = "Tabelas";

/* tables */
"tables" = "tabelas";

/* header for table list */
"TABLES" = "TABELAS";

/* header for table & views list */
"TABLES & VIEWS" = "TABELAS E VIEWS";

/* Connection test very short status message */
"Testing connection..." = "Testando a conexão...";

/* MySQL connection test very short status message */
"Testing MySQL..." = "Testing MySQL...";

/* SSH testing very short status message */
"Testing SSH..." = "Testando SSH...";

/* text label for color table (Prefs > Editor) */
"Text" = "Texto";

/* Text is too long. Maximum text length is set to %llu. */
"Text is too long. Maximum text length is set to %llu." = "O texto é muito longo. Comprimento máximo do texto é definido como %llu.";

/* Release notes dialog title thanking user for upgrade */
"Thanks for updating Sequel Ace!" = "Obrigado por atualizar Sequel Ace!";

/* Open Files : Bundle : Install-Error : Destination path already exists error dialog message */
"The Bundle ‘%@’ already exists." = "O Pacote ‘%@já existe.";

/* Open Files : Bundle: UUID : UUID-Attribute is missing in bundle's command.plist file */
"The Bundle ‘%@’ has no UUID which is necessary to identify installed Bundles." = "O Pacote ‘%@’ não possui um UUID necessário para identificar pacotes instalados.";

"‘%@’ Bundle contains legacy components" = "O Pacote ‘%@’ contém componentes de legado";

"In these files:\n\n%@\n\nDo you still want to install the bundle?" = "Nos seguintes arquivos:\n\n%@\n\nVocê ainda quer instalar o pacote?";

/* message while reading a spf file which matches non-supported formats. */
"The chosen file %@ contains ‘%@’ data." = "O arquivo selecionado “%1$@” contém dados ‘%2$@.";

/* the color theme ‘%@’ already exists. */
"The color theme ‘%@’ already exists." = "O tema de cor ‘%@' já existe.";

/* the connection is busy. please wait and try again tooltip */
"The connection is busy. Please wait and try again." = "A conexão está ocupada. Por favor aguarde e tente novamente depois.";

/* the connection of the active connection window is not identical tooltip */
"The connection of the active connection window is not identical." = "A conexão da janela de conexão ativa não é idêntica.";

/* Connection lost during import error message */
"The connection to the server was lost during the import.  The import is only partially complete." = "A conexão com o servidor foi perdida durante a importação. A importação está apenas parcialmente completa.";

/* Create syntax permission denied detail */
"The creation syntax could not be retrieved due to a permissions error.\n\nPlease check your user permissions with an administrator." = "A sintaxe de criação não pôde ser recuperada devido a um erro de permissão.\n\nPor favor, verifique suas permissões de usuário com um administrador.";

/* CSV file open error */
"The CSV file you selected could not be found or read." = "O arquivo CSV que você selecionou não pôde ser encontrado ou lido.";

/* Error when CSV appears to have too many columns to import, probably due to line ending mismatch */
"The CSV was read as containing more than 512 columns, more than the maximum columns permitted for speed reasons by Sequel Ace.\n\nThis usually happens due to errors reading the CSV; please double-check the CSV to be imported and the line endings and escape characters at the bottom of the CSV selection dialog." = "O CSV foi lido como contendo mais de 512 colunas, mais do que as colunas máximas permitidas por razões de velocidade por Sequel Ace.\n\nIsso geralmente acontece devido a erros ao ler o CSV; por favor, verifique novamente o CSV a ser importado e os delimitadores de linha e caracteres de escape na parte inferior da caixa de diálogo de seleção CSV.";

/* unsaved theme informative message */
"The current color theme is unsaved. Do you want to proceed without saving it?" = "O tema de cor atual não está salvo. Você deseja prosseguir sem salvá-lo?";

/* Short important release note for swap of custom query buttons */
"The Custom Query \"Run\" and \"Run All\" button positions and their shortcuts have been swapped." = "Executar Query Personalizada e Executar Todas, tiveram as posições dos botões e seus atalhos trocados.";

/* the following default bundles were updated:\n%@\nyour modifications were stored as “(user)”. */
"The following default Bundles were updated:\n%@\nYour modifications were stored as “(user)”." = "Os seguintes pacotes padrão foram atualizados:\n%@\nSuas modificações foram armazenadas como \"(usuário)\".";

/* favorites export error informative message */
"The following error occurred during the export process:\n\n%@" = "Ocorreu o seguinte erro durante o processo de exportação:\n\n%@";

/* favorites import error informative message */
"The following error occurred during the import process:\n\n%@" = "Ocorreu o seguinte erro durante o processo de importação:\n\n%@";

/* table structure : indexes : delete index : error 1553 : description */
"The foreign key relationship '%@' has a dependency on index '%@'. This relationship must be removed before the index can be deleted.\n\nAre you sure you want to continue to delete the relationship and the index? This action cannot be undone." = "A relação de chave estrangeira '%1$@' tem uma dependência do índice '%2$@'. Este relacionamento deve ser removido antes do índice poder ser excluído.\n\nTem certeza de que deseja continuar apagando o relacionamento e o índice? Esta ação não pode ser desfeita.";

/* table list change alert message */
"The list of tables has changed" = "A lista de tabelas foi alterada";

/* message when trying to rename a table/view/proc/etc to an already used name */
"The name '%@' is already used." = "O nome '%@' já está em uso.";

/* table list change alert informative message */
"The number of tables in this database has changed since the export dialog was opened. There are now %lu additional table(s), most likely added by an external application.\n\nHow would you like to proceed?" = "O número de tabelas neste banco de dados mudou desde que a caixa de diálogo de exportação foi aberta. Agora há %lu tabela(s) adicional(ais), provavelmente adicionada por uma aplicação externa.\n\nComo você gostaria de continuar?";

/* message of panel when no rows have been affected after writing to the db */
"The row was not written to the MySQL database. You probably haven't changed anything.\nReload the table to be sure that the row exists and use a primary key for your table.\n(This error can be turned off in the preferences.)" = "A linha não foi escrita no banco de dados MySQL. Você provavelmente não mudou nada.\nRecarregue a tabela para ter certeza de que a linha existe e use uma chave primária para sua tabela.\n(Este erro pode ser desativado nas preferências.)";

/* export : import settings : file version error description ($1 = is version, $2 = list of supported versions); note: the u00A0 is a non-breaking space, do not add more whitespace. */
"The selected export settings were stored with version %1$ld, but only settings with the following versions can be imported: %2$@.\n\nEither save the settings in a backwards compatible way or update your version of Sequel Ace." = "As configurações de exportação selecionadas foram armazenadas com a versão %1$ld, mas somente as configurações com as seguintes versões podem ser importadas: %2$@.\n\nSalve as configurações de forma compatível com versões anteriores ou atualize sua versão da Sequel Ace.";

/* export : import settings : spf content type error description */
"The selected file contains data of type “%1$@”, but type “%2$@” is needed. Please choose a different file." = "O arquivo selecionado contém dados do tipo%1$@, mas o tipo%2$@ é necessário. Por favor, escolha um arquivo diferente.";

/* export : import settings : file error description */
"The selected file is either not a valid SPF file or severely corrupted." = "O arquivo selecionado não é um arquivo SPF válido ou está seriamente corrompido.";

/* error deleting relation informative message */
"The selected relation couldn't be deleted.\n\nMySQL said: %@" = "O relacionamento selecionado não pode ser excluído.\n\nMensagem do MySQL: %@";

/* error deleting trigger informative message */
"The selected trigger couldn't be deleted.\n\nMySQL said: %@" = "O trigger selecionado não pode ser apagado.\n\nMensagem do MySQL: %@";

/* description of tinyint */
"The smallest integer type, requires 1 byte storage space. M is the optional display width and does not affect the possible value range." = "O menor tipo de inteiro, requer 1 espaço de armazenamento de byte. M é a largura de exibição opcional e não afeta o intervalo de valor possível.";

/* message of panel when connection to socket failed because optional socket could not be found */
"The socket file could not be found in any common location. Please supply the correct socket location.\n\nMySQL said: %@" = "O arquivo do socket não pôde ser encontrado em qualquer local comum. Por favor, forneça o local correto do soquete.\n\nMensagem do MySQL: %@";

/* error creating relation informative message */
"The specified relation could not be created.\n\nMySQL said: %@" = "O relacionamento especificado não pôde ser criado.\n\nMensagem do MySQL: %@";

/* error creating trigger informative message */
"The specified trigger was unable to be created.\n\nMySQL said: %@" = "O trigger especificado não pode ser criado.\n\nMensagem do MySQL: %@";

/* sql import : charset error alert : detail message */
"The SQL file uses utf8mb4 encoding, but your MySQL version only supports the limited utf8 subset.\n\nYou can continue the import, but any non-BMP characters in the SQL file (eg. some typographic and scientific special characters, archaic CJK logograms, emojis) will be unrecoverably lost!" = "O arquivo SQL usa a codificação utf8mb4, mas sua versão MySQL suporta apenas o subconjunto limitado de utf8.\n\nVocê pode continuar a importação, mas quaisquer caracteres não-BMP no arquivo SQL (ex. alguns caracteres especiais tipográficos e científicos, logotipos de CJK arcaicos, emojis) serão inrecuperavelmente perdidos!";

/* SQL file open error */
"The SQL file you selected could not be found or read." = "O arquivo SQL selecionado não pôde ser encontrado ou lido.";

/* Prompt for SSH password when keychain fetch failed */
"The SSH password could not be loaded from the keychain; please enter the SSH password for %@:" = "A senha SSH não pôde ser carregada a partir do chave; por favor, digite a senha SSH para %@:";

/* Prompt for SSH password when direct fetch failed */
"The SSH password could not be loaded; please enter the SSH password for %@:" = "A senha SSH não pôde ser carregada; por favor, digite a senha SSH para %@:";

/* SSH tunnel authentication failed message */
"The SSH Tunnel could not authenticate with the remote host. Please check your password and ensure you still have access." = "O túnel SSH não pôde ser autenticado com o servidor remoto. Por favor, verifique sua senha e certifique-se de que você ainda tem acesso.";

/* SSH tunnel unexpectedly closed */
"The SSH Tunnel has unexpectedly closed." = "O túnel SSH foi encerrado inesperadamente.";

/* SSH tunnel was closed by remote host message */
"The SSH Tunnel was closed 'by the remote host'. This may indicate a networking issue or a network timeout." = "O túnel SSH foi fechado \"pelo servidor remoto\". Isto pode indicar um problema de rede ou um tempo limite de rede.";

/* SSH tunnel forwarding port connection refused message */
"The SSH Tunnel was established successfully, but could not forward data to the remote port as the remote port refused the connection." = "O túnel SSH foi estabelecido com sucesso, mas não foi possível encaminhar dados para a porta remota quando a porta remota recusou a conexão.";

/* SSH tunnel unable to bind to local port message */
"The SSH Tunnel was unable to bind to the local port. This error may occur if you already have an SSH connection to the same server and are using a 'LocalForward' setting in your SSH configuration.\n\nWould you like to fall back to a standard connection to localhost in order to use the existing tunnel?" = "O túnel SSH não foi capaz de se ligar ao porto local. Este erro pode ocorrer se você já tem uma conexão SSH com o mesmo servidor e está usando a configuração 'LocalForward' em sua configuração SSH.\n\nVocê gostaria de voltar para uma conexão padrão localhost a fim de usar o túnel existente?";

/* SSH tunnel failed or timed out message */
"The SSH Tunnel was unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds)." = "O túnel SSH não foi capaz de conectar ao servidor %1$@, ou o pedido expirou.\n\nCertifique-se de que o endereço está correto e que você tem os privilégios necessários, ou tente aumentar o tempo limite da conexão (atualmente %2$ld segundos).";

/* message of panel when loading of table failed and presumably due to used filter argument */
"The table data couldn't be loaded presumably due to used filter clause. \n\nMySQL said: %@" = "Os dados da tabela não puderam ser carregados presumivelmente devido à cláusula de filtro utilizada. \n\nMensagem do MySQL: %@";

/* message of panel when loading of table failed */
"The table data couldn't be loaded.\n\nMySQL said: %@" = "Os dados da tabela não puderam ser carregados.\n\nMensagem do MySQL: %@";

/* Export folder not writable explanatory text */
"The target export folder is not writable.  Please select a new export location and try again." = "A pasta de exportação de destino não é gravável. Por favor, selecione um novo local de exportação e tente novamente.";

/* Export folder not chosen by user explanatory text */
"No directory selected.  Please select a new export location and try again." = "Nenhum diretório selecionado. Por favor, selecione um novo local de exportação e tente novamente.";
"No directory selected." = "Nenhum diretório selecionado.";
"Please select a new export location and try again." = "Por favor, selecione um novo local de exportação e tente novamente.";

/* Export folder missing explanatory text */
"The target export folder no longer exists.  Please select a new export location and try again." = "A pasta de exportação de destino não existe mais. Por favor selecione um novo local de exportação e tente novamente.";

/* theme name label */
"Theme Name:" = "Nome do Tema:";

/* themes installation error */
"Themes Installation Error" = "Erro na Instalação de Temas";

/* message of panel when table content cannot be copied */
"There have been errors while copying table content. Please check the new table." = "Houve erros ao copiar o conteúdo da tabela. Por favor, verifique a nova tabela.";

/* Cannot duplicate a table with triggers to a different database */
"Cannot duplicate a table with triggers to a different database." = "Não é possível duplicar uma tabela com triggers para um banco de dados diferente.";

/* text shown when query was successfull */
"There were no errors." = "Não houve erros.";

/* delete field and foreign key informative message */
"This field is part of a foreign key relationship with the table '%@'. This relationship must be removed before the field can be deleted.\n\nAre you sure you want to continue to delete the relationship and the field? This action cannot be undone." = "Este campo faz parte de um relacionamento de chave estrangeira com a tabela '%@' Este relacionamento deve ser removido antes que o campo possa ser excluído.\n\nTem certeza de que deseja continuar a excluir o relacionamento e o campo? Esta ação não pode ser desfeita.";

/* table structure : indexes : delete index : error 1553, no FK found : description */
"This index cannot be deleted, because it is used by an existing foreign key relationship.\n\nPlease remove the relationship, before trying to remove this index.\n\nMySQL said: %@" = "Este índice não pode ser excluído, porque é usado por uma relação de chave estrangeira existente.\n\nPor favor, remova a relação, antes de tentar remover este índice.\n\nMySQL disse: %@";

/* description of serial */
"This is an alias for BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE." = "Este é um pseudônimo para o BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE.";

/* description of dec
 description of fixed
 description of numeric */
"This is an alias for DECIMAL." = "Este é um apelido para DECIMAL.";

/* description of double real */
"This is an alias for DOUBLE, unless REAL_AS_FLOAT is configured." = "Este é um apelido para DOUBLE, a menos que REAL_AS_FLOAT esteja configurado.";

/* description of double precision */
"This is an alias for DOUBLE." = "Este é um apelido para DOUBLE.";

/* description of bool
 description of boolean */
"This is an alias for TINYINT(1)." = "Este é um apelido para TINYINT(1).";

/* Table Structure : Collation dropdown : database marker tooltip */
"This is the default collation of database %@." = "Esta é a ordenação padrão do banco de dados %@.";

/* Table Structure : Collation dropdown : default marker tooltip */
"This is the default collation of encoding %@." = "Esta é a ordenação padrão da codificação %@.";

/* Table Structure : Collation dropdown : table marker tooltip */
"This is the default collation of table %@." = "Esta é a ordenação padrão da tabela %@.";

/* Table Structure : Collation dropdown : server marker tooltip */
"This is the default collation of this server." = "Esta é a ordenação padrão deste servidor.";

/* Table Structure : Encoding dropdown : database marker tooltip */
"This is the default encoding of database %@." = "Esta é a codificação padrão do banco de dados %@.";

/* Table Structure : Encoding dropdown : table marker tooltip */
"This is the default encoding of table %@." = "Esta é a codificação padrão da tabela %@.";

/* Table Structure : Encoding dropdown : server marker tooltip */
"This is the default encoding of this server." = "Esta é a codificação padrão deste servidor.";

/* This table currently does not support relations. Only tables that use the InnoDB storage engine support them. */
"This table currently does not support relations. Only tables that use the InnoDB storage engine support them." = "Esta tabela não suporta relacionamentos. Apenas tabelas que usam o mecanismo de armazenamento InnoDB suportam.";

/* user has no hosts informative message */
"This user doesn't have any hosts associated with it. It will be deleted unless one is added" = "Este usuário não tem nenhum servidor associado a ele. Ele será excluído a menos que um seja adicionado";

/* error removing host informative message */
"This user doesn't seem to have any associated hosts and will be removed unless a host is added." = "Este usuário não parece ter nenhum servidor associado e será removido a menos que um servidor seja adicionado.";

/* shutdown server : confirmation dialog : message */
"This will wait for open transactions to complete and then quit the mysql daemon. Afterwards neither you nor anyone else can connect to this database!\n\nFull management access to the server's operating system is required to restart MySQL!" = "Isto esperará pela conclusão das transações abertas e, em seguida, encerrará o mysql daemon. Depois disso, nem você nem ninguém pode se conectar a esta base de dados!\n\nAcesso completo de gerenciamento ao sistema operacional do servidor é necessário para reiniciar o MySQL!";

/* export filename time token */
"Time" = "Hora";

/* toolbar item label for switching to the Table Triggers tab */
"Triggers" = "Triggers";

/* triggers for table label */
"Triggers for table: %@" = "Triggers para a tabela: %@";

/* truncate button */
"Truncate" = "Truncar";

/* truncate tables message */
"Truncate selected tables?" = "Truncar tabelas selecionadas?";

/* truncate table menu title */
"Truncate Table..." = "Truncar Tabela...";

/* truncate table message */
"Truncate table '%@'?" = "Truncar a tabela '%@'?";

/* truncate tables menu item */
"Truncate Tables" = "Truncar Tabelas";

/* type label (Navigator) */
"Type" = "Tipo";

/* type declaration header */
"Type Declaration:" = "Declaração de tipo:";

/* add index error message */
"Unable to add index" = "Não foi possível adicionar item";

/* unable to analyze selected items message */
"Unable to analyze selected items" = "Não é possível analisar os itens selecionados";

/* unable to analyze table message */
"Unable to analyze table" = "Não é possível analisar a tabela";

/* unable to check selected items message */
"Unable to check selected items" = "Não foi possível verificar os itens selecionados";

/* unable to check table message */
"Unable to check table" = "Não foi possível verificar a tabela";

/* message of panel when connection to host failed due to access denied error */
"Unable to connect to host %@ because access was denied.\n\nDouble-check your username and password and ensure that access from your current location is permitted.\n\nMySQL said: %@" = "Não foi possível conectar ao servidor %1$@ porque o acesso foi negado.\n\nVerifique seu nome de usuário e senha e certifique-se de que o acesso da sua localização atual é permitido.\n\nMySQL disse: %2$@";

/* message of panel when SSH port forwarding failed */
"Unable to connect to host %@ because the port connection via SSH was refused.\n\nPlease ensure that your MySQL host is set up to allow TCP/IP connections (no --skip-networking) and is configured to allow connections from the host you are tunnelling via.\n\nYou may also want to check the port is correct and that you have the necessary privileges.\n\nChecking the error detail will show the SSH debug log which may provide more details.\n\nMySQL said: %@" = "Não foi possível conectar ao servidor %1$@ porque a conexão de porta via SSH foi recusada.\n\nPor favor, certifique-se de que o seu host MySQL está configurado para permitir conexões TCP/IP (sem --skip-networking) e está configurado para permitir conexões do host que você está tunelando via.\n\nVocê também pode querer verificar se a porta está correta e se você tem os privilégios necessários.\n\nVerificando o detalhe do erro mostrará o log de depuração SSH que pode fornecer mais detalhes.\n\nMySQL disse: %2$@";

/* message of panel when connection to host failed */
"Unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds).\n\nMySQL said: %@" = "Não é possível conectar ao servidor %1$@, ou a solicitação expirou.\n\nCertifique-se de que o endereço está correto e que você tem os privilégios necessários, ou tente aumentar o tempo limite da conexão (atualmente %2$ld segundos).\n\nMySQL disse: %3$@";

/* message of panel when connection to host failed */
"Unable to connect via the socket, or the request timed out.\n\nDouble-check that the socket path is correct and that you have the necessary privileges, and that the server is running.\n\nMySQL said: %@" = "Não foi possível conectar-se via soquete ou a solicitação expirou.\n\nVerifique se o caminho do socket está correto e se você tem os privilégios necessários, e se o servidor está rodando.\n\nMySQL disse: %@";

/* unable to copy database message */
"Unable to copy database" = "Não foi possível copiar o banco de dados";

/* error deleting index message */
"Unable to delete index" = "Não foi possível excluir o índice";

/* error deleting relation message */
"Unable to delete relation" = "Não foi possível excluir o relacionamento";

/* error deleting trigger message */
"Unable to delete trigger" = "Não é possível excluir a trigger";

/* unable to flush selected items message */
"Unable to flush selected items" = "Incapaz de liberar os itens selecionados";

/* unable to flush table message */
"Unable to flush table" = "Não é possível liberar a tabela";

/* unable to get list of users message */
"Unable to get list of users" = "Não foi possível obter a lista de usuários";

/* error killing connection message */
"Unable to kill connection" = "Não é possível matar a conexão";

/* error killing query message */
"Unable to kill query" = "Não é possível encerrar a query";

/* unable to optimze selected items message */
"Unable to optimze selected items" = "Não é possível otimizar os itens selecionados";

/* unable to optimze table message */
"Unable to optimze table" = "Não é possível otimizar tabela";

/* unable to perform the checksum */
"Unable to perform the checksum" = "Não é possível realizar o checksum";

/* error removing host message */
"Unable to remove host" = "Não foi possível remover o servidor";

/* unable to rename database message */
"Unable to rename database" = "Impossível renomear banco de dados";

/* unable to repair selected items message */
"Unable to repair selected items" = "Não foi possível reparar os itens selecionados";

/* unable to repair table message */
"Unable to repair table" = "Impossível reparar a tabela";

/* message of panel when connection to db failed after selecting from popupbutton */
"Unable to select database %@.\nPlease check you have the necessary privileges to view the database, and that the database still exists." = "Não foi possível selecionar o banco de dados %@.\nPor favor, verifique se você tem os privilégios necessários para visualizar o banco de dados, e se o banco de dados ainda existe.";

/* Unable to write row error */
"Unable to write row" = "Não foi possível gravar a linha";

/* Table Content : Remove Row : Result : n Error title */
"Unexpected number of rows removed!" = "Número inesperado de linhas removidas!";

/* warning */
"Unknown file format" = "Formato de arquivo desconhecido";

/* unsaved changes message */
"Unsaved changes" = "Alterações não salvas";

/* unsaved theme message */
"Unsaved Theme" = "Tema não salvo";

/* Preferences : Network : Custom SSH client : warning dialog title */
"Unsupported configuration!" = "Configuração não suportada!";

/* export : import settings : file version error title */
"Unsupported version for export settings!" = "Versão não suportada para configurações de exportação!";

/* Name for an untitled connection */
"Untitled" = "Sem Título";

/* Title of a new Sequel Ace Document */
"Untitled %ld" = "Sem título %ld";

/* range for mediumblob type */
"Up to %@ bytes (16 MiB)" = "Até %@ bytes (16 MiB)";

/* range for longblob type */
"Up to %@ bytes (4 GiB)" = "Até %@ bytes (4 GiB)";

/* range for mediumtext type */
"Up to %@ characters (16 MiB)" = "Até %@ caracteres (16 miB)";

/* range for enum type */
"Up to %@ distinct members (<%@ in practice)\n1-2 bytes storage" = "Até %1$@ membros distintos (<%2$@ na prática)\n1-2 bytes de armazenamento";

/* range for tinyblob type */
"Up to 255 bytes" = "Até 255 bytes";

/* range for tinytext type */
"Up to 255 characters" = "Até 255 caracteres";

/* Open Files : Bundle : Already-Installed : Update button */
"Update" = "Atualização";

/* updated: %@ */
"updated: %@" = "atualizado: %@";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field of table `%2$@` was changed." = "Falha na atualização do conteúdo do campo. Não foi possível identificar a origem do campo de forma inequívoca (%1$ld encontrados). É bem provável que durante a edição deste campo da tabela `%2$@` tenha sido alterada.";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field the table `%2$@` was changed by an other user." = "Falha na atualização do conteúdo do campo. Não foi possível identificar a origem do campo de forma inequívoca (%1$ld encontrados). É bem provável que ao editar este campo a tabela `%2$@` tenha sido alterada por outro usuário.";

/* updating field task description */
"Updating field data..." = "Atualizando dados do campo...";

/* URL scheme command couldn't authenticated */
"URL scheme command couldn't authenticated" = "O comando esquema de URL não pôde ser autenticado";

/* URL scheme command was terminated by user */
"URL scheme command was terminated by user" = "O comando do esquema de URL foi terminado pelo usuário";

/* URL scheme command %@ unsupported */
"URL scheme command %@ unsupported" = "O comando URL do esquema %@ não é suportado";

/* Use 127.0.0.1 button */
"Use 127.0.0.1" = "Use 127.0.0.1";

/* use standard connection button */
"Use Standard Connection" = "Usar Conexão Padrão";

/* user has no hosts message */
"User has no hosts" = "Usuário não possui servidores";

/* user-defined value */
"User-defined value" = "Valor definido pelo usuário";

/* toolbar item label for switching to the User Manager tab */
"Users" = "Usuários";

/* CSV Field Mapping : Table View : Tooltip for fields with NULL value */
"Value will be imported as MySQL NULL" = "Valor será importado como MySQL NULL";

/* variable label for color table (Prefs > Editor) */
"Variable" = "Variável";

/* version */
"version" = "versão";

/* export header version label */
"Version" = "Versão";

/* view */
"view" = "ver";

/* Release notes button title */
"View full release notes" = "Ver notas completas do lançamento";

/* header for view info pane */
"VIEW INFORMATION" = "VER INFORMAÇÕES";

/* view html source code menu item title */
"View Source" = "Exibir código-fonte";

/* view structure print heading */
"View Structure" = "Visualizar estrutura";

/* views */
"views" = "visualizações";

/* warning */
"Warning" = "Aviso";

/* Short important release note for why password prompts may occur */
"We've changed Sequel Ace's digital signature for GateKeeper compatibility; you'll have to allow access to your passwords again." = "Alteramos a assinatura digital de Sequel Ace, para compatibilidade com GateKeeper; você terá que permitir o acesso a suas senhas novamente.";

/* Important release notes informational text, single change */
"We've made a few changes but we thought you should know about one particularly important one:" = "Nós fizemos algumas alterações, mas achamos que você deveria saber sobre uma particularmente importante:";

/* Important release notes informational text, multiple changes */
"We've made a few changes but we thought you should know about some particularly important ones:" = "Nós fizemos algumas alterações, mas achamos que você deve saber sobre algumas que são particularmente importantes:";

/* WHERE clause not valid */
"WHERE clause not valid" = "Cláusula WHERE não é válida";

/* Title of filter preview area when the query WHERE is negated */
"WHERE NOT query" = "Query WHERE NOT";

/* Title of filter preview area when the query WHERE is normal */
"WHERE query" = "Query WHERE";

/* Generic working description */
"Working..." = "Trabalhando...";

/* export label showing app is writing data */
"Writing data..." = "Gravando dados...";

/* text showing that app is writing text file */
"Writing..." = "Escrevendo...";

/* wrong data format or password */
"Wrong data format or password." = "Formato de dados ou senha incorretos.";

/* wrong data format */
"Wrong data format." = "Formato de dados incorreto.";

/* export : import settings : spf content type error title */
"Wrong SPF content type!" = "Tipo de conteúdo SPF errado!";

/* export filename date token */
"Year" = "Ano";

/* message of panel when trying to copy multiple rows */
"You can only copy single rows." = "Você só pode copiar linhas individuais.";

/* message of panel when trying to edit tables without index and with hidden blob/text fields */
"You can't hide blob and text fields when working with tables without index." = "Você não pode ocultar campos blob e texto quando estiver trabalhando com tabelas sem índice.";

/* You cannot delete the last field in a table. Delete the table instead. */
"You cannot delete the last field in a table. Delete the table instead." = "Você não pode excluir o último campo de uma tabela. Em vez disso, exclua a tabela.";

/* SSL connection requested but not established error detail */
"You requested that the connection should be established using SSL, but MySQL made the connection without SSL.\n\nThis may be because the server does not support SSL connections, or has SSL disabled; or insufficient details were supplied to establish an SSL connection.\n\nThis connection is not encrypted." = "Você solicitou que a conexão seja estabelecida usando SSL, mas o MySQL fez a conexão sem SSL.\n\nIsso pode ser porque o servidor não suporta conexões SSL, ou desativou o SSL; ou detalhes insuficientes foram fornecidos para estabelecer uma conexão SSL.\n\nEsta conexão não está criptografada.";

/* Query Favorites : List : Section Heading : current connection document : tooltip (arg is the name of the spf file) */
"‘%@’ based favorites" = "‘%@’ favoritos baseados";

/* table column header. Read: 'Showing all content filters for fields of type %@' (ContentFilterManager) */
"‘%@’ Fields Content Filters" = "‘%@’ Filtros de Conteúdo dos Campos";

/* Export file already exists message */
"%@ already exists. Do you want to replace it?" = "%@ já existe. Deseja substituí-lo?";

/* Bundle Editor : Outline View : Bundle item : tooltip */
"%@ Bundle" = "%@ Pacote";

/* Export file creation error title */
"%@ could not be created" = "%@ não pôde ser criado";

/* %@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item. */
"%@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item." = "%@ não pôde ser analisado. Você pode editar a configuração das colunas, mas a coluna não será exibida na visualização de conteúdo; por favor relate este problema à equipe Sequel Ace usando o item de menu Ajuda.";

/* connection view : ssl : client cert file picker : wrong format error title */
"%@ is not a valid client certificate file." = "%@ não é um arquivo de certificado de cliente válido.";

/* connection view : ssl : key file picker : wrong format error title */
"%@ is not a valid private key file." = "%@ não é um arquivo de chave privada válido.";

/* text shown when there are App Sandbox Issues */
"App Sandbox Issue" = "Problema no App Sandbox";

/* Stale Bookmarks error title */
"Stale Bookmarks" = "Favoritos obsoletos";

/* App Sandbox info link text */
"App Sandbox Info" = "Info. do App Sandbox";

/* error while selecting file title */
"File Selection Error" = "Erro ao selecionar arquivo";

/* error while selecting file message */
"The selected file is not a valid file.\n\nPlease try again.\n\nClass: %@" = "O arquivo selecionado não é um arquivo válido.\n\nTente novamente.\n\nClasse: %@";

/* known hosts not writable error message */
"The selected known hosts file is not writable.\n\n%@\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "O arquivo de hosts conhecidos selecionado não é gravável.\n\n%@\n\nPor favor, re-selecione o arquivo nas Preferências do Sequel Ace e tente novamente.";

/* known hosts is invalid message */
"The selected known hosts file is invalid.\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "O arquivo de hosts conhecidos selecionado é inválido.\n\nPor favor, re-selecione o arquivo nas Preferências do Sequel Ace e tente novamente.";

/* known hosts contains quote message */
"The selected known hosts file contains a quote (\") in its file path which is not supported.\n\n%@\n\nPlease select a different file in Sequel Ace's Preferences or rename the file/path to remove the quote." = "O arquivo de hosts conhecido selecionado contém uma citação (\") no caminho do arquivo que não é suportado.\n\n%@\n\nSelecione um arquivo diferente nas Preferências do Sequel Ace, ou renomeie o arquivo/caminho para remover a citação.";

/* SSH Tunnel Debugging menu and window title */
"SSH Tunnel Debugging Info" = "Informações de depuração do túnel SSH";

/* Question to user to see if they would like to re-request access */
"You have stale secure bookmarks:\n\n%@\n\nWould you like to re-request access now?" = "Você tem marcadores seguros obsoletos:\n\n%@\n\nGostaria de re-solicitar o acesso agora?";

/* Question to user to see if they would like to request access to missing bookmarks */
"You have missing secure bookmarks:\n\n%@\n\nWould you like to request access now?" = "Você está sem marcadores seguros:\n\n%@\n\nGostaria de solicitar acesso agora?";

/* known hosts advance menu item title */
"Use known hosts from ssh config (ADVANCED)" = "Usar servidores conhecidos de configuração ssh (ADVANCED)";

/* The answer, yes */
"Yes" = "Sim";

/* Shown to remind users of their stale bookmarks */
"A reminder of your stale secure bookmarks:<br /><br />%@<br />" = "Um lembrete dos seus favoritos obsoletos:<br /><br />%@<br />";

/* Title for Stale Secure Bookmarks help window */
"Stale Secure Bookmarks" = "Favoritos seguros obsoletos";

/* Title for Export Error alert */
"Export Error" = "Erro ao Exportar";

/* Message for Export Error alert */
"Error while writing to the export file. Could not open file: %@" = "Erro ao gravar o arquivo de exportação. Não foi possível abrir o arquivo: %@";

/* Message for User window error */
"Resultset from mysql.user contains neither 'Password' nor 'authentication_string' column." = "Resultset de mysql.user não contém coluna 'Senha' nem coluna 'autenticação_string'.";

/* Title for User window error */
"User Data Error" = "Erro de Dados do Usuário";

/* Title for Stale Bookmark file selection dialog */
"Please re-select the file '%@' in order to restore Sequel Ace's access." = "Por favor, selecione novamente o arquivo '%@' para restaurar o acesso a Sequel Ace.";

/* Title for File Preference file selection dialog */
"Please choose a file or folder to grant Sequel Ace access to." = "Por favor, escolha um arquivo ou pasta para conceder acesso ao Sequel Ace.";

/* Title for Network Preference ssh config file selection dialog */
"Please choose your ssh config files(s)" = "Por favor, escolha seu(s) arquivo(s) de configuração ssh";

/* Title for Network Preference ssh known hosts file selection dialog */
"Please choose your known hosts file" = "Por favor, escolha seu arquivo de endereços conhecido";

/* Message for field editor JSON segment when JSON is invalid */
"Invalid JSON" = "JSON inválido";

/* Applying syntax highlighting task description */
"Applying syntax highlighting..." = "Aplicando destaque de sintaxe...";

/* Error message when a bash task fails to start */
"Couldn't launch task.\nException reason: %@\n ENV length: %lu" = "Não foi possível iniciar a tarefa.\nExceção: %@\n comprimento ENV: %lu";

/* Error message title when we failed to create new connection view */
"New Connection Error" = "Erro de Nova Conexão";

/* New Connection Error informative message */
"Failed to create new database connection window. Please restart Sequel Ace and try again." = "Falha ao criar nova janela de conexão com o banco de dados. Por favor, reinicie o Sequel Ace e tente novamente.";

/* new version is available alert title */
"A new version is available" = "Uma nova versão está disponível";

/* new version is available download button title */
"Download" = "Baixar";

/* new version is available alert informativeText */
"Version %@ is available. You are currently running %@" = "Versão %@ está disponível. Você está usando %@ no momento";

/* downloading new version window title */
"Download Progress" = "Progresso do Download";

/* downloading new version time remaining placeholder text */
"Calculating time remaining..." = "Calculando o tempo restante...";

/* downloading new version window message */
"Downloading Sequel Ace - %@" = "Baixando Sequel Ace - %@";

/* downloading new version time remaining estimate text */
"About %.1f seconds left" = "Cerca de %.1f segundos restantes";

/* downloading new version failure alert title */
"Download Failed" = "Falha no download";

/* Tool tip for the Show alert when update is available preference on the Notifications pane */
"Only available for GitHub downloads" = "Disponível apenas para downloads no GitHub";

/* Tool tip warning for when the user sets auto-complete delay to zero */
"WARNING: Setting the auto-complete delay to 0.0 can result in strange output." = "AVISO: Definir o atraso de preenchimento automático para 0.0 pode resultar em saída estranha.";

/* downloading new version bytes of bytes done */
"%@ of %@" = "%@ de %@";

/* Time zone set failed informative message */
"\n\ntime_zone will be set to SYSTEM." = "\n\ntime_zone será definido como SYSTEM.";

/* Menu item title for checking for updates */
"Check for Updates..." = "Verificar Atualizações...";

/* Warning message during connection in case the variable skip-show-database is set to ON */
"The skip-show-database variable of the database server is set to ON. Thus, you won't be able to list databases unless you have the SHOW DATABASES privilege.\n\nHowever, the databases are still accessible directly through SQL queries depending on your privileges." = "A variável Skip-show-database do servidor de banco de dados está definida para LIGADO. Assim, você não poderá listar os bancos de dados a menos que tenha o privilégio SHOW DATABASES.\n\nNo entanto, os bancos de dados ainda são acessíveis diretamente através de consultas SQL, dependendo de seus privilégios.";

/* Error alert title when the request to GitHub fails */
"GitHub Request Failed" = "Solicitação do GitHub falhou";

/* Info alert title when there are Newer Releases Available */
"No Newer Release Available" = "Nenhuma versão mais recente disponível";

/* Info alert message when there are Newer Releases Available */
"You are currently running the latest release." = "Você está executando a versão mais recente.";

/* Alert button, disable the warning when skip-show-database is set to on */
"Never show this again" = "Não mostre isso novamente";

/* Info alert when editing a generated column */
"The current field \"%@\" is a generated column and therefore cannot be edited." = "O campo atual \"%@é uma coluna gerada e, portanto, não pode ser editada.";

/* Release note / info modal about change in latest release about column behavior */
"The use of the \"default\" column has changed since the last version of Sequel ACE:\n\n- No default value : Leave it blank.\n- String value : Use single '' or double quotes \"\" if you want an empty string or to wrap a string\n- Expression : Use parentheses (). Except for the TIMESTAMP and DATETIME columns where you can specify the CURRENT_TIMESTAMP function without enclosing parentheses." = "O uso da coluna \"default\" mudou desde a última versão da Sequel ACE:\n\n- Nenhum valor padrão: Deixe-o em branco.\n- Valor de string: use aspas duplas ou '' simples \"\" se você quiser uma sequência de caracteres vazia ou para embrulhar uma sequência de caracteres\n- Expressão: Use parênteses (). Exceto nas colunas TIMESTAMP e DATETIME onde você pode especificar a função CURRENT_TIMESTAMP sem incluir parênteses.";

/* Menu option to pin a view to the top of the list of tables */
"Pin View" = "Fixar Vista";

/* Menu option to pin a table to the top of the list of tables */
"Pin Table" = "Fixar tabela";

/* Menu option to pin a procedure to the top of the list of tables */
"Pin Procedure" = "Fixar procedimento";

/* Menu option to pin a function to the top of the list of tables */
"Pin Function" = "Fixar função";

/* Menu option to unpin a view which is currently pinned at the top of the list of tables */
"Unpin View" = "Desafixar Vista";

/* Menu option to unpin a table which is currently pinned at the top of the list of tables */
"Unpin Table" = "Desafixar Tabela";

/* Menu option to unpin a procedure which is currently pinned at the top of the list of tables */
"Unpin Procedure" = "Desafixar processo";

/* Menu option to unpin a function which is currently pinned at the top of the list of tables */
"Unpin Function" = "Desafixar função";

/* header for pinned table list */
"PINNED" = "FIXAR";

/* Menu option to copy a table's name with database name in the list of tables */
"Copy Table Name" = "Copiar nome da tabela";

/* sequelace URL scheme error: LaunchFavorite alert title (favorite name not found) */
"LaunchFavorite URL Scheme Error" = "Erro ao iniciar esquema de URL Favorita";

/* sequelace URL scheme error: LaunchFavorite alert description (favorite name not found) */
"The variable in the ?name= query parameter could not be matched with any of your favorites." = "A variável no parâmetro de consulta ?name= não pode ser combinada com nenhum dos seus favoritos.";
