/* Table Content : Remove Row : Result : Too Few : Part 3 : Row not deleted when using primary key for DELETE statement. */
" Check the Console for possible errors inside the primary key(s) of this table!" = " Vérifiez la console pour d'éventuelles erreurs dans la ou les clés primaires de cette table!";

/* Table Content : Remove Row : Result : Too Many : Part 2 : Generic text */
" Please check the Console and inform the Sequel Ace team!" = " Veuillez vérifier la console et informer l'équipe Sequel Ace !";

/* Table Content : Remove Row : Result : Too Few : Part 2 : Generic help message */
" Reload the table to be sure that the contents have not changed in the meantime." = " Rechargez le tableau pour vous assurer que le contenu n'a pas changé entre-temps.";

/* Table Content : Remove Row : Result : Too Few : Part 3 : no primary key in table generic message */
" You should also add a primary key to this table!" = " Vous devez également ajouter une clé primaire à cette table !";

/* Query snippet shell command syntax and placeholder */
"$(shell_command)" = "$(shell_command)";

/* text showing how many rows are selected */
"%@ %@ selected" = "%1$@ %2$@ sélectionnées";

/* History item filtered by values label */
"%@ (Filtered by %@)" = "%1$@ (filtrer par %2$@)";

/* History item with page number label */
"%@ (Page %lu)" = "%1$@ (Page %2$lu)";

/* Content Filter Manager : Initial name of copied filter
 Initial favourite name after duplicating a previous favourite */
"%@ Copy" = "%@ Copier";

/* text showing a single row a partially loaded result */
"%@ row in partial load" = "%@ ligne partiellement chargée";

/* text showing a single row in the result */
"%@ row in table" = "%@ ligne dans la table";

/* text showing how a single rows matched filter */
"%@ row of %@%@ matches filter" = "%1$@ ligne sur %2$@%3$@ correspondant au filtre";

/* text showing how many rows are in a partially loaded result */
"%@ rows in partial load" = "%@ lignes partiellement chargées";

/* text showing how many rows are in the result */
"%@ rows in table" = "%@ lignes dans la table";

/* text showing how many rows matched filter */
"%@ rows of %@%@ match filter" = "%1$@ lignes sur %2$@%3$@ correspondent au filtre";

/* text showing how many rows have been affected by a single query */
"%@; %ld rows affected" = "%1$@; %2$ld lignes affectées";

/* text showing how many rows have been affected by multiple queries */
"%@; %ld rows affected in total, by %ld queries taking %@" = "%1$@; %2$ld lignes affectées au total, par %3$ld requêtes prenant %4$@";

/* text showing one row has been affected by a single query */
"%@; 1 row affected" = "%@; 1 ligne affectée";

/* text showing one row has been affected by multiple queries */
"%@; 1 row affected in total, by %ld queries taking %@" = "%1$@; 1 ligne modifiée au total, par %2$ld requêtes prenant %3$@";

/* text showing a query was cancelled */
"%@; Cancelled after %@" = "%1$@; Annulé après %2$@";

/* text showing multiple queries were cancelled */
"%@; Cancelled in query %ld, after %@" = "%1$@; Annulé dans la requête %2$ld, après %3$@";

/* Error display text, showing original MySQL error */
"%@\n\nMySQL said: %@" = "%1$@\n\nnMySQL dit : %2$@";

/* favorite singular label (%d == 1) */
"%lu favorite" = "%lu favori";

/* favorites plural label (%d != 1) */
"%lu favorites" = "%lu favoris";

/* favorite group singular label (%d == 1) */
"%lu group" = "%lu groupe";

/* favorite groups plural label (%d != 1) */
"%lu groups" = "%lu groupes";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+y (y!=1) rows instead of n selected were deleted. */
"%ld additional rows were removed!" = "%ld lignes supplémentaires ont été supprimées !";

/* Label showing the index of the selected CSV row */
"%ld of %lu record(s)" = "%1$ld de %2$lu enregistrement(s)";

/* Label showing the index of the selected CSV row (csv partially loaded) */
"%ld of first %lu record(s)" = "%1$ld de(s) premier(s) %2$lu enregistrement(s)";

/* Table Content : Remove Row : Result : Too Few : Part 1 : n-x (x!=1) of n selected rows were deleted. */
"%ld rows were not removed." = "%ld lignes n'ont pas été supprimées.";

/* Export file already exists message */
"%lu files already exist. Do you want to replace them?" = "%lu fichiers existent déjà. Voulez-vous les remplacer ?";

/* Export files creation error title */
"%lu files could not be created" = "%lu fichiers n'ont pas pu être créés";

/* Some export files already exist explanatory text */
"%lu files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "%lu fichiers portant le même nom existent déjà dans le dossier cible. Les remplacer écrasera leur contenu actuel.";

/* filtered item count */
"%lu of %lu" = "%1$lu de %2$lu";

/* Export folder not writable for some files explanatory text */
"%lu of the export files could not be created because their target export folder is not writable; please select a new export location and try again." = "%lu des fichiers d'exportation n'ont pas pu être créés car leur dossier d'exportation cible n'est pas accessible en écriture ; veuillez sélectionner un nouvel emplacement d'exportation et réessayez.";

/* Export folder missing for some files explanatory text */
"%lu of the export files could not be created because their target export folder no longer exists; please select a new export location and try again." = "%lu des fichiers d'exportation n'ont pas pu être créés car leur dossier d'exportation cible n'existe plus; veuillez sélectionner un nouvel emplacement d'exportation et réessayez.";

/* History item title with nothing selected */
"(no selection)" = "(aucune sélection)";

/* value shown for hidden blob and text fields */
"(not loaded)" = "(non chargé)";

/* Explanation for MySQL server has gone away error */
"(This usually indicates that the connection has been closed by the server after inactivity, but can also occur due to other conditions.  The connection has been restored; please try again if the query is safe to re-run.)" = "(Cela indique généralement que la connexion a été fermée par le serveur après une inactivité, mais cela peut également se produire en raison d'autres conditions. La connexion a été restaurée ; veuillez réessayer si la requête peut être réexécutée en toute sécurité.)";

/* Custom Query : text appended to the “x row(s) affected” messages. $1 is a time interval */
", first row available after %1$@" = ", première ligne disponible après %1$@";

/* Custom Query : text appended to the “x row(s) affected” messages (for update/delete queries). $1 is a time interval */
", taking %1$@" = ", prend %1$@";

/* warning shown in the console when no rows have been affected after writing to the db */
"/* WARNING: No rows have been affected */\n" = "/* ATTENTION : aucune ligne n'a été affectée */\n";

/* error text when multiple custom query failed */
"[ERROR in query %ld] %@\n" = "[EURREUR dans la requête %1$ld] %2$@\n";

/* error text when reading of csv file gave errors */
"[ERROR in row %ld] %@\n" = "[ERREUR sur la ligne %1$ld] %2$@\n";

/* [multiple selection] */
"[multiple selection]" = "[sélection multiple]";

/* displayed when new content filter has empty Name field (ContentFilterManager) */
"[name required]" = "[Nom obligatoire]";

/* [no selection] */
"[no selection]" = "[aucune sélection]";

/* Additional export file errors */
"\n\n(In addition, one or more errors occurred while attempting to create the export files: %lu could not be created. These files will be ignored.)" = "\n\n(De plus, une ou plusieurs erreurs se sont produites lors de la tentative de création des fichiers d'exportation : %lu n'a pas pu être créé. Ces fichiers seront ignorés.)";

/* \n\npress shift for binary search tooltip message */
"\n\nPress ⇧ for binary search (case-sensitive)." = "\n\nAppuyez sur <unk> pour une recherche binaire (sensible à la casse).";

/* description of bit */
"A bit-field type. M specifies the number of bits. If shorter values are inserted, they will be aligned on the least significant bit. See the SET type if you want to explicitly name each bit." = "Un type de champ bits. M spécifie le nombre de bits. Si des valeurs plus courtes sont insérées, elles seront alignées sur le bit le moins significatif. Voir le type SET si vous voulez explicitement nommer chaque bit.";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog message */
"A Bundle ‘%@’ is already installed. Do you want to update it?" = "Un pack ‘%@’ est déjà installé. Voulez-vous le mettre à jour ?";

/* description of binary */
"A byte array with fixed length. Shorter values will always be padded to the right with 0x00 until they fit M." = "Un tableau d'octets avec une longueur fixe. Les valeurs plus courtes seront toujours complétées à droite avec 0x00 jusqu'à ce qu'elles soient ajustées à M.";

/* description of varbinary */
"A byte array with variable length. The actual number of bytes is further limited by the values of other fields in the row." = "Un tableau d'octets de longueur de variable. Le nombre réel d'octets est plus limité par les valeurs des autres champs de la ligne.";

/* description of blob
 description of longblob
 description of mediumblob
 description of tinyblob */
"A byte array with variable length. Unlike VARBINARY this type does not count towards the maximum row length." = "Un tableau d'octets de longueur de variable. Contrairement à VARBINARY, ce type ne compte pas pour la longueur maximale de la ligne.";

/* description of tinytext */
"A character string that can store up to 255 bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Une chaîne de caractères qui peut stocker jusqu'à 255 octets, mais qui nécessite moins d'espace pour des valeurs plus courtes. Le nombre réel de caractères est encore limité par l'encodage utilisé. Contrairement à VARCHAR, ce type ne compte pas pour la longueur maximale de la ligne.";

/* description of varchar */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding and the values of other fields in the row." = "Une chaîne de caractères qui peut stocker jusqu'à M octets, mais qui nécessite moins d'espace pour des valeurs plus courtes. Le nombre réel de caractères est encore limité par l'encodage utilisé et les valeurs des autres champs de la ligne.";

/* description of text */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Une chaîne de caractères qui peut stocker jusqu'à M octets, mais qui nécessite moins d'espace pour des valeurs plus courtes. Le nombre réel de caractères est encore limité par l'encodage utilisé. Contrairement à VARCHAR, ce type ne compte pas pour la longueur maximale de la ligne.";

/* description of char */
"A character string that will require M×w bytes per row, independent of the actual content length. w is the maximum number of bytes a single character can occupy in the given encoding." = "Une chaîne de caractères qui nécessitera des octets M×w par ligne, indépendamment de la longueur réelle du contenu. w est le nombre maximum d'octets qu'un seul caractère peut occuper dans l'encodage donné.";

/* description of longtext
 description of mediumtext */
"A character string with variable length. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Une chaîne de caractères avec une longueur variable. Le nombre réel de caractères est encore limité par l'encodage utilisé. Contrairement à VARCHAR, ce type ne compte pas pour la longueur maximale de la ligne.";

/* description of json */
"A data type that validates JSON data on INSERT and internally stores it in a binary format that is both, more compact and faster to access than textual JSON.\nAvailable from MySQL 5.7.8." = "Un type de données qui valide les données JSON lors de l'INSERT et les stocke en interne dans un format binaire qui est à la fois plus compact et plus rapide d'accès que le JSON textuel\n. Disponible à partir de MySQL 5.7.8.";

/* Export file already exists explanatory text */
"A file with the same name already exists in the target folder. Replacing it will overwrite its current contents." = "Un fichier avec le même nom existe déjà dans le dossier cible. Remplacer le fichier remplacera son contenu actuel.";

/* description of decimal */
"A fixed-point, exact decimal value. M is the maxium number of digits, of which D may be after the decimal point. When rounding, 0-4 is always rounded down, 5-9 up (“round towards nearest”)." = "Un point fixé, la valeur décimale exacte. M est le nombre maximal de chiffres, dont D peut être après la virgule. En arrondissement, 0-4 est toujours arrondi vers le bas, 5-9 vers le haut (« rond vers le plus près »).";

/* table structure : indexes : delete index : error 1553 : title
 table structure : indexes : delete index : error 1553, no FK found : title */
"A foreign key needs this index" = "Une clé étrangère a besoin de cet index";

/* description of set */
"A SET can define up to 64 members (as strings) of which a field can use one or more using a comma-separated list. Upon insertion the order of members is automatically normalized and duplicate members will be eliminated. Assignment of numbers is supported using the same semantics as for BIT types." = "Un SET peut définir jusqu'à 64 membres (comme des chaînes) dont un champ peut utiliser un ou plusieurs en utilisant une liste séparée par des virgules. Lors de l'insertion, l'ordre des membres est automatiquement normalisé et les membres en double seront éliminés. L'affectation des nombres est supportée en utilisant la même sémantique que pour les types BIT.";

/* SSH key not found message */
"A SSH key location was specified, but no file was found in the specified location.  Please re-select the key and try again." = "Un emplacement de clé SSH a été spécifié, mais aucun fichier n'a été trouvé dans l'emplacement spécifié. Veuillez re-sélectionner la clé et réessayer.";

/* SSL CA certificate file not found message */
"A SSL Certificate Authority certificate location was specified, but no file was found in the specified location.  Please re-select the Certificate Authority certificate and try again." = "Un certificat d'autorité de certification SSL a été spécifié, mais aucun fichier n'a été trouvé à l'emplacement spécifié. Veuillez sélectionner à nouveau le certificat de l'autorité de certification et réessayer.";

/* SSL certificate file not found message */
"A SSL certificate location was specified, but no file was found in the specified location.  Please re-select the certificate and try again." = "Un emplacement de certificat SSL a été spécifié, mais aucun fichier n'a été trouvé à l'emplacement spécifié. Veuillez re-sélectionner le certificat et réessayer.";

/* SSL key file not found message */
"A SSL key file location was specified, but no file was found in the specified location.  Please re-select the key file and try again." = "Un emplacement de fichier de clé SSL a été spécifié, mais aucun fichier n'a été trouvé à l'emplacement spécifié. Veuillez re-sélectionner le fichier de clé et réessayer.";

/* duplicate host informative message */
"A user with the host '%@' already exists" = "Un utilisateur avec l'hôte '%@' existe déjà";

/* duplicate user informative message */
"A user with the name '%@' already exists" = "Un utilisateur avec le nom '%@' existe déjà";

/* table content : editing : error message description when parsing as hex string failed */
"A valid hex string may only contain the numbers 0-9 and letters A-F (a-f). It can optionally begin with „0x“ and spaces will be ignored.\nAlternatively the syntax X'val' is supported, too." = "Une chaîne hexadécimale valide ne peut contenir que les chiffres 0-9 et les lettres A-F (a-f). Il peut éventuellement commencer par „0x“ et les espaces seront ignorés.\nAlternativement, la syntaxe X'val' est également prise en charge.";

/* connection failed due to access denied title */
"Access denied!" = "Accès refusé !";

/* range of double */
"Accurate to approx. 15 decimal places" = "Préciser à environ 15 décimales";

/* range of float */
"Accurate to approx. 7 decimal places" = "Préciser à environ 7 décimales";

/* active connection window is busy. please wait and try again. tooltip */
"Active connection window is busy. Please wait and try again." = "La fenêtre de connexion active est occupée. Veuillez patienter et réessayer.";

/* header for activities pane */
"ACTIVITIES" = "ACTIVITÉS";

/* Add trigger button label */
"Add" = "Ajouter";

/* menu item to add db */
"Add Database..." = "Ajouter une base de données...";

/* Add Host */
"Add Host" = "Ajouter un hôte";

/* add global value or expression menu item */
"Add Value or Expression…" = "Ajouter une valeur ou une expression…";

/* adding index task status message */
"Adding index..." = "Ajout de l'index...";

/* Advanced options short title */
"Advanced" = "Avancé";

/* notifications preference pane name */
"Alerts & Logs" = "Alertes et Logs";

/* notifications preference pane tooltip */
"Alerts & Logs Preferences" = "Préférences alertes et logs";

/* All databases placeholder */
"All Databases" = "Toutes les bases de données";

/* All databases (%) placeholder */
"All Databases (%)" = "Toutes les bases de données (%)";

/* All export files already exist message */
"All the export files already exist. Do you want to replace them?" = "Tous les fichiers d'exportation existent déjà. Voulez-vous les remplacer ?";

/* An error for sequelace URL scheme command occurred. Probably no corresponding connection window found. */
"An error for sequelace URL scheme command occurred. Probably no corresponding connection window found." = "Une erreur pour la commande de schéma d'URL de suite s'est produite. Probablement aucune fenêtre de connexion correspondante trouvée.";

/* no connection available informatie message */
"An error has occurred and there doesn't seem to be a connection available." = "Une erreur est survenue et il ne semble pas y avoir de connexion disponible.";

/* an error occur while executing a scheme command. if the scheme command was invoked by a bundle command, it could be that the command still runs. you can try to terminate it by pressing ⌘+. or via the activities pane. */
"An error occur while executing a scheme command. If the scheme command was invoked by a Bundle command, it could be that the command still runs. You can try to terminate it by pressing ⌘+. or via the Activities pane." = "Une erreur est survenue lors de l'exécution d'une commande de schéma. Si la commande du schéma était appelée par une commande Bundle, il se peut que la commande soit toujours exécutée. Vous pouvez essayer de le terminer en appuyant sur \"+\" ou via le volet \"Activités\".";

/* error killing query informative message */
"An error occurred while attempting to kill connection %lld.\n\nMySQL said: %@" = "Une erreur est survenue lors de la tentative de fermeture de la connexion %1$lld.\n\nMySQL a dit: %2$@";

/* error killing query informative message */
"An error occurred while attempting to kill the query associated with connection %lld.\n\nMySQL said: %@" = "Une erreur s'est produite lors de la tentative de tuer la requête associée à la connexion %1$lld.\n\nMySQL a dit: %2$@";

/* Error shown when unable to show create table syntax */
"An error occurred while creating table syntax.\n\n: %@" = "Une erreur s'est produite lors de la création de la syntaxe de la table.\n\n: %@";

/* rename table error - no temporary name found */
"An error occurred while renaming '%@'. No temporary name could be found. Please try renaming to something else first." = "Une erreur s'est produite lors du renommage de '%@'. Aucun nom temporaire n'a pu être trouvé. Essayez d'abord de renommer quelque chose d'autre.";

/* rename table error informative message */
"An error occurred while renaming '%@'.\n\nMySQL said: %@" = "Une erreur s'est produite lors du renommage de '%1$@'.\n\nMySQL a dit: %2$@";

/* rename error - don't know what type the renamed thing is */
"An error occurred while renaming. '%@' is of an unknown type." = "Une erreur est survenue lors du renommage. '%@' est d'un type inconnu.";

/* rename precedure/function error - can't delete old procedure */
"An error occurred while renaming. I couldn't delete '%@'.\n\nMySQL said: %@" = "Une erreur s'est produite lors du renommage. Je n'ai pas pu supprimer '%1$@'.\n\nMySQL a dit: %2$@";

/* rename precedure/function error - can't recreate procedure */
"An error occurred while renaming. I couldn't recreate '%@'.\n\nMySQL said: %@" = "Une erreur s'est produite lors du renommage. Je n'ai pas pu recréer '%1$@'.\n\nMySQL a dit: %2$@";

/* rename precedure/function error - can't retrieve syntax */
"An error occurred while renaming. I couldn't retrieve the syntax for '%@'.\n\nMySQL said: %@" = "Une erreur s'est produite lors du renommage. Je n'ai pas pu récupérer la syntaxe pour '%1$@'.\n\nMySQL a dit: %2$@";

/* rename error - invalid create syntax */
"An error occurred while renaming. The CREATE syntax of '%@' could not be parsed." = "Une erreur s'est produite lors du renommage. La syntaxe CREATE de '%@' n'a pas pu être analysée.";

/* message of panel when retrieving view information failed */
"An error occurred while retrieving status data.\n\nMySQL said: %@" = "Une erreur s'est produite lors de la récupération des données de statut.\n\nMySQL a dit: %@";

/* message of panel when create syntax cannot be retrieved */
"An error occurred while retrieving the create syntax for '%@'.\nMySQL said: %@" = "Une erreur s'est produite lors de la récupération de la syntaxe de création pour '%1$@'.\nMySQL a dit: %2$@";

/* add index error informative message */
"An error occurred while trying to add the index.\n\nMySQL said: %@" = "Une erreur s'est produite en essayant d'ajouter l'index.\n\nMySQL a dit: %@";

/* error adding password to keychain informative message */
"An error occurred while trying to add the password to your Keychain. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Une erreur est survenue lors de l'ajout du mot de passe à votre trousse. La réparation de votre trousseau pourrait résoudre ce problème, mais si ce n'est pas le cas, veuillez le signaler à l'équipe de Sequel Ace en fournissant le code d'erreur %i.";

/* unable to copy database message informative message */
"An error occurred while trying to copy the database '%@' to '%@'." = "Une erreur est survenue lors de la copie de la base de données '%1$@' vers '%2$@'.";

/* error deleting index informative message */
"An error occurred while trying to delete the index.\n\nMySQL said: %@" = "Une erreur s'est produite en essayant de supprimer l'index.\n\nMySQL a dit: %@";

/* table status : row count query failed : error message */
"An error occurred while trying to determine the number of rows for %@.\nMySQL said: %@ (%lu)" = "Une erreur s'est produite en essayant de déterminer le nombre de lignes pour «%1$@».\nMySQL a dit: %2$@ (%3$lu)";

/* unable to rename database message informative message */
"An error occurred while trying to rename the database '%@' to '%@'." = "Une erreur s'est produite en essayant de renommer la base de données '%1$@' en '%2$@'.";

/* error finding keychain item to edit informative message */
"An error occurred while trying to retrieve the Keychain item you're trying to edit. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Une erreur est survenue lors de la tentative de récupération de l'élément Trousseau que vous essayez de modifier. La réparation de votre trousseau pourrait résoudre ce problème, mais si ce n'est pas le cas, veuillez le signaler à l'équipe de Sequel Ace en fournissant le code d'erreur %i.";

/* error updating keychain item informative message */
"An error occurred while trying to update the Keychain item. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Une erreur est survenue lors de la mise à jour de l'élément Trousseau La réparation de votre trousseau pourrait résoudre ce problème, mais si ce n'est pas le cas, veuillez le signaler à l'équipe de Sequel Ace en fournissant le code d'erreur %i.";

/* mysql error occurred message */
"An error occurred" = "Une erreur s'est produite";

/* MySQL table info retrieval error message */
"An error occurred retrieving table information.  MySQL said: %@" = "Une erreur s'est produite lors de la récupération des informations de la table. MySQL a dit: %@";

/* SQL encoding read error */
"An error occurred when reading the file, as it could not be read in the encoding you selected (%@).\n\nOnly %ld queries were executed." = "Une erreur s'est produite lors de la lecture du fichier, car il n'a pas pu être lu dans l'encodage sélectionné (%1$@).\n\nSeules les requêtes %2$ld ont été exécutées.";

/* CSV encoding read error */
"An error occurred when reading the file, as it could not be read using the encoding you selected (%@).\n\nOnly %ld rows were imported." = "Une erreur s'est produite lors de la lecture du fichier, car il n'a pas pu être lu en utilisant l'encodage que vous avez sélectionné (%1$@).\n\nSeules les lignes %2$ld ont été importées.";

/* SQL read error, including detail from system */
"An error occurred when reading the file.\n\nOnly %ld queries were executed.\n\n(%@)" = "Une erreur s'est produite lors de la lecture du fichier.\n\nSeules les requêtes %1$ld ont été exécutées.\n\n(%2$@)";

/* CSV read error, including detail string from system */
"An error occurred when reading the file.\n\nOnly %ld rows were imported.\n\n(%@)" = "Une erreur s'est produite lors de la lecture du fichier.\n\nSeulement %1$ld lignes ont été importées.\n\n(%2$@)";

/* error adding field informative message */
"An error occurred when trying to add the field '%@' via\n\n%@\n\nMySQL said: %@" = "Une erreur s'est produite en essayant d'ajouter le champ '%1$@' via\n\n%2$@\n\nMySQL a dit: %3$@";

/* error changing field informative message */
"An error occurred when trying to change the field '%@' via\n\n%@\n\nMySQL said: %@" = "Une erreur s'est produite en essayant de changer le champ '%1$@' via\n\n%2$@\n\nMySQL a dit: %3$@";

/* error changing table collation informative message */
"An error occurred when trying to change the table collation to '%@'.\n\nMySQL said: %@" = "Une erreur s'est produite en essayant de changer le collationnement de la table en '%1$@'.\n\nMySQL a dit: %2$@";

/* error changing table encoding informative message */
"An error occurred when trying to change the table encoding to '%@'.\n\nMySQL said: %@" = "Une erreur s'est produite en essayant de changer l'encodage de la table en '%1$@'.\n\nMySQL a dit: %2$@";

/* error changing table type informative message */
"An error occurred when trying to change the table type to '%@'.\n\nMySQL said: %@" = "Une erreur s'est produite lors de la tentative de modification du type de table en '%1$@'.\n\nMySQL a dit : %2$@";

/* error changing table comment informative message */
"An error occurred when trying to change the table's comment to '%@'.\n\nMySQL said: %@" = "Une erreur s'est produite en essayant de modifier le commentaire de la table à '%1$@'.\n\nMySQL a dit: %2$@";

/* an error occurred while analyzing the %@.\n\nMySQL said:%@ */
"An error occurred while analyzing the %@.\n\nMySQL said:%@" = "Une erreur est survenue lors de l'analyse de la %1$@.\n\nMySQL a dit:%2$@";

/* an error occurred while fetching the optimized field type.\n\nMySQL said:%@ */
"An error occurred while fetching the optimized field type.\n\nMySQL said:%@" = "Une erreur s'est produite lors de la récupération du type de champ optimisé.\n\nMySQL a dit:%@";

/* an error occurred while trying to flush the %@.\n\nMySQL said:%@ */
"An error occurred while flushing the %@.\n\nMySQL said:%@" = "Une erreur est survenue lors du vidage de la %1$@.\n\nMySQL a dit:%2$@";

/* sql import error message */
"An error occurred while importing SQL" = "Une erreur s'est produite lors de l'import SQL";

/* an error occurred while trying to optimze the %@.\n\nMySQL said:%@ */
"An error occurred while optimzing the %@.\n\nMySQL said:%@" = "Une erreur s'est produite lors de l'optimisation de la %1$@.\n\nMySQL a dit:%2$@";

/* an error occurred while performing the checksum on the %@.\n\nMySQL said:%@ */
"An error occurred while performing the checksum on %@.\n\nMySQL said:%@" = "Une erreur s'est produite lors de l'exécution de la somme de contrôle sur %1$@.\n\nMySQL a dit:%2$@";

/* an error occurred while trying to repair the %@.\n\nMySQL said:%@ */
"An error occurred while repairing the %@.\n\nMySQL said:%@" = "Une erreur s'est produite lors de la réparation de la %1$@.\n\nMySQL a dit:%2$@";

/* message of panel when retrieving information failed */
"An error occurred while retrieving information.\nMySQL said: %@" = "Une erreur s'est produite lors de la récupération des informations.\nMySQL a dit: %@";

/* error retrieving table information informative message */
"An error occurred while retrieving the information for table '%@'. Please try again.\n\nMySQL said: %@" = "Une erreur s'est produite lors de la récupération des informations de la table '%1$@'. Veuillez réessayer.\n\nMySQL a dit: %2$@";

/* error retrieving table information informative message */
"An error occurred while retrieving the trigger information for table '%@'. Please try again.\n\nMySQL said: %@" = "Une erreur s'est produite lors de la récupération des informations de déclenchement pour la table '%1$@'. Veuillez réessayer.\n\nMySQL a dit: %2$@";

/* error adding new column informative message */
"An error occurred while trying to add the new column '%@' by\n\n%@.\n\nMySQL said: %@" = "Une erreur s'est produite en essayant d'ajouter la nouvelle colonne '%1$@' par\n\n%2$@.\n\nMySQL a dit: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@' by\n\n%@.\n\nMySQL said: %@" = "Une erreur s'est produite en essayant d'ajouter la nouvelle table '%1$@' par\n\n%2$@.\n\nMySQL a dit: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@'.\n\nMySQL said: %@" = "Une erreur s'est produite lors de la tentative d'ajout de la nouvelle table '%1$@'.\n\nMySQL a dit : %2$@";

/* error while trying to alter table message */
"An error occurred while trying to alter table '%@'.\n\nMySQL said: %@" = "Une erreur s'est produite lors de la tentative de modification de la table '%1$@'.\n\nMySQL a dit : %2$@";

/* an error occurred while trying to check the %@.\n\nMySQL said:%@ */
"An error occurred while trying to check the %@.\n\nMySQL said:%@" = "Une erreur est survenue lors de la vérification de l' %1$@.\n\nMySQL a dit:%2$@";

/* error deleting relation informative message */
"An error occurred while trying to delete the relation '%@'.\n\nMySQL said: %@" = "Une erreur s'est produite en essayant de supprimer la relation '%1$@'.\n\nMySQL a dit: %2$@";

/* unable to get list of users informative message */
"An error occurred while trying to get the list of users. Please make sure you have the necessary privileges to perform user management, including access to the mysql.user table." = "Une erreur s'est produite en essayant d'obtenir la liste des utilisateurs. Assurez-vous d'avoir les privilèges nécessaires pour effectuer la gestion des utilisateurs, y compris l'accès à la table mysql.user .";

/* error importing table informative message */
"An error occurred while trying to import a table via: \n%@\n\n\nMySQL said: %@" = "Une erreur s'est produite en essayant d'importer une table via : \n%1$@\n\n\nMySQL a dit: %2$@";

/* error moving field informative message */
"An error occurred while trying to move the field.\n\nMySQL said: %@" = "Une erreur s'est produite en essayant de déplacer le champ.\n\nMySQL a dit: %@";

/* error resetting auto_increment informative message */
"An error occurred while trying to reset AUTO_INCREMENT of table '%@'.\n\nMySQL said: %@" = "Une erreur s'est produite en essayant de réinitialiser AUTO_INCREMENT de la table '%1$@'.\n\nMySQL a dit: %2$@";

/* error truncating table informative message */
"An error occurred while trying to truncate the table '%@'.\n\nMySQL said: %@" = "Une erreur s'est produite en essayant de tronquer la table '%1$@'.\n\nMySQL a dit: %2$@";

/* mysql error occurred informative message */
"An error occurred whilst trying to perform the operation.\n\nMySQL said: %@" = "Une erreur s'est produite en essayant d'effectuer l'opération.\n\nMySQL a dit: %@";

/* mysql resource limits unsupported message */
"Resource Limits are not supported for your version of MySQL. Any Resouce Limits you specified have been discarded and not saved. MySQL said: %@" = "Les limites de ressources ne sont pas prises en charge pour votre version de MySQL. Toutes les limites de récupération que vous avez spécifiées ont été rejetées et non sauvegardées. MySQL a dit: %@";

/* Export files creation error explanatory text */
"An unhandled error occurred when attempting to create %lu of the export files.  Please check the details and try again." = "Une erreur non gérée s'est produite en tentant de créer %lu des fichiers d'exportation. Veuillez vérifier les détails et réessayer.";

/* All export files creation error explanatory text */
"An unhandled error occurred when attempting to create each of the export files.  Please check the details and try again." = "Une erreur non gérée s'est produite lors de la création de chacun des fichiers d'exportation. Veuillez vérifier les détails et réessayer.";

/* Export file creation error explanatory text */
"An unhandled error occurred when attempting to create the export file.  Please check the details and try again." = "Une erreur non gérée s'est produite lors de la création du fichier d'exportation. Veuillez vérifier les détails et réessayer.";

/* ANALYZE one or more tables - result title */
"Analyze %@" = "Analyser %@";

/* analyze selected items menu item */
"Analyze Selected Items" = "Analyser les éléments sélectionnés";

/* analyze table menu item */
"Analyze Table" = "Analyser la table";

/* analyze table failed message */
"Analyze table failed." = "L'analyse de la table a échoué.";

/* change table type informative message */
"Are you sure you want to change this table's type to %@?\n\nPlease be aware that changing a table's type has the potential to cause the loss of some or all of its data. This action cannot be undone." = "Êtes-vous sûr de vouloir changer le type de cette table en %@?\n\nVeuillez noter que le changement de type d'une table peut entraîner la perte d'une partie ou de la totalité de ses données. Cette action ne peut être annulée.";

/* clear global history list informative message */
"Are you sure you want to clear the global history list? This action cannot be undone." = "Êtes-vous sûr de vouloir effacer la liste de l'historique global ? Cette action est irréversible.";

/* clear history list for %@ informative message */
"Are you sure you want to clear the history list for %@? This action cannot be undone." = "Êtes-vous sûr de vouloir effacer l'historique pour %@? Cette action ne peut pas être annulée.";

/* truncate tables informative message */
"Are you sure you want to delete ALL records in the selected tables? This operation cannot be undone." = "Êtes-vous sûr de vouloir supprimer TOUS les enregistrements des tables sélectionnées ? Cette opération ne peut pas être annulée.";

/* truncate table informative message */
"Are you sure you want to delete ALL records in the table '%@'? This operation cannot be undone." = "Êtes-vous sûr de vouloir supprimer TOUS les enregistrements de la table '%@' ? Cette opération ne peut pas être annulée.";

/* delete all rows informative message */
"Are you sure you want to delete all the rows from this table? This action cannot be undone." = "Êtes-vous sûr de vouloir supprimer toutes les lignes de cette table ? Cette action ne peut pas être annulée.";

/* delete table/view informative message */
"Are you sure you want to delete the %@ '%@'? This operation cannot be undone." = "Êtes-vous sûr de vouloir supprimer la %1$@ '%2$@'? Cette opération ne peut pas être annulée.";

/* delete database informative message */
"Are you sure you want to delete the database '%@'? This operation cannot be undone." = "Êtes-vous sûr de vouloir supprimer la base de données '%@' ? Cette opération ne peut pas être annulée.";

/* delete database informative message */
"Are you sure you want to delete the favorite '%@'? This operation cannot be undone." = "Êtes-vous sûr de vouloir supprimer le favori '%@' ? Cette opération ne peut pas être annulée.";

/* delete field informative message */
"Are you sure you want to delete the field '%@'? This action cannot be undone." = "Êtes-vous sûr de vouloir supprimer le champ '%@' ? Cette action ne peut pas être annulée.";

/* delete database informative message */
"Are you sure you want to delete the group '%@'? All groups and favorites within this group will also be deleted. This operation cannot be undone." = "Êtes-vous sûr de vouloir supprimer le groupe '%@' ? Tous les groupes et les favoris de ce groupe seront également supprimés. Cette opération ne peut être annulée.";

/* delete index informative message */
"Are you sure you want to delete the index '%@'? This action cannot be undone." = "Êtes-vous sûr de vouloir supprimer l'index '%@' ? Cette action ne peut pas être annulée.";

/* delete tables/views informative message */
"Are you sure you want to delete the selected %@? This operation cannot be undone." = "Êtes-vous sûr de vouloir supprimer la sélection %@? Cette opération ne peut pas être annulée.";

/* delete rows informative message */
"Are you sure you want to delete the selected %ld rows from this table? This action cannot be undone." = "Êtes-vous sûr de vouloir supprimer les lignes %ld sélectionnées de cette table? Cette action ne peut pas être annulée.";

/* delete selected relation informative message */
"Are you sure you want to delete the selected relations? This action cannot be undone." = "Êtes-vous sûr de vouloir supprimer les relations sélectionnées ? Cette action ne peut pas être annulée.";

/* delete selected row informative message */
"Are you sure you want to delete the selected row from this table? This action cannot be undone." = "Êtes-vous sûr de vouloir supprimer la ligne sélectionnée de cette table? Cette action ne peut pas être annulée.";

/* delete selected trigger informative message */
"Are you sure you want to delete the selected triggers? This action cannot be undone." = "Êtes-vous sûr de vouloir supprimer les déclencheurs sélectionnés ? Cette action ne peut pas être annulée.";

/* kill connection informative message */
"Are you sure you want to kill connection ID %lld?\n\nPlease be aware that continuing to kill this connection may result in data corruption. Please proceed with caution." = "Êtes-vous sûr de vouloir tuer l'ID de connexion %lld?\n\nVeuillez noter que continuer à tuer cette connexion peut entraîner une corruption des données. S'il vous plaît, procédez avec prudence.";

/* kill query informative message */
"Are you sure you want to kill the current query executing on connection ID %lld?\n\nPlease be aware that continuing to kill this query may result in data corruption. Please proceed with caution." = "Êtes-vous sûr de vouloir tuer la requête en cours exécutée sur l'ID de connexion %lld?\n\nVeuillez noter que continuer à tuer cette requête peut entraîner une corruption des données. S'il vous plaît, procédez avec prudence.";

/* Bundle Editor : Remove-Bundle: remove dialog message */
"Are you sure you want to move the selected Bundle to the Trash and remove them respectively?" = "Êtes-vous sûr de vouloir déplacer le lot sélectionné dans la corbeille et les supprimer respectivement?";

/* continue to print informative message */
"Are you sure you want to print the current content view of the table '%@'?\n\nIt currently contains %@ rows, which may take a significant amount of time to print." = "Êtes-vous sûr de vouloir imprimer la vue de contenu actuelle de la table '%1$@' ?\n\nIl contient actuellement des rangées %2$@ , ce qui peut prendre beaucoup de temps à imprimer.";

/* remove all query favorites informative message */
"Are you sure you want to remove all of your saved query favorites? This action cannot be undone." = "Êtes-vous sûr de vouloir supprimer tous vos favoris de requête enregistrés ? Cette action ne peut pas être annulée.";

/* remove all selected content filters informative message */
"Are you sure you want to remove all selected content filters? This action cannot be undone." = "Êtes-vous sûr de vouloir supprimer tous les filtres de contenu sélectionnés ? Cette action ne peut pas être annulée.";

/* remove all selected query favorites informative message */
"Are you sure you want to remove all selected query favorites? This action cannot be undone." = "Êtes-vous sûr de vouloir supprimer tous les favoris de la requête sélectionnée ? Cette action ne peut pas être annulée.";

/* Table Info Section : current value of auto_increment */
"auto_increment: %@" = "auto_incrément: %@";

/* Encoding autodetect menu item */
"Autodetect" = "Détection automatique";

/* background label for color table (Prefs > Editor) */
"Background" = "Arrière-plan";

/* backtick quote label for color table (Prefs > Editor) */
"Backtick Quote" = "Citation en arrière tick";

/* bash error */
"BASH Error" = "Erreur BASH";

/* toolbar item label for switching to the Table Content tab */
"Browse & Edit Table Content" = "Parcourir et éditer le contenu de la table";

/* build label */
"build" = "construire";

/* build label */
"Build" = "Construire";

/* bundle editor menu item label */
"Bundle Editor" = "Éditeur de lots";

/* bundle error */
"Bundle Error" = "Erreur de bundle";

/* bundles menu item label */
"Bundles" = "Bundles";

/* Bundle Editor : Outline View : 'BUNDLES' item */
"BUNDLES" = "BUNDLES";

/* Bundle Editor : Outline View : Menu Category item : tooltip */
"Bundles in category %@" = "Bundles dans la catégorie %@";

/* bundles installation error */
"Bundles Installation Error" = "Erreur d'installation des paquets";

/* bzip2 compression export summary - within a sentence */
"bzip2 compression" = "Compression bzip2";

/* description of geometry */
"Can store a single spatial value of types POINT, LINESTRING or POLYGON. Spatial support in MySQL is based on the OpenGIS Geometry Model." = "Vous pouvez stocker une seule valeur spatiale de type POINT, LINESTRING ou POLYGON.";

/* Bundle Editor : Remove-Bundle: cancel button
 cancel
 cancel button
 Open Files : Bundle : Already-Installed : Cancel button
 Preferences : Network : Custom SSH client : warning dialog : cancel button
 shutdown server : confirmation dialog : cancel button */
"Cancel" = "Annuler";

/* sql import : charset error alert : cancel button */
"Cancel Import" = "Annuler l'import";

/* cancelling task status message */
"Cancelling..." = "Annulation en cours...";

/* empty query informative message */
"Cannot save an empty query." = "Impossible d'enregistrer une requête vide.";

/* caret label for color table (Prefs > Editor) */
"Caret" = "Carets";

/* change button */
"Change" = "Changement";

/* change focus to table list menu item */
"Change Focus to Table List" = "Passer la mise au point à la liste des tables";

/* change table type message */
"Change table type" = "Changer le type de table";

/* unsaved changes informative message */
"Changes have been made, which will be lost if this window is closed. Are you sure you want to continue" = "Des modifications ont été faites, qui seront perdues si cette fenêtre est fermée. Êtes-vous sûr de vouloir continuer";

/* quitting app informal alert title */
"Close the app?" = "Fermer l'application ?";

/* quitting app informal alert body */
"Are you sure you want to quit the app?" = "Êtes-vous certain de vouloir fermer l'application ?";

/* character set client: %@ */
"character set client: %@" = "jeu de caractères client: %@";

/* CHECK one or more tables - result title */
"Check %@" = "Vérifier %@";

/* check of all selected items successfully passed message */
"Check of all selected items successfully passed." = "Vérification de tous les éléments sélectionnés réussie.";

/* check option: %@ */
"check option: %@" = "option de vérification : %@";

/* check selected items menu item */
"Check Selected Items" = "Vérifier les éléments sélectionnés";

/* check table menu item */
"Check Table" = "Vérification de la table";

/* check table failed message */
"Check table failed." = "La vérification de la table a échoué.";

/* check table successfully passed message */
"Check table successfully passed." = "La table a été vérifiée avec succès.";

/* check view menu item */
"Check View" = "Vérifier la vue";

/* checking field data for editing task description */
"Checking field data for editing..." = "Vérification des données du champ pour l'édition...";

/* checksum %@ message */
"Checksum %@" = "Somme de contrôle %@";

/* checksum selected items menu item */
"Checksum Selected Items" = "Eléments sélectionnés de la somme de contrôle";

/* checksum table menu item */
"Checksum Table" = "Table de somme de contrôle";

/* Checksums of %@ message */
"Checksums of %@" = "Sommes de contrôle %@";

/* menu item for choose db */
"Choose Database..." = "Choisir la base de données...";

/* cancelling export cleaning up message */
"Cleaning up..." = "Nettoyage en cours...";

/* clear button */
"Clear" = "Nettoyer";

/* toolbar item for clear console */
"Clear Console" = "Effacer la console";

/* clear global history menu item title */
"Clear Global History" = "Effacer l'historique global";

/* clear history for %@ menu title */
"Clear History for %@" = "Effacer l'historique pour %@";

/* clear history message */
"Clear History?" = "Effacer l'historique ?";

/* tooltip for toolbar item for clear console */
"Clear the console which shows all MySQL commands performed by Sequel Ace" = "Effacer la console qui affiche toutes les commandes MySQL exécutées par Séquel Ace";

/* clear the document-based history list tooltip message */
"Clear the document-based history list" = "Vider la liste de l'historique basée sur le document";

/* clear the global history list tooltip message */
"Clear the global history list" = "Effacer la liste de l'historique global";

/* Close menu item */
"Close" = "Fermer";

/* close tab context menu item */
"Close Tab" = "Fermer l'onglet";

/* Close Window menu item */
"Close Window" = "Fermer la fenêtre";

/* collation label (Navigator) */
"Collation" = "Regroupement";

/* collation connection: %@ */
"collation connection: %@" = "connexion au collationnement : %@";

/* comment label */
"Comment" = "Commenter";

/* Title of action menu item to comment line */
"Comment Line" = "Ligne de commentaire";

/* Title of action menu item to comment selection */
"Comment Selection" = "Commenter la sélection";

/* connect button */
"Connect" = "Connecter";

/* Connect via socket button */
"Connect via socket" = "Se connecter via le socket";

/* connection established message */
"Connected" = "Connecté";

/* description for connected notification */
"Connected to %@" = "Connecté à %@";

/* message of panel when connection to db failed */
"Connected to host, but unable to connect to database %@.\n\nBe sure that the database exists and that you have the necessary privileges.\n\nMySQL said: %@" = "Connecté à l'hôte, mais impossible de se connecter à la base de données %1$@.\n\nAssurez-vous que la base de données existe et que vous avez les privilèges nécessaires.\n\nMySQL a dit: %2$@";

/* Generic connecting very short status message */
"Connecting..." = "Connexion en cours...";

/* window title string indicating that sp is connecting */
"Connecting…" = "Connexion à…";

/* error while reading connection data file */
"Connection data file couldn't be read." = "Le fichier de données de connexion n'a pas pu être lu.";

/* message error while reading connection data file and suggesting to save it under a differnet name */
"Connection data file %@ couldn't be read. Please try to save the document under a different name." = "Le fichier de données de connexion %@ n'a pas pu être lu. Veuillez essayer d'enregistrer le document sous un nom différent.";

/* connection failed title */
"Connection failed!" = "Échec de la connexion !";

/* Connection file is encrypted */
"Connection file is encrypted" = "Le fichier de connexion est chiffré";

/* Connection success very short status message */
"Connection succeeded" = "Connexion réussie";

/* Console */
"Console" = "Console";

/* Console : Save as : Initial filename */
"ConsoleLog" = "Journal de la console";

/* toolbar item label for switching to the Table Content tab */
"Content" = "Contenus";

/* content filter clause is empty tooltip. */
"Content Filter clause is empty." = "La clause de filtre de contenu est vide.";

/* continue button
 Continue button title */
"Continue" = "Continuer";

/* continue to print message */
"Continue to print?" = "Continuer à imprimer ?";

/* Copy as RTF */
"Copy as RTF" = "Copier en tant que RTF";

/* copy create func syntax menu item
 Table List : Context Menu : copy CREATE FUNCTION syntax */
"Copy Create Function Syntax" = "Copier la syntaxe de création de fonction";

/* copy create proc syntax menu item
 Table List : Context Menu : Copy CREATE PROCEDURE syntax
 Table List : Gear Menu : Copy CREATE PROCEDURE syntax */
"Copy Create Procedure Syntax" = "Copier la syntaxe de création d'acte";

/* copy create syntaxes menu item
 Table List : Context Menu : Copy CREATE syntax (multiple selection)
 Table List : Gear Menu : Copy CREATE syntax (multiple selection) */
"Copy Create Syntaxes" = "Copier la création de syntaxes";

/* copy create table syntax menu item
 Table List : Context Menu : Copy CREATE syntax (single table)
 Table List : Gear Menu : Copy CREATE syntax (single table) */
"Copy Create Table Syntax" = "Copier Créer une syntaxe de table";

/* copy create view syntax menu item
 Table List : Context Menu : Copy CREATE view statement
 Table List : Gear Menu : Copy CREATE view statement */
"Copy Create View Syntax" = "Copier la syntaxe de la vue";

/* copy server variable name menu item */
"Copy Variable Name" = "Copier le nom de la variable";

/* copy server variable names menu item */
"Copy Variable Names" = "Copier les noms des variables";

/* copy server variable value menu item */
"Copy Variable Value" = "Copier la valeur variable";

/* copy server variable values menu item */
"Copy Variable Values" = "Copier les valeurs des variables";

/* Procedure/function export permission error */
"Could not export the %@ '%@' because of a permissions error.\n" = "Impossible d'exporter la %1$@ '%2$@' à cause d'une erreur de permission.\n";

/* Error when we can't parse/split file as CSV */
"Could not parse file as CSV" = "Impossible d'analyser le fichier comme CSV";

/* message when database selection failed */
"Could not select database" = "Impossible de sélectionner la base de données";

/* Alter Database : Query Failed ($1 = mysql error message) */
"Couldn't alter database.\nMySQL said: %@" = "Impossible de modifier la base de données.\nMySQL a dit: %@";

/* Couldn't copy default themes to Application Support Theme folder!\nError: %@ */
"Couldn't copy default themes to Application Support Theme folder!\nError: %@" = "Impossible de copier les thèmes par défaut dans le dossier du thème de prise en charge de l'application!\nErreur: %@";

/* message of panel when table cannot be created */
"Couldn't create '%@'.\nMySQL said: %@" = "Impossible de créer '%1$@'.\nMySQL a dit: %2$@";

/* Couldn't create Application Support Bundle folder!\nError: %@ */
"Couldn't create Application Support Bundle folder!\nError: %@" = "Impossible de créer le dossier Bundle de prise en charge de l'application!\nErreur: %@";

/* Couldn't create Application Support Theme folder!\nError: %@ */
"Couldn't create Application Support Theme folder!\nError: %@" = "Impossible de créer le dossier du thème de prise en charge de l'application !\nErreur: %@";

/* message of panel when creation of db failed */
"Couldn't create database.\nMySQL said: %@" = "Impossible de créer la base de données.\nMySQL a dit: %@";

/* message of panel when an item cannot be deleted */
"Couldn't delete '%@'.\n\nMySQL said: %@" = "Impossible de supprimer '%1$@'.\n\nMySQL a dit: %2$@";

/* message of panel when an item cannot be deleted including informative message about using force deletion */
"Couldn't delete '%@'.\n\nSelecting the 'Force delete' option may prevent this issue, but may leave the database in an inconsistent state.\n\nMySQL said: %@" = "Impossible de supprimer '%1$@'.\n\nLa sélection de l'option 'Forcer la suppression' peut empêcher ce problème, mais peut laisser la base de données dans un état incohérent.\n\nMySQL a dit: %2$@";

/* message of panel when field cannot be deleted */
"Couldn't delete field %@.\nMySQL said: %@" = "Impossible de supprimer le champ %1$@.\nMySQL a dit: %2$@";

/* message when deleteing all rows failed */
"Couldn't delete rows.\n\nMySQL said: %@" = "Impossible de supprimer les lignes.\n\nMySQL a dit: %@";

/* message of panel when deleting db failed */
"Couldn't delete the database.\nMySQL said: %@" = "Impossible de supprimer la base de données.\nMySQL a dit: %@";

/* message of panel when an item cannot be renamed */
"Couldn't duplicate '%@'.\nMySQL said: %@" = "Impossible de dupliquer '%1$@'.\nMySQL a dit: %2$@";

/* message of panel when flushing privs failed */
"Couldn't flush privileges.\nMySQL said: %@" = "Impossible de vider les privilèges.\nMySQL a dit: %@";

/* message of panel when table information cannot be retrieved */
"Couldn't get create syntax.\nMySQL said: %@" = "Impossible d'obtenir la syntaxe.\nMySQL a dit: %@";

/* Custom Query result editing error - could not identify a corresponding column */
"Couldn't identify field origin unambiguously. The column '%@' contains data from more than one table." = "Impossible d'identifier l'origine du champ sans ambiguïté. La colonne '%@' contient des données provenant de plus d'une table.";

/* message of panel when loading of row failed */
"Couldn't load the row. Reload the table to be sure that the row exists and use a primary key for your table." = "Impossible de charger la ligne. Rechargez la table pour être sûr que la ligne existe et utilisez une clé primaire pour votre table.";

/* Couldn't read the file content of */
"Couldn't read the file content of" = "Impossible de lire le contenu du fichier de";

/* text shown if an error occurred while sorting the result table */
"Couldn't sort column." = "Impossible de trier la colonne.";

/* message of panel when sorting of table failed */
"Couldn't sort table. MySQL said: %@" = "Impossible de trier la table. MySQL a dit: %@";

/* message of panel when error while updating field to db */
"Couldn't write field.\nMySQL said: %@" = "Impossible d'écrire le champ.\nMySQL a dit: %@";

/* create syntax for table comment */
"Create syntax for" = "Créer une syntaxe pour";

/* Create syntax label */
"Create syntax for %@ '%@'" = "Créer une syntaxe pour %1$@ '%2$@ ' '";

/* Create syntaxes for selected items label */
"Create syntaxes for selected items" = "Créer des syntaxes pour les éléments sélectionnés";

/* Table Info Section : table create options */
"create_options: %@" = "create_options: %@";

/* created: %@
 Table Info Section : time+date table was created at */
"created: %@" = "créé: %@";

/* description of polygon */
"Creates a surface by combining one LinearRing (ie. a LineString that is closed and simple) as the outside boundary with zero or more inner LinearRings acting as \"holes\"." = "Crée une surface en combinant un anneau linéaire (c.-à-d. une Chaîne linéaire qui est fermée et simple) comme la frontière extérieure avec zéro ou plus d'anneaux linéaires internes agissant comme \"trous\".";

/* Creating table task string */
"Creating %@..." = "Création de %@...";

/* Bundle Editor : Fallback Input source dropdown : 'current line' item */
"Current Line" = "Ligne actuelle";

/* Bundle Editor : Fallback Input source dropdown : 'current query' item */
"Current Query" = "Requête actuelle";

/* header for current selection in filtered list */
"CURRENT SELECTION" = "SÉLECTION ACTUELLE";

/* Bundle Editor : Fallback Input source dropdown : 'current word' item */
"Current Word" = "Mot actuel";

/* SSH connection : debug header with user-defined ssh binary */
"Custom SSH binary enabled. Disable in Preferences to rule out incompatibilities!" = "binaire SSH personnalisé activé. Désactivez dans les préférences pour exclure les incompatibilités!";

/* customize file name label */
"Customize Filename (%@)" = "Personnaliser le nom du fichier (%@)";

/* data access: %@ (%@) */
"data access: %@ (%@)" = "accès aux données : %1$@ (%2$@)";

/* Bundle Editor : Scope dropdown : 'data table' item
 data table menu item label */
"Data Table" = "Tableau de données";

/* Bundle Editor : Outline View : 'Data Table' item : tooltip */
"Data Table Scope\ncommands will run on the Content and Query data tables" = "Les commandes de tableau de données\ns'exécuteront sur les tables de données Contenu et Requête";

/* export filename database token
 export header database label
 export source
 Table Structure : Collation dropdown : 'item is the same as the collation of database' marker
 Table Structure : Encoding dropdown : 'item is database default' marker */
"Database" = "Base de données";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'database changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'database changed' item */
"Database changed" = "Base de données modifiée";

/* message of panel when no db name is given */
"Database must have a name." = "La base de données doit avoir un nom.";

/* databsse rename unsupported message */
"Database Rename Unsupported" = "Renommer la base de données non prise en charge";

/* export filename date token */
"Date" = "Date";

/* export filename date token */
"Day" = "Jour";

/* Add Table Sheet : Collation : Default (unknown)
 Collation Dropdown : Default (unknown)
 default label
 Table Structure : Collation dropdown : 'item is the same as the default collation of the row's charset' marker */
"Default" = "Par défaut";

/* Add Table : Collation : Default ($1 = collation name)
 Charset Dropdown : Default item ($1 = charset name)
 Collation Dropdown : Default ($1 = collation name)
 Collation Dropdown : Default collation for given charset ($1 = collation name)
 New Table Sheet : Table Engine Dropdown : Default */
"Default (%@)" = "Par défaut (%@)";

/* default bundles update */
"Default Bundles Update" = "Mise à jour des packs par défaut";

/* import : csv field mapping : field default value */
"Default: %@" = "Par défaut: %@";

/* Query snippet default value placeholder */
"default_value" = "valeur par défaut";

/* definer label (Navigator) */
"Definer" = "Défini";

/* definer: %@ */
"definer: %@" = "définir: %@";

/* description of enum */
"Defines a list of members, of which every field can use at most one. Values are sorted by their index number (starting at 0 for the first member)." = "Définit une liste de membres, dont chaque champ peut être utilisé au plus un. Les valeurs sont triées par leur numéro d'index (à partir de 0 pour le premier membre).";

/* delete button */
"Delete" = "Supprimer";

/* delete table/view message */
"Delete %@ '%@'?" = "Supprimer %1$@ '%2$@ ' ?";

/* table structure : indexes : delete index : error 1553 : delete index and FK button */
"Delete Both" = "Supprimer les deux";

/* delete database message */
"Delete database '%@'?" = "Supprimer la base de données '%@ ' ?";

/* delete database message */
"Delete favorite '%@'?" = "Supprimer le favori '%@ ' ?";

/* delete field message */
"Delete field '%@'?" = "Supprimer le champ '%@ ' ?";

/* delete func menu title */
"Delete Function" = "Supprimer la fonction";

/* delete functions menu title */
"Delete Functions" = "Supprimer les fonctions";

/* delete database message */
"Delete group '%@'?" = "Supprimer le groupe '%@ ' ?";

/* delete index message */
"Delete index '%@'?" = "Supprimer l'index%@'?";

/* delete items menu title */
"Delete Items" = "Supprimer les éléments";

/* delete proc menu title */
"Delete Procedure" = "Supprimer la procédure";

/* delete procedures menu title */
"Delete Procedures" = "Supprimer les Actes";

/* delete relation menu item */
"Delete Relation" = "Supprimer la relation";

/* delete relation message */
"Delete relation" = "Supprimer la relation";

/* delete relations menu item */
"Delete Relations" = "Supprimer les relations";

/* delete row menu item singular */
"Delete Row" = "Supprimer la ligne";

/* delete rows menu item plural */
"Delete Rows" = "Supprimer les lignes";

/* delete rows message */
"Delete rows?" = "Supprimer les lignes ?";

/* delete tables/views message */
"Delete selected %@?" = "Supprimer la sélection %@?";

/* delete selected row message */
"Delete selected row?" = "Supprimer la ligne sélectionnée ?";

/* delete table menu title */
"Delete Table..." = "Supprimer la table...";

/* delete tables menu title */
"Delete Tables" = "Supprimer les tables";

/* delete trigger menu item */
"Delete Trigger" = "Supprimer le déclencheur";

/* delete trigger message */
"Delete trigger" = "Supprimer le déclencheur";

/* delete triggers menu item */
"Delete Triggers" = "Supprimer les déclencheurs";

/* delete view menu title */
"Delete View" = "Supprimer la vue";

/* delete views menu title */
"Delete Views" = "Supprimer les vues";

/* Preferences : Network : SSL Chiper suites : List seperator */
"Disabled Cipher Suites" = "Suites de chiffrement désactivées";

/* force table deltion button text tooltip */
"Disables foreign key checks (FOREIGN_KEY_CHECKS) before deletion and re-enables them afterwards." = "Désactive les vérifications de clés étrangères (FOREIGN_KEY_CHECKS) avant la suppression et les réactive ensuite.";

/* discard changes button */
"Discard changes" = "Annuler les modifications";

/* description for disconnected notification */
"Disconnected from %@" = "Déconnecté de %@";

/* do update operator tooltip */
"Do UPDATE where field contents match" = "MISE À JOUR où le contenu du champ correspond";

/* message of panel asking for confirmation for loading large text into the query editor */
"Do you really want to load a SQL file with %@ of data into the Query Editor?" = "Voulez-vous vraiment charger un fichier SQL avec %@ de données dans l'éditeur de requête ?";

/* message of panel asking for confirmation for inserting large text from dragging action */
"Do you really want to proceed with %@ of data?" = "Voulez-vous vraiment continuer avec %@ de données ?";

/* shutdown server : confirmation dialog : title */
"Do you really want to shutdown the server?" = "Voulez-vous vraiment éteindre le serveur?";

/* dtd identifier label (Navigator) */
"DTD Identifier" = "Identificateur DTD";

/* sql export dump of table label */
"Dump of table" = "Dump de la table";

/* sql export dump of view label */
"Dump of view" = "Extrait de la vue";

/* text showing that app is writing dump */
"Dumping..." = "Dump en cours...";

/* duplicate object message */
"Duplicate %@ '%@' to:" = "Dupliquer %1$@ '%2$@' à :";

/* duplicate func menu title */
"Duplicate Function..." = "Dupliquer la fonction...";

/* duplicate host message */
"Duplicate Host" = "Hôte en double";

/* duplicate proc menu title */
"Duplicate Procedure..." = "Procédure en double...";

/* duplicate table menu title */
"Duplicate Table..." = "Dupliquer la table...";

/* duplicate user message */
"Duplicate User" = "Dupliquer l'utilisateur";

/* duplicate view menu title */
"Duplicate View..." = "Dupliquer la vue...";

/* partial copy database support informative message */
"Duplicating the database '%@' is only partially supported as it contains objects other than tables (i.e. views, procedures, functions, etc.), which will not be copied.\n\nWould you like to continue?" = "La duplication de la base de données '%@' n'est que partiellement supportée car elle contient des objets autres que les tables (i.e. vues, procédures, fonctions, etc.), qui ne seront pas copiées.\n\nVoulez-vous continuer ?";

/* edit filter */
"Edit Filters…" = "Modifier les filtres…";

/* Edit row button */
"Edit row" = "Editer la ligne";

/* toolbar item label for switching to the Table Structure tab */
"Edit Table Structure" = "Modifier la structure de la table";

/* edit theme list label */
"Edit Theme List…" = "Modifier la liste des thèmes…";

/* edit user-defined filter */
"Edit user-defined Filters…" = "Editer les filtres prédéfinis…";

/* empty query message */
"Empty query" = "Requête vide";

/* MySQL search language code - eg in http://search.mysql.com/search?q=select&site=refman-50&lr=lang_en */
"en" = "fr";

/* encoding label (Navigator) */
"Encoding" = "Encodage";

/* Table Info Section : $1 = table charset */
"encoding: %1$@" = "encodage : %1$@";

/* Table Info Section : $1 = table charset, $2 = table collation */
"encoding: %1$@ (%2$@)" = "encodage : %1$@ (%2$@)";

/* Table Info Section : Table Engine */
"engine: %@" = "moteur: %@";

/* enter connection details label */
"Enter connection details below, or choose a favorite" = "Entrez les informations de connexion ci-dessous, ou choisissez un favori";

/* SSH key password prompt */
"Enter your password for the SSH key\n\"%@\"" = "Entrez votre mot de passe pour la clé SSH\n'%@";

/* Bundle Editor : Fallback Input source dropdown : 'entire content' item
 Bundle Editor : Scope=Field : Input source dropdown: 'entire content' item */
"Entire Content" = "Tout le contenu";

/* Bundle Editor : Copy-Command-Error : error dialog title
 error */
"Error" = "Erreur";

/* error adding field message */
"Error adding field" = "Erreur lors de l'ajout du champ";

/* error adding new column message */
"Error adding new column" = "Erreur lors de l'ajout d'une nouvelle colonne";

/* error adding new table message */
"Error adding new table" = "Erreur lors de l'ajout d'une nouvelle table";

/* error adding password to keychain message */
"Error adding password to Keychain" = "Erreur lors de l'ajout du mot de passe au trousseau";

/* error changing field message */
"Error changing field" = "Erreur lors du changement de champ";

/* error changing table collation message */
"Error changing table collation" = "Erreur lors du changement du collationnement de la table";

/* error changing table comment message */
"Error changing table comment" = "Erreur lors du changement du commentaire de la table";

/* error changing table encoding message */
"Error changing table encoding" = "Erreur lors du changement de l'encodage de la table";

/* error changing table type message */
"Error changing table type" = "Erreur lors du changement du type de table";

/* error creating relation message */
"Error creating relation" = "Erreur de création de la relation";

/* error creating trigger message */
"Error creating trigger" = "Erreur lors de la création du déclencheur";

/* error for message */
"Error for" = "Erreur pour";

/* error for bash command ($1), $2=message */
"Error for “%1$@”:\n%2$@" = "Erreur pour «%1$@» :\n%2$@";

/* error moving field message */
"Error moving field" = "Erreur lors du déplacement du champ";

/* error occurred */
"error occurred" = "une erreur s'est produite";

/* error reading import file */
"Error reading import file." = "Erreur lors de la lecture du fichier d'importation.";

/* error finding keychain item to edit message */
"Error retrieving Keychain item to edit" = "Erreur lors de la récupération de l'élément Trousseau à modifier";

/* error retrieving table information message */
"Error retrieving table information" = "Erreur lors de la récupération des informations de la table";

/* error retrieving trigger information message */
"Error retrieving trigger information" = "Erreur lors de la récupération des informations du déclencheur";

/* error truncating table message */
"Error truncating table" = "Erreur lors du tronquage de la table";

/* error updating keychain item message */
"Error updating Keychain item" = "Erreur lors de la mise à jour de l'élément Trousseau";

/* error while analyzing selected items message */
"Error while analyzing selected items" = "Erreur lors de l'analyse des éléments sélectionnés";

/* error while checking selected items message */
"Error while checking selected items" = "Erreur lors de la vérification des éléments sélectionnés";

/* error while converting color scheme data */
"Error while converting color scheme data" = "Erreur lors de la conversion des données du schéma de couleurs";

/* error while converting connection data */
"Error while converting connection data" = "Erreur lors de la conversion des données de connexion";

/* Content filters could not be converted to plist upon export - message title (ContentFilterManager) */
"Error while converting content filter data" = "Erreur lors de la conversion des données du filtre de contenu";

/* error while converting query favorite data */
"Error while converting query favorite data" = "Erreur lors de la conversion des données favoris de la requête";

/* error while converting session data */
"Error while converting session data" = "Erreur lors de la conversion des données de session";

/* Error while deleting field */
"Error while deleting field" = "Erreur lors de la suppression du champ";

/* Bundle Editor : Copy-Command-Error : Copying failed error message */
"Error while duplicating Bundle content." = "Erreur lors de la duplication du contenu du paquet.";

/* error while executing javascript bash command */
"Error while executing JavaScript BASH command" = "Erreur lors de l'exécution de la commande JavaScript BASH";

/* error while fetching the optimized field type message */
"Error while fetching the optimized field type" = "Erreur lors de la récupération du type de champ optimisé";

/* error while flushing selected items message */
"Error while flushing selected items" = "Erreur lors du vidage des éléments sélectionnés";

/* error while importing table message */
"Error while importing table" = "Erreur lors de l'importation de la table";

/* Open Files : Bundle : Install-Error : error dialog title
 Open Files : Bundle : UUID : Error dialog title */
"Error while installing Bundle" = "Erreur lors de l'installation du bundle";

/* error while installing color theme file */
"Error while installing color theme file" = "Erreur lors de l'installation du fichier de thème de couleur";

/* Bundle Editor : Trash-Bundle(s)-Error : error dialog title
 error while moving %@ to trash
 Open Files : Bundle : Already-Installed : Delete-Old-Error : Could not delete old bundle before installing new version. */
"Error while moving %@ to Trash." = "Erreur lors du déplacement de %@ vers la corbeille.";

/* error while optimizing selected items message */
"Error while optimizing selected items" = "Erreur lors de l'optimisation des éléments sélectionnés";

/* error while parsing CREATE TABLE syntax */
"Error while parsing CREATE TABLE syntax" = "Erreur lors de l'analyse de la syntaxe CREATE TABLE";

/* error while reading connection data file */
"Error while reading connection data file" = "Erreur lors de la lecture du fichier de données de connexion";

/* error while reading data file */
"Error while reading data file" = "Erreur lors de la lecture du fichier de données";

/* error while repairing selected items message */
"Error while repairing selected items" = "Erreur lors de la réparation des éléments sélectionnés";

/* Bundle Editor : Save-Bundle-Error : error dialog title */
"Error while saving the Bundle." = "Erreur lors de l'enregistrement du bundle.";

/* Bundle Editor : Save-and-Close-Error : error dialog title */
"Error while saving %@." = "Erreur lors de la sauvegarde de %@.";

/* Errors title */
"Errors" = "Erreurs";

/* Mysql Help Viewer : Help Topic: Example section title */
"Example:" = "Exemple:";

/* Bundle Editor : BLOB dropdown : 'exclude BLOB' item */
"exclude BLOB" = "exclure le BLOB";

/* execution privilege label (Navigator) */
"Execution Privilege" = "Privilège d'Exécution";

/* execution privilege: %@ */
"execution privilege: %@" = "privilège d'exécution : %@";

/* execution stopped message */
"Execution stopped!\n" = "Exécution arrêtée !\n";

/* export selected favorites menu item */
"Export Selected..." = "Exporter la sélection ...";

/* text showing that the application is importing a supplied format */
"Exporting %@" = "Exportation de %@";

/* text showing that the application is exporting a Dot file */
"Exporting Dot File" = "Exportation du fichier point";

/* text showing that the application is exporting SQL */
"Exporting SQL" = "Exportation du SQL";

/* extra label (Navigator) */
"Extra" = "Supplément";

/* table structure : indexes : delete index : no columns error : title */
"Failed to remove index '%@'" = "Impossible de supprimer l'index '%@'";

/* fatal error */
"Fatal Error" = "Erreur fatale";

/* export filename favorite name token */
"Favorite" = "Favoris";

/* favorites label */
"Favorites" = "Favoris";

/* favorites export error message */
"Favorites export error" = "Erreur d'exportation des Favoris";

/* favorites import error message */
"Favorites import error" = "Erreur d'importation des Favoris";

/* export label showing that the app is fetching data */
"Fetching data..." = "Récupération des données...";

/* fetching database structure data in progress */
"fetching database structure data in progress" = "récupération des données de la structure de la base de données en cours";

/* fetching database structure in progress */
"fetching database structure in progress" = "récupération de la structure de la base de données en cours";

/* fetching table data for completion in progress message */
"fetching table data…" = "récupération des données de la table…";

/* popup menuitem for field (showing only if disabled) */
"field" = "champ";

/* Field */
"Field" = "Champ";

/* Query result editing error - could not match row being edited uniquely */
"Field is not editable. Couldn't identify field origin unambiguously (%ld matches)." = "Le champ n'est pas éditable. Impossible d'identifier l'origine du champ sans ambiguïté (%ld correspondances).";

/* field is not editable due to no table/database */
"Field is not editable. Field has no or multiple table or database origin(s)." = "Le champ n'est pas modifiable. Le champ n'a pas de table ou origine de base de données multiple.";

/* Custom Query result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload data, check encoding, or try to add\na primary key field or more fields\nin your SELECT statement for table '%@'\nto identify field origin unambiguously." = "Le champ n'est pas modifiable. Aucun enregistrement correspondant trouvé.\nRecharger les données, vérifier l'encodage, ou essayez d'ajouter\nun champ clé primaire ou plus de champs\ndans votre instruction SELECT pour la table '%@'\npour identifier sans ambiguïté l'origine du champ.";

/* Table Content result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload table, check the encoding, or try to add\na primary key field or more fields\nin the view declaration of '%@' to identify\nfield origin unambiguously." = "Le champ n'est pas modifiable. Aucun enregistrement correspondant trouvé.\nRecharger la table, vérifier l'encodage, ou essayez d'ajouter\nun champ clé primaire ou plus de champs\ndans la déclaration de vue de '%@' pour identifier sans ambiguïté l'origine du champ\n.";

/* error while reading data file */
"File couldn't be read." = "Le fichier n'a pas pu être lu.";
"File couldn't be read: %@\n\nIt will be deleted." = "Le fichier n'a pas pu être lu: %@\n\nIl sera supprimé.";

/* File read error title (Import Dialog) */
"File read error" = "Erreur de lecture du fichier";

/* All export files already exist explanatory text */
"Files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "Des fichiers portant le même nom existent déjà dans le dossier cible. Les remplacer écrasera leur contenu actuel.";

/* filter label */
"Filter" = "Filtre";

/* apply filter label */
"Apply Filter(s)" = "Appliquer le(s) filtre(s)";

/* filter tables menu item */
"Filter Tables" = "Filtrer les tables";

/* export source */
"Filtered table content" = "Contenu de la table filtrée";

/* filtering failed. please try again. tooltip */
"Filtering failed. Please try again." = "Le filtrage a échoué. Veuillez réessayer.";

/* Filtering table task description */
"Filtering table..." = "Filtrage de la table...";

/* description for finished exporting notification */
"Finished exporting to %@" = "Exportation vers %@ terminée";

/* description for finished importing notification */
"Finished importing %@" = "Import terminé %@";

/* FLUSH one or more tables - result title */
"Flush %@" = "Vider %@";

/* flush selected items menu item */
"Flush Selected Items" = "Vider les éléments sélectionnés";

/* flush table menu item */
"Flush Table" = "Vider la table";

/* flush table failed message */
"Flush table failed." = "La table de vidage a échoué.";

/* flush view menu item */
"Flush View" = "Vider la vue";

/* title of panel when successfully flushed privs */
"Flushed Privileges" = "Privilèges purgés";

/* For BIT fields only “1” or “0” are allowed. */
"For BIT fields only “1” or “0” are allowed." = "Pour les champs BIT seuls « 1 » ou « 0 » sont autorisés.";

/* force table deletion button text */
"Force delete (disables integrity checks)" = "Forcer la suppression (désactive les vérifications d'intégrité)";

/* full text index menu item title */
"FULLTEXT" = "FULLTEXT";

/* function */
"function" = "fonction";

/* header for function info pane */
"FUNCTION INFORMATION" = "INFORMATIONS DE FONCTION";

/* functions */
"functions" = "fonctions";

/* Bundle Editor : Scope dropdown : 'general' item
 general menu item label
 general preference pane name */
"General" = "Généraux";

/* general preference pane tooltip */
"General Preferences" = "Préférences Générales";

/* Bundle Editor : Outline View : 'General' item : tooltip */
"General Scope\ncommands will run application-wide" = "Les commandes générales Scope\nexécuteront l'ensemble de l'application";

/* generating print document status message */
"Generating print document..." = "Génération du document d'impression...";

/* export header generation time label */
"Generation Time" = "Temps de génération";

/* Content Filter Manager : Filter Entry List: 'Global' Header
 Query Favorites : List : Section Heading : global query favorites */
"Global" = "Globale";

/* Query Favorites : List : Section Heading : global : tooltip */
"Globally stored favorites" = "Favoris stockés globalement";

/* Preferences : Notifications : status text : notification setup */
"Notifications are delivered via the Notification Center." = "Les notifications sont envoyées via le centre de notification.";

/* Gzip compression export summary - within a sentence */
"Gzip compression" = "Compression Gzip";

/* MySQL Help Viewer : Results list : Page title */
"Help topics for %@" = "Sujets d'aide pour %@";

/* hide console */
"Hide Console" = "Masquer la console";

/* hide navigator */
"Hide Navigator" = "Hide Navigator";

/* hide tab bar */
"Hide Tab Bar" = "Masquer la barre d'onglets";

/* Hide Toolbar menu item */
"Hide Toolbar" = "Cacher la barre d'outils";

/* export filename host token
 export header host label */
"Host" = "Hôte";

/* description of double */
"IEEE 754 double-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "Valeur à virgule flottante IEEE 754. M est le nombre maximal de chiffres, dont D peut être après la virgule. Note: De nombreux nombres décimaux ne peuvent être approchés que par des valeurs à virgule flottante. Voir DECIMAL si vous avez besoin de résultats exacts.";

/* description of float */
"IEEE 754 single-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "La valeur à virgule flottante IEEE 754 mono-précision. M est le nombre maximal de chiffres, dont D peut être après la virgule. Note: De nombreux nombres décimaux ne peuvent être approchés que par des valeurs à virgule flottante. Voir DECIMAL si vous avez besoin de résultats exacts.";

/* ignore button */
"Ignore" = "Ignorer";

/* ignore errors button */
"Ignore All Errors" = "Ignorer toutes les erreurs";

/* ignore all fields menu item */
"Ignore all Fields" = "Ignorer tous les champs";

/* ignore field label */
"Ignore field" = "Ignorer le champ";

/* ignore field label */
"Ignore Field" = "Ignorer le champ";

/* import button */
"Import" = "Importation";

/* import all fields menu item */
"Import all Fields" = "Importer tous les champs";

/* sql import : charset error alert : continue button */
"Import Anyway" = "Importer quand même";

/* import cancelled message */
"Import cancelled!\n" = "Importation annulée!\n";

/* Import Error title */
"Import Error" = "Erreur d'importation";

/* import field operator tooltip */
"Import field" = "Champ d'import";

/* import file does not exist message */
"Import file does not exist." = "Le fichier d'importation n'existe pas.";

/* Export file format cannot be imported warning */
"Import of the selected data is currently not supported." = "L'importation des données sélectionnées n'est actuellement pas prise en charge.";

/* SQL import progress text */
"Imported %@ of %@" = "Importé %1$@ de %2$@";

/* CSV import progress text where total size is unknown */
"Imported %@ of CSV data" = "%@ de données CSV importées";

/* SQL import progress text where total size is unknown */
"Imported %@ of SQL" = "Importé %@ de SQL";

/* text showing that the application is importing CSV */
"Importing CSV" = "Importation de CSV";

/* text showing that the application is importing SQL */
"Importing SQL" = "Importation du SQL";

/* Bundle Editor : BLOB dropdown : 'include BLOB' item */
"include BLOB" = "inclure BLOB";

/* include content table column tooltip */
"Include content" = "Inclure le contenu";

/* sql import error message */
"Incompatible encoding in SQL file" = "Encodage incompatible dans le fichier SQL";

/* header for blank info pane */
"INFORMATION" = "INFORMATIONS";

/* New Table Sheet : Table Collation Dropdown : Default inherited from database
 New Table Sheet : Table Encoding Dropdown : Default inherited from database */
"Inherit from database (%@)" = "Hériter de la base de données (%@)";

/* initializing export label */
"Initializing..." = "Initialisation en cours...";

/* Bundle Editor : Scope dropdown : 'input field' item
 input field menu item label */
"Input Field" = "Champ de saisie";

/* input field  doesn't support insertion of snippets. */
"Input Field doesn't support insertion of snippets." = "Le champ d'entrée ne supporte pas l'insertion de snippets.";

/* input field is not editable. */
"Input Field is not editable." = "Le champ de saisie n'est pas modifiable.";

/* Bundle Editor : Outline View : 'Input Field' item : tooltip */
"Input Field Scope\ncommands will run on each text input field" = "Les commandes de champ de saisie\nseront exécutées sur chaque champ de saisie de texte";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as snippet' item */
"Insert as Snippet" = "Insérer en tant que snippet";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as text' item */
"Insert as Text" = "Insérer en tant que texte";

/* Bundle Editor : Outline View : 'BUNDLES' item : tooltip */
"Installed Bundles" = "Bundles installés";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog title */
"Installing Bundle" = "Installation du bundle";

/* insufficient details message */
"Insufficient connection details" = "Pas assez de détails de connexion";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please enter at least the hostname." = "Pas assez de détails fournis pour établir une connexion. Veuillez saisir au moins le nom d'hôte.";

/* insufficient SSH tunnel details informative message */
"Insufficient details provided to establish a connection. Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Informations insuffisantes fournies pour établir une connexion. Veuillez entrer le nom d'hôte du tunnel SSH, ou désactiver le tunnel SSH.";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please provide at least a host." = "Informations insuffisantes pour établir une connexion. Veuillez fournir au moins un hôte.";

/* Interpret data as: */
"Interpret data as:" = "Interpréter les données comme :";

/* Invalid database very short status message */
"Invalid database" = "Base de données invalide";

/* export : import settings : file error title */
"Invalid file supplied!" = "Fichier fourni invalide !";

/* table content : editing : error message title when parsing as hex string failed */
"Invalid hexadecimal value" = "Valeur hexadécimale invalide";

/* is deterministic label (Navigator) */
"Is Deterministic" = "Est déterministe";

/* is nullable label (Navigator) */
"Is Nullable" = "Est nullable";

/* is updatable: %@ */
"is updatable: %@" = "est modifiable : %@";

/* items */
"items" = "Eléments";

/* javascript exception */
"JavaScript Exception" = "Exception JavaScript";

/* javascript parsing error */
"JavaScript Parsing Error" = "Erreur d'analyse JavaScript";

/* key label (Navigator) */
"Key" = "Clés";

/* keyword label for color table (Prefs > Editor) */
"Keyword" = "Keyword";

/* kill button */
"Kill" = "Éliminer";

/* kill connection message */
"Kill connection?" = "Tuer la connexion?";

/* kill query message */
"Kill query?" = "Tuer la requête?";

/* Last Error Message */
"Last Error Message" = "Dernier message d'erreur";

/* Last Used entry in favorites menu */
"Last Used" = "Dernière utilisation";

/* range for json type */
"Limited to @@max_allowed_packet" = "Limité à @@max_allowed_packet";

/* Loading table task string */
"Loading %@..." = "Chargement de %@...";

/* Loading database task string */
"Loading database '%@'..." = "Chargement de la base de données '%@'...";

/* Loading history entry task desc */
"Loading history entry..." = "Chargement de la saisie de l'historique...";

/* Loading table page task string */
"Loading page %lu..." = "Chargement de la page %lu...";

/* Loading referece task string */
"Loading reference..." = "Chargement de la référence...";

/* Loading table data string */
"Loading table data..." = "Chargement des données du tableau...";

/* Low memory export summary */
"Low memory" = "Mémoire faible";

/* range of decimal */
"M (precision): Up to 65 digits\nD (scale): 0 to 30 digits" = "M (précision) : jusqu'à 65 chiffres\nD (échelle) : 0 à 30 chiffres";

/* range for blob type
 range for varbinary type */
"M: %@ to %@ bytes" = "M: %1$@ à %2$@ octets";

/* range for text type
 range for varchar type */
"M: %@ to %@ characters" = "M: Caractères %1$@ à %2$@";

/* range for longtext type */
"M: %@ to %@ characters (4 GiB)" = "M: %1$@ à %2$@ caractères (4 Go)";

/* range for binary type */
"M: 0 to 255 bytes" = "M: 0 à 255 octets";

/* range for char type */
"M: 0 to 255 characters" = "M: 0 à 255 caractères";

/* range for bit type */
"M: 1 (default) to 64" = "M: 1 (par défaut) à 64";

/* connection view : ssl : key file picker : wrong format error description */
"Make sure the file contains a RSA private key and is using PEM encoding." = "Assurez-vous que le fichier contient une clé privée RSA et utilise l'encodage PEM.";

/* connection view : ssl : client cert picker : wrong format error description */
"Make sure the file contains a X.509 client certificate and is using PEM encoding." = "Assurez-vous que le fichier contient un certificat client X.509 et qu'il utilise l'encodage PEM.";

/* match field menu item */
"Match Field" = "Champ de correspondance";

/* Shown when user inserts too many arguments (ContentFilterManager) */
"Maximum number of arguments is 2!" = "Le nombre maximum d'arguments est de 2!";

/* Maximum text length is set to %ld. */
"Maximum text length is set to %ld." = "La longueur maximale du texte est définie à %ld.";

/* Maximum text length is set to %ld. Inserted text was truncated. */
"Maximum text length is set to %ld. Inserted text was truncated." = "La longueur maximale du texte est définie à %ld. Le texte ajouté a été tronqué.";

/* Maximum text length is set to %llu. */
"Maximum text length is set to %llu." = "La longueur maximale du texte est définie à %llu.";

/* Maximum text length is set to %llu. Inserted text was truncated. */
"Maximum text length is set to %llu. Inserted text was truncated." = "La longueur maximale du texte est définie à %llu. Le texte ajouté a été tronqué.";

/* message column title */
"Message" = "Message";

/* mgtemplateengine error */
"MGTemplateEngine Error" = "Erreur MGTemplateEngine";

/* export filename date token */
"Month" = "Mois";

/* multiple selection */
"multiple selection" = "sélection multiple";

/* MySQL connecting very short status message */
"MySQL connecting..." = "Connexion à MySQL...";

/* mysql error message */
"MySQL Error" = "MySQL Error";

/* mysql help */
"MySQL Help" = "MySQL Help";

/* MySQL Help for Selection */
"MySQL Help for Selection" = "Aide MySQL pour la sélection";

/* MySQL Help for Word */
"MySQL Help for Word" = "Aide MySQL pour Word";

/* mysql help categories */
"MySQL Help – Categories" = "Aide MySQL – Catégories";

/* mysql said message */
"MySQL said:" = "MySQL a dit:";

/* shutdown server : error dialog : message */
"MySQL said:\n%@" = "MySQL a dit:\n%@";

/* message of panel when error while adding row to db */
"MySQL said:\n\n%@" = "MySQL a dit:\n\n%@";

/* Preferences : Themes : Initial filename for 'Export' */
"MyTheme" = "Mon thème";

/* network preference pane name */
"Network" = "Réseau";

/* network preference pane tooltip */
"Network Preferences" = "Préférences réseau";

/* file preference pane name */
"Files" = "Fichiers";

/* file preference pane tooltip */
"File Preferences" = "Préférences du fichier";

/* Bundle Editor : Default name for new bundle in the list on the left */
"New Bundle" = "Nouveau pack";

/* new column name placeholder string */
"New Column Name" = "Nom de la nouvelle colonne";

/* new favorite name */
"New Favorite" = "Nouveau favori";

/* Content Filter Manager : Initial name for new filter */
"New Filter" = "Nouveau filtre";

/* new folder placeholder name */
"New Folder" = "Nouveau dossier";

/* Bundle Editor : Default name for a new bundle in the menu */
"New Name" = "Nouveau nom";

/* new table menu item */
"New Table" = "Nouvelle table";

/* error that no color theme found */
"No color theme data found." = "Aucune donnée de thème de couleur trouvée.";

/* No compression export summary - within a sentence */
"no compression" = "pas de compression";

/* no connection available message */
"No connection available" = "Aucune connexion disponible";

/* no connection data found */
"No connection data found." = "Aucune donnée de connexion trouvée.";

/* No content filters were found in file to import (ContentFilterManager) */
"No content filters found." = "Aucun filtre de contenu trouvé.";

/* no data found */
"No data found." = "Aucune donnée trouvée.";

/* No errors title */
"No errors" = "Aucune erreur";

/* No favorites entry in favorites menu */
"No Favorties" = "Aucun favori";

/* All export files creation error title */
"No files could be created" = "Aucun fichier n'a pu être créé";

/* no item found message */
"No item found" = "Aucun élément trouvé";

/* SSH tunnel could not be created because no local port could be allocated */
"No local port could be allocated for the SSH Tunnel." = "Aucun port local n'a pu être alloué pour le tunnel SSH.";

/* header for no matches in filtered list */
"NO MATCHES" = "AUCUN MATCH";

/* no optimized field type found. message */
"No optimized field type found." = "Aucun type de champ optimisé trouvé.";

/* error that no query favorites found */
"No query favorites found." = "Aucun favori de requête trouvé.";

/* Mysql Help Viewer : Search : No results */
"No results found." = "Aucun résultat trouvé.";

/* Bundle Editor : Fallback Input source dropdown : 'none' item
 Bundle Editor : Scope=? : ? dropdown: 'None' item
 Bundle Editor : Scope=Data-Table : Input source dropdown: 'none' item
 Bundle Editor : Scope=Data-Table : Output dropdown : 'none' item
 Bundle Editor : Scope=Data-Table : Trigger dropdown : 'none' item
 Bundle Editor : Scope=Field : Input source dropdown: 'None' item
 Bundle Editor : Scope=Field : Output dropdown : 'none' item
 Bundle Editor : Scope=Field : Trigger dropdown : 'none' item
 Bundle Editor : Scope=General : Input source dropdown: 'None' item
 Bundle Editor : Scope=General : Output dropdown : 'none' item
 Bundle Editor : Scope=General : Trigger dropdown : 'none' item
 compression: none */
"None" = "Aucun";

/* not available label */
"Not available" = "Indisponible";

/* Argument count (ContentFilterManager) */
"Number of arguments: %lu" = "Nombre d'arguments : %lu";

/* numeric label for color table (Prefs > Editor) */
"Numeric" = "Numeric";

/* Bundle Editor : Copy-Command-Error : OK button
 Bundle Editor : Save-and-Close-Error : OK button
 Bundle Editor : Save-Bundle-Error : OK button
 Bundle Editor : Trash-Bundle(s)-Error : OK button
 OK
 OK button
 Open Files : Bundle : Already-Installed : Delete-Old-Error : OK button
 Open Files : Bundle : Install-Error : OK button
 Open Files : Bundle : UUID : OK button
 Preferences : Network : Custom SSH client : warning dialog : accept button */
"OK" = "Ok";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+1 rows instead of n selected were deleted. */
"One additional row was removed!" = "Une ligne supplémentaire a été supprimée!";

/* Table Content : Remove Row : Result : Too Few : Part 1 : Only n-1 of n selected rows were deleted. */
"One row was not removed." = "Une ligne n'a pas été supprimée.";

/* Only one dragged item allowed. */
"Only one dragged item allowed." = "Un seul élément déplacé est autorisé.";

/* partial copy database support message */
"Only Partially Supported" = "Partiellement pris en charge";

/* open function in new table title */
"Open Function in New Tab" = "Ouvrir la fonction dans un nouvel onglet";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Function in New Window" = "Ouvrir la fonction dans une nouvelle fenêtre";

/* open connection in new tab context menu item */
"Open in New Tab" = "Ouvrir dans un nouvel onglet";

/* menu item open in new window */
"Open in New Window" = "Ouvrir dans une nouvelle fenêtre";

/* open procedure in new table title */
"Open Procedure in New Tab" = "Ouvrir la procédure dans un nouvel onglet";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Procedure in New Window" = "Ouvrir la procédure dans une nouvelle fenêtre";

/* open table in new tab title
 open table in new table title */
"Open Table in New Tab" = "Ouvrir la table dans un nouvel onglet";

/* Table List : Context Menu : Duplicate connection to new window
 Table List : Gear Menu : Duplicate connection to new window */
"Open Table in New Window" = "Ouvrir la table dans une nouvelle fenêtre";

/* open view in new tab title
 open view in new table title */
"Open View in New Tab" = "Ouvrir la vue dans un nouvel onglet";

/* Tables List : Context Menu : Duplicate connection to new window
 Tables List : Gear Menu : Duplicate connection to new window */
"Open View in New Window" = "Ouvrir la vue dans une nouvelle fenêtre";

/* menu item open %@ in new window */
"Open %@ in New Window" = "Ouvrir %@ dans une nouvelle fenêtre";

/* OPTIMIZE one or more tables - result title */
"Optimize %@" = "Optimiser %@";

/* optimize selected items menu item */
"Optimize Selected Items" = "Optimiser les éléments sélectionnés";

/* optimize table menu item */
"Optimize Table" = "Optimiser la table";

/* optimize table failed message */
"Optimize table failed." = "L'optimisation de la table a échoué.";

/* Optimized type for field %@ */
"Optimized type for field '%@'" = "Type optimisé pour le champ '%@'";

/* optional placeholder string */
"optional" = "optionnel";

/* Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed. */
"Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed." = "Le paramètre passé n'a pas pu être interprété. Seule une chaîne ou un tableau (avec 2 éléments) sont autorisés.";

/* Permission Denied */
"Permission Denied" = "Autorisation refusée";

/* please choose a favorite connection view label */
"Please choose a favorite" = "Veuillez choisir un favori";

/* message of panel when ssh details are incomplete */
"Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Veuillez entrer le nom d'hôte du tunnel SSH, ou désactiver le tunnel SSH.";

/* Please enter the password */
"Please enter the password for ‘%@’:" = "Veuillez entrer le mot de passe pour ‘%@’:";

/* print button */
"Print" = "Imprimer";

/* print page menu item title */
"Print Page…" = "Imprimer la page…";

/* privileges label (Navigator) */
"Privileges" = "Privilèges";

/* procedure */
"procedure" = "Acte";

/* header for procedure info pane */
"PROCEDURE INFORMATION" = "INFORMATIONS DU PROCEDURE";

/* procedures */
"procedures" = "Actes";

/* proceed button */
"Proceed" = "Procéder";

/* header for procs & funcs list */
"PROCS & FUNCS" = "PROCS & FUNCS";

/* toolbar item label for switching to the Run Query tab */
"Query" = "Requête";

/* query background label for color table (Prefs > Editor) */
"Query Background" = "Fond de la requête";

/* Query cancelled error */
"Query cancelled." = "Requête annulée.";

/* Query cancel by resetting connection error */
"Query cancelled.  Please note that to cancel the query the connection had to be reset; transactions and connection variables were reset." = "Requête annulée. Veuillez noter que pour annuler la requête, la connexion a dû être réinitialisée; les transactions et les variables de connexion ont été réinitialisées.";

/* query editor preference pane name */
"Query Editor" = "Éditeur de requête";

/* query editor preference pane tooltip */
"Query Editor Preferences" = "Préférences de l'éditeur de requête";

/* query logging currently disabled label
 query logging disabled label */
"Query logging is currently disabled" = "L'enregistrement des requêtes est actuellement désactivé";

/* query result print heading */
"Query Result" = "Résultat de la requête";

/* export source */
"Query results" = "Résultats de la requête";

/* Query Status */
"Query Status" = "Statut de la requête";

/* table status : row count query failed : error title */
"Querying row count failed" = "La requête du nombre de lignes a échoué";

/* Quick connect item label */
"Quick Connect" = "Connexion Rapide";

/* quote label for color table (Prefs > Editor) */
"Quote" = "Devis";

/* databsse rename unsupported informative message */
"Renaming the database '%@' is currently unsupported as it contains objects other than tables (i.e. views, procedures, functions, etc.).\n\nIf you would like to rename a database please use the 'Duplicate Database', move any non-table objects manually then drop the old database." = "Le renommage de la base de données '%@' n'est actuellement pas pris en charge car elle contient des objets autres que les tables (ex: vues, procédures, fonctions, etc.).\n\nSi vous souhaitez renommer une base de données, veuillez utiliser la 'Dupliquer la base de données', déplacer tous les objets qui ne sont pas des tables manuellement, puis supprimer l'ancienne base de données.";

/* range for serial type */
"Range: %@ to %@" = "Plage : %1$@ à %2$@";

/* range for time type */
"Range: -838:59:59.0 to 838:59:59.0\nF (precision): 0 (1s) to 6 (1µs)" = "Intervalle : -838:59:59.0 à 838:59:59.0\nF (précision) : 0 (1s) à 6 (1μs)";

/* range for year type */
"Range: 0000, 1901 to 2155" = "Plage : 0000, 1901 à 2155";

/* range for set type */
"Range: 1 to 64 members\n1, 2, 3, 4 or 8 bytes storage" = "Plage de stockage : 1 à 64 membres\n1, 2, 3, 4 ou 8 octets";

/* range for datetime type */
"Range: 1000-01-01 00:00:00.0 to 9999-12-31 23:59:59.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Portée : 1000-01-01 00:00:00.0 à 9999-12-31 23:59:59.999999\nF (précision): 0 (1s) à 6 (1μs)";

/* range for date type */
"Range: 1000-01-01 to 9999-12-31" = "Plage : 1000-01-01 à 9999-12-31";

/* range for timestamp type */
"Range: 1970-01-01 00:00:01.0 to 2038-01-19 03:14:07.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Portée : 1970-01-01 00:00:01.0 à 2038-01-19 03:14:07.9999\nF (précision) : 0 (1s) à 6 (1μs)";

/* text showing that app is reading dump */
"Reading..." = "Lecture...";

/* menu item to refresh databases */
"Refresh Databases" = "Rafraîchir les bases de données";

/* refresh list menu item */
"Refresh List" = "Rafraîchir la liste";

/* toolbar item label for switching to the Table Relations tab */
"Relations" = "Relations avec le client";

/* Relations tab subtitle showing table name */
"Relations for table: %@" = "Relations pour la table: %@";

/* reload bundles menu item label */
"Reload Bundles" = "Recharger les packs";

/* Reloading data task description */
"Reloading data..." = "Rechargement des données...";

/* Reloading table task string */
"Reloading..." = "Reloading...";

/* remote error */
"Remote Error" = "Erreur distante";

/* Bundle Editor : Remove-Bundle: remove button
 remove button */
"Remove" = "Retirer";

/* remove all button */
"Remove All" = "Retirer tout";

/* remove all query favorites message */
"Remove all query favorites?" = "Supprimer tous les favoris de la requête ?";

/* Bundle Editor : Remove-Bundle: remove dialog title */
"Remove selected Bundle?" = "Supprimer le lot sélectionné ?";

/* remove selected content filters message */
"Remove selected content filters?" = "Supprimer les filtres de contenu sélectionnés ?";

/* remove selected query favorites message */
"Remove selected query favorites?" = "Supprimer les favoris de la requête sélectionnée ?";

/* removing field task status message */
"Removing field..." = "Suppression du champ...";

/* removing index task status message */
"Removing index..." = "Suppression de l'index...";

/* rename database message */
"Rename database '%@' to:" = "Renommer la base de données '%@' en :";

/* rename func menu title */
"Rename Function..." = "Renommer la fonction...";

/* rename proc menu title */
"Rename Procedure..." = "Renommer la procédure...";

/* rename table menu title */
"Rename Table..." = "Renommer la table...";

/* rename view menu title */
"Rename View..." = "Renommer la vue...";

/* REPAIR one or more tables - result title */
"Repair %@" = "Réparer %@";

/* repair selected items menu item */
"Repair Selected Items" = "Réparer les éléments sélectionnés";

/* repair table menu item */
"Repair Table" = "Table de réparation";

/* repair table failed message */
"Repair table failed." = "La réparation de la table a échoué.";

/* Replace button */
"Replace" = "Remplacer";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace entire content' item */
"Replace Entire Content" = "Remplacer tout le contenu";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace selection' item */
"Replace Selection" = "Remplacer la sélection";

/* description of year */
"Represents a 4 digit year value, stored as 1 byte. Invalid values are converted to 0000 and two digit values 0 to 69 will be converted to years 2000 to 2069, resp. values 70 to 99 to years 1970 to 1999.\nThe YEAR(2) type was removed in MySQL 5.7.5." = "Représente une valeur d'année de 4 chiffres, stockée en 1 octet. Les valeurs invalides sont converties en 0000 et les valeurs de deux chiffres de 0 à 69 seront converties en années 2000 à 2069, resp. valeurs 70 à 99 années 1970 à 1999.\nLe type YEAR(2) a été supprimé dans MySQL 5.7.5.";

/* description of multilinestring */
"Represents a collection of LineStrings." = "Représente une collection de LineStrings.";

/* description of geometrycollection */
"Represents a collection of objects of any other single- or multi-valued spatial type. The only restriction being, that all objects must share a common coordinate system." = "Représente une collection d'objets de tout autre type spatial à valeur unique ou à valeur multiples. La seule restriction est que tous les objets doivent partager un système commun de coordonnées.";

/* description of multipolygon */
"Represents a collection of Polygons. The Polygons making up the MultiPolygon must not intersect." = "Représente une collection de Polygons. Les polygones qui composent le multipolygone ne doivent pas se croiser.";

/* description of multipoint */
"Represents a set of Points without specifying any kind of relation and/or order between them." = "Représente un ensemble de Points sans spécifier de relation et/ou d'ordre entre eux.";

/* description of point */
"Represents a single location in coordinate space using X and Y coordinates. The point is zero-dimensional." = "Représente un seul emplacement dans l'espace de coordonnées en utilisant les coordonnées X et Y. Le point est de dimension zéro.";

/* description of linestring */
"Represents an ordered set of coordinates where each consecutive pair of two points is connected by a straight line." = "Représente un ensemble ordonné de coordonnées où chaque paire consécutive de deux points est connectée par une ligne droite.";

/* required placeholder string */
"required" = "Obligatoire";

/* description of smallint */
"Requires 2 bytes storage space. M is the optional display width and does not affect the possible value range." = "Nécessite 2 octets d'espace de stockage. M est la largeur d'affichage optionnelle et n'affecte pas la plage de valeurs possible.";

/* description of mediumint */
"Requires 3 bytes storage space. M is the optional display width and does not affect the possible value range." = "Nécessite 3 octets d'espace de stockage. M est la largeur d'affichage optionnelle et n'affecte pas la plage de valeurs possible.";

/* description of int */
"Requires 4 bytes storage space. M is the optional display width and does not affect the possible value range. INTEGER is an alias to this type." = "Nécessite 4 octets d'espace de stockage. M est la largeur d'affichage optionnelle et n'affecte pas la plage de valeurs possible. INTEGER est un alias de ce type.";

/* description of bigint */
"Requires 8 bytes storage space. M is the optional display width and does not affect the possible value range. Note: Arithmetic operations might fail for large numbers." = "Nécessite 8 octets d'espace de stockage. M est la largeur d'affichage optionnelle et n'affecte pas la plage de valeurs possible. Note : Les opérations Arithmetic peuvent échouer pour de grands nombres.";

/* reset auto_increment after deletion of all rows message */
"Reset AUTO_INCREMENT after deletion\n(only for Delete ALL ROWS IN TABLE)?" = "Réinitialiser AUTO_INCREMENT après la suppression\n(uniquement pour supprimer TOUTES LES ROUILLES DANS LA TABLE) ?";

/* delete selected row button */
"Delete Selected Row" = "Supprimer la ligne sélectionnée";

/* delete selected rows button */
"Delete Selected Rows" = "Supprimer les lignes sélectionnées";

/* delete all rows in table button */
"Delete ALL ROWS IN TABLE" = "Supprimer toutes les ROWS dans la TABLE";

/* Restoring session task description */
"Restoring session..." = "Restauration de la session...";

/* return type label (Navigator) */
"Return Type" = "Type de retour";

/* return type: %@ */
"return type: %@" = "type de retour : %@";

/* singular word for row */
"row" = "ligne";

/* plural word for rows */
"rows" = "lignes";

/* text showing how many rows are in the limited filter match */
"Rows %@ - %@ from filtered matches" = "Lignes %1$@ - %2$@ des correspondances filtrées";

/* text showing how many rows are in the limited result */
"Rows %@ - %@ of %@%@ from table" = "Lignes %1$@ - %2$@ de %3$@%4$@ depuis la table";

/* Table Info Section : number of rows (exact value) */
"rows: %@" = "lignes : %@";

/* Table Info Section : number of rows (estimated value) */
"rows: ~%@" = "lignes: ~%@";

/* run all button */
"Run All" = "Exécuter tout";

/* Run All menu item title */
"Run All Queries" = "Exécuter toutes les requêtes";

/* Title of button to run current query in custom query view */
"Run Current" = "Exécuter le courant";

/* Title of action menu item to run current query in custom query view */
"Run Current Query" = "Exécuter la requête actuelle";

/* toolbar item label for switching to the Run Query tab */
"Run Custom Query" = "Exécuter une requête personnalisée";

/* Title of button to run query just before text caret in custom query view */
"Run Previous" = "Exécuter Précédent";

/* Title of action menu item to run query just before text caret in custom query view */
"Run Previous Query" = "Exécuter la requête précédente";

/* Title of action menu item to run selected text in custom query view */
"Run Selected Text" = "Exécuter le texte sélectionné";

/* Title of button to run selected text in custom query view */
"Run Selection" = "Exécuter la sélection";

/* Running multiple queries string */
"Running query %i of %lu..." = "Exécution de la requête %1$i de %2$lu...";

/* Running multiple queries string */
"Running query %ld of %lu..." = "Exécution de la requête %1$ld de %2$lu...";

/* Running single query string */
"Running query..." = "Requête en cours...";

/* Save trigger button label */
"Save" = "Enregistrer";

/* Save All to Favorites */
"Save All to Favorites" = "Tout enregistrer dans les favoris";

/* save as button title */
"Save As..." = "Enregistrer sous...";

/* Bundle Editor : BLOB dropdown : 'save BLOB as dat file' item */
"save BLOB as dat file" = "enregistrer BLOB en tant que fichier dat";

/* Bundle Editor : BLOB dropdown : 'save BLOB as image file' item */
"save BLOB as image file" = "enregistrer BLOB en tant que fichier image";

/* Save Current Query to Favorites */
"Save Current Query to Favorites" = "Enregistrer la requête actuelle dans les favoris";

/* save page as menu item title */
"Save Page As…" = "Enregistrer la page comme…";

/* Save Queries… */
"Save Queries…" = "Enregistrer les requêtes…";

/* Save Query… */
"Save Query…" = "Enregistrer la requête…";

/* Save Selection to Favorites */
"Save Selection to Favorites" = "Enregistrer la sélection dans les favoris";

/* save view as button title */
"Save View As..." = "Enregistrer la vue sous...";

/* schema path header for completion tooltip */
"Schema path:" = "Chemin du schéma :";

/* Search in MySQL Documentation */
"Search in MySQL Documentation" = "Rechercher dans la documentation MySQL";

/* Search in MySQL Help */
"Search in MySQL Help" = "Rechercher dans l'aide MySQL";

/* Select Active Query */
"Select Active Query" = "Sélectionner une requête active";

/* toolbar item for selecting a db */
"Select Database" = "Sélectionner la base de données";

/* selected items */
"selected items" = "éléments sélectionnés";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as comma-separated' item */
"Selected Rows (CSV)" = "Lignes sélectionnées (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as SQL' item */
"Selected Rows (SQL)" = "Lignes sélectionnées (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as tab-separated' item */
"Selected Rows (TSV)" = "Lignes sélectionnées (TSV)";

/* Bundle Editor : Scope=Field : Input source dropdown: 'selected text' item */
"Selected Text" = "Texte sélectionné";

/* selection label for color table (Prefs > Editor) */
"Selection" = "Sélection";

/* table structure : indexes : delete index : no columns error : description */
"Sequel Ace could not find any columns belonging to this index. Maybe it has been removed already?" = "La suite Ace n'a pas pu trouver de colonne appartenant à cet index. Peut-être a-t-elle déjà été supprimée ?";

/* Preferences : Network : Custom SSH client : warning dialog message */
"Sequel Ace only supports and is tested with the default OpenSSH client versions included with macOS. Using different clients might cause connection issues, security risks or not work at all.\n\nPlease be aware, that we cannot provide support for such configurations." = "Sequel Ace ne prend en charge que et est testé avec les versions par défaut du client OpenSSH incluses avec macOS. L'utilisation de différents clients peut causer des problèmes de connexion, des risques de sécurité ou ne pas fonctionner du tout.\n\nVeuillez être conscient que nous ne pouvons pas fournir de support pour de telles configurations.";

/* sequelace URL scheme command not supported. */
"sequelace URL scheme command not supported." = "commande de schéma d'URL de suite non prise en charge.";

/* sequelace url Scheme Error */
"sequelace URL Scheme Error" = "Erreur de schéma d'URL de la suite";

/* Table Structure : Collation dropdown : 'item is the same as the collation of server' marker
 Table Structure : Encoding dropdown : 'item is server default' marker */
"Server" = "Serveur";

/* Add Database : Charset dropdown : default item ($1 = charset name)
 Add Database : Collation dropdown : default item ($1 = collation name) */
"Server Default (%@)" = "Serveur par défaut (%@)";

/* server processes window title (var = hostname) */
"Server Processes on %@" = "Processus serveur sur %@";

/* Initial filename for 'Save session' file */
"Session" = "Séance";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html' item */
"Show as HTML" = "Afficher en HTML";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html tooltip' item */
"Show as HTML Tooltip" = "Afficher sous forme d'infobulle HTML";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as text tooltip' item */
"Show as Text Tooltip" = "Afficher comme info-bulle de texte";

/* show console */
"Show Console" = "Afficher la console";

/* show create func syntax menu item */
"Show Create Function Syntax..." = "Afficher la syntaxe de la fonction de création...";

/* show create proc syntax menu item */
"Show Create Procedure Syntax..." = "Afficher la syntaxe de création de la procédure...";

/* show create syntaxes menu item */
"Show Create Syntaxes..." = "Afficher la création de syntaxes...";

/* show create table syntax menu item */
"Show Create Table Syntax..." = "Afficher la création de syntaxe de tableau...";

/* show create view syntax menu item */
"Show Create View Syntax..." = "Afficher la syntaxe de création de vue...";

/* Show detail button */
"Show Detail" = "Afficher les détails";

/* MySQL Help Viewer : Results list : Link tooltip */
"Show MySQL help for %@" = "Afficher l'aide MySQL pour %@";

/* show navigator */
"Show Navigator" = "Show Navigator";

/* show tab bar */
"Show Tab Bar" = "Afficher la barre d'onglets";

/* tooltip for toolbar item for show console */
"Show the console which shows all MySQL commands performed by Sequel Ace" = "Afficher la console qui affiche toutes les commandes MySQL exécutées par Séquel Ace";

/* Show Toolbar menu item */
"Show Toolbar" = "Afficher la barre d'outils";

/* filtered item count */
"Showing %lu of %lu processes" = "Montrer %1$lu processus sur %2$lu";

/* shutdown server : confirmation dialog : shutdown button */
"Shutdown" = "Arrêt";

/* shutdown server : error dialog : title */
"Shutdown failed!" = "L'arrêt a échoué !";

/* range of integer types */
"Signed: %@ to %@\nUnsigned: %@ to %@" = "Signed: %1$@ à %2$@\nNon signée : %3$@ à %4$@";

/* Table Info Section : table size on disk */
"size: %@" = "taille : %@";

/* skip existing button */
"Skip existing" = "Ignorer les éléments existants";

/* skip problems button */
"Skip problems" = "Ignorer les problèmes";

/* beta build label */
"Beta Build" = "Version bêta";

/* socket connection failed title */
"Socket connection failed!" = "Échec de la connexion à la socket !";

/* socket not found title */
"Socket not found!" = "Socket introuvable !";

/* Some export folders not writable explanatory text */
"Some of the target export folders are not writable.  Please select a new export location and try again." = "Certains des dossiers d'exportation cibles ne sont pas modifiables. Veuillez sélectionner un nouvel emplacement d'exportation et réessayez.";

/* Some export folders missing explanatory text */
"Some of the target export folders no longer exist.  Please select a new export location and try again." = "Certains des dossiers d'exportation cible n'existent plus. Veuillez sélectionner un nouvel emplacement d'exportation et réessayer.";

/* Sorting table task description */
"Sorting table..." = "Tri de la table...";

/* spatial index menu item title */
"SPATIAL" = "SPATIAL";

/* sql data access label (Navigator) */
"SQL Data Access" = "Accès aux données SQL";


/* Connection Secured via SSL information text */
"Connection Secured via SSL" = "Connexion sécurisée par SSL";

/* SSH connected titlebar marker */
"SSH Connected" = "SSH connecté";

/* SSH connecting very short status message */
"SSH connecting..." = "Connexion SSH...";

/* SSH connecting titlebar marker */
"SSH Connecting…" = "Connexion SSH en cours…";

/* SSH connection failed title */
"SSH connection failed!" = "Échec de la connexion SSH!";

/* SSH disconnected titlebar marker */
"SSH Disconnected" = "SSH déconnecté";

/* SSH key check error */
"SSH Key not found" = "Clé SSH introuvable";

/* title when ssh tunnel port forwarding failed */
"SSH port forwarding failed" = "La redirection du port SSH a échoué";

/* SSL certificate authority file check error */
"SSL Certificate Authority File not found" = "Fichier d'autorité de certification SSL introuvable";

/* SSL certificate file check error */
"SSL Certificate File not found" = "Fichier de certificat SSL introuvable";

/* SSL requested but not used title */
"SSL connection not established" = "Connexion SSL non établie";

/* SSL key file check error */
"SSL Key File not found" = "Fichier de clé SSL introuvable";

/* Standard memory export summary */
"Standard memory" = "Mémoire standard";

/* started */
"started" = "démarré";

/* status file for sequelace url scheme command couldn't be written error message */
"Status file for sequelace url scheme command couldn't be written!" = "Le fichier de statut pour la commande de schéma d'url de la suite n'a pas pu être écrit !";

/* stop button */
"Stop" = "Arrêter";

/* Stop queries string */
"Stop queries" = "Arrêter les requêtes";

/* Stop query string */
"Stop query" = "Arrêter la requête";

/* description of timestamp */
"Stores a date and time of day as seconds since the beginning of the UNIX epoch (1970-01-01 00:00:00). The values displayed/stored are affected by the connection's @@time_zone setting.\nThe representation is the same as for DATETIME. Invalid values, as well as \"second zero\", are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F. Some additional rules may apply." = "Enregistre une date et une heure de la journée en tant que secondes depuis le début de l'époque UNIX (1970-01-01 00:00:00). Les valeurs affichées/stockées sont affectées par le paramètre @@time_zone de la connexion.\nLa représentation est la même que pour DATETIME. Les valeurs invalides, ainsi que le \"second zéro\", sont converties en 0000-00-00 00:00:00.0. Les secondes fractionnelles ont été ajoutées dans MySQL 5. .4 avec une précision jusqu'à des microsecondes (6), spécifiée par F. Certaines règles supplémentaires peuvent s'appliquer.";

/* description of datetime */
"Stores a date and time of day. The representation is YYYY-MM-DD HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Enregistre une date et une heure de la journée. La représentation est AAAA-MM-JJ HH:MM:SS[. *], je suis en secondes fractionnées. La valeur n'est pas affectée par aucun réglage de fuseau horaire. Les valeurs invalides sont converties en 0000-00-00 00:00:00.0. Les secondes fractionnelles ont été ajoutées en MySQL 5.6.4 avec une précision jusqu'à microsecondes (6), spécifiées par F.";

/* description of date */
"Stores a date without time information. The representation is YYYY-MM-DD. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00." = "Stocke une date sans information d'heure. La représentation est AAAA-MM-JJ. La valeur n'est affectée par aucun paramètre de fuseau horaire. Les valeurs non valides sont converties en 0000-00-00.";

/* description of time */
"Stores a time of day, duration or time interval. The representation is HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 00:00:00. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Stocke une heure de jour, de durée ou d'intervalle. La représentation est HH:MM:SS[. *], je suis en secondes fractionnées. La valeur n'est pas affectée par aucun réglage de fuseau horaire. Les valeurs invalides sont converties en 00:00:00. Les secondes fractionnées ont été ajoutées en MySQL 5.6.4 avec une précision jusqu'à microsecondes (6), spécifiée par F.";

/* toolbar item label for switching to the Table Structure tab */
"Structure" = "Structure";

/* successfully analyzed all selected items message */
"Successfully analyzed all selected items." = "Analyse réussie de tous les éléments sélectionnés.";

/* analyze table successfully passed message */
"Successfully analyzed table." = "Tableau analysé avec succès.";

/* successfully flushed all selected items message */
"Successfully flushed all selected items." = "Tous les éléments sélectionnés ont été vidés avec succès.";

/* message of panel when successfully flushed privs */
"Successfully flushed privileges." = "Les privilèges ont été effacés avec succès.";

/* flush table successfully passed message */
"Successfully flushed table." = "La table a été vidée avec succès.";

/* successfully optimized all selected items message */
"Successfully optimized all selected items." = "Optimisation réussie de tous les éléments sélectionnés.";

/* optimize table successfully passed message */
"Successfully optimized table." = "Table optimisée avec succès.";

/* successfully repaired all selected items message */
"Successfully repaired all selected items." = "Tous les éléments sélectionnés ont été réparés avec succès.";

/* repair table successfully passed message */
"Successfully repaired table." = "Table réparée avec succès.";

/* tooltip for toolbar item for switching to the Run Query tab */
"Switch to the Run Query tab" = "Basculer vers l’onglet Exécuter la requête";

/* tooltip for toolbar item for switching to the Table Content tab */
"Switch to the Table Content tab" = "Basculer vers l'onglet Contenu de la table";

/* tooltip for toolbar item for switching to the Table Info tab */
"Switch to the Table Info tab" = "Basculer vers l'onglet Info Tableau";

/* tooltip for toolbar item for switching to the Table Relations tab */
"Switch to the Table Relations tab" = "Basculer vers l'onglet Relations de la table";

/* tooltip for toolbar item for switching to the Table Structure tab */
"Switch to the Table Structure tab" = "Basculer vers l'onglet Structure de la table";

/* tooltip for toolbar item for switching to the Table Triggers tab */
"Switch to the Table Triggers tab" = "Basculer vers l'onglet Déclencheurs de la table";

/* tooltip for toolbar item for switching to the User Manager tab */
"Switch to the User Manager tab" = "Basculer vers l'onglet Gestionnaire d'utilisateurs";

/* description for table syntax copied notification */
"Syntax for %@ table copied" = "Syntaxe pour la table %@ copiée";

/* table */
"table" = "table";

/* csv export table heading
 table
 Table Structure : Collation dropdown : 'item is the same as the collation of table' marker
 Table Structure : Encoding dropdown : 'item is table default' marker */
"Table" = "Tableau";

/* export label showing that the app is fetching data for a specific table */
"Table %lu of %lu (%@): Fetching data..." = "Table %1$lu de %2$lu (%3$@) : récupération des données...";

/* export label showing app is fetching relations data for a specific table */
"Table %lu of %lu (%@): Fetching relations data..." = "Tableau %1$lu de %2$lu (%3$@): Récupération des données de relations...";

/* export label showing app if writing data for a specific table */
"Table %lu of %lu (%@): Writing data..." = "Table %1$lu de %2$lu (%3$@) : Écriture des données...";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'table changed' item */
"Table changed" = "Table modifiée";

/* table checksum message */
"Table checksum" = "Somme de contrôle de la table";

/* table checksum: %@ */
"Table checksum: %@" = "Somme de contrôle du tableau : %@";

/* table content print heading */
"Table Content" = "Contenu de la table";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as comma-separated' item */
"Table Content (CSV)" = "Contenu de la table (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as SQL' item */
"Table Content (SQL)" = "Contenu de la table (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as tab-separated' item */
"Table Content (TSV)" = "Contenu de la table (TSV)";

/* toolbar item for navigation history */
"Table History" = "Historique du tableau";

/* toolbar item label for switching to the Table Info tab */
"Table Info" = "Informations sur la table";

/* header for table info pane */
"TABLE INFORMATION" = "INFORMATIONS TABLE";

/* table information print heading */
"Table Information" = "Informations sur le tableau";

/* message of panel when no name is given for table */
"Table must have a name." = "La table doit avoir un nom.";

/* general preference pane tooltip */
"Table Preferences" = "Préférences du tableau";

/* toolbar item label for switching to the Table Relations tab */
"Table Relations" = "Relations avec la table";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table row changed' item */
"Table Row changed" = "Ligne de table modifiée";

/* table structure print heading */
"Table Structure" = "Structure de table";

/* toolbar item label for switching to the Table Triggers tab */
"Table Triggers" = "Déclencheurs de table";

/* Add Relation sheet title, showing table name */
"Table: %@" = "Tableau : %@";

/* tables preference pane name */
"Tables" = "Tables";

/* tables */
"tables" = "tables";

/* header for table list */
"TABLES" = "TABLES";

/* header for table & views list */
"TABLES & VIEWS" = "TABLES & Vues";

/* Connection test very short status message */
"Testing connection..." = "Test de la connexion...";

/* MySQL connection test very short status message */
"Testing MySQL..." = "Testing MySQL...";

/* SSH testing very short status message */
"Testing SSH..." = "Test de SSH...";

/* text label for color table (Prefs > Editor) */
"Text" = "Texte du texte";

/* Text is too long. Maximum text length is set to %llu. */
"Text is too long. Maximum text length is set to %llu." = "Le texte est trop long. La longueur maximale du texte est définie à %llu.";

/* Release notes dialog title thanking user for upgrade */
"Thanks for updating Sequel Ace!" = "Merci d'avoir mis à jour Sequel Ace!";

/* Open Files : Bundle : Install-Error : Destination path already exists error dialog message */
"The Bundle ‘%@’ already exists." = "Le bundle ‘%@’ existe déjà.";

/* Open Files : Bundle: UUID : UUID-Attribute is missing in bundle's command.plist file */
"The Bundle ‘%@’ has no UUID which is necessary to identify installed Bundles." = "Le bundle ‘%@’ n’a pas d’UUID nécessaire pour identifier les paquets installés.";

"‘%@’ Bundle contains legacy components" = "Le bundle ‘%@’ contient des composants hérités";

"In these files:\n\n%@\n\nDo you still want to install the bundle?" = "Dans ces fichiers :\n\n%@\n\nVoulez-vous toujours installer le bundle?";

/* message while reading a spf file which matches non-supported formats. */
"The chosen file %@ contains ‘%@’ data." = "Le fichier choisi «%1$@» contient les données «%2$@».";

/* the color theme ‘%@’ already exists. */
"The color theme ‘%@’ already exists." = "Le thème de couleur ‘%@’ existe déjà.";

/* the connection is busy. please wait and try again tooltip */
"The connection is busy. Please wait and try again." = "La connexion est occupée. Veuillez patienter et réessayer.";

/* the connection of the active connection window is not identical tooltip */
"The connection of the active connection window is not identical." = "La connexion de la fenêtre de connexion active n'est pas identique.";

/* Connection lost during import error message */
"The connection to the server was lost during the import.  The import is only partially complete." = "La connexion au serveur a été perdue pendant l'importation. L'importation n'est que partiellement terminée.";

/* Create syntax permission denied detail */
"The creation syntax could not be retrieved due to a permissions error.\n\nPlease check your user permissions with an administrator." = "La syntaxe de création n'a pas pu être récupérée en raison d'une erreur de permission.\n\nVeuillez vérifier vos permissions utilisateur avec un administrateur.";

/* CSV file open error */
"The CSV file you selected could not be found or read." = "Le fichier CSV que vous avez sélectionné n'a pas pu être trouvé ou lu.";

/* Error when CSV appears to have too many columns to import, probably due to line ending mismatch */
"The CSV was read as containing more than 512 columns, more than the maximum columns permitted for speed reasons by Sequel Ace.\n\nThis usually happens due to errors reading the CSV; please double-check the CSV to be imported and the line endings and escape characters at the bottom of the CSV selection dialog." = "Le CSV a été lu comme contenant plus de 512 colonnes, plus que le maximum autorisé pour des raisons de vitesse par Sequel Ace.\n\nCela se produit généralement à cause d'erreurs de lecture du CSV ; veuillez vérifier le CSV à importer et les extrémités de ligne et les caractères d'échappement en bas de la boîte de dialogue de sélection CSV.";

/* unsaved theme informative message */
"The current color theme is unsaved. Do you want to proceed without saving it?" = "Le thème de couleur actuel n'est pas enregistré. Voulez-vous continuer sans l'enregistrer ?";

/* Short important release note for swap of custom query buttons */
"The Custom Query \"Run\" and \"Run All\" button positions and their shortcuts have been swapped." = "Les positions des boutons « Exécuter des requêtes personnalisées » et « Exécuter tous » et leurs raccourcis ont été échangés.";

/* the following default bundles were updated:\n%@\nyour modifications were stored as “(user)”. */
"The following default Bundles were updated:\n%@\nYour modifications were stored as “(user)”." = "Les Bundles par défaut suivants ont été mis à jour :\n%@\nVos modifications ont été enregistrées en tant que « (utilisateur)».";

/* favorites export error informative message */
"The following error occurred during the export process:\n\n%@" = "L'erreur suivante s'est produite lors du processus d'exportation :\n\n%@";

/* favorites import error informative message */
"The following error occurred during the import process:\n\n%@" = "L'erreur suivante s'est produite pendant le processus d'import :\n\n%@";

/* table structure : indexes : delete index : error 1553 : description */
"The foreign key relationship '%@' has a dependency on index '%@'. This relationship must be removed before the index can be deleted.\n\nAre you sure you want to continue to delete the relationship and the index? This action cannot be undone." = "La relation de clé étrangère '%1$@' a une dépendance sur l'index '%2$@'. Cette relation doit être supprimée avant de pouvoir supprimer l'index.\n\nÊtes-vous sûr de vouloir supprimer la relation et l'index ? Cette action ne peut pas être annulée.";

/* table list change alert message */
"The list of tables has changed" = "La liste des tables a changé";

/* message when trying to rename a table/view/proc/etc to an already used name */
"The name '%@' is already used." = "Le nom '%@' est déjà utilisé.";

/* table list change alert informative message */
"The number of tables in this database has changed since the export dialog was opened. There are now %lu additional table(s), most likely added by an external application.\n\nHow would you like to proceed?" = "Le nombre de tables dans cette base de données a changé depuis l'ouverture de la boîte de dialogue d'exportation. Il y a maintenant %lu table(s) supplémentaire(s), probablement ajoutée(s) par une application externe.\n\nComment souhaitez-vous procéder ?";

/* message of panel when no rows have been affected after writing to the db */
"The row was not written to the MySQL database. You probably haven't changed anything.\nReload the table to be sure that the row exists and use a primary key for your table.\n(This error can be turned off in the preferences.)" = "La ligne n'a pas été écrite dans la base de données MySQL. Vous n'avez probablement rien changé.\nRecharger la table pour être sûr que la ligne existe et utiliser une clé primaire pour votre table.\n(Cette erreur peut être désactivée dans les préférences.)";

/* export : import settings : file version error description ($1 = is version, $2 = list of supported versions); note: the u00A0 is a non-breaking space, do not add more whitespace. */
"The selected export settings were stored with version %1$ld, but only settings with the following versions can be imported: %2$@.\n\nEither save the settings in a backwards compatible way or update your version of Sequel Ace." = "Les paramètres d'exportation sélectionnés ont été enregistrés avec la version%1$ld, mais seuls les paramètres avec les versions suivantes peuvent être importés : %2$@.\n\nSoit enregistrer les paramètres de manière rétrocompatible ou mettre à jour votre version de Sequel Ace.";

/* export : import settings : spf content type error description */
"The selected file contains data of type “%1$@”, but type “%2$@” is needed. Please choose a different file." = "Le fichier sélectionné contient des données de type «%1$@», mais le type «%2$@» est nécessaire. Veuillez choisir un autre fichier.";

/* export : import settings : file error description */
"The selected file is either not a valid SPF file or severely corrupted." = "Le fichier sélectionné n'est pas un fichier SPF valide ou est gravement corrompu.";

/* error deleting relation informative message */
"The selected relation couldn't be deleted.\n\nMySQL said: %@" = "La relation sélectionnée n'a pas pu être supprimée.\n\nMySQL a dit: %@";

/* error deleting trigger informative message */
"The selected trigger couldn't be deleted.\n\nMySQL said: %@" = "Le déclencheur sélectionné n'a pas pu être supprimé.\n\nMySQL a dit: %@";

/* description of tinyint */
"The smallest integer type, requires 1 byte storage space. M is the optional display width and does not affect the possible value range." = "Le type d'entier le plus petit requiert un espace de stockage de 1 octet. M est la largeur d'affichage optionnelle et n'affecte pas la plage de valeurs possible.";

/* message of panel when connection to socket failed because optional socket could not be found */
"The socket file could not be found in any common location. Please supply the correct socket location.\n\nMySQL said: %@" = "Le fichier socket n'a pas pu être trouvé à un endroit commun. Veuillez fournir le bon emplacement de socket.\n\nMySQL a dit: %@";

/* error creating relation informative message */
"The specified relation could not be created.\n\nMySQL said: %@" = "La relation spécifiée n'a pas pu être créée.\n\nMySQL a dit: %@";

/* error creating trigger informative message */
"The specified trigger was unable to be created.\n\nMySQL said: %@" = "Le déclencheur spécifié n'a pas pu être créé.\n\nMySQL a dit: %@";

/* sql import : charset error alert : detail message */
"The SQL file uses utf8mb4 encoding, but your MySQL version only supports the limited utf8 subset.\n\nYou can continue the import, but any non-BMP characters in the SQL file (eg. some typographic and scientific special characters, archaic CJK logograms, emojis) will be unrecoverably lost!" = "Le fichier SQL utilise l'encodage utf8mb4, mais votre version MySQL ne supporte que le sous-ensemble limité utf8.\n\nVous pouvez continuer l'importation, mais tous les caractères non-BMP dans le fichier SQL (par ex. quelques caractères spéciaux typographiques et scientifiques, des logos archaïques CJK, des emojis) seront perdus de façon irrécupérable!";

/* SQL file open error */
"The SQL file you selected could not be found or read." = "Le fichier SQL que vous avez sélectionné n'a pas pu être trouvé ou lu.";

/* Prompt for SSH password when keychain fetch failed */
"The SSH password could not be loaded from the keychain; please enter the SSH password for %@:" = "Le mot de passe SSH n'a pas pu être chargé depuis le trousseau; veuillez entrer le mot de passe SSH pour %@:";

/* Prompt for SSH password when direct fetch failed */
"The SSH password could not be loaded; please enter the SSH password for %@:" = "Le mot de passe SSH n'a pas pu être chargé; veuillez entrer le mot de passe SSH pour %@:";

/* SSH tunnel authentication failed message */
"The SSH Tunnel could not authenticate with the remote host. Please check your password and ensure you still have access." = "Le tunnel SSH n'a pas pu s'authentifier avec l'hôte distant. Veuillez vérifier votre mot de passe et vous assurer que vous avez toujours accès.";

/* SSH tunnel unexpectedly closed */
"The SSH Tunnel has unexpectedly closed." = "Le tunnel SSH a été fermé de manière inattendue.";

/* SSH tunnel was closed by remote host message */
"The SSH Tunnel was closed 'by the remote host'. This may indicate a networking issue or a network timeout." = "Le tunnel SSH a été fermé 'par l'hôte distant'. Cela peut indiquer un problème de réseau ou un délai d'attente réseau.";

/* SSH tunnel forwarding port connection refused message */
"The SSH Tunnel was established successfully, but could not forward data to the remote port as the remote port refused the connection." = "Le tunnel SSH a été établi avec succès, mais n'a pas pu transférer les données vers le port distant car le port distant a refusé la connexion.";

/* SSH tunnel unable to bind to local port message */
"The SSH Tunnel was unable to bind to the local port. This error may occur if you already have an SSH connection to the same server and are using a 'LocalForward' setting in your SSH configuration.\n\nWould you like to fall back to a standard connection to localhost in order to use the existing tunnel?" = "Le tunnel SSH n'a pas pu être lié au port local. Cette erreur peut se produire si vous avez déjà une connexion SSH vers le même serveur et que vous utilisez un paramètre 'LocalForward' dans votre configuration SSH.\n\nVoulez-vous revenir à une connexion standard à localhost afin d'utiliser le tunnel existant ?";

/* SSH tunnel failed or timed out message */
"The SSH Tunnel was unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds)." = "Le tunnel SSH n'a pas pu se connecter à l'hôte %1$@, ou la requête a expiré.\n\nAssurez-vous que l'adresse est correcte et que vous avez les privilèges nécessaires, ou essayez d'augmenter le délai de connexion (actuellement %2$ld secondes).";

/* message of panel when loading of table failed and presumably due to used filter argument */
"The table data couldn't be loaded presumably due to used filter clause. \n\nMySQL said: %@" = "Les données de la table n'ont pas pu être chargées probablement en raison de la clause de filtre utilisée. \n\nMySQL a dit: %@";

/* message of panel when loading of table failed */
"The table data couldn't be loaded.\n\nMySQL said: %@" = "Les données de la table n'ont pas pu être chargées.\n\nMySQL a dit: %@";

/* Export folder not writable explanatory text */
"The target export folder is not writable.  Please select a new export location and try again." = "Le dossier d'exportation cible n'est pas accessible en écriture. Veuillez sélectionner un nouvel emplacement d'exportation et réessayez.";

/* Export folder not chosen by user explanatory text */
"No directory selected.  Please select a new export location and try again." = "Aucun répertoire sélectionné. Veuillez sélectionner un nouvel emplacement d'exportation et réessayer.";
"No directory selected." = "Aucun répertoire sélectionné.";
"Please select a new export location and try again." = "Veuillez sélectionner un nouvel emplacement d'exportation et réessayer.";

/* Export folder missing explanatory text */
"The target export folder no longer exists.  Please select a new export location and try again." = "Le dossier d'exportation cible n'existe plus. Veuillez sélectionner un nouvel emplacement d'exportation et réessayer.";

/* theme name label */
"Theme Name:" = "Nom du thème :";

/* themes installation error */
"Themes Installation Error" = "Erreur d'installation des thèmes";

/* message of panel when table content cannot be copied */
"There have been errors while copying table content. Please check the new table." = "Il y a eu des erreurs lors de la copie du contenu de la table. Veuillez vérifier la nouvelle table.";

/* Cannot duplicate a table with triggers to a different database */
"Cannot duplicate a table with triggers to a different database." = "Impossible de dupliquer une table avec des triggers dans une base de données différente.";

/* text shown when query was successfull */
"There were no errors." = "Il n'y a pas d'erreurs.";

/* delete field and foreign key informative message */
"This field is part of a foreign key relationship with the table '%@'. This relationship must be removed before the field can be deleted.\n\nAre you sure you want to continue to delete the relationship and the field? This action cannot be undone." = "Ce champ fait partie d'une relation de clé étrangère avec la table '%@'. Cette relation doit être supprimée avant que le champ ne puisse être supprimé.\n\nÊtes-vous sûr de vouloir supprimer la relation et le champ ? Cette action ne peut pas être annulée.";

/* table structure : indexes : delete index : error 1553, no FK found : description */
"This index cannot be deleted, because it is used by an existing foreign key relationship.\n\nPlease remove the relationship, before trying to remove this index.\n\nMySQL said: %@" = "Cet index ne peut pas être supprimé, car il est utilisé par une relation de clé étrangère existante.\n\nVeuillez supprimer la relation, avant d'essayer de supprimer cet index.\n\nMySQL a dit: %@";

/* description of serial */
"This is an alias for BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE." = "Ceci est un alias pour BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE.";

/* description of dec
 description of fixed
 description of numeric */
"This is an alias for DECIMAL." = "Ceci est un alias pour DECIMAL.";

/* description of double real */
"This is an alias for DOUBLE, unless REAL_AS_FLOAT is configured." = "C'est un alias pour DOUBLE, sauf si REAL_AS_FLOAT est configuré.";

/* description of double precision */
"This is an alias for DOUBLE." = "Ceci est un alias pour DOUBLE.";

/* description of bool
 description of boolean */
"This is an alias for TINYINT(1)." = "Ceci est un alias pour TINYINT(1).";

/* Table Structure : Collation dropdown : database marker tooltip */
"This is the default collation of database %@." = "C'est le collationnement par défaut de la base de données %@.";

/* Table Structure : Collation dropdown : default marker tooltip */
"This is the default collation of encoding %@." = "C'est le collationnement par défaut de l'encodage %@.";

/* Table Structure : Collation dropdown : table marker tooltip */
"This is the default collation of table %@." = "C'est le collationnement par défaut de la table %@.";

/* Table Structure : Collation dropdown : server marker tooltip */
"This is the default collation of this server." = "C'est le collationnement par défaut de ce serveur.";

/* Table Structure : Encoding dropdown : database marker tooltip */
"This is the default encoding of database %@." = "C'est l'encodage par défaut de la base de données %@.";

/* Table Structure : Encoding dropdown : table marker tooltip */
"This is the default encoding of table %@." = "C'est l'encodage par défaut de la table %@.";

/* Table Structure : Encoding dropdown : server marker tooltip */
"This is the default encoding of this server." = "C'est l'encodage par défaut de ce serveur.";

/* This table currently does not support relations. Only tables that use the InnoDB storage engine support them. */
"This table currently does not support relations. Only tables that use the InnoDB storage engine support them." = "Cette table ne supporte pas actuellement les relations. Seules les tables qui utilisent le moteur de stockage InnoDB le supportent.";

/* user has no hosts informative message */
"This user doesn't have any hosts associated with it. It will be deleted unless one is added" = "Cet utilisateur n'a aucun hôte associé. Il sera supprimé à moins qu'il ne soit ajouté";

/* error removing host informative message */
"This user doesn't seem to have any associated hosts and will be removed unless a host is added." = "Cet utilisateur ne semble pas avoir d'hôtes associés et sera supprimé à moins qu'un hôte ne soit ajouté.";

/* shutdown server : confirmation dialog : message */
"This will wait for open transactions to complete and then quit the mysql daemon. Afterwards neither you nor anyone else can connect to this database!\n\nFull management access to the server's operating system is required to restart MySQL!" = "Cela attendra que les transactions ouvertes se terminent, puis quittera le démon mysql. Ensuite, ni vous ni personne d'autre ne pouvez vous connecter à cette base de données !\n\nUn accès complet à la gestion du système d'exploitation du serveur est nécessaire pour redémarrer MySQL!";

/* export filename time token */
"Time" = "Date et heure";

/* toolbar item label for switching to the Table Triggers tab */
"Triggers" = "Déclencheurs";

/* triggers for table label */
"Triggers for table: %@" = "Déclencheurs pour la table : %@";

/* truncate button */
"Truncate" = "Tronquer";

/* truncate tables message */
"Truncate selected tables?" = "Tronquer les tables sélectionnées ?";

/* truncate table menu title */
"Truncate Table..." = "Tronquer la table...";

/* truncate table message */
"Truncate table '%@'?" = "Tronquer la table '%@ ' ?";

/* truncate tables menu item */
"Truncate Tables" = "Tables tronqués";

/* type label (Navigator) */
"Type" = "Type de texte";

/* type declaration header */
"Type Declaration:" = "Déclaration de type :";

/* add index error message */
"Unable to add index" = "Impossible d'ajouter l'index";

/* unable to analyze selected items message */
"Unable to analyze selected items" = "Impossible d'analyser les éléments sélectionnés";

/* unable to analyze table message */
"Unable to analyze table" = "Impossible d'analyser la table";

/* unable to check selected items message */
"Unable to check selected items" = "Impossible de vérifier les éléments sélectionnés";

/* unable to check table message */
"Unable to check table" = "Impossible de vérifier la table";

/* message of panel when connection to host failed due to access denied error */
"Unable to connect to host %@ because access was denied.\n\nDouble-check your username and password and ensure that access from your current location is permitted.\n\nMySQL said: %@" = "Impossible de se connecter à l'hôte %1$@ car l'accès a été refusé.\n\nVérifiez votre nom d'utilisateur et votre mot de passe et assurez-vous que l'accès à partir de votre emplacement actuel est autorisé.\n\nMySQL a dit: %2$@";

/* message of panel when SSH port forwarding failed */
"Unable to connect to host %@ because the port connection via SSH was refused.\n\nPlease ensure that your MySQL host is set up to allow TCP/IP connections (no --skip-networking) and is configured to allow connections from the host you are tunnelling via.\n\nYou may also want to check the port is correct and that you have the necessary privileges.\n\nChecking the error detail will show the SSH debug log which may provide more details.\n\nMySQL said: %@" = "Impossible de se connecter à l'hôte %1$@ car la connexion via SSH a été refusée.\n\nVeuillez vous assurer que votre hôte MySQL est configuré pour autoriser les connexions TCP/IP (sans --skip-networking) et est configuré pour autoriser les connexions depuis l'hôte que vous utilisez via le tunnel.\n\nVous pouvez également vérifier que le port est correct et que vous avez les privilèges nécessaires.\n\nVérifier le détail de l'erreur affichera le journal de débogage SSH qui peut fournir plus de détails.\n\nMySQL a dit: %2$@";

/* message of panel when connection to host failed */
"Unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds).\n\nMySQL said: %@" = "Impossible de se connecter à l'hôte %1$@, ou la requête a expiré.\n\nAssurez-vous que l'adresse est correcte et que vous avez les privilèges nécessaires, ou essayez d'augmenter le délai de connexion (actuellement %2$ld secondes).\n\nMySQL a dit: %3$@";

/* message of panel when connection to host failed */
"Unable to connect via the socket, or the request timed out.\n\nDouble-check that the socket path is correct and that you have the necessary privileges, and that the server is running.\n\nMySQL said: %@" = "Impossible de se connecter via le socket, ou la requête a expiré.\n\nVérifiez que le chemin du socket est correct et que vous avez les privilèges nécessaires et que le serveur fonctionne.\n\nMySQL a dit: %@";

/* unable to copy database message */
"Unable to copy database" = "Impossible de copier la base de données";

/* error deleting index message */
"Unable to delete index" = "Impossible de supprimer l'index";

/* error deleting relation message */
"Unable to delete relation" = "Impossible de supprimer la relation";

/* error deleting trigger message */
"Unable to delete trigger" = "Impossible de supprimer le déclencheur";

/* unable to flush selected items message */
"Unable to flush selected items" = "Impossible de vider les éléments sélectionnés";

/* unable to flush table message */
"Unable to flush table" = "Impossible de vider la table";

/* unable to get list of users message */
"Unable to get list of users" = "Impossible d'obtenir la liste des utilisateurs";

/* error killing connection message */
"Unable to kill connection" = "Impossible de tuer la connexion";

/* error killing query message */
"Unable to kill query" = "Impossible de tuer la requête";

/* unable to optimze selected items message */
"Unable to optimze selected items" = "Impossible d'optimiser les éléments sélectionnés";

/* unable to optimze table message */
"Unable to optimze table" = "Impossible d'optimiser la table";

/* unable to perform the checksum */
"Unable to perform the checksum" = "Impossible d'effectuer la somme de contrôle";

/* error removing host message */
"Unable to remove host" = "Impossible de supprimer l'hôte";

/* unable to rename database message */
"Unable to rename database" = "Impossible de renommer la base de données";

/* unable to repair selected items message */
"Unable to repair selected items" = "Impossible de réparer les éléments sélectionnés";

/* unable to repair table message */
"Unable to repair table" = "Impossible de réparer la table";

/* message of panel when connection to db failed after selecting from popupbutton */
"Unable to select database %@.\nPlease check you have the necessary privileges to view the database, and that the database still exists." = "Impossible de sélectionner la base de données %@.\nVeuillez vérifier que vous avez les privilèges nécessaires pour afficher la base de données, et que la base de données existe toujours.";

/* Unable to write row error */
"Unable to write row" = "Impossible d'écrire la ligne";

/* Table Content : Remove Row : Result : n Error title */
"Unexpected number of rows removed!" = "Nombre de lignes inattendu supprimé !";

/* warning */
"Unknown file format" = "Format de fichier inconnu";

/* unsaved changes message */
"Unsaved changes" = "Modifications non enregistrées";

/* unsaved theme message */
"Unsaved Theme" = "Thème non enregistré";

/* Preferences : Network : Custom SSH client : warning dialog title */
"Unsupported configuration!" = "Configuration non prise en charge !";

/* export : import settings : file version error title */
"Unsupported version for export settings!" = "Version non prise en charge pour les paramètres d'export !";

/* Name for an untitled connection */
"Untitled" = "Sans titre";

/* Title of a new Sequel Ace Document */
"Untitled %ld" = "Sans titre %ld";

/* range for mediumblob type */
"Up to %@ bytes (16 MiB)" = "Jusqu'à %@ octets (16 MiB)";

/* range for longblob type */
"Up to %@ bytes (4 GiB)" = "Jusqu'à %@ bytes (4 GiB)";

/* range for mediumtext type */
"Up to %@ characters (16 MiB)" = "Jusqu\"à %@ caractères (16 Mo)";

/* range for enum type */
"Up to %@ distinct members (<%@ in practice)\n1-2 bytes storage" = "Jusqu'à %1$@ membres distincts (<%2$@ en pratique)\n1-2 octets de stockage";

/* range for tinyblob type */
"Up to 255 bytes" = "Jusqu'à 255 octets";

/* range for tinytext type */
"Up to 255 characters" = "Jusqu'à 255 caractères";

/* Open Files : Bundle : Already-Installed : Update button */
"Update" = "Mise à jour";

/* updated: %@ */
"updated: %@" = "mis à jour : %@";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field of table `%2$@` was changed." = "La mise à jour du contenu du champ a échoué. Impossible d'identifier l'origine du champ sans ambiguïté (%1$ld correspondances). Il est très probable que lors de la modification de ce champ de la table `%2$@`, celui-ci ait été modifié.";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field the table `%2$@` was changed by an other user." = "La mise à jour du contenu du champ a échoué. Impossible d'identifier l'origine du champ sans ambiguïté (%1$ld correspondances). Il est très probable que lors de la modification de ce champ, la table `%2$@` ait été modifiée par un autre utilisateur.";

/* updating field task description */
"Updating field data..." = "Mise à jour des données du champ...";

/* URL scheme command couldn't authenticated */
"URL scheme command couldn't authenticated" = "La commande du schéma d'URL n'a pas pu être authentifiée";

/* URL scheme command was terminated by user */
"URL scheme command was terminated by user" = "La commande du schéma d'URL a été terminée par l'utilisateur";

/* URL scheme command %@ unsupported */
"URL scheme command %@ unsupported" = "Commande de schéma d'URL %@ non prise en charge";

/* Use 127.0.0.1 button */
"Use 127.0.0.1" = "Utiliser 127.0.0.1";

/* use standard connection button */
"Use Standard Connection" = "Utiliser la connexion standard";

/* user has no hosts message */
"User has no hosts" = "L'utilisateur n'a pas d'hôtes";

/* user-defined value */
"User-defined value" = "Valeur définie par l'utilisateur";

/* toolbar item label for switching to the User Manager tab */
"Users" = "Utilisateurs";

/* CSV Field Mapping : Table View : Tooltip for fields with NULL value */
"Value will be imported as MySQL NULL" = "La valeur sera importée en tant que NULL MySQL";

/* variable label for color table (Prefs > Editor) */
"Variable" = "Variable";

/* version */
"version" = "version";

/* export header version label */
"Version" = "Version";

/* view */
"view" = "Voir";

/* Release notes button title */
"View full release notes" = "Voir toutes les notes de version";

/* header for view info pane */
"VIEW INFORMATION" = "AFFICHER LES INFORMATIONS";

/* view html source code menu item title */
"View Source" = "Voir la source";

/* view structure print heading */
"View Structure" = "Voir la structure";

/* views */
"views" = "Vues";

/* warning */
"Warning" = "Avertissement";

/* Short important release note for why password prompts may occur */
"We've changed Sequel Ace's digital signature for GateKeeper compatibility; you'll have to allow access to your passwords again." = "Nous avons changé la signature numérique de Sequel Ace pour la compatibilité GateKeeper; vous devrez autoriser l'accès à vos mots de passe à nouveau.";

/* Important release notes informational text, single change */
"We've made a few changes but we thought you should know about one particularly important one:" = "Nous avons fait quelques modifications, mais nous pensions que vous devriez en savoir un particulièrement important :";

/* Important release notes informational text, multiple changes */
"We've made a few changes but we thought you should know about some particularly important ones:" = "Nous avons fait quelques modifications, mais nous pensions que vous devriez en savoir plus sur certains d'entre eux particulièrement importants :";

/* WHERE clause not valid */
"WHERE clause not valid" = "Clause WHERE non valide";

/* Title of filter preview area when the query WHERE is negated */
"WHERE NOT query" = "Où, PAS de requête";

/* Title of filter preview area when the query WHERE is normal */
"WHERE query" = "Requête WHERE";

/* Generic working description */
"Working..." = "Traitement...";

/* export label showing app is writing data */
"Writing data..." = "Écriture des données...";

/* text showing that app is writing text file */
"Writing..." = "Écriture en cours...";

/* wrong data format or password */
"Wrong data format or password." = "Format de données ou mot de passe incorrect.";

/* wrong data format */
"Wrong data format." = "Mauvais format de données.";

/* export : import settings : spf content type error title */
"Wrong SPF content type!" = "Mauvais type de contenu SPF !";

/* export filename date token */
"Year" = "Année";

/* message of panel when trying to copy multiple rows */
"You can only copy single rows." = "Vous ne pouvez copier que des lignes simples.";

/* message of panel when trying to edit tables without index and with hidden blob/text fields */
"You can't hide blob and text fields when working with tables without index." = "Vous ne pouvez pas masquer les champs blob et texte lorsque vous travaillez avec des tables sans index.";

/* You cannot delete the last field in a table. Delete the table instead. */
"You cannot delete the last field in a table. Delete the table instead." = "Vous ne pouvez pas supprimer le dernier champ d'une table. Supprimez la table à la place.";

/* SSL connection requested but not established error detail */
"You requested that the connection should be established using SSL, but MySQL made the connection without SSL.\n\nThis may be because the server does not support SSL connections, or has SSL disabled; or insufficient details were supplied to establish an SSL connection.\n\nThis connection is not encrypted." = "Vous avez demandé que la connexion soit établie en utilisant SSL, mais MySQL a fait la connexion sans SSL.\n\nCela peut être dû au fait que le serveur ne prend pas en charge les connexions SSL, ou a SSL désactivé ; ou des détails insuffisants ont été fournis pour établir une connexion SSL.\n\nCette connexion n'est pas chiffrée.";

/* Query Favorites : List : Section Heading : current connection document : tooltip (arg is the name of the spf file) */
"‘%@’ based favorites" = "Favoris basés sur «%@»";

/* table column header. Read: 'Showing all content filters for fields of type %@' (ContentFilterManager) */
"‘%@’ Fields Content Filters" = "Filtres de contenu des champs ‘%@’";

/* Export file already exists message */
"%@ already exists. Do you want to replace it?" = "%@ existent déjà. Voulez-vous les remplacer ?";

/* Bundle Editor : Outline View : Bundle item : tooltip */
"%@ Bundle" = "Lot %@";

/* Export file creation error title */
"%@ could not be created" = "%@ n'a pas pu être créé";

/* %@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item. */
"%@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item." = "%@ n'a pas pu être analysé. Vous pouvez modifier la configuration de la colonne, mais la colonne ne sera pas affichée dans la vue Contenu ; Veuillez signaler ce problème à l'équipe de Sequel Ace à l'aide du menu Aide.";

/* connection view : ssl : client cert file picker : wrong format error title */
"%@ is not a valid client certificate file." = "%@ n'est pas un fichier de certificat client valide.";

/* connection view : ssl : key file picker : wrong format error title */
"%@ is not a valid private key file." = "%@ n'est pas un fichier de clé privée valide.";

/* text shown when there are App Sandbox Issues */
"App Sandbox Issue" = "Problème de l'application Sandbox";

/* Stale Bookmarks error title */
"Stale Bookmarks" = "Marque-pages de l'histoire";

/* App Sandbox info link text */
"App Sandbox Info" = "Infos de l'application Sandbox";

/* error while selecting file title */
"File Selection Error" = "Erreur de sélection de fichier";

/* error while selecting file message */
"The selected file is not a valid file.\n\nPlease try again.\n\nClass: %@" = "Le fichier sélectionné n'est pas un fichier valide.\n\nVeuillez réessayer.\n\nClasse : %@";

/* known hosts not writable error message */
"The selected known hosts file is not writable.\n\n%@\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "Le fichier hosts sélectionné n'est pas accessible en écriture.\n\n%@\n\nVeuillez re-sélectionner le fichier dans les préférences de Sequel Ace et réessayer.";

/* known hosts is invalid message */
"The selected known hosts file is invalid.\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "Le fichier hôte sélectionné est invalide.\n\nVeuillez re-sélectionner le fichier dans les préférences de Sequel Ace et réessayer.";

/* known hosts contains quote message */
"The selected known hosts file contains a quote (\") in its file path which is not supported.\n\n%@\n\nPlease select a different file in Sequel Ace's Preferences or rename the file/path to remove the quote." = "Le fichier hôte connu sélectionné contient un guillemet (\") dans son chemin de fichier qui n'est pas pris en charge.\n\n%@\n\nVeuillez sélectionner un fichier différent dans les Préférences de Sequel Ace ou renommez le fichier/chemin pour supprimer le guillemet.";

/* SSH Tunnel Debugging menu and window title */
"SSH Tunnel Debugging Info" = "Infos de débogage du tunnel SSH";

/* Question to user to see if they would like to re-request access */
"You have stale secure bookmarks:\n\n%@\n\nWould you like to re-request access now?" = "Vous avez dépassé les signets sécurisés :\n\n%@\n\nSouhaitez-vous demander un nouvel accès maintenant ?";

/* Question to user to see if they would like to request access to missing bookmarks */
"You have missing secure bookmarks:\n\n%@\n\nWould you like to request access now?" = "Il vous manque des signets sécurisés :\n\n%@\n\nVoulez-vous demander l'accès maintenant ?";

/* known hosts advance menu item title */
"Use known hosts from ssh config (ADVANCED)" = "Utiliser les hôtes connus de la configuration ssh (AVANCED)";

/* The answer, yes */
"Yes" = "Oui";

/* Shown to remind users of their stale bookmarks */
"A reminder of your stale secure bookmarks:<br /><br />%@<br />" = "Un rappel de vos favoris sécurisés obsolètes :<br /><br />%@<br />";

/* Title for Stale Secure Bookmarks help window */
"Stale Secure Bookmarks" = "Signets Stale Secure";

/* Title for Export Error alert */
"Export Error" = "Erreur d'exportation";

/* Message for Export Error alert */
"Error while writing to the export file. Could not open file: %@" = "Erreur lors de l'écriture dans le fichier d'exportation. Impossible d'ouvrir le fichier : %@";

/* Message for User window error */
"Resultset from mysql.user contains neither 'Password' nor 'authentication_string' column." = "Resultset de mysql.user ne contient ni la colonne 'Mot de passe' ni 'authentication_string'.";

/* Title for User window error */
"User Data Error" = "Erreur de données utilisateur";

/* Title for Stale Bookmark file selection dialog */
"Please re-select the file '%@' in order to restore Sequel Ace's access." = "Veuillez sélectionner à nouveau le fichier '%@' afin de restaurer l'accès à Sequel Ace.";

/* Title for File Preference file selection dialog */
"Please choose a file or folder to grant Sequel Ace access to." = "Veuillez choisir un fichier ou un dossier pour accorder l'accès à Sequel Ace.";

/* Title for Network Preference ssh config file selection dialog */
"Please choose your ssh config files(s)" = "Veuillez choisir le(s) fichier(s) de configuration ssh";

/* Title for Network Preference ssh known hosts file selection dialog */
"Please choose your known hosts file" = "Veuillez choisir votre fichier hosts connu";

/* Message for field editor JSON segment when JSON is invalid */
"Invalid JSON" = "JSON invalide";

/* Applying syntax highlighting task description */
"Applying syntax highlighting..." = "Application de la coloration syntaxique...";

/* Error message when a bash task fails to start */
"Couldn't launch task.\nException reason: %@\n ENV length: %lu" = "Impossible de démarrer la tâche.\nRaison d'exception : %@\n Longueur ENV : %lu";

/* Error message title when we failed to create new connection view */
"New Connection Error" = "Nouvelle erreur de connexion";

/* New Connection Error informative message */
"Failed to create new database connection window. Please restart Sequel Ace and try again." = "Impossible de créer une nouvelle fenêtre de connexion à la base de données. Veuillez redémarrer Ace et réessayer.";

/* new version is available alert title */
"A new version is available" = "Une nouvelle version est disponible";

/* new version is available download button title */
"Download" = "Télécharger";

/* new version is available alert informativeText */
"Version %@ is available. You are currently running %@" = "La version %@ est disponible. Vous utilisez actuellement %@";

/* downloading new version window title */
"Download Progress" = "Progression du téléchargement";

/* downloading new version time remaining placeholder text */
"Calculating time remaining..." = "Calcul du temps restant...";

/* downloading new version window message */
"Downloading Sequel Ace - %@" = "Téléchargement de Sequel Ace - %@";

/* downloading new version time remaining estimate text */
"About %.1f seconds left" = "Il reste environ %.1f secondes";

/* downloading new version failure alert title */
"Download Failed" = "Échec du téléchargement";

/* Tool tip for the Show alert when update is available preference on the Notifications pane */
"Only available for GitHub downloads" = "Uniquement disponible pour les téléchargements GitHub";

/* Tool tip warning for when the user sets auto-complete delay to zero */
"WARNING: Setting the auto-complete delay to 0.0 can result in strange output." = "AVERTISSEMENT : Définir le délai de saisie automatique à 0.0 peut entraîner une sortie étrange.";

/* downloading new version bytes of bytes done */
"%@ of %@" = "%@ de %@";

/* Time zone set failed informative message */
"\n\ntime_zone will be set to SYSTEM." = "\n\ntime_zone sera défini sur SYSTEM.";

/* Menu item title for checking for updates */
"Check for Updates..." = "Vérifier les mises à jour...";

/* Warning message during connection in case the variable skip-show-database is set to ON */
"The skip-show-database variable of the database server is set to ON. Thus, you won't be able to list databases unless you have the SHOW DATABASES privilege.\n\nHowever, the databases are still accessible directly through SQL queries depending on your privileges." = "La variable skip-show-database du serveur de base de données est définie à ON. Ainsi, vous ne pourrez pas lister les bases de données si vous n'avez pas le privilège SHOW DATABASES.\n\nCependant, les bases de données sont toujours accessibles directement par les requêtes SQL en fonction de vos privilèges.";

/* Error alert title when the request to GitHub fails */
"GitHub Request Failed" = "La requête GitHub a échoué";

/* Info alert title when there are Newer Releases Available */
"No Newer Release Available" = "Aucune nouvelle version disponible";

/* Info alert message when there are Newer Releases Available */
"You are currently running the latest release." = "Vous utilisez actuellement la dernière version.";

/* Alert button, disable the warning when skip-show-database is set to on */
"Never show this again" = "Ne plus afficher ceci";

/* Info alert when editing a generated column */
"The current field \"%@\" is a generated column and therefore cannot be edited." = "Le champ actuel \"%@\" est une colonne générée et ne peut donc pas être modifié.";

/* Release note / info modal about change in latest release about column behavior */
"The use of the \"default\" column has changed since the last version of Sequel ACE:\n\n- No default value : Leave it blank.\n- String value : Use single '' or double quotes \"\" if you want an empty string or to wrap a string\n- Expression : Use parentheses (). Except for the TIMESTAMP and DATETIME columns where you can specify the CURRENT_TIMESTAMP function without enclosing parentheses." = "L'utilisation de la colonne \"par défaut\" a changé depuis la dernière version de l'ACE de Sequel :\n\n- Aucune valeur par défaut : Laisser vide.\n- Valeur de chaîne : Utiliser des guillemets simples '' ou doubles \"\" si vous voulez une chaîne vide ou pour envelopper une chaîne\n- Expression : Utiliser des parenthèses (). Sauf pour les colonnes TIMESTAMP et DATETIME où vous pouvez spécifier la fonction CURRENT_TIMESTAMP sans enfermer les parenthèses.";

/* Menu option to pin a view to the top of the list of tables */
"Pin View" = "Épingler l'affichage";

/* Menu option to pin a table to the top of the list of tables */
"Pin Table" = "Épingler la table";

/* Menu option to pin a procedure to the top of the list of tables */
"Pin Procedure" = "Procédure d'épingle";

/* Menu option to pin a function to the top of the list of tables */
"Pin Function" = "Fonction Pin";

/* Menu option to unpin a view which is currently pinned at the top of the list of tables */
"Unpin View" = "Désépingler l'affichage";

/* Menu option to unpin a table which is currently pinned at the top of the list of tables */
"Unpin Table" = "Désépingler la table";

/* Menu option to unpin a procedure which is currently pinned at the top of the list of tables */
"Unpin Procedure" = "Désépingler la procédure";

/* Menu option to unpin a function which is currently pinned at the top of the list of tables */
"Unpin Function" = "Désépingler la fonction";

/* header for pinned table list */
"PINNED" = "PINNED";

/* Menu option to copy a table's name with database name in the list of tables */
"Copy Table Name" = "Copier le nom du tableau";

/* sequelace URL scheme error: LaunchFavorite alert title (favorite name not found) */
"LaunchFavorite URL Scheme Error" = "Erreur de schéma d'URL de LaunchFavorite";

/* sequelace URL scheme error: LaunchFavorite alert description (favorite name not found) */
"The variable in the ?name= query parameter could not be matched with any of your favorites." = "La variable dans le paramètre de requête ?name= n'a pas pu être associée à aucun de vos favoris.";
