/* Table Content : Remove Row : Result : Too Few : Part 3 : Row not deleted when using primary key for DELETE statement. */
" Check the Console for possible errors inside the primary key(s) of this table!" = " Проверьте консоль на наличие возможных ошибок основного ключа(ей) этой таблицы!";

/* Table Content : Remove Row : Result : Too Many : Part 2 : Generic text */
" Please check the Console and inform the Sequel Ace team!" = " Пожалуйста, передайте необходимую информацию из консоли команде Sequel Ace!";

/* Table Content : Remove Row : Result : Too Few : Part 2 : Generic help message */
" Reload the table to be sure that the contents have not changed in the meantime." = " Перезагрузите таблицу, чтобы убедиться, что содержимое не изменилось за это время.";

/* Table Content : Remove Row : Result : Too Few : Part 3 : no primary key in table generic message */
" You should also add a primary key to this table!" = " Вы также должны добавить первичный ключ к этой таблице!";

/* Query snippet shell command syntax and placeholder */
"$(shell_command)" = "$(shell_command)";

/* text showing how many rows are selected */
"%@ %@ selected" = "Выбрано %1$@ %2$@";

/* History item filtered by values label */
"%@ (Filtered by %@)" = "%1$@ (отфильтровано по %2$@)";

/* History item with page number label */
"%@ (Page %lu)" = "%1$@ (страница %2$lu)";

/* Content Filter Manager : Initial name of copied filter
 Initial favourite name after duplicating a previous favourite */
"%@ Copy" = "%@ — копия";

/* text showing a single row a partially loaded result */
"%@ row in partial load" = "%@ строка с частичной нагрузкой";

/* text showing a single row in the result */
"%@ row in table" = "%@ строка в таблице";

/* text showing how a single rows matched filter */
"%@ row of %@%@ matches filter" = "%1$@ из %2$@%3$@ отфильтрованных строк";

/* text showing how many rows are in a partially loaded result */
"%@ rows in partial load" = "%@ строк с частичной загрузкой";

/* text showing how many rows are in the result */
"%@ rows in table" = "%@ строк в таблице";

/* text showing how many rows matched filter */
"%@ rows of %@%@ match filter" = "%1$@ из %2$@%3$@ отфильтрованных строк";

/* text showing how many rows have been affected by a single query */
"%@; %ld rows affected" = "%1$@; %2$ld строк затронуто";

/* text showing how many rows have been affected by multiple queries */
"%@; %ld rows affected in total, by %ld queries taking %@" = "%1$@; при выполнении %2$ld запросов(а) была затронуто строк: %3$ld, что заняло %4$@";

/* text showing one row has been affected by a single query */
"%@; 1 row affected" = "%@; 1 строка изменена";

/* text showing one row has been affected by multiple queries */
"%@; 1 row affected in total, by %ld queries taking %@" = "%1$@; при выполнении %2$ld запросов(а) была затронута 1 строка, что заняло %3$@";

/* text showing a query was cancelled */
"%@; Cancelled after %@" = "%1$@; Отменено после %2$@";

/* text showing multiple queries were cancelled */
"%@; Cancelled in query %ld, after %@" = "%1$@; Отменено на запросе %2$ld, после %3$@";

/* Error display text, showing original MySQL error */
"%@\n\nMySQL said: %@" = "%1$@\n\nMySQL сообщил: %2$@";

/* favorite singular label (%d == 1) */
"%lu favorite" = "%lu избранное";

/* favorites plural label (%d != 1) */
"%lu favorites" = "%lu избранные";

/* favorite group singular label (%d == 1) */
"%lu group" = "%lu группа";

/* favorite groups plural label (%d != 1) */
"%lu groups" = "%lu группы";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+y (y!=1) rows instead of n selected were deleted. */
"%ld additional rows were removed!" = "Дополнительно удалено строк: %ld!";

/* Label showing the index of the selected CSV row */
"%ld of %lu record(s)" = "%1$ld из %2$lu записей(и)";

/* Label showing the index of the selected CSV row (csv partially loaded) */
"%ld of first %lu record(s)" = "%1$ld из первых %2$lu записей(и)";

/* Table Content : Remove Row : Result : Too Few : Part 1 : n-x (x!=1) of n selected rows were deleted. */
"%ld rows were not removed." = "%ld строк(и) не удалено.";

/* Export file already exists message */
"%lu files already exist. Do you want to replace them?" = "%lu файлы(ов) уже существуют. Вы хотите заменить их?";

/* Export files creation error title */
"%lu files could not be created" = "%lu файла(ов) не могут быть созданы";

/* Some export files already exist explanatory text */
"%lu files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "%lu файлов с теми же именами уже существует в целевой папке. Их замена перезапишет текущее содержимое.";

/* filtered item count */
"%lu of %lu" = "%1$lu из %2$lu";

/* Export folder not writable for some files explanatory text */
"%lu of the export files could not be created because their target export folder is not writable; please select a new export location and try again." = "Не получается создать %lu файлов экспорта, потому что их целевая папка недоступна для записи; пожалуйста, выберите новое место для экспорта и повторите попытку.";

/* Export folder missing for some files explanatory text */
"%lu of the export files could not be created because their target export folder no longer exists; please select a new export location and try again." = "Не получается создать %lu файлов экспорта, потому что их целевая папка больше не существует; пожалуйста, выберите новое место для экспорта и повторите попытку.";

/* History item title with nothing selected */
"(no selection)" = "(ничего не выбрано)";

/* value shown for hidden blob and text fields */
"(not loaded)" = "(не загружено)";

/* Explanation for MySQL server has gone away error */
"(This usually indicates that the connection has been closed by the server after inactivity, but can also occur due to other conditions.  The connection has been restored; please try again if the query is safe to re-run.)" = "(Обычно это указывает на то, что соединение было закрыто сервером из-за неактивности, но может быть и по другим причинам. Соединение было восстановлено; пожалуйста, повторите попытку, если запрос безопасен для перезапуска.)";

/* Custom Query : text appended to the “x row(s) affected” messages. $1 is a time interval */
", first row available after %1$@" = ", первые данные начали поступать через %1$@";

/* Custom Query : text appended to the “x row(s) affected” messages (for update/delete queries). $1 is a time interval */
", taking %1$@" = ", затрачено %1$@";

/* warning shown in the console when no rows have been affected after writing to the db */
"/* WARNING: No rows have been affected */\n" = "/* ВНИМАНИЕ: Не было затронуто ни одной строки */\n";

/* error text when multiple custom query failed */
"[ERROR in query %ld] %@\n" = "[ОШИБКА в запросе %1$ld] %2$@\n";

/* error text when reading of csv file gave errors */
"[ERROR in row %ld] %@\n" = "[ОШИБКА в строке %1$ld] %2$@\n";

/* [multiple selection] */
"[multiple selection]" = "[множественный выбор]";

/* displayed when new content filter has empty Name field (ContentFilterManager) */
"[name required]" = "[требуется имя]";

/* [no selection] */
"[no selection]" = "[ничего не выбрано]";

/* Additional export file errors */
"\n\n(In addition, one or more errors occurred while attempting to create the export files: %lu could not be created. These files will be ignored.)" = "\n\n(Кроме того, при создании экспортируемых файлов произошла одна или несколько ошибок: %lu не может быть создан. Эти файлы будут проигнорированы.)";

/* \n\npress shift for binary search tooltip message */
"\n\nPress ⇧ for binary search (case-sensitive)." = "\n\nНажмите ⇧ для бинарного поиска (с учётом регистра).";

/* description of bit */
"A bit-field type. M specifies the number of bits. If shorter values are inserted, they will be aligned on the least significant bit. See the SET type if you want to explicitly name each bit." = "Битовый тип поля. M задает количество битов. Если добавляются более короткие значения, то они будут выровнены по наименее значиму биту. Смотрите тип SET, если вы хотите явно называть каждый бит.";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog message */
"A Bundle ‘%@’ is already installed. Do you want to update it?" = "Пакет ‘%@’ уже установлен. Вы хотите обновить его?";

/* description of binary */
"A byte array with fixed length. Shorter values will always be padded to the right with 0x00 until they fit M." = "Байт-массив с фиксированной длиной. Более короткие значения всегда заполняются справа значением 0x00 до достижениями ими длины M.";

/* description of varbinary */
"A byte array with variable length. The actual number of bytes is further limited by the values of other fields in the row." = "Массив байтов с переменной длиной. Фактическое количество байт ограничено значениями последующих полей в строке таблицы.";

/* description of blob
 description of longblob
 description of mediumblob
 description of tinyblob */
"A byte array with variable length. Unlike VARBINARY this type does not count towards the maximum row length." = "Массив байтов с переменной длиной. В отличие от VARBINARY этот тип не учитывается при подсчёте максимальной длины строки таблицы.";

/* description of tinytext */
"A character string that can store up to 255 bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Символьная строка, которая может хранить до 255 байт, но занимает меньше места для более коротких значений. Фактическое количество символов ограничено используемой кодировкой. В отличие от VARCHAR этот тип не засчитывается при подсчёте максимальной длины строки таблицы.";

/* description of varchar */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding and the values of other fields in the row." = "Символьная строка, которая может хранить до M байт, но занимает меньше места для более коротких значений. Фактическое количество символов также ограничено используемой кодировкой и значениями других полей в строке таблицы.";

/* description of text */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Символьная строка, которая может хранить до M байт, но требует меньше места для более коротких значений. Фактическое количество символов ограничено используемой кодировкой. В отличие от VARCHAR этот тип не засчитывается в максимальную длину строки.";

/* description of char */
"A character string that will require M×w bytes per row, independent of the actual content length. w is the maximum number of bytes a single character can occupy in the given encoding." = "Символьная строка, которая потребует M×w байт в одной строке, независимо от фактической длины содержимого. w - максимальное количество байт, которое может занять один символ в данной кодировке.";

/* description of longtext
 description of mediumtext */
"A character string with variable length. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Символьная строка с длиной переменной. Фактическое количество символов дополнительно ограничено используемой кодировкой. В отличие от VARCHAR этот тип не засчитывается в максимальную длину строки.";

/* description of json */
"A data type that validates JSON data on INSERT and internally stores it in a binary format that is both, more compact and faster to access than textual JSON.\nAvailable from MySQL 5.7.8." = "Тип данных, который проверяет JSON данные при INSERT-е и хранит их в двоичном формате, что одновременно компактнее и способно обеспечить более быстрый доступ по сравнению с текстовым JSON-ном.\nДоступно в MySQL 5.7.8.";

/* Export file already exists explanatory text */
"A file with the same name already exists in the target folder. Replacing it will overwrite its current contents." = "Файл с таким именем уже существует в целевой папке. Замена файла перезапишет его содержимое.";

/* description of decimal */
"A fixed-point, exact decimal value. M is the maxium number of digits, of which D may be after the decimal point. When rounding, 0-4 is always rounded down, 5-9 up (“round towards nearest”)." = "Фиксированная точка, точное десятичное значение. M - это максимальное количество цифр, из которых D может быть после десятичной точки. При округлении 0-4 всегда округляется вниз, 5-9 вверх («круглый в сторону ближайшего»).";

/* table structure : indexes : delete index : error 1553 : title
 table structure : indexes : delete index : error 1553, no FK found : title */
"A foreign key needs this index" = "Внешний ключ нуждается в этом индексе";

/* description of set */
"A SET can define up to 64 members (as strings) of which a field can use one or more using a comma-separated list. Upon insertion the order of members is automatically normalized and duplicate members will be eliminated. Assignment of numbers is supported using the same semantics as for BIT types." = "Тип SET может содержать до 64 значений (строк), которые могут задаваться как одно или несколько значений поля, разделённые запятыми. Задания значений, их порядок автоматически нормализуется, а дубликаты удаляются. Присвоение значений чисел поддерживается при использовании той же семантики, что и для типов BIT.";

/* SSH key not found message */
"A SSH key location was specified, but no file was found in the specified location.  Please re-select the key and try again." = "Расположение ключа SSH было указано, но файл не был найден в указанном месте. Пожалуйста, выберите ключ повторно и повторите попытку.";

/* SSL CA certificate file not found message */
"A SSL Certificate Authority certificate location was specified, but no file was found in the specified location.  Please re-select the Certificate Authority certificate and try again." = "Местоположение SSL сертификата Центра сертификации было указано, но в указанном месте файл не найден. Пожалуйста, выберите сертификат и повторите попытку.";

/* SSL certificate file not found message */
"A SSL certificate location was specified, but no file was found in the specified location.  Please re-select the certificate and try again." = "Местоположение сертификата SSL было указано, но в указанном месте файла не найдено. Пожалуйста, выберите сертификат и повторите попытку.";

/* SSL key file not found message */
"A SSL key file location was specified, but no file was found in the specified location.  Please re-select the key file and try again." = "Местоположение ключа для сертификата SSL было указано, но в указанном месте файла не найдено. Пожалуйста, выберите сертификат и повторите попытку.";

/* duplicate host informative message */
"A user with the host '%@' already exists" = "Пользователь на узле '%@' уже существует";

/* duplicate user informative message */
"A user with the name '%@' already exists" = "Пользователь с именем '%@' уже существует";

/* table content : editing : error message description when parsing as hex string failed */
"A valid hex string may only contain the numbers 0-9 and letters A-F (a-f). It can optionally begin with „0x“ and spaces will be ignored.\nAlternatively the syntax X'val' is supported, too." = "A valid hex string may only contain the numbers 0-9 and letters A-F (a-f). It can optionally begin with „0x“ and spaces will be ignored.\nAlternatively the syntax X'val' is supported, too.";

/* connection failed due to access denied title */
"Access denied!" = "Доступ запрещён!";

/* range of double */
"Accurate to approx. 15 decimal places" = "С точностью примерно 15 знаков после запятой";

/* range of float */
"Accurate to approx. 7 decimal places" = "С точностью примерно 7 знаков после запятой";

/* active connection window is busy. please wait and try again. tooltip */
"Active connection window is busy. Please wait and try again." = "Текущее окно подключения занято. Пожалуйста, подождите и повторите попытку.";

/* header for activities pane */
"ACTIVITIES" = "ДЕЙСТВИЯ";

/* Add trigger button label */
"Add" = "Добавить";

/* menu item to add db */
"Add Database..." = "Создать базу данных...";

/* Add Host */
"Add Host" = "Добавить узел";

/* add global value or expression menu item */
"Add Value or Expression…" = "Добавить значение или выражение…";

/* adding index task status message */
"Adding index..." = "Добавление индекса...";

/* Advanced options short title */
"Advanced" = "Расширенные";

/* notifications preference pane name */
"Alerts & Logs" = "Оповещения и журналы";

/* notifications preference pane tooltip */
"Alerts & Logs Preferences" = "Настройки оповещений и журналов";

/* All databases placeholder */
"All Databases" = "Все базы данных";

/* All databases (%) placeholder */
"All Databases (%)" = "Все базы данных (%)";

/* All export files already exist message */
"All the export files already exist. Do you want to replace them?" = "Все экспортируемые файлы уже существуют. Вы хотите заменить их?";

/* An error for sequelace URL scheme command occurred. Probably no corresponding connection window found. */
"An error for sequelace URL scheme command occurred. Probably no corresponding connection window found." = "Произошла ошибка для команды URL схемы, возможно не найдено соответствующего окна соединения.";

/* no connection available informatie message */
"An error has occurred and there doesn't seem to be a connection available." = "Произошла ошибка и похоже, что соединение недоступно.";

/* an error occur while executing a scheme command. if the scheme command was invoked by a bundle command, it could be that the command still runs. you can try to terminate it by pressing ⌘+. or via the activities pane. */
"An error occur while executing a scheme command. If the scheme command was invoked by a Bundle command, it could be that the command still runs. You can try to terminate it by pressing ⌘+. or via the Activities pane." = "При выполнении команды к БД произошла ошибка. Если команда к БД была вызвана командой пакета, то возможно она всё ещё выполняется. Вы можете попробовать её прервать, нажав ⌘+. или сделать это через панель Действий.";

/* error killing query informative message */
"An error occurred while attempting to kill connection %lld.\n\nMySQL said: %@" = "При попытке принудительного разрыва соединения %1$lld произошла ошибка.\n\nMySQL сообщил: %2$@";

/* error killing query informative message */
"An error occurred while attempting to kill the query associated with connection %lld.\n\nMySQL said: %@" = "При попытке принудительно завершить запрос, связанный с соединением %1$lld произошла ошибка.\n\nMySQL сообщил: %2$@";

/* Error shown when unable to show create table syntax */
"An error occurred while creating table syntax.\n\n: %@" = "Произошла ошибка при формировании запроса создания таблицы.\n\n: %@";

/* rename table error - no temporary name found */
"An error occurred while renaming '%@'. No temporary name could be found. Please try renaming to something else first." = "Произошла ошибка при переименовании '%@'. Временное название не найдено. Пожалуйста, попробуйте сперва использовать другое название.";

/* rename table error informative message */
"An error occurred while renaming '%@'.\n\nMySQL said: %@" = "При переименовании '%1$@' произошла ошибка.\n\nMySQL сообщил: %2$@";

/* rename error - don't know what type the renamed thing is */
"An error occurred while renaming. '%@' is of an unknown type." = "Произошла ошибка при переименовании: '%@' имеет неизвестный тип.";

/* rename precedure/function error - can't delete old procedure */
"An error occurred while renaming. I couldn't delete '%@'.\n\nMySQL said: %@" = "При переименовании произошла ошибка: не получается удалить '%1$@'.\n\nMySQL сообщил: %2$@";

/* rename precedure/function error - can't recreate procedure */
"An error occurred while renaming. I couldn't recreate '%@'.\n\nMySQL said: %@" = "При переименовании произошла ошибка: не получается пересоздать '%1$@'.\n\nMySQL сообщил: %2$@";

/* rename precedure/function error - can't retrieve syntax */
"An error occurred while renaming. I couldn't retrieve the syntax for '%@'.\n\nMySQL said: %@" = "При переименовании произошла ошибка: не получается получить запрос создания '%1$@'.\n\nMySQL сообщил: %2$@";

/* rename error - invalid create syntax */
"An error occurred while renaming. The CREATE syntax of '%@' could not be parsed." = "При переименовании произошла ошибка: не удалось разобрать CREATE запрос для '%@'.";

/* message of panel when retrieving view information failed */
"An error occurred while retrieving status data.\n\nMySQL said: %@" = "При получении данных о состоянии произошла ошибка.\n\nMySQL сообщил: %@";

/* message of panel when create syntax cannot be retrieved */
"An error occurred while retrieving the create syntax for '%@'.\nMySQL said: %@" = "При получении запроса создания '%1$@' произошла ошибка.\nMySQL сообщил: %2$@";

/* add index error informative message */
"An error occurred while trying to add the index.\n\nMySQL said: %@" = "При попытке добавить индекс произошла ошибка.\n\nMySQL сообщил: %@";

/* error adding password to keychain informative message */
"An error occurred while trying to add the password to your Keychain. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Произошла ошибка при попытке добавить пароль в Связку ключей. Попробуйте восстановить Связку ключей, а если это не поможет, то сообщите команде Sequel Ace код ошибки %i.";

/* unable to copy database message informative message */
"An error occurred while trying to copy the database '%@' to '%@'." = "Произошла ошибка при попытке скопировать базу данных '%1$@' в '%2$@'.";

/* error deleting index informative message */
"An error occurred while trying to delete the index.\n\nMySQL said: %@" = "При попытке удалить индекс произошла ошибка.\n\nMySQL сообщил: %@";

/* table status : row count query failed : error message */
"An error occurred while trying to determine the number of rows for %@.\nMySQL said: %@ (%lu)" = "При попытке определить количество строк для “%1$@” произошла ошибка.\nMySQL сообщил: %2$@ (%3$lu)";

/* unable to rename database message informative message */
"An error occurred while trying to rename the database '%@' to '%@'." = "Произошла ошибка при попытке переименовать базу данных '%1$@' в '%2$@'.";

/* error finding keychain item to edit informative message */
"An error occurred while trying to retrieve the Keychain item you're trying to edit. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Произошла ошибка при попытке загрузить пароль из Связки ключей для вашей правки. Попробуйте восстановить Связку ключей, а если это не поможет, то сообщите команде Sequel Ace код ошибки %i.";

/* error updating keychain item informative message */
"An error occurred while trying to update the Keychain item. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Произошла ошибка при попытке обновить пароль в Связке ключей. Попробуйте восстановить Связку ключей, а если это не поможет, то сообщите команде Sequel Ace код ошибки %i.";

/* mysql error occurred message */
"An error occurred" = "Произошла ошибка";

/* MySQL table info retrieval error message */
"An error occurred retrieving table information.  MySQL said: %@" = "При получении информации о таблице произошла ошибка. MySQL сообщил: %@";

/* SQL encoding read error */
"An error occurred when reading the file, as it could not be read in the encoding you selected (%@).\n\nOnly %ld queries were executed." = "Произошла ошибка при чтении файла, так как он не может быть прочитан в выбранной кодировке (%1$@).\n\nВыполнены только запросы %2$ld.";

/* CSV encoding read error */
"An error occurred when reading the file, as it could not be read using the encoding you selected (%@).\n\nOnly %ld rows were imported." = "Произошла ошибка при чтении файла, так как его невозможно прочитать с помощью выбранной кодировки (%1$@).\n\nТолько %2$ld строки были импортированы.";

/* SQL read error, including detail from system */
"An error occurred when reading the file.\n\nOnly %ld queries were executed.\n\n(%@)" = "Произошла ошибка при чтении файла.\n\nвыполнены только %1$ld запросов.\n\n(%2$@)";

/* CSV read error, including detail string from system */
"An error occurred when reading the file.\n\nOnly %ld rows were imported.\n\n(%@)" = "Произошла ошибка при чтении файла.\n\nБыло импортировано строк: %1$ld.\n\n(%2$@)";

/* error adding field informative message */
"An error occurred when trying to add the field '%@' via\n\n%@\n\nMySQL said: %@" = "При попытке добавить поле '%1$@' через\n\n%2$@ произошла ошибка.\n\nMySQL сообщил: %3$@";

/* error changing field informative message */
"An error occurred when trying to change the field '%@' via\n\n%@\n\nMySQL said: %@" = "При попытке изменить поле '%1$@' через\n\n%2$@ произошла ошибка.\n\nMySQL сообщил: %3$@";

/* error changing table collation informative message */
"An error occurred when trying to change the table collation to '%@'.\n\nMySQL said: %@" = "При попытке изменить сортировку таблицы на '%1$@' произошла ошибка.\n\nMySQL сообщил: %2$@";

/* error changing table encoding informative message */
"An error occurred when trying to change the table encoding to '%@'.\n\nMySQL said: %@" = "При попытке изменить кодировку таблицы на '%1$@' произошла ошибка.\n\nMySQL сообщил: %2$@";

/* error changing table type informative message */
"An error occurred when trying to change the table type to '%@'.\n\nMySQL said: %@" = "При попытке изменить тип таблицы на '%1$@' произошла ошибка.\n\nMySQL сообщил: %2$@";

/* error changing table comment informative message */
"An error occurred when trying to change the table's comment to '%@'.\n\nMySQL said: %@" = "При попытке изменить комментарий таблицы на '%1$@' произошла ошибка.\n\nMySQL сообщил: %2$@";

/* an error occurred while analyzing the %@.\n\nMySQL said:%@ */
"An error occurred while analyzing the %@.\n\nMySQL said:%@" = "При анализе %1$@ произошла ошибка.\n\nMySQL сообщил:%2$@";

/* an error occurred while fetching the optimized field type.\n\nMySQL said:%@ */
"An error occurred while fetching the optimized field type.\n\nMySQL said:%@" = "При получении оптимизированного типа поля произошла ошибка.\n\nMySQL сообщил:%@";

/* an error occurred while trying to flush the %@.\n\nMySQL said:%@ */
"An error occurred while flushing the %@.\n\nMySQL said:%@" = "При очистке %1$@ произошла ошибка.\n\nMySQL сообщил:%2$@";

/* sql import error message */
"An error occurred while importing SQL" = "При импорте SQL произошла ошибка";

/* an error occurred while trying to optimze the %@.\n\nMySQL said:%@ */
"An error occurred while optimzing the %@.\n\nMySQL said:%@" = "При оптимизации %1$@ произошла ошибка.\n\nMySQL сообщил:%2$@";

/* an error occurred while performing the checksum on the %@.\n\nMySQL said:%@ */
"An error occurred while performing the checksum on %@.\n\nMySQL said:%@" = "При вычислении контрольной суммы %1$@ произошла ошибка.\n\nMySQL сообщил:%2$@";

/* an error occurred while trying to repair the %@.\n\nMySQL said:%@ */
"An error occurred while repairing the %@.\n\nMySQL said:%@" = "Произошла ошибка при исправлении %1$@.\n\nMySQL сообщил:%2$@";

/* message of panel when retrieving information failed */
"An error occurred while retrieving information.\nMySQL said: %@" = "При получении информации произошла ошибка.\nMySQL сообщил: %@";

/* error retrieving table information informative message */
"An error occurred while retrieving the information for table '%@'. Please try again.\n\nMySQL said: %@" = "При получении информации по таблице '%1$@' произошла ошибка. Пожалуйста, попробуйте ещё раз.\n\nMySQL сообщил: %2$@";

/* error retrieving table information informative message */
"An error occurred while retrieving the trigger information for table '%@'. Please try again.\n\nMySQL said: %@" = "При получении информации триггера для таблицы '%1$@' произошла ошибка. Пожалуйста, попробуйте ещё раз.\n\nMySQL сообщил: %2$@";

/* error adding new column informative message */
"An error occurred while trying to add the new column '%@' by\n\n%@.\n\nMySQL said: %@" = "При попытке добавить новый столбец '%1$@' произошла ошибка.\n\n%2$@.\n\nMySQL сообщил: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@' by\n\n%@.\n\nMySQL said: %@" = "Произошла ошибка при попытке добавить новую таблицу '%1$@' от\n\n%2$@.\n\nMySQL сказал: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@'.\n\nMySQL said: %@" = "Произошла ошибка при попытке добавить новую таблицу '%1$@'.\n\nMySQL сообщил: %2$@";

/* error while trying to alter table message */
"An error occurred while trying to alter table '%@'.\n\nMySQL said: %@" = "Произошла ошибка при попытке изменить таблицу '%1$@'.\n\nMySQL сообщил: %2$@";

/* an error occurred while trying to check the %@.\n\nMySQL said:%@ */
"An error occurred while trying to check the %@.\n\nMySQL said:%@" = "Произошла ошибка при попытке проверить %1$@.\n\nMySQL сообщил:%2$@";

/* error deleting relation informative message */
"An error occurred while trying to delete the relation '%@'.\n\nMySQL said: %@" = "Произошла ошибка при попытке удалить связь '%1$@'.\n\nMySQL сообщил: %2$@";

/* unable to get list of users informative message */
"An error occurred while trying to get the list of users. Please make sure you have the necessary privileges to perform user management, including access to the mysql.user table." = "Произошла ошибка при попытке получить список пользователей. Пожалуйста, убедитесь, что у вас есть необходимые права управления пользователями, включая доступ к таблице mysql.user.";

/* error importing table informative message */
"An error occurred while trying to import a table via: \n%@\n\n\nMySQL said: %@" = "При импорте таблицы произошла ошибка: \n%1$@\n\n\nMySQL сообщил: %2$@";

/* error moving field informative message */
"An error occurred while trying to move the field.\n\nMySQL said: %@" = "При попытке передвинуть поле произошла ошибка.\n\nMySQL сообщил: %@";

/* error resetting auto_increment informative message */
"An error occurred while trying to reset AUTO_INCREMENT of table '%@'.\n\nMySQL said: %@" = "Произошла ошибка при попытке сбросить AUTO_INCREMENT таблицы '%1$@'.\n\nMySQL сообщил: %2$@";

/* error truncating table informative message */
"An error occurred while trying to truncate the table '%@'.\n\nMySQL said: %@" = "При попытке очистить таблицу '%1$@' произошла ошибка.\n\nMySQL сообщил: %2$@";

/* mysql error occurred informative message */
"An error occurred whilst trying to perform the operation.\n\nMySQL said: %@" = "При попытке выполнить операцию произошла ошибка.\n\nMySQL сообщил: %@";

/* mysql resource limits unsupported message */
"Resource Limits are not supported for your version of MySQL. Any Resouce Limits you specified have been discarded and not saved. MySQL said: %@" = "Ограничения ресурсов не поддерживаются для вашей версии MySQL. Любые ограничения ресурсов, которые вы указали, были отклонены и не сохранились. MySQL сообщил: %@";

/* Export files creation error explanatory text */
"An unhandled error occurred when attempting to create %lu of the export files.  Please check the details and try again." = "Произошла необработанная ошибка при попытке создать %lu экспортируемых файлов. Проверьте детали и повторите попытку.";

/* All export files creation error explanatory text */
"An unhandled error occurred when attempting to create each of the export files.  Please check the details and try again." = "Произошла необработанная ошибка при попытке создать каждый из экспортируемых файлов. Проверьте детали и повторите попытку.";

/* Export file creation error explanatory text */
"An unhandled error occurred when attempting to create the export file.  Please check the details and try again." = "Произошла необработанная ошибка при попытке создать файл экспорта. Пожалуйста, проверьте детали и повторите попытку.";

/* ANALYZE one or more tables - result title */
"Analyze %@" = "Анализ %@";

/* analyze selected items menu item */
"Analyze Selected Items" = "Проанализировать выбранные элементы";

/* analyze table menu item */
"Analyze Table" = "Проанализировать таблицу";

/* analyze table failed message */
"Analyze table failed." = "Не получилось проанализировать таблицу.";

/* change table type informative message */
"Are you sure you want to change this table's type to %@?\n\nPlease be aware that changing a table's type has the potential to cause the loss of some or all of its data. This action cannot be undone." = "Вы уверены, что хотите изменить тип этой таблицы на %@?\n\nИмейте в виду, что изменение типа таблицы может вызвать потерю некоторых или всех ее данных. Это действие нельзя отменить.";

/* clear global history list informative message */
"Are you sure you want to clear the global history list? This action cannot be undone." = "Вы уверены, что хотите очистить всю историю запросов? Это действие необратимо.";

/* clear history list for %@ informative message */
"Are you sure you want to clear the history list for %@? This action cannot be undone." = "Вы уверены, что хотите очистить историю запросов для %@? Это действие необратимо.";

/* truncate tables informative message */
"Are you sure you want to delete ALL records in the selected tables? This operation cannot be undone." = "Вы уверены, что хотите удалить ВСЕ записи в выбранных таблицах? Это действие необратимо.";

/* truncate table informative message */
"Are you sure you want to delete ALL records in the table '%@'? This operation cannot be undone." = "Вы уверены, что хотите удалить ВСЕ записи таблицы '%@'? Это действие необратимо.";

/* delete all rows informative message */
"Are you sure you want to delete all the rows from this table? This action cannot be undone." = "Вы уверены, что хотите удалить все строки этой таблицы? Это действие небратимо.";

/* delete table/view informative message */
"Are you sure you want to delete the %@ '%@'? This operation cannot be undone." = "Вы уверены, что хотите удалить %1$@ '%2$@'? Это действие необратимо.";

/* delete database informative message */
"Are you sure you want to delete the database '%@'? This operation cannot be undone." = "Вы уверены, что хотите удалить базу данных'%@'? Это действие необратимо.";

/* delete database informative message */
"Are you sure you want to delete the favorite '%@'? This operation cannot be undone." = "Вы уверены, что хотите удалить из избранного\"%@'? Это действие необратимо.";

/* delete field informative message */
"Are you sure you want to delete the field '%@'? This action cannot be undone." = "Вы уверены, что хотите удалить поле \"%@'? Это действие необратимо.";

/* delete database informative message */
"Are you sure you want to delete the group '%@'? All groups and favorites within this group will also be deleted. This operation cannot be undone." = "Вы уверены, что хотите удалить группу '%@'? Все подгруппы и избранное в этой группе также будут удалены. Это действие необратимо.";

/* delete index informative message */
"Are you sure you want to delete the index '%@'? This action cannot be undone." = "Вы уверены, что хотите удалить индекс \"%@'? Это действие необратимо.";

/* delete tables/views informative message */
"Are you sure you want to delete the selected %@? This operation cannot be undone." = "Вы уверены, что хотите удалить выбранное(ые) %@? Это действие необратимо.";

/* delete rows informative message */
"Are you sure you want to delete the selected %ld rows from this table? This action cannot be undone." = "Вы уверены, что хотите удалить %ld строк этой таблицы? Это действие необратимо.";

/* delete selected relation informative message */
"Are you sure you want to delete the selected relations? This action cannot be undone." = "Вы действительно хотите удалить выбранную связь? Это действие необратимо.";

/* delete selected row informative message */
"Are you sure you want to delete the selected row from this table? This action cannot be undone." = "Вы уверены, что хотите удалить выбранную строку этой таблицы? Это действие необратимо.";

/* delete selected trigger informative message */
"Are you sure you want to delete the selected triggers? This action cannot be undone." = "Вы уверены, что хотите выбранные триггеры \"%@'? Это действие необратимо.";

/* kill connection informative message */
"Are you sure you want to kill connection ID %lld?\n\nPlease be aware that continuing to kill this connection may result in data corruption. Please proceed with caution." = "Вы уверены, что хотите разорвать соединение ID %lld?\n\nИмейте в виду, что разрыв этого соединения может привести к повреждению данных. Пожалуйста, будьте осторожны.";

/* kill query informative message */
"Are you sure you want to kill the current query executing on connection ID %lld?\n\nPlease be aware that continuing to kill this query may result in data corruption. Please proceed with caution." = "Вы уверены, что хотите принудительно завершить текущий запрос в подключении ID %lld?\n\nИмейте в виду, что принудительное завершение этого запроса может привести к повреждению данных. Пожалуйста, проявляйте осторожность.";

/* Bundle Editor : Remove-Bundle: remove dialog message */
"Are you sure you want to move the selected Bundle to the Trash and remove them respectively?" = "Вы уверены, что хотите удалить выбранный пакет, переместив его в корзину?";

/* continue to print informative message */
"Are you sure you want to print the current content view of the table '%@'?\n\nIt currently contains %@ rows, which may take a significant amount of time to print." = "Вы уверены, что хотите распечатать текущее содержимое таблицы '%1$@'?\n\nНа данный момент содержит строки %2$@ , что может занять значительное время для печати.";

/* remove all query favorites informative message */
"Are you sure you want to remove all of your saved query favorites? This action cannot be undone." = "Вы уверены, что хотите удалить все ваши избранные запросы? Это действие необратимо.";

/* remove all selected content filters informative message */
"Are you sure you want to remove all selected content filters? This action cannot be undone." = "Вы уверены, что хотите удалить все выбранные фильтры контента? Это действие нельзя отменить.";

/* remove all selected query favorites informative message */
"Are you sure you want to remove all selected query favorites? This action cannot be undone." = "Вы уверены, что хотите удалить все избранные запросы? Это действие нельзя отменить.";

/* Table Info Section : current value of auto_increment */
"auto_increment: %@" = "auto_increment: %@";

/* Encoding autodetect menu item */
"Autodetect" = "Автоопределение";

/* background label for color table (Prefs > Editor) */
"Background" = "Фон";

/* backtick quote label for color table (Prefs > Editor) */
"Backtick Quote" = "Обратные кавычки";

/* bash error */
"BASH Error" = "Ошибка BASH";

/* toolbar item label for switching to the Table Content tab */
"Browse & Edit Table Content" = "Просмотр и правка содержимого таблицы";

/* build label */
"build" = "сборка";

/* build label */
"Build" = "Сборка";

/* bundle editor menu item label */
"Bundle Editor" = "Редактор пакетов";

/* bundle error */
"Bundle Error" = "Ошибка пакета";

/* bundles menu item label */
"Bundles" = "Пакеты";

/* Bundle Editor : Outline View : 'BUNDLES' item */
"BUNDLES" = "ПАКЕТЫ";

/* Bundle Editor : Outline View : Menu Category item : tooltip */
"Bundles in category %@" = "Пакет в категории %@";

/* bundles installation error */
"Bundles Installation Error" = "Ошибка установки пакетов";

/* bzip2 compression export summary - within a sentence */
"bzip2 compression" = "bzip2 сжатие";

/* description of geometry */
"Can store a single spatial value of types POINT, LINESTRING or POLYGON. Spatial support in MySQL is based on the OpenGIS Geometry Model." = "Может хранить одноместное значение типов POINT, LINESTRING или POLYGON. Поддержка пространств в MySQL основана на OpenGIS Geometry Model.";

/* Bundle Editor : Remove-Bundle: cancel button
 cancel
 cancel button
 Open Files : Bundle : Already-Installed : Cancel button
 Preferences : Network : Custom SSH client : warning dialog : cancel button
 shutdown server : confirmation dialog : cancel button */
"Cancel" = "Отменить";

/* sql import : charset error alert : cancel button */
"Cancel Import" = "Отменить импорт";

/* cancelling task status message */
"Cancelling..." = "Отмена...";

/* empty query informative message */
"Cannot save an empty query." = "Невозможно сохранить пустой запрос.";

/* caret label for color table (Prefs > Editor) */
"Caret" = "Курсор";

/* change button */
"Change" = "Изменить";

/* change focus to table list menu item */
"Change Focus to Table List" = "Сменить фокус на список таблиц";

/* change table type message */
"Change table type" = "Изменить тип таблицы";

/* unsaved changes informative message */
"Changes have been made, which will be lost if this window is closed. Are you sure you want to continue" = "Были внесены изменения, которые будут потеряны при закрытии этого окна. Вы уверены, что хотите продолжить";

/* quitting app informal alert title */
"Close the app?" = "Закрыть приложение?";

/* quitting app informal alert body */
"Are you sure you want to quit the app?" = "Вы уверены, что хотите закрыть приложение?";

/* character set client: %@ */
"character set client: %@" = "кодировка: %@";

/* CHECK one or more tables - result title */
"Check %@" = "Проверка %@";

/* check of all selected items successfully passed message */
"Check of all selected items successfully passed." = "Проверка всех выбранных элементов успешно пройдена.";

/* check option: %@ */
"check option: %@" = "проверка изменений: %@";

/* check selected items menu item */
"Check Selected Items" = "Проверить выбранные элементы";

/* check table menu item */
"Check Table" = "Проверить таблицу";

/* check table failed message */
"Check table failed." = "Не получилось проверить таблицу.";

/* check table successfully passed message */
"Check table successfully passed." = "Проверка таблицы успешно пройдена.";

/* check view menu item */
"Check View" = "Проверить представление";

/* checking field data for editing task description */
"Checking field data for editing..." = "Подготовка данных поля к правке...";

/* checksum %@ message */
"Checksum %@" = "Контрольная сумма %@";

/* checksum selected items menu item */
"Checksum Selected Items" = "Контрольная сумма выбранных элементов";

/* checksum table menu item */
"Checksum Table" = "Контрольная сумма таблицы";

/* Checksums of %@ message */
"Checksums of %@" = "Контрольные суммы для %@";

/* menu item for choose db */
"Choose Database..." = "Выберите базу данных...";

/* cancelling export cleaning up message */
"Cleaning up..." = "Очистка...";

/* clear button */
"Clear" = "Очистить";

/* toolbar item for clear console */
"Clear Console" = "Очистить консоль";

/* clear global history menu item title */
"Clear Global History" = "Стереть всю историю";

/* clear history for %@ menu title */
"Clear History for %@" = "Стереть историю для %@";

/* clear history message */
"Clear History?" = "Стереть историю?";

/* tooltip for toolbar item for clear console */
"Clear the console which shows all MySQL commands performed by Sequel Ace" = "Очистить консоль, которая показывает все команды MySQL, выполненные Sequel Ace";

/* clear the document-based history list tooltip message */
"Clear the document-based history list" = "Очистить историю запросов, связанных с документом";

/* clear the global history list tooltip message */
"Clear the global history list" = "Очистить всю историю запросов";

/* Close menu item */
"Close" = "Закрыть";

/* close tab context menu item */
"Close Tab" = "Закрыть вкладку";

/* Close Window menu item */
"Close Window" = "Закрыть окно";

/* collation label (Navigator) */
"Collation" = "Правила сортировки";

/* collation connection: %@ */
"collation connection: %@" = "сортировка: %@";

/* comment label */
"Comment" = "Комментарий";

/* Title of action menu item to comment line */
"Comment Line" = "Закомментировать строку";

/* Title of action menu item to comment selection */
"Comment Selection" = "Закомментировать выделенное";

/* connect button */
"Connect" = "Подключиться";

/* Connect via socket button */
"Connect via socket" = "Подключиться через сокет";

/* connection established message */
"Connected" = "Подключено";

/* description for connected notification */
"Connected to %@" = "Подключено к %@";

/* message of panel when connection to db failed */
"Connected to host, but unable to connect to database %@.\n\nBe sure that the database exists and that you have the necessary privileges.\n\nMySQL said: %@" = "После соединения с узлом не удалось подключиться к базе данных %1$@.\n\nУбедитесь, что база данных существует и что у вас есть необходимые права.\n\nMySQL сообщил: %2$@";

/* Generic connecting very short status message */
"Connecting..." = "Подключение...";

/* window title string indicating that sp is connecting */
"Connecting…" = "Подключение…";

/* error while reading connection data file */
"Connection data file couldn't be read." = "Невозможно прочитать файл с данными для подключения.";

/* message error while reading connection data file and suggesting to save it under a differnet name */
"Connection data file %@ couldn't be read. Please try to save the document under a different name." = "Файл с данными для подключения %@ не читается. Пожалуйста, попробуйте сохранить документ под другим именем.";

/* connection failed title */
"Connection failed!" = "Сбой соединения!";

/* Connection file is encrypted */
"Connection file is encrypted" = "Файл подключения зашифрован";

/* Connection success very short status message */
"Connection succeeded" = "Соединение успешно установлено";

/* Console */
"Console" = "Консоль";

/* Console : Save as : Initial filename */
"ConsoleLog" = "Журнал консоли";

/* toolbar item label for switching to the Table Content tab */
"Content" = "Содержимое";

/* content filter clause is empty tooltip. */
"Content Filter clause is empty." = "Условие фильтра содержимого пусто.";

/* continue button
 Continue button title */
"Continue" = "Продолжить";

/* continue to print message */
"Continue to print?" = "Продолжить печать?";

/* Copy as RTF */
"Copy as RTF" = "Скопировать как RTF";

/* copy create func syntax menu item
 Table List : Context Menu : copy CREATE FUNCTION syntax */
"Copy Create Function Syntax" = "Скопировать запрос создания функции";

/* copy create proc syntax menu item
 Table List : Context Menu : Copy CREATE PROCEDURE syntax
 Table List : Gear Menu : Copy CREATE PROCEDURE syntax */
"Copy Create Procedure Syntax" = "Скопировать запрос создания процедуры";

/* copy create syntaxes menu item
 Table List : Context Menu : Copy CREATE syntax (multiple selection)
 Table List : Gear Menu : Copy CREATE syntax (multiple selection) */
"Copy Create Syntaxes" = "Скопировать запросы создания";

/* copy create table syntax menu item
 Table List : Context Menu : Copy CREATE syntax (single table)
 Table List : Gear Menu : Copy CREATE syntax (single table) */
"Copy Create Table Syntax" = "Скопировать запрос создания таблицы";

/* copy create view syntax menu item
 Table List : Context Menu : Copy CREATE view statement
 Table List : Gear Menu : Copy CREATE view statement */
"Copy Create View Syntax" = "Скопировать запрос создания представления";

/* copy server variable name menu item */
"Copy Variable Name" = "Скопировать имя переменной";

/* copy server variable names menu item */
"Copy Variable Names" = "Скопировать имена переменных";

/* copy server variable value menu item */
"Copy Variable Value" = "Скопировать значение переменной";

/* copy server variable values menu item */
"Copy Variable Values" = "Скопировать значения переменных";

/* Procedure/function export permission error */
"Could not export the %@ '%@' because of a permissions error.\n" = "Не удалось экспортировать %1$@ '%2$@' из-за ошибки прав доступа.\n";

/* Error when we can't parse/split file as CSV */
"Could not parse file as CSV" = "Не удалось разобрать CSV файл";

/* message when database selection failed */
"Could not select database" = "Не удалось выбрать базу данных";

/* Alter Database : Query Failed ($1 = mysql error message) */
"Couldn't alter database.\nMySQL said: %@" = "Не удалось изменить базу данных.\nMySQL сообщил: %@";

/* Couldn't copy default themes to Application Support Theme folder!\nError: %@ */
"Couldn't copy default themes to Application Support Theme folder!\nError: %@" = "Не удалось скопировать базовые темы в папку тем программы!\nОшибка: %@";

/* message of panel when table cannot be created */
"Couldn't create '%@'.\nMySQL said: %@" = "Не удалось создать '%1$@'.\nMySQL сообщил: %2$@";

/* Couldn't create Application Support Bundle folder!\nError: %@ */
"Couldn't create Application Support Bundle folder!\nError: %@" = "Не удалось создать папку для хранения пакетов!\nОшибка: %@";

/* Couldn't create Application Support Theme folder!\nError: %@ */
"Couldn't create Application Support Theme folder!\nError: %@" = "Не удалось создать папку для хранения тем!\nОшибка: %@";

/* message of panel when creation of db failed */
"Couldn't create database.\nMySQL said: %@" = "Не удалось создать базу данных.\nMySQL сообщил: %@";

/* message of panel when an item cannot be deleted */
"Couldn't delete '%@'.\n\nMySQL said: %@" = "Не удалось удалить '%1$@'.\n\nMySQL сообщил: %2$@";

/* message of panel when an item cannot be deleted including informative message about using force deletion */
"Couldn't delete '%@'.\n\nSelecting the 'Force delete' option may prevent this issue, but may leave the database in an inconsistent state.\n\nMySQL said: %@" = "Не удалось удалить '%1$@'.\n\nВыбор параметра «Принудительное удаление» может решить эту проблему, но при этом привести базу данных в рассогласованное состояние.\n\nMySQL сообщил: %2$@";

/* message of panel when field cannot be deleted */
"Couldn't delete field %@.\nMySQL said: %@" = "Не удалось удалить поле %1$@.\nMySQL сообщил: %2$@";

/* message when deleteing all rows failed */
"Couldn't delete rows.\n\nMySQL said: %@" = "Не удалось удалить строки.\n\nMySQL сообщил: %@";

/* message of panel when deleting db failed */
"Couldn't delete the database.\nMySQL said: %@" = "Не удалось удалить базу данных.\nMySQL сообщил: %@";

/* message of panel when an item cannot be renamed */
"Couldn't duplicate '%@'.\nMySQL said: %@" = "Не удалось дублировать '%1$@'.\nMySQL сообщил: %2$@";

/* message of panel when flushing privs failed */
"Couldn't flush privileges.\nMySQL said: %@" = "Не удалось перезагрузить привилегии.\nMySQL сообщил: %@";

/* message of panel when table information cannot be retrieved */
"Couldn't get create syntax.\nMySQL said: %@" = "Не удалось получить запрос создания.\nMySQL сообщил: %@";

/* Custom Query result editing error - could not identify a corresponding column */
"Couldn't identify field origin unambiguously. The column '%@' contains data from more than one table." = "Не удалось однозначно определить источник полей. Столбец '%@' содержит данные из более чем одной таблицы.";

/* message of panel when loading of row failed */
"Couldn't load the row. Reload the table to be sure that the row exists and use a primary key for your table." = "Не удалось загрузить строку. Перезагрузите таблицу, чтобы убедиться, что она существует и используйте первичный ключ в вашей таблицы.";

/* Couldn't read the file content of */
"Couldn't read the file content of" = "Не удалось прочитать содержимое файла";

/* text shown if an error occurred while sorting the result table */
"Couldn't sort column." = "Не удалось отсортировать столбец.";

/* message of panel when sorting of table failed */
"Couldn't sort table. MySQL said: %@" = "Не удалось отсортировать таблицу. MySQL сообщил: %@";

/* message of panel when error while updating field to db */
"Couldn't write field.\nMySQL said: %@" = "Не удалось записать поле.\nMySQL сообщил: %@";

/* create syntax for table comment */
"Create syntax for" = "Запрос создания для";

/* Create syntax label */
"Create syntax for %@ '%@'" = "Запрос создания для %1$@ '%2$@ '";

/* Create syntaxes for selected items label */
"Create syntaxes for selected items" = "Запросы создания для выбранных элементов";

/* Table Info Section : table create options */
"create_options: %@" = "опции_создания: %@";

/* created: %@
 Table Info Section : time+date table was created at */
"created: %@" = "создана: %@";

/* description of polygon */
"Creates a surface by combining one LinearRing (ie. a LineString that is closed and simple) as the outside boundary with zero or more inner LinearRings acting as \"holes\"." = "Создает поверхность, объединив одно кольцо (т.е. Линейная строка, которая закрыта и простая) как внешняя граница, при которой \"дыры\" или \"нулевые или более внутренние рейсы\" действуют как \"дыры\".";

/* Creating table task string */
"Creating %@..." = "Создание %@...";

/* Bundle Editor : Fallback Input source dropdown : 'current line' item */
"Current Line" = "Текущая строка";

/* Bundle Editor : Fallback Input source dropdown : 'current query' item */
"Current Query" = "Текущий запрос";

/* header for current selection in filtered list */
"CURRENT SELECTION" = "ТЕКУЩИЙ ВЫБОР";

/* Bundle Editor : Fallback Input source dropdown : 'current word' item */
"Current Word" = "Текущее слово";

/* SSH connection : debug header with user-defined ssh binary */
"Custom SSH binary enabled. Disable in Preferences to rule out incompatibilities!" = "Включён пользовательский бинарный SSH. Отключите в настройках для устранения несовместимости!";

/* customize file name label */
"Customize Filename (%@)" = "Задать имя файла (%@)";

/* data access: %@ (%@) */
"data access: %@ (%@)" = "результат: %1$@ (%2$@)";

/* Bundle Editor : Scope dropdown : 'data table' item
 data table menu item label */
"Data Table" = "Содержимое таблицы";

/* Bundle Editor : Outline View : 'Data Table' item : tooltip */
"Data Table Scope\ncommands will run on the Content and Query data tables" = "Область таблиц данных\nкоманды будут нацелены на содержимое таблиц и запросы к ним";

/* export filename database token
 export header database label
 export source
 Table Structure : Collation dropdown : 'item is the same as the collation of database' marker
 Table Structure : Encoding dropdown : 'item is database default' marker */
"Database" = "База данных";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'database changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'database changed' item */
"Database changed" = "База данных изменена";

/* message of panel when no db name is given */
"Database must have a name." = "База данных должна иметь имя.";

/* databsse rename unsupported message */
"Database Rename Unsupported" = "Переименование базы данных не поддерживается";

/* export filename date token */
"Date" = "Дата";

/* export filename date token */
"Day" = "День";

/* Add Table Sheet : Collation : Default (unknown)
 Collation Dropdown : Default (unknown)
 default label
 Table Structure : Collation dropdown : 'item is the same as the default collation of the row's charset' marker */
"Default" = "По умолчанию";

/* Add Table : Collation : Default ($1 = collation name)
 Charset Dropdown : Default item ($1 = charset name)
 Collation Dropdown : Default ($1 = collation name)
 Collation Dropdown : Default collation for given charset ($1 = collation name)
 New Table Sheet : Table Engine Dropdown : Default */
"Default (%@)" = "По умолчанию (%@)";

/* default bundles update */
"Default Bundles Update" = "Обновление базовых пакетов";

/* import : csv field mapping : field default value */
"Default: %@" = "По умолчанию: %@";

/* Query snippet default value placeholder */
"default_value" = "значение_по_умолчанию";

/* definer label (Navigator) */
"Definer" = "Создатель";

/* definer: %@ */
"definer: %@" = "создатель: %@";

/* description of enum */
"Defines a list of members, of which every field can use at most one. Values are sorted by their index number (starting at 0 for the first member)." = "Определяет список участников, из которых каждое поле может использовать не более одного. Значения сортируются по их индексу (начиная с 0 для первого пользователя).";

/* delete button */
"Delete" = "Удалить";

/* delete table/view message */
"Delete %@ '%@'?" = "Удалить %1$@ '%2$@'?";

/* table structure : indexes : delete index : error 1553 : delete index and FK button */
"Delete Both" = "Удалить оба";

/* delete database message */
"Delete database '%@'?" = "Удалить базу данных '%@'?";

/* delete database message */
"Delete favorite '%@'?" = "Удалить избранное '%@'?";

/* delete field message */
"Delete field '%@'?" = "Удалить поле '%@'?";

/* delete func menu title */
"Delete Function" = "Удалить функцию";

/* delete functions menu title */
"Delete Functions" = "Удалить функции";

/* delete database message */
"Delete group '%@'?" = "Удалить группу '%@'?";

/* delete index message */
"Delete index '%@'?" = "Удалить индекс '%@'?";

/* delete items menu title */
"Delete Items" = "Удалить элементы";

/* delete proc menu title */
"Delete Procedure" = "Удалить процедуру";

/* delete procedures menu title */
"Delete Procedures" = "Удалить процедуры";

/* delete relation menu item */
"Delete Relation" = "Удалить связь";

/* delete relation message */
"Delete relation" = "Удаление связи";

/* delete relations menu item */
"Delete Relations" = "Удалить связь";

/* delete row menu item singular */
"Delete Row" = "Удалить строку";

/* delete rows menu item plural */
"Delete Rows" = "Удалить строки";

/* delete rows message */
"Delete rows?" = "Удалить строки?";

/* delete tables/views message */
"Delete selected %@?" = "Удалить выделенное %@?";

/* delete selected row message */
"Delete selected row?" = "Удалить выбранную строку?";

/* delete table menu title */
"Delete Table..." = "Удалить таблицу...";

/* delete tables menu title */
"Delete Tables" = "Удалить таблицы";

/* delete trigger menu item */
"Delete Trigger" = "Удалить триггер";

/* delete trigger message */
"Delete trigger" = "Удаление триггера";

/* delete triggers menu item */
"Delete Triggers" = "Удалить триггеры";

/* delete view menu title */
"Delete View" = "Удалить представление";

/* delete views menu title */
"Delete Views" = "Удалить представления";

/* Preferences : Network : SSL Chiper suites : List seperator */
"Disabled Cipher Suites" = "Отключенные наборы шифрования";

/* force table deltion button text tooltip */
"Disables foreign key checks (FOREIGN_KEY_CHECKS) before deletion and re-enables them afterwards." = "Временно отключает проверку внешних ключей (FOREIGN_KEY_CHECKS) перед удалением, а затем снова включает её.";

/* discard changes button */
"Discard changes" = "Откатить изменения";

/* description for disconnected notification */
"Disconnected from %@" = "Отключено от %@";

/* do update operator tooltip */
"Do UPDATE where field contents match" = "Do UPDATE where field contents match";

/* message of panel asking for confirmation for loading large text into the query editor */
"Do you really want to load a SQL file with %@ of data into the Query Editor?" = "Вы действительно хотите загрузить SQL файл с %@ данными в Редактор запросов?";

/* message of panel asking for confirmation for inserting large text from dragging action */
"Do you really want to proceed with %@ of data?" = "Вы действительно хотите продолжить обработку %@ данных?";

/* shutdown server : confirmation dialog : title */
"Do you really want to shutdown the server?" = "Вы действительно хотите выключить сервер?";

/* dtd identifier label (Navigator) */
"DTD Identifier" = "Идентификатор DTD";

/* sql export dump of table label */
"Dump of table" = "Дамп таблицы";

/* sql export dump of view label */
"Dump of view" = "Дамп представления";

/* text showing that app is writing dump */
"Dumping..." = "Запись дампа...";

/* duplicate object message */
"Duplicate %@ '%@' to:" = "Дублировать %1$@ '%2$@' в:";

/* duplicate func menu title */
"Duplicate Function..." = "Дублировать функцию...";

/* duplicate host message */
"Duplicate Host" = "Дублировать узел";

/* duplicate proc menu title */
"Duplicate Procedure..." = "Дублировать процедуру...";

/* duplicate table menu title */
"Duplicate Table..." = "Дублировать таблицу...";

/* duplicate user message */
"Duplicate User" = "Дублировать пользователя";

/* duplicate view menu title */
"Duplicate View..." = "Дублировать представление...";

/* partial copy database support informative message */
"Duplicating the database '%@' is only partially supported as it contains objects other than tables (i.e. views, procedures, functions, etc.), which will not be copied.\n\nWould you like to continue?" = "Создание дубликата базы данных '%@' имеет частичную поддержку ввиду того, что она содержит отличные от таблиц объекты (такие как: представления, процедуры, функции и т.п.), которые не будут скопированы.\n\nВы хотите продолжить?";

/* edit filter */
"Edit Filters…" = "Редактировать фильтры…";

/* Edit row button */
"Edit row" = "Изменить строку";

/* toolbar item label for switching to the Table Structure tab */
"Edit Table Structure" = "Изменить структуру таблицы";

/* edit theme list label */
"Edit Theme List…" = "Редактировать список тем…";

/* edit user-defined filter */
"Edit user-defined Filters…" = "Редактирование пользовательских фильтров…";

/* empty query message */
"Empty query" = "Пустой запрос";

/* MySQL search language code - eg in http://search.mysql.com/search?q=select&site=refman-50&lr=lang_en */
"en" = "en";

/* encoding label (Navigator) */
"Encoding" = "Кодировка";

/* Table Info Section : $1 = table charset */
"encoding: %1$@" = "кодировка: %1$@";

/* Table Info Section : $1 = table charset, $2 = table collation */
"encoding: %1$@ (%2$@)" = "кодировка: %1$@ (%2$@)";

/* Table Info Section : Table Engine */
"engine: %@" = "движок: %@";

/* enter connection details label */
"Enter connection details below, or choose a favorite" = "Введите данные о подключении ниже, или выберите избранное";

/* SSH key password prompt */
"Enter your password for the SSH key\n\"%@\"" = "Введите ваш пароль для SSH ключа\n'%@";

/* Bundle Editor : Fallback Input source dropdown : 'entire content' item
 Bundle Editor : Scope=Field : Input source dropdown: 'entire content' item */
"Entire Content" = "Всё содержимое";

/* Bundle Editor : Copy-Command-Error : error dialog title
 error */
"Error" = "Ошибка";

/* error adding field message */
"Error adding field" = "Ошибка добавления поля";

/* error adding new column message */
"Error adding new column" = "Ошибка при добавлении новой колонки";

/* error adding new table message */
"Error adding new table" = "Ошибка при добавлении новой таблицы";

/* error adding password to keychain message */
"Error adding password to Keychain" = "Ошибка при добавлении пароля в Keychain";

/* error changing field message */
"Error changing field" = "Ошибка изменения поля";

/* error changing table collation message */
"Error changing table collation" = "Ошибка изменения сортировки таблицы";

/* error changing table comment message */
"Error changing table comment" = "Ошибка изменения комментария к таблице";

/* error changing table encoding message */
"Error changing table encoding" = "Ошибка изменения кодировки таблицы";

/* error changing table type message */
"Error changing table type" = "Ошибка изменения типа таблицы";

/* error creating relation message */
"Error creating relation" = "Ошибка при создании связи";

/* error creating trigger message */
"Error creating trigger" = "Ошибка создания триггера";

/* error for message */
"Error for" = "Ошибка для";

/* error for bash command ($1), $2=message */
"Error for “%1$@”:\n%2$@" = "Ошибка для “%1$@”:\n%2$@";

/* error moving field message */
"Error moving field" = "Ошибка перемещения поля";

/* error occurred */
"error occurred" = "произошла ошибка";

/* error reading import file */
"Error reading import file." = "Ошибка чтения файла импорта.";

/* error finding keychain item to edit message */
"Error retrieving Keychain item to edit" = "Ошибка получения элемента Keychain для редактирования";

/* error retrieving table information message */
"Error retrieving table information" = "Ошибка получения информации о таблице";

/* error retrieving trigger information message */
"Error retrieving trigger information" = "Ошибка при получении информации о триггере";

/* error truncating table message */
"Error truncating table" = "Ошибка очистки таблицы";

/* error updating keychain item message */
"Error updating Keychain item" = "Ошибка при обновлении элемента Keychain";

/* error while analyzing selected items message */
"Error while analyzing selected items" = "Ошибка при анализе выбранных элементов";

/* error while checking selected items message */
"Error while checking selected items" = "Ошибка при проверке выбранных элементов";

/* error while converting color scheme data */
"Error while converting color scheme data" = "Ошибка при преобразовании данных цветовой схемы";

/* error while converting connection data */
"Error while converting connection data" = "Ошибка при преобразовании данных подключения";

/* Content filters could not be converted to plist upon export - message title (ContentFilterManager) */
"Error while converting content filter data" = "Ошибка при преобразовании данных фильтра содержимого";

/* error while converting query favorite data */
"Error while converting query favorite data" = "Ошибка преобразования данных избранного запроса";

/* error while converting session data */
"Error while converting session data" = "Ошибка при преобразовании данных сессии";

/* Error while deleting field */
"Error while deleting field" = "Ошибка при удалении поля";

/* Bundle Editor : Copy-Command-Error : Copying failed error message */
"Error while duplicating Bundle content." = "Ошибка при дублировании содержимого пакета.";

/* error while executing javascript bash command */
"Error while executing JavaScript BASH command" = "Ошибка выполнения JavaScript BASH команды";

/* error while fetching the optimized field type message */
"Error while fetching the optimized field type" = "Ошибка поиска оптимизированного типа поля";

/* error while flushing selected items message */
"Error while flushing selected items" = "Ошибка при сбросе выбранных элементов";

/* error while importing table message */
"Error while importing table" = "Ошибка при импорте таблицы";

/* Open Files : Bundle : Install-Error : error dialog title
 Open Files : Bundle : UUID : Error dialog title */
"Error while installing Bundle" = "Ошибка установки пакета";

/* error while installing color theme file */
"Error while installing color theme file" = "Ошибка при установке файла цветовой темы";

/* Bundle Editor : Trash-Bundle(s)-Error : error dialog title
 error while moving %@ to trash
 Open Files : Bundle : Already-Installed : Delete-Old-Error : Could not delete old bundle before installing new version. */
"Error while moving %@ to Trash." = "Ошибка при отправке %@ в корзину.";

/* error while optimizing selected items message */
"Error while optimizing selected items" = "Ошибка при оптимизации выбранных элементов";

/* error while parsing CREATE TABLE syntax */
"Error while parsing CREATE TABLE syntax" = "Ошибка разбора запроса CREATE TABLE";

/* error while reading connection data file */
"Error while reading connection data file" = "Ошибка чтения файла с данными для подключения";

/* error while reading data file */
"Error while reading data file" = "Ошибка при чтении файла данных";

/* error while repairing selected items message */
"Error while repairing selected items" = "Ошибка при восстановлении выбранных элементов";

/* Bundle Editor : Save-Bundle-Error : error dialog title */
"Error while saving the Bundle." = "Ошибка сохранения пакета.";

/* Bundle Editor : Save-and-Close-Error : error dialog title */
"Error while saving %@." = "Ошибка при сохранении %@.";

/* Errors title */
"Errors" = "Ошибки";

/* Mysql Help Viewer : Help Topic: Example section title */
"Example:" = "Пример:";

/* Bundle Editor : BLOB dropdown : 'exclude BLOB' item */
"exclude BLOB" = "исключая BLOB";

/* execution privilege label (Navigator) */
"Execution Privilege" = "Делегированные права";

/* execution privilege: %@ */
"execution privilege: %@" = "права: %@";

/* execution stopped message */
"Execution stopped!\n" = "Выполнение остановлено!\n";

/* export selected favorites menu item */
"Export Selected..." = "Экспортировать выбранное...";

/* text showing that the application is importing a supplied format */
"Exporting %@" = "Экспорт %@";

/* text showing that the application is exporting a Dot file */
"Exporting Dot File" = "Экспорт Dot файла";

/* text showing that the application is exporting SQL */
"Exporting SQL" = "Экспорт SQL";

/* extra label (Navigator) */
"Extra" = "Доп.";

/* table structure : indexes : delete index : no columns error : title */
"Failed to remove index '%@'" = "Не удалось удалить индекс '%@'";

/* fatal error */
"Fatal Error" = "Критическая ошибка";

/* export filename favorite name token */
"Favorite" = "Избранное";

/* favorites label */
"Favorites" = "Избранное";

/* favorites export error message */
"Favorites export error" = "Ошибка экспорта Избранного";

/* favorites import error message */
"Favorites import error" = "Ошибка импорта Избранного";

/* export label showing that the app is fetching data */
"Fetching data..." = "Получение данных...";

/* fetching database structure data in progress */
"fetching database structure data in progress" = "идёт загрузка структуры базы данных";

/* fetching database structure in progress */
"fetching database structure in progress" = "загрузка структуры базы данных";

/* fetching table data for completion in progress message */
"fetching table data…" = "получение данных таблицы…";

/* popup menuitem for field (showing only if disabled) */
"field" = "поле";

/* Field */
"Field" = "Поле";

/* Query result editing error - could not match row being edited uniquely */
"Field is not editable. Couldn't identify field origin unambiguously (%ld matches)." = "Поле не редактируемо. Невозможно однозначно определить его происхождение (%ld совпадений).";

/* field is not editable due to no table/database */
"Field is not editable. Field has no or multiple table or database origin(s)." = "Поле не является редактируемым. Поле не связано с одной или несколькими таблицами или базами данных.";

/* Custom Query result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload data, check encoding, or try to add\na primary key field or more fields\nin your SELECT statement for table '%@'\nto identify field origin unambiguously." = "Поле не редактируемо. Не найена соответствующая запись.\nПерезагрузите данные, проверить кодировку или попробуйте добавить первичный ключ к одному или нескольким полям в вашем SELECT запросе для таблицы '%@' для однозначного определения записей.";

/* Table Content result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload table, check the encoding, or try to add\na primary key field or more fields\nin the view declaration of '%@' to identify\nfield origin unambiguously." = "Поле не редактируемо. Не найена соответствующая запись.\nПерезагрузите таблицу, проверьте кодировку или попробуйте добавить первичный ключ к одному или нескольким полям в разделе Структура для '%@' для однозначного определения записей.";

/* error while reading data file */
"File couldn't be read." = "Файл не читается.";
"File couldn't be read: %@\n\nIt will be deleted." = "Файл не читается.: %@\n\nОн будет удалён.";

/* File read error title (Import Dialog) */
"File read error" = "Ошибка чтения файла";

/* All export files already exist explanatory text */
"Files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "Файлов с теми же именами уже существует в целевой папке. Их замена перезапишет текущее содержимое.";

/* filter label */
"Filter" = "Отфильтровать";

/* apply filter label */
"Apply Filter(s)" = "Применить фильтр(ы)";

/* filter tables menu item */
"Filter Tables" = "Фильтр таблиц";

/* export source */
"Filtered table content" = "Отфильтрованное содержимое таблицы";

/* filtering failed. please try again. tooltip */
"Filtering failed. Please try again." = "Фильтрация не удалась. Пожалуйста, попробуйте ещё раз.";

/* Filtering table task description */
"Filtering table..." = "Фильтрация таблицы...";

/* description for finished exporting notification */
"Finished exporting to %@" = "Экспорт в %@ завершён";

/* description for finished importing notification */
"Finished importing %@" = "Импорт %@ завершён";

/* FLUSH one or more tables - result title */
"Flush %@" = "Очистка %@";

/* flush selected items menu item */
"Flush Selected Items" = "Очистить выбранные элементы";

/* flush table menu item */
"Flush Table" = "Очистить таблицу";

/* flush table failed message */
"Flush table failed." = "Сбой очистки таблицы.";

/* flush view menu item */
"Flush View" = "Очистить представление";

/* title of panel when successfully flushed privs */
"Flushed Privileges" = "Привилегии перезагружены";

/* For BIT fields only “1” or “0” are allowed. */
"For BIT fields only “1” or “0” are allowed." = "Для BIT полей разрешено использовать только \"1\" или \"0\".";

/* force table deletion button text */
"Force delete (disables integrity checks)" = "Принудительно удалить (отключить проверки целостности)";

/* full text index menu item title */
"FULLTEXT" = "FULLTEXT";

/* function */
"function" = "функция";

/* header for function info pane */
"FUNCTION INFORMATION" = "О ФУНКЦИИ";

/* functions */
"functions" = "функции";

/* Bundle Editor : Scope dropdown : 'general' item
 general menu item label
 general preference pane name */
"General" = "Основные";

/* general preference pane tooltip */
"General Preferences" = "Основные настройки";

/* Bundle Editor : Outline View : 'General' item : tooltip */
"General Scope\ncommands will run application-wide" = "Общая область\nкоманды будут нацелены на всё приложение";

/* generating print document status message */
"Generating print document..." = "Создание документа для печати...";

/* export header generation time label */
"Generation Time" = "Время формирования";

/* Content Filter Manager : Filter Entry List: 'Global' Header
 Query Favorites : List : Section Heading : global query favorites */
"Global" = "Общие";

/* Query Favorites : List : Section Heading : global : tooltip */
"Globally stored favorites" = "Общие избранные";

/* Preferences : Notifications : status text : notification setup */
"Notifications are delivered via the Notification Center." = "Уведомления доставляются через центр уведомлений.";

/* Gzip compression export summary - within a sentence */
"Gzip compression" = "Gzip сжатие";

/* MySQL Help Viewer : Results list : Page title */
"Help topics for %@" = "Справки для %@";

/* hide console */
"Hide Console" = "Скрыть консоль";

/* hide navigator */
"Hide Navigator" = "Скрыть Навигатор";

/* hide tab bar */
"Hide Tab Bar" = "Скрыть панель вкладок";

/* Hide Toolbar menu item */
"Hide Toolbar" = "Скрыть панель инструментов";

/* export filename host token
 export header host label */
"Host" = "Хост";

/* description of double */
"IEEE 754 double-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 двойная точность с плавающей точкой. M - это максимальное количество цифр, из которых D может быть после десятичной точки. Примечание: Многие десятичные числа могут быть приблизительны только значениями с плавающей точкой. Смотрите DECIMAL, если требуется точный результат.";

/* description of float */
"IEEE 754 single-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 одноточное значение с плавающей точкой. M - это максимальное количество цифр, из которых D может быть после десятичной точки. Примечание: Многие десятичные числа могут быть приблизительны только значениями с плавающей точкой. Смотрите DECIMAL, если требуется точный результат.";

/* ignore button */
"Ignore" = "Пропустить";

/* ignore errors button */
"Ignore All Errors" = "Игнорировать все ошибки";

/* ignore all fields menu item */
"Ignore all Fields" = "Игнорировать все поля";

/* ignore field label */
"Ignore field" = "Игнорировать поле";

/* ignore field label */
"Ignore Field" = "Игнорировать поле";

/* import button */
"Import" = "Импорт";

/* import all fields menu item */
"Import all Fields" = "Импорт всех полей";

/* sql import : charset error alert : continue button */
"Import Anyway" = "Всё равно импортировать";

/* import cancelled message */
"Import cancelled!\n" = "Импорт отменён!\n";

/* Import Error title */
"Import Error" = "Ошибка импорта";

/* import field operator tooltip */
"Import field" = "Импорт поля";

/* import file does not exist message */
"Import file does not exist." = "Импортируемый файл не существует.";

/* Export file format cannot be imported warning */
"Import of the selected data is currently not supported." = "Импорт выбранных данных в настоящее время не поддерживается.";

/* SQL import progress text */
"Imported %@ of %@" = "Импортировано %1$@ из %2$@";

/* CSV import progress text where total size is unknown */
"Imported %@ of CSV data" = "Импортировано %@ из CSV данных";

/* SQL import progress text where total size is unknown */
"Imported %@ of SQL" = "Импортировано SQL: %@";

/* text showing that the application is importing CSV */
"Importing CSV" = "Импорт CSV";

/* text showing that the application is importing SQL */
"Importing SQL" = "Импорт SQL";

/* Bundle Editor : BLOB dropdown : 'include BLOB' item */
"include BLOB" = "включая BLOB";

/* include content table column tooltip */
"Include content" = "Включая содержимое";

/* sql import error message */
"Incompatible encoding in SQL file" = "Несовместимая кодировка в SQL файле";

/* header for blank info pane */
"INFORMATION" = "ИНФОРМАЦИЯ";

/* New Table Sheet : Table Collation Dropdown : Default inherited from database
 New Table Sheet : Table Encoding Dropdown : Default inherited from database */
"Inherit from database (%@)" = "Наследовать из базы данных (%@)";

/* initializing export label */
"Initializing..." = "Инициализация...";

/* Bundle Editor : Scope dropdown : 'input field' item
 input field menu item label */
"Input Field" = "Поле ввода";

/* input field  doesn't support insertion of snippets. */
"Input Field doesn't support insertion of snippets." = "Входное поле не поддерживает вставку сниппетов.";

/* input field is not editable. */
"Input Field is not editable." = "Поле ввода не редактируемо.";

/* Bundle Editor : Outline View : 'Input Field' item : tooltip */
"Input Field Scope\ncommands will run on each text input field" = "Область поля ввода\nкоманды будут нацелены на каждое поле ввода текста";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as snippet' item */
"Insert as Snippet" = "Вставить как сниппет";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as text' item */
"Insert as Text" = "Вставить как текст";

/* Bundle Editor : Outline View : 'BUNDLES' item : tooltip */
"Installed Bundles" = "Установленные пакеты";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog title */
"Installing Bundle" = "Установка пакета";

/* insufficient details message */
"Insufficient connection details" = "Недостаточно данных для подключения";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please enter at least the hostname." = "Недостаточно данных для установки соединения. Пожалуйста, введите по крайней мере имя хоста.";

/* insufficient SSH tunnel details informative message */
"Insufficient details provided to establish a connection. Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Недостаточно данных для установки соединения. Пожалуйста, введите имя хоста для туннеля SSH или отключите туннель SSH.";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please provide at least a host." = "Недостаточно подробных данных для установки соединения. Пожалуйста, предоставьте хост.";

/* Interpret data as: */
"Interpret data as:" = "Данные интерпретированы как:";

/* Invalid database very short status message */
"Invalid database" = "Повреждённая база данных";

/* export : import settings : file error title */
"Invalid file supplied!" = "Недопустимый файл!";

/* table content : editing : error message title when parsing as hex string failed */
"Invalid hexadecimal value" = "Неверное шестнадцатеричное значение";

/* is deterministic label (Navigator) */
"Is Deterministic" = "Детерминированный";

/* is nullable label (Navigator) */
"Is Nullable" = "NULL разрешён";

/* is updatable: %@ */
"is updatable: %@" = "обновляемо: %@";

/* items */
"items" = "элементы";

/* javascript exception */
"JavaScript Exception" = "Ошибка JavaScript";

/* javascript parsing error */
"JavaScript Parsing Error" = "Ошибка разбора JavaScript";

/* key label (Navigator) */
"Key" = "Ключ";

/* keyword label for color table (Prefs > Editor) */
"Keyword" = "Ключевое слово";

/* kill button */
"Kill" = "Завершить принудительно";

/* kill connection message */
"Kill connection?" = "Разорвать соединение?";

/* kill query message */
"Kill query?" = "Принудительно завершить запрос?";

/* Last Error Message */
"Last Error Message" = "Сообщение об ошибке";

/* Last Used entry in favorites menu */
"Last Used" = "Выполнялся";

/* range for json type */
"Limited to @@max_allowed_packet" = "Ограничено до @@max_allowed_packet";

/* Loading table task string */
"Loading %@..." = "Загрузка %@...";

/* Loading database task string */
"Loading database '%@'..." = "Загрузка базы данных '%@'...";

/* Loading history entry task desc */
"Loading history entry..." = "Загрузка записи истории...";

/* Loading table page task string */
"Loading page %lu..." = "Загрузка страницы %lu...";

/* Loading referece task string */
"Loading reference..." = "Загрузка связи...";

/* Loading table data string */
"Loading table data..." = "Загрузка данных таблицы...";

/* Low memory export summary */
"Low memory" = "Экономия памяти";

/* range of decimal */
"M (precision): Up to 65 digits\nD (scale): 0 to 30 digits" = "M (точность): до 65 цифр\nD (шкала): от 0 до 30 цифр";

/* range for blob type
 range for varbinary type */
"M: %@ to %@ bytes" = "М: от %1$@ до %2$@ байт";

/* range for text type
 range for varchar type */
"M: %@ to %@ characters" = "М: от %1$@ до %2$@ символов";

/* range for longtext type */
"M: %@ to %@ characters (4 GiB)" = "М: от %1$@ до %2$@ символов (4 ГиБ)";

/* range for binary type */
"M: 0 to 255 bytes" = "М: от 0 до 255 байт";

/* range for char type */
"M: 0 to 255 characters" = "М: от 0 до 255 символов";

/* range for bit type */
"M: 1 (default) to 64" = "М: от 1 (по умолчанию) до 64";

/* connection view : ssl : key file picker : wrong format error description */
"Make sure the file contains a RSA private key and is using PEM encoding." = "Убедитесь, что файл содержит закрытый ключ RSA и использует кодировку PEM.";

/* connection view : ssl : client cert picker : wrong format error description */
"Make sure the file contains a X.509 client certificate and is using PEM encoding." = "Убедитесь, что файл содержит сертификат клиента X.509 и использует PEM кодировку.";

/* match field menu item */
"Match Field" = "Поле матча";

/* Shown when user inserts too many arguments (ContentFilterManager) */
"Maximum number of arguments is 2!" = "Максимальное количество аргументов: 2!";

/* Maximum text length is set to %ld. */
"Maximum text length is set to %ld." = "Длина текста ограничена %ld.";

/* Maximum text length is set to %ld. Inserted text was truncated. */
"Maximum text length is set to %ld. Inserted text was truncated." = "Длина текста ограничена %ld. Вставленный текст был усечён.";

/* Maximum text length is set to %llu. */
"Maximum text length is set to %llu." = "Длина текста ограничена %llu.";

/* Maximum text length is set to %llu. Inserted text was truncated. */
"Maximum text length is set to %llu. Inserted text was truncated." = "Длина текста ограничена %llu. Вставленный текст был усечён.";

/* message column title */
"Message" = "Сообщение";

/* mgtemplateengine error */
"MGTemplateEngine Error" = "Ошибка MGTemplateEngine";

/* export filename date token */
"Month" = "Месяц";

/* multiple selection */
"multiple selection" = "множественный выбор";

/* MySQL connecting very short status message */
"MySQL connecting..." = "Подключение к MySQL...";

/* mysql error message */
"MySQL Error" = "Ошибка MySQL";

/* mysql help */
"MySQL Help" = "Справка по MySQL";

/* MySQL Help for Selection */
"MySQL Help for Selection" = "MySQL справка по выделенному";

/* MySQL Help for Word */
"MySQL Help for Word" = "MySQL справка по слову";

/* mysql help categories */
"MySQL Help – Categories" = "MySQL справка – Категории";

/* mysql said message */
"MySQL said:" = "MySQL сообщил:";

/* shutdown server : error dialog : message */
"MySQL said:\n%@" = "MySQL сообщил:\n%@";

/* message of panel when error while adding row to db */
"MySQL said:\n\n%@" = "MySQL сообщил:\n\n%@";

/* Preferences : Themes : Initial filename for 'Export' */
"MyTheme" = "МояTема";

/* network preference pane name */
"Network" = "Сеть";

/* network preference pane tooltip */
"Network Preferences" = "Настройки сети";

/* file preference pane name */
"Files" = "Файлы";

/* file preference pane tooltip */
"File Preferences" = "Настройки файлов";

/* Bundle Editor : Default name for new bundle in the list on the left */
"New Bundle" = "Новый пакет";

/* new column name placeholder string */
"New Column Name" = "Имя нового столбца";

/* new favorite name */
"New Favorite" = "Новое избранное";

/* Content Filter Manager : Initial name for new filter */
"New Filter" = "Новый фильтр";

/* new folder placeholder name */
"New Folder" = "Новая папка";

/* Bundle Editor : Default name for a new bundle in the menu */
"New Name" = "Новое имя";

/* new table menu item */
"New Table" = "Новая таблица";

/* error that no color theme found */
"No color theme data found." = "Данные цветовой темы не найдены.";

/* No compression export summary - within a sentence */
"no compression" = "без сжатия";

/* no connection available message */
"No connection available" = "Нет доступного подключения";

/* no connection data found */
"No connection data found." = "Данные для подключения не найдены.";

/* No content filters were found in file to import (ContentFilterManager) */
"No content filters found." = "Фильтры контента не найдены.";

/* no data found */
"No data found." = "Данные не найдены.";

/* No errors title */
"No errors" = "Без ошибок";

/* No favorites entry in favorites menu */
"No Favorties" = "Нет избранных";

/* All export files creation error title */
"No files could be created" = "Файлы не могут быть созданы";

/* no item found message */
"No item found" = "Элементы не найдены";

/* SSH tunnel could not be created because no local port could be allocated */
"No local port could be allocated for the SSH Tunnel." = "Ни один локальный порт не может быть выделен для SSH туннеля.";

/* header for no matches in filtered list */
"NO MATCHES" = "НЕТ СОВПАДЕНИЙ";

/* no optimized field type found. message */
"No optimized field type found." = "Не найден оптимизированный тип поля.";

/* error that no query favorites found */
"No query favorites found." = "Не найдено избранных запросов.";

/* Mysql Help Viewer : Search : No results */
"No results found." = "Ничего не найдено.";

/* Bundle Editor : Fallback Input source dropdown : 'none' item
 Bundle Editor : Scope=? : ? dropdown: 'None' item
 Bundle Editor : Scope=Data-Table : Input source dropdown: 'none' item
 Bundle Editor : Scope=Data-Table : Output dropdown : 'none' item
 Bundle Editor : Scope=Data-Table : Trigger dropdown : 'none' item
 Bundle Editor : Scope=Field : Input source dropdown: 'None' item
 Bundle Editor : Scope=Field : Output dropdown : 'none' item
 Bundle Editor : Scope=Field : Trigger dropdown : 'none' item
 Bundle Editor : Scope=General : Input source dropdown: 'None' item
 Bundle Editor : Scope=General : Output dropdown : 'none' item
 Bundle Editor : Scope=General : Trigger dropdown : 'none' item
 compression: none */
"None" = "Нет";

/* not available label */
"Not available" = "Недоступно";

/* Argument count (ContentFilterManager) */
"Number of arguments: %lu" = "Количество аргументов: %lu";

/* numeric label for color table (Prefs > Editor) */
"Numeric" = "Числа";

/* Bundle Editor : Copy-Command-Error : OK button
 Bundle Editor : Save-and-Close-Error : OK button
 Bundle Editor : Save-Bundle-Error : OK button
 Bundle Editor : Trash-Bundle(s)-Error : OK button
 OK
 OK button
 Open Files : Bundle : Already-Installed : Delete-Old-Error : OK button
 Open Files : Bundle : Install-Error : OK button
 Open Files : Bundle : UUID : OK button
 Preferences : Network : Custom SSH client : warning dialog : accept button */
"OK" = "ОК";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+1 rows instead of n selected were deleted. */
"One additional row was removed!" = "Удалена одна дополнительная строка!";

/* Table Content : Remove Row : Result : Too Few : Part 1 : Only n-1 of n selected rows were deleted. */
"One row was not removed." = "Одна строка не удалилась.";

/* Only one dragged item allowed. */
"Only one dragged item allowed." = "Допускается только один перетаскиваемый элемент.";

/* partial copy database support message */
"Only Partially Supported" = "Только частичная поддержка";

/* open function in new table title */
"Open Function in New Tab" = "Открыть функцию в новой вкладке";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Function in New Window" = "Открыть функцию в новом окне";

/* open connection in new tab context menu item */
"Open in New Tab" = "Открыть в новой вкладке";

/* menu item open in new window */
"Open in New Window" = "Открыть в новом окне";

/* open procedure in new table title */
"Open Procedure in New Tab" = "Открыть процедуру в новой вкладке";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Procedure in New Window" = "Открыть процедуру в новом окне";

/* open table in new tab title
 open table in new table title */
"Open Table in New Tab" = "Открыть таблицу в новой вкладке";

/* Table List : Context Menu : Duplicate connection to new window
 Table List : Gear Menu : Duplicate connection to new window */
"Open Table in New Window" = "Открыть таблицу в новом окне";

/* open view in new tab title
 open view in new table title */
"Open View in New Tab" = "Открыть представление в новой вкладке";

/* Tables List : Context Menu : Duplicate connection to new window
 Tables List : Gear Menu : Duplicate connection to new window */
"Open View in New Window" = "Открыть представление в новом окне";

/* menu item open %@ in new window */
"Open %@ in New Window" = "Открыть %@ в новом окне";

/* OPTIMIZE one or more tables - result title */
"Optimize %@" = "Оптимизировать %@";

/* optimize selected items menu item */
"Optimize Selected Items" = "Оптимизировать выбранные элементы";

/* optimize table menu item */
"Optimize Table" = "Оптимизировать таблицу";

/* optimize table failed message */
"Optimize table failed." = "Не удалось оптимизировать таблицу.";

/* Optimized type for field %@ */
"Optimized type for field '%@'" = "Оптимизированный тип поля '%@'";

/* optional placeholder string */
"optional" = "необязательно";

/* Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed. */
"Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed." = "Не удается интерпретировать данный параметр. Разрешена только строка или массив (с двумя элементами).";

/* Permission Denied */
"Permission Denied" = "Доступ запрещён";

/* please choose a favorite connection view label */
"Please choose a favorite" = "Пожалуйста, выберите избранное";

/* message of panel when ssh details are incomplete */
"Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Пожалуйста, введите имя хоста для SSH туннеля или отключите SSH туннель.";

/* Please enter the password */
"Please enter the password for ‘%@’:" = "Пожалуйста, введите пароль для «%@»:";

/* print button */
"Print" = "Напечатать";

/* print page menu item title */
"Print Page…" = "Напечатать страницу…";

/* privileges label (Navigator) */
"Privileges" = "Права";

/* procedure */
"procedure" = "процедура";

/* header for procedure info pane */
"PROCEDURE INFORMATION" = "О ПРОЦЕДУРЕ";

/* procedures */
"procedures" = "процедуры";

/* proceed button */
"Proceed" = "Продолжить";

/* header for procs & funcs list */
"PROCS & FUNCS" = "ПРОЦЕДУРЫ И ФУНКЦИИ";

/* toolbar item label for switching to the Run Query tab */
"Query" = "Запрос";

/* query background label for color table (Prefs > Editor) */
"Query Background" = "Фон запроса";

/* Query cancelled error */
"Query cancelled." = "Запрос отменён.";

/* Query cancel by resetting connection error */
"Query cancelled.  Please note that to cancel the query the connection had to be reset; transactions and connection variables were reset." = "Запрос отменен. Пожалуйста, обратите внимание, что для отмены запроса соединение должно быть сброшено; переменные подключения были сброшены.";

/* query editor preference pane name */
"Query Editor" = "Редактор запросов";

/* query editor preference pane tooltip */
"Query Editor Preferences" = "Настройки редактора запроса";

/* query logging currently disabled label
 query logging disabled label */
"Query logging is currently disabled" = "Журнал запросов в настоящее время отключён";

/* query result print heading */
"Query Result" = "Результат запроса";

/* export source */
"Query results" = "Результаты запроса";

/* Query Status */
"Query Status" = "Статус запроса";

/* table status : row count query failed : error title */
"Querying row count failed" = "Не удалось запросить количество строк";

/* Quick connect item label */
"Quick Connect" = "Быстрое подключение";

/* quote label for color table (Prefs > Editor) */
"Quote" = "Кавычки";

/* databsse rename unsupported informative message */
"Renaming the database '%@' is currently unsupported as it contains objects other than tables (i.e. views, procedures, functions, etc.).\n\nIf you would like to rename a database please use the 'Duplicate Database', move any non-table objects manually then drop the old database." = "Переименование базы данных '%@' в настоящее время не поддерживается, поскольку она содержит объекты, отличные от таблиц (например, представления, процедуры, функции и т.д.).\n\nЕсли вы хотите переименовать базу данных, пожалуйста, используйте функцию 'Дублировать базу данных', затем переместите нетабличные объекты вручную, а потом удалите старую базу данных.";

/* range for serial type */
"Range: %@ to %@" = "Диапазон: от %1$@ до %2$@";

/* range for time type */
"Range: -838:59:59.0 to 838:59:59.0\nF (precision): 0 (1s) to 6 (1µs)" = "Диапазон: от -838:59:59.0 до 838:59:59:59.0\nF (точность): 0 (1s) до 6 (1μs)";

/* range for year type */
"Range: 0000, 1901 to 2155" = "Диапазон: 0000, 1901–2155";

/* range for set type */
"Range: 1 to 64 members\n1, 2, 3, 4 or 8 bytes storage" = "Диапазон: от 1 до 64 участников\n1, 2, 3, 4 или 8 байт хранения";

/* range for datetime type */
"Range: 1000-01-01 00:00:00.0 to 9999-12-31 23:59:59.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Диапазон: с 1000-01-01 00:00.0 по 9999-12-31 23:59:59.999999\nF (точность): 0 (1s) до 6 (1μs)";

/* range for date type */
"Range: 1000-01-01 to 9999-12-31" = "Диапазон: с 1000-01-01 по 9999-12-31";

/* range for timestamp type */
"Range: 1970-01-01 00:00:01.0 to 2038-01-19 03:14:07.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Диапазон: с 1970-01-01 00:00:01.0 по 2038-01-19 03:14:07.999999\nF (точность): 0 (1s) до 6 (1μs)";

/* text showing that app is reading dump */
"Reading..." = "Чтение...";

/* menu item to refresh databases */
"Refresh Databases" = "Обновить базы данных";

/* refresh list menu item */
"Refresh List" = "Обновить список";

/* toolbar item label for switching to the Table Relations tab */
"Relations" = "Связи";

/* Relations tab subtitle showing table name */
"Relations for table: %@" = "Связи таблицы: %@";

/* reload bundles menu item label */
"Reload Bundles" = "Перезагрузить пакеты";

/* Reloading data task description */
"Reloading data..." = "Перезагрузка данных...";

/* Reloading table task string */
"Reloading..." = "Перезагрузка...";

/* remote error */
"Remote Error" = "Внешняя ошибка";

/* Bundle Editor : Remove-Bundle: remove button
 remove button */
"Remove" = "Удалить";

/* remove all button */
"Remove All" = "Удалить все";

/* remove all query favorites message */
"Remove all query favorites?" = "Удалить все избранные запросы?";

/* Bundle Editor : Remove-Bundle: remove dialog title */
"Remove selected Bundle?" = "Удалить выбранный пакет?";

/* remove selected content filters message */
"Remove selected content filters?" = "Удалить выбранные фильтры содержимого?";

/* remove selected query favorites message */
"Remove selected query favorites?" = "Удалить выбранные избранные запросы?";

/* removing field task status message */
"Removing field..." = "Удаление поля...";

/* removing index task status message */
"Removing index..." = "Удаление индекса...";

/* rename database message */
"Rename database '%@' to:" = "Переименовать базу данных '%@' в:";

/* rename func menu title */
"Rename Function..." = "Переименовать функцию...";

/* rename proc menu title */
"Rename Procedure..." = "Переименовать процедуру...";

/* rename table menu title */
"Rename Table..." = "Переименовать таблицу...";

/* rename view menu title */
"Rename View..." = "Переименовать представление...";

/* REPAIR one or more tables - result title */
"Repair %@" = "Исправление %@";

/* repair selected items menu item */
"Repair Selected Items" = "Исправить выбранные элементы";

/* repair table menu item */
"Repair Table" = "Исправить таблицу";

/* repair table failed message */
"Repair table failed." = "Не удалось исправить таблицу.";

/* Replace button */
"Replace" = "Заменить";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace entire content' item */
"Replace Entire Content" = "Заменить всё содержимое";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace selection' item */
"Replace Selection" = "Заменить выделенное";

/* description of year */
"Represents a 4 digit year value, stored as 1 byte. Invalid values are converted to 0000 and two digit values 0 to 69 will be converted to years 2000 to 2069, resp. values 70 to 99 to years 1970 to 1999.\nThe YEAR(2) type was removed in MySQL 5.7.5." = "Представляет собой 4-значное значение года, хранимое как 1 байт. Неверные значения конвертируются в 0000 и две цифры от 0 до 69 будут конвертированы в 2069. значения от 70 до 99 до лет 1970 до 1999 г.\nТип YEAR(2) был удален в MySQL 5.7.5.";

/* description of multilinestring */
"Represents a collection of LineStrings." = "Представляет собой коллекцию LineStrings.";

/* description of geometrycollection */
"Represents a collection of objects of any other single- or multi-valued spatial type. The only restriction being, that all objects must share a common coordinate system." = "Представляет собой коллекцию объектов любого другого однозначного или многозначного пространственного типа. Единственное ограничение заключается в том, что все объекты должны иметь общую систему координат.";

/* description of multipolygon */
"Represents a collection of Polygons. The Polygons making up the MultiPolygon must not intersect." = "Представляет собой коллекцию полигонов. Полигоны, составляющие многополигон, не должны пересекаться.";

/* description of multipoint */
"Represents a set of Points without specifying any kind of relation and/or order between them." = "Представляет собой набор точек без указания каких-либо отношений и/или порядка между ними.";

/* description of point */
"Represents a single location in coordinate space using X and Y coordinates. The point is zero-dimensional." = "Представляет собой одно местоположение в координатном пространстве с использованием координат X и Y. Точка равна нулевому измерению.";

/* description of linestring */
"Represents an ordered set of coordinates where each consecutive pair of two points is connected by a straight line." = "Представляет собой упорядоченный набор координат, где каждая последовательная пара двух точек соединяется прямой линией.";

/* required placeholder string */
"required" = "требуется";

/* description of smallint */
"Requires 2 bytes storage space. M is the optional display width and does not affect the possible value range." = "Требует 2 байта памяти места. M является необязательной шириной дисплея и не влияет на диапазон возможных значений.";

/* description of mediumint */
"Requires 3 bytes storage space. M is the optional display width and does not affect the possible value range." = "Требует 3 байт памяти места. M является дополнительной шириной дисплея и не влияет на диапазон возможных значений.";

/* description of int */
"Requires 4 bytes storage space. M is the optional display width and does not affect the possible value range. INTEGER is an alias to this type." = "Требуется 4 байта пространства памяти. M является дополнительной шириной дисплея и не влияет на диапазон возможных значений. INTEGER является псевдонимом для данного типа.";

/* description of bigint */
"Requires 8 bytes storage space. M is the optional display width and does not affect the possible value range. Note: Arithmetic operations might fail for large numbers." = "Требует 8 байт памяти места. M является дополнительной шириной дисплея и не влияет на диапазон возможных значений. Примечание: Арифметические операции могут быть не выполнены для больших чисел.";

/* reset auto_increment after deletion of all rows message */
"Reset AUTO_INCREMENT after deletion\n(only for Delete ALL ROWS IN TABLE)?" = "Сбросить AUTO_INCREMENT после удаления\n(только при условии удаления всех строк из таблицы)?";

/* delete selected row button */
"Delete Selected Row" = "Удалить выбранную строку";

/* delete selected rows button */
"Delete Selected Rows" = "Удалить выбранные строки";

/* delete all rows in table button */
"Delete ALL ROWS IN TABLE" = "Удалить ALL ROWS IN TABLE";

/* Restoring session task description */
"Restoring session..." = "Восстановление сессии...";

/* return type label (Navigator) */
"Return Type" = "Возвращает";

/* return type: %@ */
"return type: %@" = "возвращает тип: %@";

/* singular word for row */
"row" = "строка";

/* plural word for rows */
"rows" = "строки";

/* text showing how many rows are in the limited filter match */
"Rows %@ - %@ from filtered matches" = "Строки %1$@ - %2$@ из отфильтрованных данных";

/* text showing how many rows are in the limited result */
"Rows %@ - %@ of %@%@ from table" = "Строки %1$@ - %2$@ из %3$@%4$@ из таблицы";

/* Table Info Section : number of rows (exact value) */
"rows: %@" = "строк: %@";

/* Table Info Section : number of rows (estimated value) */
"rows: ~%@" = "строк: ~%@";

/* run all button */
"Run All" = "Выполнить все";

/* Run All menu item title */
"Run All Queries" = "Выполнить все запросы";

/* Title of button to run current query in custom query view */
"Run Current" = "Выполнить текущий";

/* Title of action menu item to run current query in custom query view */
"Run Current Query" = "Выполнить текущий запрос";

/* toolbar item label for switching to the Run Query tab */
"Run Custom Query" = "Выполнить пользовательский запрос";

/* Title of button to run query just before text caret in custom query view */
"Run Previous" = "Выполнить предыдущий";

/* Title of action menu item to run query just before text caret in custom query view */
"Run Previous Query" = "Выполнить предыдущий запрос";

/* Title of action menu item to run selected text in custom query view */
"Run Selected Text" = "Выполнить выбранный текст";

/* Title of button to run selected text in custom query view */
"Run Selection" = "Выполнить выделенное";

/* Running multiple queries string */
"Running query %i of %lu..." = "Выполняется запрос %1$i из %2$lu...";

/* Running multiple queries string */
"Running query %ld of %lu..." = "Выполняется запрос %1$ld из %2$lu...";

/* Running single query string */
"Running query..." = "Выполнение запроса...";

/* Save trigger button label */
"Save" = "Сохранить";

/* Save All to Favorites */
"Save All to Favorites" = "Сохранить все в Избранное";

/* save as button title */
"Save As..." = "Сохранить как...";

/* Bundle Editor : BLOB dropdown : 'save BLOB as dat file' item */
"save BLOB as dat file" = "сохранить BLOB как dat файл";

/* Bundle Editor : BLOB dropdown : 'save BLOB as image file' item */
"save BLOB as image file" = "сохранить BLOB как файл изображения";

/* Save Current Query to Favorites */
"Save Current Query to Favorites" = "Сохранить текущий запрос в Избранное";

/* save page as menu item title */
"Save Page As…" = "Сохранить страницу как…";

/* Save Queries… */
"Save Queries…" = "Сохранить запросы…";

/* Save Query… */
"Save Query…" = "Сохранить запрос…";

/* Save Selection to Favorites */
"Save Selection to Favorites" = "Сохранить выделенное в Избранное";

/* save view as button title */
"Save View As..." = "Сохранить представление как...";

/* schema path header for completion tooltip */
"Schema path:" = "Путь схемы:";

/* Search in MySQL Documentation */
"Search in MySQL Documentation" = "Искать в документации MySQL";

/* Search in MySQL Help */
"Search in MySQL Help" = "Поиск по MySQL справке";

/* Select Active Query */
"Select Active Query" = "Выделить активный запрос";

/* toolbar item for selecting a db */
"Select Database" = "Выберите базу данных";

/* selected items */
"selected items" = "выбранные элементы";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as comma-separated' item */
"Selected Rows (CSV)" = "Выбранные строки (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as SQL' item */
"Selected Rows (SQL)" = "Выбранные строки (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as tab-separated' item */
"Selected Rows (TSV)" = "Выбранные строки (TSV)";

/* Bundle Editor : Scope=Field : Input source dropdown: 'selected text' item */
"Selected Text" = "Выбранный текст";

/* selection label for color table (Prefs > Editor) */
"Selection" = "Выделенное";

/* table structure : indexes : delete index : no columns error : description */
"Sequel Ace could not find any columns belonging to this index. Maybe it has been removed already?" = "Последовательный Туз не мог найти колонки, относящиеся к этому индексу. Может быть, он уже был удален?";

/* Preferences : Network : Custom SSH client : warning dialog message */
"Sequel Ace only supports and is tested with the default OpenSSH client versions included with macOS. Using different clients might cause connection issues, security risks or not work at all.\n\nPlease be aware, that we cannot provide support for such configurations." = "Последовательный Ace поддерживает и тестируется с версиями клиента OpenSSH по умолчанию, включенными в macOS. Использование различных клиентов может вызвать проблемы связи, риски безопасности или вообще не работать.\n\nПожалуйста, имейте в виду, что мы не можем оказывать поддержку таким конфигурациям.";

/* sequelace URL scheme command not supported. */
"sequelace URL scheme command not supported." = "Команда Sequel Ace URL схемы не поддерживается.";

/* sequelace url Scheme Error */
"sequelace URL Scheme Error" = "Ошибка URL схемы Sequel Ace";

/* Table Structure : Collation dropdown : 'item is the same as the collation of server' marker
 Table Structure : Encoding dropdown : 'item is server default' marker */
"Server" = "Сервер";

/* Add Database : Charset dropdown : default item ($1 = charset name)
 Add Database : Collation dropdown : default item ($1 = collation name) */
"Server Default (%@)" = "Настройки сервера по умолчанию (%@)";

/* server processes window title (var = hostname) */
"Server Processes on %@" = "Процессы сервера %@";

/* Initial filename for 'Save session' file */
"Session" = "Сессия";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html' item */
"Show as HTML" = "Показать как HTML";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html tooltip' item */
"Show as HTML Tooltip" = "Показывать как HTML-подсказку";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as text tooltip' item */
"Show as Text Tooltip" = "Показывать как текстовую подсказку";

/* show console */
"Show Console" = "Открыть консоль";

/* show create func syntax menu item */
"Show Create Function Syntax..." = "Показать запрос создания функции...";

/* show create proc syntax menu item */
"Show Create Procedure Syntax..." = "Показать запрос создания процедуры...";

/* show create syntaxes menu item */
"Show Create Syntaxes..." = "Показать запросы создания...";

/* show create table syntax menu item */
"Show Create Table Syntax..." = "Показать запрос создания таблицы...";

/* show create view syntax menu item */
"Show Create View Syntax..." = "Показать запрос создания представления...";

/* Show detail button */
"Show Detail" = "Показать сведения";

/* MySQL Help Viewer : Results list : Link tooltip */
"Show MySQL help for %@" = "Показать MySQL справку для %@";

/* show navigator */
"Show Navigator" = "Показать Навигатор";

/* show tab bar */
"Show Tab Bar" = "Показывать панель вкладок";

/* tooltip for toolbar item for show console */
"Show the console which shows all MySQL commands performed by Sequel Ace" = "Показать консоль, которая показывает все команды MySQL, выполненные Sequel Ace";

/* Show Toolbar menu item */
"Show Toolbar" = "Показать панель инструментов";

/* filtered item count */
"Showing %lu of %lu processes" = "Показано %1$lu из %2$lu процессов";

/* shutdown server : confirmation dialog : shutdown button */
"Shutdown" = "Выключить";

/* shutdown server : error dialog : title */
"Shutdown failed!" = "Сбой выключения!";

/* range of integer types */
"Signed: %@ to %@\nUnsigned: %@ to %@" = "Со знаком: %1$@ в %2$@\nБез знака: %3$@ в %4$@";

/* Table Info Section : table size on disk */
"size: %@" = "размер: %@";

/* skip existing button */
"Skip existing" = "Пропустить существующий";

/* skip problems button */
"Skip problems" = "Проигнорировать проблемы";

/* beta build label */
"Beta Build" = "Предварительная версия";

/* socket connection failed title */
"Socket connection failed!" = "Ошибка соединения через сокет!";

/* socket not found title */
"Socket not found!" = "Сокет не найден!";

/* Some export folders not writable explanatory text */
"Some of the target export folders are not writable.  Please select a new export location and try again." = "Некоторые папки для экспорта недоступны для записи. Пожалуйста, выберите новое место для экспорта и повторите попытку.";

/* Some export folders missing explanatory text */
"Some of the target export folders no longer exist.  Please select a new export location and try again." = "Некоторые папки для экспорта больше не существуют. Пожалуйста, выберите новое место для экспорта и повторите попытку.";

/* Sorting table task description */
"Sorting table..." = "Сортировка таблицы...";

/* spatial index menu item title */
"SPATIAL" = "SPATIAL";

/* sql data access label (Navigator) */
"SQL Data Access" = "Доступ к данным SQL";


/* Connection Secured via SSL information text */
"Connection Secured via SSL" = "Соединение защищено через SSL";

/* SSH connected titlebar marker */
"SSH Connected" = "SSH подключён";

/* SSH connecting very short status message */
"SSH connecting..." = "Подключение SSH...";

/* SSH connecting titlebar marker */
"SSH Connecting…" = "Подключение SSH…";

/* SSH connection failed title */
"SSH connection failed!" = "Сбой SSH соединения!";

/* SSH disconnected titlebar marker */
"SSH Disconnected" = "SSH отключён";

/* SSH key check error */
"SSH Key not found" = "SSH ключ не найден";

/* title when ssh tunnel port forwarding failed */
"SSH port forwarding failed" = "Ошибка перенаправления SSH порта";

/* SSL certificate authority file check error */
"SSL Certificate Authority File not found" = "Файл центра сертификации SSL не найден";

/* SSL certificate file check error */
"SSL Certificate File not found" = "Файл сертификата SSL не найден";

/* SSL requested but not used title */
"SSL connection not established" = "SSL соединение не установлено";

/* SSL key file check error */
"SSL Key File not found" = "Файл SSL ключа не найден";

/* Standard memory export summary */
"Standard memory" = "Стандартное использование памяти";

/* started */
"started" = "запущено";

/* status file for sequelace url scheme command couldn't be written error message */
"Status file for sequelace url scheme command couldn't be written!" = "Файл состояния для команды url sequelace не может быть записан!";

/* stop button */
"Stop" = "Стоп";

/* Stop queries string */
"Stop queries" = "Остановить запросы";

/* Stop query string */
"Stop query" = "Остановить запрос";

/* description of timestamp */
"Stores a date and time of day as seconds since the beginning of the UNIX epoch (1970-01-01 00:00:00). The values displayed/stored are affected by the connection's @@time_zone setting.\nThe representation is the same as for DATETIME. Invalid values, as well as \"second zero\", are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F. Some additional rules may apply." = "Хранит дату и время в виде секунд, прошедших с начала UNIX эпохи (01.01.1970 00:00). Отображаемые/сохраненные значения зависят от настройки часового пояса (@@time_zone) для соедин ения.\nОтображается совпадает с DATETIME. Недопустимые значения, а также «второй ноль» конвертируются в 0000-00-00 00:00:00.0. Доли секунды были добавлены в MySQL 5.6.4 и их точность может настраиваться в диапазоне от 0 (секунды) до 6 (микросекунды). Также могут применяться некоторые дополнительные правила.";

/* description of datetime */
"Stores a date and time of day. The representation is YYYY-MM-DD HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Хранит дату и время. Представление ГГГГ-ММ-ДД ЧЧ:ММ:СС[.|*], может содержать дроби секунды. Это значение не зависит от настройки часового пояса. Неверные значения преобразовываются в 0000-0000:00:00.0. В MySQL 5.6.4 были добавлены дробные секунды с точностью до микросекунд (6).";

/* description of date */
"Stores a date without time information. The representation is YYYY-MM-DD. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00." = "Хранит дату без информации о времени. Представление ГГГ-ММ-ДД. На значение не влияет ни один часовой пояс. Неправильные значения преобразуются в 0000-00.";

/* description of time */
"Stores a time of day, duration or time interval. The representation is HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 00:00:00. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Хранит время, продолжительность или интервал времени. Представление ЧЧ:ММ:СС[.|*] может содержать дроби секунды. Это значение не зависит от настройки часового пояса. Неверные значения преобразовываются в 00:00:00. В MySQL 5.6.4 были добавлены дробные секунды с точностью до микросекунд (6).";

/* toolbar item label for switching to the Table Structure tab */
"Structure" = "Структура";

/* successfully analyzed all selected items message */
"Successfully analyzed all selected items." = "Все выбранные элементы успешно проанализированы.";

/* analyze table successfully passed message */
"Successfully analyzed table." = "Таблица успешно проанализирована.";

/* successfully flushed all selected items message */
"Successfully flushed all selected items." = "Все выбранные элементы успешно очищены.";

/* message of panel when successfully flushed privs */
"Successfully flushed privileges." = "Привилегии успешно обновлены.";

/* flush table successfully passed message */
"Successfully flushed table." = "Таблица успешно очищена.";

/* successfully optimized all selected items message */
"Successfully optimized all selected items." = "Выбранные выбранные элементы успешно оптимизированы.";

/* optimize table successfully passed message */
"Successfully optimized table." = "Таблица успешно оптимизирована.";

/* successfully repaired all selected items message */
"Successfully repaired all selected items." = "Все выбранные элементы успешно исправлены.";

/* repair table successfully passed message */
"Successfully repaired table." = "Таблица исправлена.";

/* tooltip for toolbar item for switching to the Run Query tab */
"Switch to the Run Query tab" = "Переключиться на вкладку пользовательских запросов";

/* tooltip for toolbar item for switching to the Table Content tab */
"Switch to the Table Content tab" = "Переключиться на вкладку содержимого таблицы";

/* tooltip for toolbar item for switching to the Table Info tab */
"Switch to the Table Info tab" = "Переключиться на вкладку сводки таблицы";

/* tooltip for toolbar item for switching to the Table Relations tab */
"Switch to the Table Relations tab" = "Переключиться на вкладку связей таблицы";

/* tooltip for toolbar item for switching to the Table Structure tab */
"Switch to the Table Structure tab" = "Переключиться на вкладку структуры таблицы";

/* tooltip for toolbar item for switching to the Table Triggers tab */
"Switch to the Table Triggers tab" = "Переключиться на вкладку триггеров таблицы";

/* tooltip for toolbar item for switching to the User Manager tab */
"Switch to the User Manager tab" = "Переключиться на вкладку управления пользователями";

/* description for table syntax copied notification */
"Syntax for %@ table copied" = "Запрос создания для таблицы %@ скопирован";

/* table */
"table" = "таблица";

/* csv export table heading
 table
 Table Structure : Collation dropdown : 'item is the same as the collation of table' marker
 Table Structure : Encoding dropdown : 'item is table default' marker */
"Table" = "Таблица";

/* export label showing that the app is fetching data for a specific table */
"Table %lu of %lu (%@): Fetching data..." = "Таблица %1$lu из %2$lu (%3$@): Получение данных...";

/* export label showing app is fetching relations data for a specific table */
"Table %lu of %lu (%@): Fetching relations data..." = "Таблица %1$lu из %2$lu (%3$@): Получение данных о связях...";

/* export label showing app if writing data for a specific table */
"Table %lu of %lu (%@): Writing data..." = "Таблица %1$lu из %2$lu (%3$@): Запись данных...";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'table changed' item */
"Table changed" = "Таблица изменена";

/* table checksum message */
"Table checksum" = "Контрольная сумма таблицы";

/* table checksum: %@ */
"Table checksum: %@" = "Контрольная сумма таблицы: %@";

/* table content print heading */
"Table Content" = "Содержимое таблицы";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as comma-separated' item */
"Table Content (CSV)" = "Содержимое таблицы (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as SQL' item */
"Table Content (SQL)" = "Содержимое таблицы (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as tab-separated' item */
"Table Content (TSV)" = "Содержимое таблицы (TSV)";

/* toolbar item for navigation history */
"Table History" = "История таблиц";

/* toolbar item label for switching to the Table Info tab */
"Table Info" = "Сводка";

/* header for table info pane */
"TABLE INFORMATION" = "О ТАБЛИЦЕ";

/* table information print heading */
"Table Information" = "Информация о таблице";

/* message of panel when no name is given for table */
"Table must have a name." = "У таблицы должно быть имя.";

/* general preference pane tooltip */
"Table Preferences" = "Настройки таблиц";

/* toolbar item label for switching to the Table Relations tab */
"Table Relations" = "Связи таблицы";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table row changed' item */
"Table Row changed" = "Изменена строка таблицы";

/* table structure print heading */
"Table Structure" = "Структура таблицы";

/* toolbar item label for switching to the Table Triggers tab */
"Table Triggers" = "Триггеры таблицы";

/* Add Relation sheet title, showing table name */
"Table: %@" = "Таблица: %@";

/* tables preference pane name */
"Tables" = "Таблицы";

/* tables */
"tables" = "таблицы";

/* header for table list */
"TABLES" = "ТАБЛИЦЫ";

/* header for table & views list */
"TABLES & VIEWS" = "ТАБЛИЦЫ и ПРЕДСТАВЛЕНИЯ";

/* Connection test very short status message */
"Testing connection..." = "Проверка соединения...";

/* MySQL connection test very short status message */
"Testing MySQL..." = "Проверка MySQL...";

/* SSH testing very short status message */
"Testing SSH..." = "Проверка SSH...";

/* text label for color table (Prefs > Editor) */
"Text" = "Текст";

/* Text is too long. Maximum text length is set to %llu. */
"Text is too long. Maximum text length is set to %llu." = "Текст слишком длинный. Длина ограничена %llu.";

/* Release notes dialog title thanking user for upgrade */
"Thanks for updating Sequel Ace!" = "Спасибо за обновление Sequel Ace!";

/* Open Files : Bundle : Install-Error : Destination path already exists error dialog message */
"The Bundle ‘%@’ already exists." = "Пакет ‘%@’ уже существует.";

/* Open Files : Bundle: UUID : UUID-Attribute is missing in bundle's command.plist file */
"The Bundle ‘%@’ has no UUID which is necessary to identify installed Bundles." = "У пакета ‘%@’ нет UUID, который необходим для идентификации установленных пакетов.";

"‘%@’ Bundle contains legacy components" = "‘%@’ пакет содержит устаревшие компоненты";

"In these files:\n\n%@\n\nDo you still want to install the bundle?" = "В этих файлах:\n\n%@\n\nВы всё ещё хотите установить пакет?";

/* message while reading a spf file which matches non-supported formats. */
"The chosen file %@ contains ‘%@’ data." = "Выбранный файл “%1$@” содержит данные ‘%2$@’.";

/* the color theme ‘%@’ already exists. */
"The color theme ‘%@’ already exists." = "Цветовая тема ‘%@` уже существует.";

/* the connection is busy. please wait and try again tooltip */
"The connection is busy. Please wait and try again." = "Соединение занято. Пожалуйста, подождите и повторите попытку.";

/* the connection of the active connection window is not identical tooltip */
"The connection of the active connection window is not identical." = "Подключение окна активного соединения не идентично.";

/* Connection lost during import error message */
"The connection to the server was lost during the import.  The import is only partially complete." = "Соединение с сервером было потеряно во время импорта. Импорт завершен лишь частично.";

/* Create syntax permission denied detail */
"The creation syntax could not be retrieved due to a permissions error.\n\nPlease check your user permissions with an administrator." = "Невозможно получить синтаксис создания из-за ошибки прав доступа.\n\nПожалуйста, уточните наличие необходимых полномочий у администратора.";

/* CSV file open error */
"The CSV file you selected could not be found or read." = "Не удалось найти или прочитать выбранный CSV файл.";

/* Error when CSV appears to have too many columns to import, probably due to line ending mismatch */
"The CSV was read as containing more than 512 columns, more than the maximum columns permitted for speed reasons by Sequel Ace.\n\nThis usually happens due to errors reading the CSV; please double-check the CSV to be imported and the line endings and escape characters at the bottom of the CSV selection dialog." = "CSV содержит более 512 колонок, что превышает максимальное разрешённое число (это влияет на производительность Sequel Ace).\n\nОбычно это происходит из-за ошибок чтения CSV; пожалуйста, перепроверьте содержимое CSV, а также символы окончания и экранирования строк, указываемые внизу окна выбора CSV.";

/* unsaved theme informative message */
"The current color theme is unsaved. Do you want to proceed without saving it?" = "Текущая цветовая схема не сохранена. Продолжить без сохранения?";

/* Short important release note for swap of custom query buttons */
"The Custom Query \"Run\" and \"Run All\" button positions and their shortcuts have been swapped." = "Пользовательский запрос Запуск и Запуск Кнопки Все и их ярлыки переключены.";

/* the following default bundles were updated:\n%@\nyour modifications were stored as “(user)”. */
"The following default Bundles were updated:\n%@\nYour modifications were stored as “(user)”." = "Следующие базовые наборы обновились:\n%@\nВаши изменения были сохранены как “(user)”.";

/* favorites export error informative message */
"The following error occurred during the export process:\n\n%@" = "При экспорте произошла ошибка:\n\n%@";

/* favorites import error informative message */
"The following error occurred during the import process:\n\n%@" = "При импорте произошла ошибка:\n\n%@";

/* table structure : indexes : delete index : error 1553 : description */
"The foreign key relationship '%@' has a dependency on index '%@'. This relationship must be removed before the index can be deleted.\n\nAre you sure you want to continue to delete the relationship and the index? This action cannot be undone." = "Связи с внешним ключом '%1$@' имеют зависимость от индекса '%2$@'. Перед удалением индекса эти отношения должны быть удалены.\n\nВы уверены, что хотите продолжить удаление отношений и индекса? Это действие нельзя отменить.";

/* table list change alert message */
"The list of tables has changed" = "Список таблиц изменился";

/* message when trying to rename a table/view/proc/etc to an already used name */
"The name '%@' is already used." = "Имя '%@' уже используется.";

/* table list change alert informative message */
"The number of tables in this database has changed since the export dialog was opened. There are now %lu additional table(s), most likely added by an external application.\n\nHow would you like to proceed?" = "Количество таблиц в этой базе данных изменилось с момента открытия экспортного диалога. Сейчас существует %lu дополнительных таблиц. Скорее всего, добавлено внешним приложением.\n\nКак бы вы хотели продолжить?";

/* message of panel when no rows have been affected after writing to the db */
"The row was not written to the MySQL database. You probably haven't changed anything.\nReload the table to be sure that the row exists and use a primary key for your table.\n(This error can be turned off in the preferences.)" = "Строка не была записана в базу данных MySQL. Возможно, вы ничего не изменили.\nПерезагрузите таблицу, чтобы убедиться, что она существует и используйте первичный ключ в вашей таблицы.\n(Эту ошибку можно отключить в настройках)";

/* export : import settings : file version error description ($1 = is version, $2 = list of supported versions); note: the u00A0 is a non-breaking space, do not add more whitespace. */
"The selected export settings were stored with version %1$ld, but only settings with the following versions can be imported: %2$@.\n\nEither save the settings in a backwards compatible way or update your version of Sequel Ace." = "Выбранные параметры экспорта были сохранены в версии%1$ld, но могут быть импортированы только настройки со следующими версиями: %2$@.\n\nСохраните настройки обратно совместимым способом или обновите вашу версию Sequel Ace.";

/* export : import settings : spf content type error description */
"The selected file contains data of type “%1$@”, but type “%2$@” is needed. Please choose a different file." = "Выбранный файл содержит данные типа “%1$@”, но необходим тип “%2$@”. Пожалуйста, выберите другой файл.";

/* export : import settings : file error description */
"The selected file is either not a valid SPF file or severely corrupted." = "Выбранный файл не является допустимым SPF файлом или сильно поврежден.";

/* error deleting relation informative message */
"The selected relation couldn't be deleted.\n\nMySQL said: %@" = "Выбранная связь не может быть удалена.\n\nMySQL сообщил: %@";

/* error deleting trigger informative message */
"The selected trigger couldn't be deleted.\n\nMySQL said: %@" = "Выбранный триггер не может быть удалён.\n\nMySQL сообщил: %@";

/* description of tinyint */
"The smallest integer type, requires 1 byte storage space. M is the optional display width and does not affect the possible value range." = "Наименьший целочисленный тип, требует 1 байт пространства памяти. M является необязательной шириной дисплея и не влияет на возможный диапазон значений.";

/* message of panel when connection to socket failed because optional socket could not be found */
"The socket file could not be found in any common location. Please supply the correct socket location.\n\nMySQL said: %@" = "Файл сокета не найден в общем месте. Пожалуйста, укажите правильное расположение сокета.\n\nMySQL сказал: %@";

/* error creating relation informative message */
"The specified relation could not be created.\n\nMySQL said: %@" = "Не получается установить связь.\n\nMySQL сообщил: %@";

/* error creating trigger informative message */
"The specified trigger was unable to be created.\n\nMySQL said: %@" = "Не получается создать указанный триггер.\n\nMySQL сообщил: %@";

/* sql import : charset error alert : detail message */
"The SQL file uses utf8mb4 encoding, but your MySQL version only supports the limited utf8 subset.\n\nYou can continue the import, but any non-BMP characters in the SQL file (eg. some typographic and scientific special characters, archaic CJK logograms, emojis) will be unrecoverably lost!" = "SQL-файл использует кодировку utf8mb4, но ваша версия MySQL поддерживает только ограниченный набор подмножеств utf8.\n\nВы можете продолжить импорт, но любые не-BMP символы в SQL файле (например, некоторые типографические и научные специальные символы, древние логотипы CJK, смайлики) будут безвозвратно потеряны!";

/* SQL file open error */
"The SQL file you selected could not be found or read." = "Не удалось найти или прочитать выбранный SQL файл.";

/* Prompt for SSH password when keychain fetch failed */
"The SSH password could not be loaded from the keychain; please enter the SSH password for %@:" = "SSH пароль не может быть загружен из Связки ключей; пожалуйста, введите SSH пароль для %@:";

/* Prompt for SSH password when direct fetch failed */
"The SSH password could not be loaded; please enter the SSH password for %@:" = "SSH пароль не может быть загружен из Связки ключей; пожалуйста, введите SSH пароль для %@:";

/* SSH tunnel authentication failed message */
"The SSH Tunnel could not authenticate with the remote host. Please check your password and ensure you still have access." = "SSH туннель не может аутентифицироваться с удаленным хостом. Пожалуйста, проверьте пароль и убедитесь, что у вас все еще есть доступ.";

/* SSH tunnel unexpectedly closed */
"The SSH Tunnel has unexpectedly closed." = "SSH туннель неожиданно закрылся.";

/* SSH tunnel was closed by remote host message */
"The SSH Tunnel was closed 'by the remote host'. This may indicate a networking issue or a network timeout." = "Туннель SSH был закрыт «удаленным хостом». Это может указывать на проблему сетевого взаимодействия или время ожидания сети.";

/* SSH tunnel forwarding port connection refused message */
"The SSH Tunnel was established successfully, but could not forward data to the remote port as the remote port refused the connection." = "Туннель SSH был успешно создан, но не смог перенаправить данные на удаленный порт, так как удаленный порт отказал соединению.";

/* SSH tunnel unable to bind to local port message */
"The SSH Tunnel was unable to bind to the local port. This error may occur if you already have an SSH connection to the same server and are using a 'LocalForward' setting in your SSH configuration.\n\nWould you like to fall back to a standard connection to localhost in order to use the existing tunnel?" = "Не удалось пробросить туннель SSH через локальный порт. Эта ошибка может произойти, если у вас уже есть SSH соединение с тем же сервером и вы используете параметр 'LocalForward' в вашей конфигурации SSH.\n\nВы хотите вернуться к стандартному соединению с локальным хостом, чтобы использовать существующий туннель?";

/* SSH tunnel failed or timed out message */
"The SSH Tunnel was unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds)." = "SSH туннель не смог подключиться к хосту %1$@или истек срок ожидания запроса.\n\nУбедитесь, что адрес правильный и что у вас есть необходимые привилегии, или попробуйте увеличить тайм-аут соединения (сейчас %2$ld секунд).";

/* message of panel when loading of table failed and presumably due to used filter argument */
"The table data couldn't be loaded presumably due to used filter clause. \n\nMySQL said: %@" = "Не получается загрузить данные таблицы предположительно из-за настроек фильтра.\n\nMySQL сообщил: %@";

/* message of panel when loading of table failed */
"The table data couldn't be loaded.\n\nMySQL said: %@" = "Не получается загрузить данные таблицы.\n\nMySQL сообщил: %@";

/* Export folder not writable explanatory text */
"The target export folder is not writable.  Please select a new export location and try again." = "Папка для экспорта недоступна для записи. Пожалуйста, выберите новое место для экспорта и повторите попытку.";

/* Export folder not chosen by user explanatory text */
"No directory selected.  Please select a new export location and try again." = "Каталог не выбран. Пожалуйста, выберите новое место для экспорта и повторите попытку.";
"No directory selected." = "Каталог не выбран.";
"Please select a new export location and try again." = "Пожалуйста, выберите новое место для экспорта и повторите попытку.";

/* Export folder missing explanatory text */
"The target export folder no longer exists.  Please select a new export location and try again." = "Целевая папка экспорта больше не существует. Пожалуйста, выберите новое место для экспорта и повторите попытку.";

/* theme name label */
"Theme Name:" = "Название темы:";

/* themes installation error */
"Themes Installation Error" = "Ошибка установки тем";

/* message of panel when table content cannot be copied */
"There have been errors while copying table content. Please check the new table." = "Произошла ошибка при копировании содержимого таблицы. Пожалуйста, проверьте новую таблицу.";

/* Cannot duplicate a table with triggers to a different database */
"Cannot duplicate a table with triggers to a different database." = "Невозможно дублировать таблицу с триггерами в другую базу данных.";

/* text shown when query was successfull */
"There were no errors." = "Без ошибок.";

/* delete field and foreign key informative message */
"This field is part of a foreign key relationship with the table '%@'. This relationship must be removed before the field can be deleted.\n\nAre you sure you want to continue to delete the relationship and the field? This action cannot be undone." = "Это поле является частью внешних ключевых связей с таблицей '%@'. Эта связь должна быть удалена перед удалением поля.\n\nВы уверены, что хотите продолжить удаление отношений и поля? Это действие не может быть отменено.";

/* table structure : indexes : delete index : error 1553, no FK found : description */
"This index cannot be deleted, because it is used by an existing foreign key relationship.\n\nPlease remove the relationship, before trying to remove this index.\n\nMySQL said: %@" = "Этот индекс не может быть удалён, так как он используется существующей связью внешнего ключа.\n\nПожалуйста, удалите эту связь прежде чем пытаться удалить этот индекс.\n\nMySQL сообщил: %@";

/* description of serial */
"This is an alias for BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE." = "Это псевдоним для BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE.";

/* description of dec
 description of fixed
 description of numeric */
"This is an alias for DECIMAL." = "Это псевдоним для DECIMAL.";

/* description of double real */
"This is an alias for DOUBLE, unless REAL_AS_FLOAT is configured." = "Это псевдоним для DOUBLE, пока не задан REAL_AS_FLOAT.";

/* description of double precision */
"This is an alias for DOUBLE." = "Это псевдоним для DOUBLE.";

/* description of bool
 description of boolean */
"This is an alias for TINYINT(1)." = "Это псевдоним для TINYINT(1).";

/* Table Structure : Collation dropdown : database marker tooltip */
"This is the default collation of database %@." = "Это сортировка по умолчанию для базы данных %@.";

/* Table Structure : Collation dropdown : default marker tooltip */
"This is the default collation of encoding %@." = "Это сортировка по умолчанию для кодировки %@.";

/* Table Structure : Collation dropdown : table marker tooltip */
"This is the default collation of table %@." = "Это сортировка по умолчанию для таблицы %@.";

/* Table Structure : Collation dropdown : server marker tooltip */
"This is the default collation of this server." = "Это базовая сортировка для этого сервера.";

/* Table Structure : Encoding dropdown : database marker tooltip */
"This is the default encoding of database %@." = "Это кодировка по умолчанию для базы данных %@.";

/* Table Structure : Encoding dropdown : table marker tooltip */
"This is the default encoding of table %@." = "Это кодировка по умолчанию для таблицы %@.";

/* Table Structure : Encoding dropdown : server marker tooltip */
"This is the default encoding of this server." = "Это базовая кодировка для этого сервера.";

/* This table currently does not support relations. Only tables that use the InnoDB storage engine support them. */
"This table currently does not support relations. Only tables that use the InnoDB storage engine support them." = "В настоящее время эта таблица не поддерживает связи. Их поддерживают только таблицы, использующие движок хранилища InnoDB.";

/* user has no hosts informative message */
"This user doesn't have any hosts associated with it. It will be deleted unless one is added" = "У этого пользователя нет связанных с ним хостов. Он будет удален, если не будет добавлен";

/* error removing host informative message */
"This user doesn't seem to have any associated hosts and will be removed unless a host is added." = "Похоже, у этого пользователя нет связанных узлов и он будет удален, если не будет добавлен хост.";

/* shutdown server : confirmation dialog : message */
"This will wait for open transactions to complete and then quit the mysql daemon. Afterwards neither you nor anyone else can connect to this database!\n\nFull management access to the server's operating system is required to restart MySQL!" = "Ожидается завершение открытых транзакций и выход из mysql демона. После этого ни вы, ни кто-либо другой не можете подключиться к этой базе данных!\n\nДля перезапуска MySQL необходим полный доступ к операционной системе сервера!";

/* export filename time token */
"Time" = "Время";

/* toolbar item label for switching to the Table Triggers tab */
"Triggers" = "Триггеры";

/* triggers for table label */
"Triggers for table: %@" = "Триггеры для таблицы: %@";

/* truncate button */
"Truncate" = "Очистить";

/* truncate tables message */
"Truncate selected tables?" = "Очистить выбранные таблицы?";

/* truncate table menu title */
"Truncate Table..." = "Очистить таблицу...";

/* truncate table message */
"Truncate table '%@'?" = "Очистить таблицу '%@'?";

/* truncate tables menu item */
"Truncate Tables" = "Очистить таблицу";

/* type label (Navigator) */
"Type" = "Тип";

/* type declaration header */
"Type Declaration:" = "Объявление типа:";

/* add index error message */
"Unable to add index" = "Не удаётся добавить индекс";

/* unable to analyze selected items message */
"Unable to analyze selected items" = "Не удаётся проанализировать выбранные элементы";

/* unable to analyze table message */
"Unable to analyze table" = "Не удаётся проанализировать таблицу";

/* unable to check selected items message */
"Unable to check selected items" = "Не удается проверить выбранные элементы";

/* unable to check table message */
"Unable to check table" = "Не удается проверить таблицу";

/* message of panel when connection to host failed due to access denied error */
"Unable to connect to host %@ because access was denied.\n\nDouble-check your username and password and ensure that access from your current location is permitted.\n\nMySQL said: %@" = "Не удаётся подключиться к узлу %1$@ из-за запрета доступа.\n\nПерепроверьте имя пользователя и пароль и убедитесь, что доступ к текущему ресурсу разрешён.\n\nMySQL сообщил: %2$@";

/* message of panel when SSH port forwarding failed */
"Unable to connect to host %@ because the port connection via SSH was refused.\n\nPlease ensure that your MySQL host is set up to allow TCP/IP connections (no --skip-networking) and is configured to allow connections from the host you are tunnelling via.\n\nYou may also want to check the port is correct and that you have the necessary privileges.\n\nChecking the error detail will show the SSH debug log which may provide more details.\n\nMySQL said: %@" = "Не удаётся соединиться с узлом %1$@ из-за отказа в подключении к порту через SSH.\n\nПожалуйста, убедитесь, что у вашего MySQL включено TCP/IP соединение (нет ключа --skip-networking при запуске) и настроено разрешение соединений с того хоста, пропущенного через туннель.\n\nВы также можете проверить правильность порта и наличие необходимых привилегий.\n\nОшибка добавлена в отладочный SSH журнал, где вы можете найти более детальную информацию для анализа.\n\nMySQL сообщил: %2$@";

/* message of panel when connection to host failed */
"Unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds).\n\nMySQL said: %@" = "Не удалось подключиться к узлу %1$@ или время ожидания истекло.\n\nУбедитесь в правильности адреса и наличии необходимых привилегий или попробуйте увеличить время ожидания соединения (сейчас %2$ld сек.).\n\nMySQL сообщил: %3$@";

/* message of panel when connection to host failed */
"Unable to connect via the socket, or the request timed out.\n\nDouble-check that the socket path is correct and that you have the necessary privileges, and that the server is running.\n\nMySQL said: %@" = "Не удается подключиться через сокет или время ожидания запроса истекло.\n\nПерепроверьте путь к сокету и наличии необходимых прав, а также запущен ли сервер.\n\nMySQL сообщил: %@";

/* unable to copy database message */
"Unable to copy database" = "Не удаётся переименовать базу данных";

/* error deleting index message */
"Unable to delete index" = "Не удаётся удалить индекс";

/* error deleting relation message */
"Unable to delete relation" = "Не удаётся удалить связь";

/* error deleting trigger message */
"Unable to delete trigger" = "Не удаётся удалить триггер";

/* unable to flush selected items message */
"Unable to flush selected items" = "Невозможно очистить выбранные элементы";

/* unable to flush table message */
"Unable to flush table" = "Не удаётся очистить таблицу";

/* unable to get list of users message */
"Unable to get list of users" = "Не удаётся получить список пользователей";

/* error killing connection message */
"Unable to kill connection" = "Не удаётся разорвать соединение";

/* error killing query message */
"Unable to kill query" = "Не удалось прервать запрос";

/* unable to optimze selected items message */
"Unable to optimze selected items" = "Не удаётся оптимизировать выбранные элементы";

/* unable to optimze table message */
"Unable to optimze table" = "Не удалось оптимизировать таблицу";

/* unable to perform the checksum */
"Unable to perform the checksum" = "Не удалось вычислить контрольную сумму";

/* error removing host message */
"Unable to remove host" = "Не удаётся удалить узел";

/* unable to rename database message */
"Unable to rename database" = "Не удаётся переименовать базу данных";

/* unable to repair selected items message */
"Unable to repair selected items" = "Не удаётся восстановить выбранные элементы";

/* unable to repair table message */
"Unable to repair table" = "Не удаётся исправить таблицу";

/* message of panel when connection to db failed after selecting from popupbutton */
"Unable to select database %@.\nPlease check you have the necessary privileges to view the database, and that the database still exists." = "Невозможно выбрать базу данных %@.\nПожалуйста, проверьте наличие необходимых прав на просмотр базы данных и её наличие.";

/* Unable to write row error */
"Unable to write row" = "Не удается записать строку";

/* Table Content : Remove Row : Result : n Error title */
"Unexpected number of rows removed!" = "Удалено неожиданное количество строк!";

/* warning */
"Unknown file format" = "Неизвестный формат файла";

/* unsaved changes message */
"Unsaved changes" = "Несохранённые изменения";

/* unsaved theme message */
"Unsaved Theme" = "Несохранённая тема";

/* Preferences : Network : Custom SSH client : warning dialog title */
"Unsupported configuration!" = "Неподдерживаемая конфигурация!";

/* export : import settings : file version error title */
"Unsupported version for export settings!" = "Неподдерживаемая для экспорта настроек версия!";

/* Name for an untitled connection */
"Untitled" = "Без названия";

/* Title of a new Sequel Ace Document */
"Untitled %ld" = "Безымянный %ld";

/* range for mediumblob type */
"Up to %@ bytes (16 MiB)" = "До %@ байт (16 МиБ)";

/* range for longblob type */
"Up to %@ bytes (4 GiB)" = "До %@ байт (4 ГиБ)";

/* range for mediumtext type */
"Up to %@ characters (16 MiB)" = "До %@ символов (16 МиБ)";

/* range for enum type */
"Up to %@ distinct members (<%@ in practice)\n1-2 bytes storage" = "До %1$@ отдельных членов (<%2$@ на практике)\n1-2 байт хранения";

/* range for tinyblob type */
"Up to 255 bytes" = "До 255 байт";

/* range for tinytext type */
"Up to 255 characters" = "До 255 символов";

/* Open Files : Bundle : Already-Installed : Update button */
"Update" = "Обновить";

/* updated: %@ */
"updated: %@" = "обновлено: %@";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field of table `%2$@` was changed." = "Не удалось обновить содержимое поля. Не удалось однозначно определить источник полей (%1$ld совпадения). Очень вероятно, что при редактировании этого поля таблицы `%2$@была изменена.";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field the table `%2$@` was changed by an other user." = "Не удалось обновить содержимое поля. Не удалось однозначно определить источник полей (%1$ld совпадения). Очень вероятно, что при редактировании этого поля таблица `%2$@была изменена другим пользователем.";

/* updating field task description */
"Updating field data..." = "Обновление данных поля...";

/* URL scheme command couldn't authenticated */
"URL scheme command couldn't authenticated" = "Ошибка аутентификации команды URL схемы";

/* URL scheme command was terminated by user */
"URL scheme command was terminated by user" = "Команда URL схемы была прервана пользователем";

/* URL scheme command %@ unsupported */
"URL scheme command %@ unsupported" = "Команда URL схемы %@ не поддерживается";

/* Use 127.0.0.1 button */
"Use 127.0.0.1" = "Использовать 127.0.0.1";

/* use standard connection button */
"Use Standard Connection" = "Использовать стандартное соединение";

/* user has no hosts message */
"User has no hosts" = "Для пользователя не указан узел";

/* user-defined value */
"User-defined value" = "Пользовательское значение";

/* toolbar item label for switching to the User Manager tab */
"Users" = "Пользователи";

/* CSV Field Mapping : Table View : Tooltip for fields with NULL value */
"Value will be imported as MySQL NULL" = "Значение будет импортировано как MySQL NULL";

/* variable label for color table (Prefs > Editor) */
"Variable" = "Переменная";

/* version */
"version" = "версия";

/* export header version label */
"Version" = "Версия";

/* view */
"view" = "представление";

/* Release notes button title */
"View full release notes" = "Просмотреть все примечания к версии";

/* header for view info pane */
"VIEW INFORMATION" = "О ПРЕДСТАВЛЕНИИ";

/* view html source code menu item title */
"View Source" = "Исходный код";

/* view structure print heading */
"View Structure" = "Структура представления";

/* views */
"views" = "представления";

/* warning */
"Warning" = "Предупреждение";

/* Short important release note for why password prompts may occur */
"We've changed Sequel Ace's digital signature for GateKeeper compatibility; you'll have to allow access to your passwords again." = "Мы изменили цифровую подпись Sequel Ace для совместимости GateKeeper; вам необходимо разрешить доступ к вашим паролям.";

/* Important release notes informational text, single change */
"We've made a few changes but we thought you should know about one particularly important one:" = "Мы внеси некоторые изменения и думаем, что вы должны узнать о наиболее важном:";

/* Important release notes informational text, multiple changes */
"We've made a few changes but we thought you should know about some particularly important ones:" = "Мы внеси некоторые изменения и думаем, что вы должны узнать о наиболее важных:";

/* WHERE clause not valid */
"WHERE clause not valid" = "Недопустимое условие WHERE";

/* Title of filter preview area when the query WHERE is negated */
"WHERE NOT query" = "WHERE NOT запрос";

/* Title of filter preview area when the query WHERE is normal */
"WHERE query" = "WHERE запрос";

/* Generic working description */
"Working..." = "Обработка...";

/* export label showing app is writing data */
"Writing data..." = "Запись данных...";

/* text showing that app is writing text file */
"Writing..." = "Запись...";

/* wrong data format or password */
"Wrong data format or password." = "Неверный формат данных или пароль.";

/* wrong data format */
"Wrong data format." = "Неверный формат данных.";

/* export : import settings : spf content type error title */
"Wrong SPF content type!" = "Неправильный SPF тип содержимого!";

/* export filename date token */
"Year" = "Год";

/* message of panel when trying to copy multiple rows */
"You can only copy single rows." = "Вы можете скопировать только одну строку.";

/* message of panel when trying to edit tables without index and with hidden blob/text fields */
"You can't hide blob and text fields when working with tables without index." = "Вы не можете скрыть блоки и текстовые поля при работе с таблицами без индекса.";

/* You cannot delete the last field in a table. Delete the table instead. */
"You cannot delete the last field in a table. Delete the table instead." = "Вы не можете удалить последнее поле в таблице. Вместо этого удалите саму таблицу.";

/* SSL connection requested but not established error detail */
"You requested that the connection should be established using SSL, but MySQL made the connection without SSL.\n\nThis may be because the server does not support SSL connections, or has SSL disabled; or insufficient details were supplied to establish an SSL connection.\n\nThis connection is not encrypted." = "Вы инициализировали установку соединения с помощью SSL, но MySQL подключился без SSL.\n\nЭто может быть потому, что сервер не поддерживает SSL-соединения, или на нём SSL отключено, или задано недостаточно параметров для установки SSL-соединения.\n\nЭто соединение не зашифровано.";

/* Query Favorites : List : Section Heading : current connection document : tooltip (arg is the name of the spf file) */
"‘%@’ based favorites" = "‘%@' избранное";

/* table column header. Read: 'Showing all content filters for fields of type %@' (ContentFilterManager) */
"‘%@’ Fields Content Filters" = "‘%@' Поля Контентные Фильтры";

/* Export file already exists message */
"%@ already exists. Do you want to replace it?" = "%@ уже существует. Вы хотите заменить его?";

/* Bundle Editor : Outline View : Bundle item : tooltip */
"%@ Bundle" = "Пакет %@";

/* Export file creation error title */
"%@ could not be created" = "%@ не может быть создан";

/* %@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item. */
"%@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item." = "%@ не может быть обработан. Вы можете изменить настройки столбца, но столбец не будет отображаться в виде содержимого; пожалуйста, сообщите об этом команде Sequel Ace с помощью пункта меню справки.";

/* connection view : ssl : client cert file picker : wrong format error title */
"%@ is not a valid client certificate file." = "%@ не является допустимым файлом сертификата клиента.";

/* connection view : ssl : key file picker : wrong format error title */
"%@ is not a valid private key file." = "%@ не является допустимым файлом приватного ключа.";

/* text shown when there are App Sandbox Issues */
"App Sandbox Issue" = "Проблема в Sandbox приложения";

/* Stale Bookmarks error title */
"Stale Bookmarks" = "Старые закладки";

/* App Sandbox info link text */
"App Sandbox Info" = "Информация о песочнице приложения";

/* error while selecting file title */
"File Selection Error" = "Ошибка выбора файла";

/* error while selecting file message */
"The selected file is not a valid file.\n\nPlease try again.\n\nClass: %@" = "Выбранный файл не является допустимым.\n\nПожалуйста, попробуйте ещё раз.\n\nКласс: %@";

/* known hosts not writable error message */
"The selected known hosts file is not writable.\n\n%@\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "Выбранный файл hosts-файлов не доступен для записи.\n\n%@\n\nПожалуйста выберите файл в настройках Sequel Ace и повторите попытку.";

/* known hosts is invalid message */
"The selected known hosts file is invalid.\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "Выбранный файл с известными хостами повреждён.\n\nПожалуйста, выберите файл заново в Настройках Sequel Ace и затем повторите попытку.";

/* known hosts contains quote message */
"The selected known hosts file contains a quote (\") in its file path which is not supported.\n\n%@\n\nPlease select a different file in Sequel Ace's Preferences or rename the file/path to remove the quote." = "Выбранный файл hosts содержит кавычки (\") в пути файла, который не поддерживается.\n\n%@\n\nПожалуйста, выберите другой файл в Настройках Последователя или переименуйте путь к файлу, чтобы удалить кавычку.";

/* SSH Tunnel Debugging menu and window title */
"SSH Tunnel Debugging Info" = "Отладочная информация SSH туннеля";

/* Question to user to see if they would like to re-request access */
"You have stale secure bookmarks:\n\n%@\n\nWould you like to re-request access now?" = "Обнаружено, что доступ к некоторым защищенным закладкам просрочен:\n\n%@\n\nХотите повторно запросить доступ к ним сейчас?";

/* Question to user to see if they would like to request access to missing bookmarks */
"You have missing secure bookmarks:\n\n%@\n\nWould you like to request access now?" = "Обнаружено отсутствие некоторых защищенных закладок:\n\n%@\n\nХотите запросить доступ к ним сейчас?";

/* known hosts advance menu item title */
"Use known hosts from ssh config (ADVANCED)" = "Использовать известные хосты из SSH настроек (ПРОДВИНУТЫЙ РЕЖИМ)";

/* The answer, yes */
"Yes" = "Да";

/* Shown to remind users of their stale bookmarks */
"A reminder of your stale secure bookmarks:<br /><br />%@<br />" = "Напоминание об устаревших защищенных закладках:<br /><br />%@<br />";

/* Title for Stale Secure Bookmarks help window */
"Stale Secure Bookmarks" = "Старые безопасные закладки";

/* Title for Export Error alert */
"Export Error" = "Ошибка экспорта";

/* Message for Export Error alert */
"Error while writing to the export file. Could not open file: %@" = "Ошибка записи в файл экспорта. Не удалось открыть файл: %@";

/* Message for User window error */
"Resultset from mysql.user contains neither 'Password' nor 'authentication_string' column." = "Результат от mysql.user не содержит ни 'Пароль', ни 'authentication_string'.";

/* Title for User window error */
"User Data Error" = "Ошибка данных пользователя";

/* Title for Stale Bookmark file selection dialog */
"Please re-select the file '%@' in order to restore Sequel Ace's access." = "Пожалуйста, перевыберите файл '%@' для восстановления доступа к Sequel Ace.";

/* Title for File Preference file selection dialog */
"Please choose a file or folder to grant Sequel Ace access to." = "Пожалуйста, выберите файл или папку для предоставления доступа Sequel Ace.";

/* Title for Network Preference ssh config file selection dialog */
"Please choose your ssh config files(s)" = "Пожалуйста, выберите файл(ы) SSH конфигурации";

/* Title for Network Preference ssh known hosts file selection dialog */
"Please choose your known hosts file" = "Пожалуйста, выберите файл известных узлов";

/* Message for field editor JSON segment when JSON is invalid */
"Invalid JSON" = "Неверный формат JSON";

/* Applying syntax highlighting task description */
"Applying syntax highlighting..." = "Применение подсветки синтаксиса...";

/* Error message when a bash task fails to start */
"Couldn't launch task.\nException reason: %@\n ENV length: %lu" = "Не удалось запустить задачу.\nПричина исключения: %@\n ENV: %lu";

/* Error message title when we failed to create new connection view */
"New Connection Error" = "Ошибка нового соединения";

/* New Connection Error informative message */
"Failed to create new database connection window. Please restart Sequel Ace and try again." = "Не удалось создать новое окно подключения к базе данных. Перезапустите Sequel Ace и повторите попытку.";

/* new version is available alert title */
"A new version is available" = "Доступна новая версия";

/* new version is available download button title */
"Download" = "Загрузить";

/* new version is available alert informativeText */
"Version %@ is available. You are currently running %@" = "Доступна версия %@. Сейчас вы используете %@";

/* downloading new version window title */
"Download Progress" = "Прогресс загрузки";

/* downloading new version time remaining placeholder text */
"Calculating time remaining..." = "Оценка оставшегося времени...";

/* downloading new version window message */
"Downloading Sequel Ace - %@" = "Загрузка Sequel Ace - %@";

/* downloading new version time remaining estimate text */
"About %.1f seconds left" = "Осталось приблизительно секунд: %.1f";

/* downloading new version failure alert title */
"Download Failed" = "Ошибка загрузки";

/* Tool tip for the Show alert when update is available preference on the Notifications pane */
"Only available for GitHub downloads" = "Загрузить возможно только из GitHub";

/* Tool tip warning for when the user sets auto-complete delay to zero */
"WARNING: Setting the auto-complete delay to 0.0 can result in strange output." = "ВНИМАНИЕ: Нулевая задержка подсказок ввода может стать причиной странного результата.";

/* downloading new version bytes of bytes done */
"%@ of %@" = "%@ из %@";

/* Time zone set failed informative message */
"\n\ntime_zone will be set to SYSTEM." = "\n\nчасовой пояс будет установлен в SYSTEM.";

/* Menu item title for checking for updates */
"Check for Updates..." = "Проверить наличие обновлений...";

/* Warning message during connection in case the variable skip-show-database is set to ON */
"The skip-show-database variable of the database server is set to ON. Thus, you won't be able to list databases unless you have the SHOW DATABASES privilege.\n\nHowever, the databases are still accessible directly through SQL queries depending on your privileges." = "Переменная skip-show-database сервера базы данных установлена в ВКЛЮЧЕНИЕ. Таким образом, вы не сможете список баз данных, если не имеете привилегии SHOW DATABASES.\n\nОднако, базы данных по-прежнему доступны непосредственно через SQL-запросы в зависимости от ваших привилегий.";

/* Error alert title when the request to GitHub fails */
"GitHub Request Failed" = "Ошибка запроса к GitHub";

/* Info alert title when there are Newer Releases Available */
"No Newer Release Available" = "Нет более новых версий";

/* Info alert message when there are Newer Releases Available */
"You are currently running the latest release." = "Вы используете самую свежую версию.";

/* Alert button, disable the warning when skip-show-database is set to on */
"Never show this again" = "Больше не показывать это";

/* Info alert when editing a generated column */
"The current field \"%@\" is a generated column and therefore cannot be edited." = "Текущее поле \"%@\" является вычисленным столбцом и поэтому не может быть изменено.";

/* Release note / info modal about change in latest release about column behavior */
"The use of the \"default\" column has changed since the last version of Sequel ACE:\n\n- No default value : Leave it blank.\n- String value : Use single '' or double quotes \"\" if you want an empty string or to wrap a string\n- Expression : Use parentheses (). Except for the TIMESTAMP and DATETIME columns where you can specify the CURRENT_TIMESTAMP function without enclosing parentheses." = "Использование колонки \"default\" изменилось с момента выхода последней версии Sequel ACE:\n\n- Если нет значения по умолчанию, то оставьте его пустым.\n- Для строкового значения одинарный кавычки ('…') или двоные кавычки для пустой строки или переноса строки (\"…\")\n- Для ввода выражения используйте скобки (…). Для колонок TIMESTAMP и DATETIME можно указать функцию CURRENT_TIMESTAMP без скобок.";

/* Menu option to pin a view to the top of the list of tables */
"Pin View" = "Закрепить представление";

/* Menu option to pin a table to the top of the list of tables */
"Pin Table" = "Закрепить таблицу";

/* Menu option to pin a procedure to the top of the list of tables */
"Pin Procedure" = "Закрепить процедуру";

/* Menu option to pin a function to the top of the list of tables */
"Pin Function" = "Закрепить функцию";

/* Menu option to unpin a view which is currently pinned at the top of the list of tables */
"Unpin View" = "Открепить представление";

/* Menu option to unpin a table which is currently pinned at the top of the list of tables */
"Unpin Table" = "Открепить таблицу";

/* Menu option to unpin a procedure which is currently pinned at the top of the list of tables */
"Unpin Procedure" = "Открепить процедуру";

/* Menu option to unpin a function which is currently pinned at the top of the list of tables */
"Unpin Function" = "Открепить функцию";

/* header for pinned table list */
"PINNED" = "ЗАКРЕПЛЁННЫЕ";

/* Menu option to copy a table's name with database name in the list of tables */
"Copy Table Name" = "Скопировать имя таблицы";

/* sequelace URL scheme error: LaunchFavorite alert title (favorite name not found) */
"LaunchFavorite URL Scheme Error" = "Ошибка запуска Избранной URL схемы";

/* sequelace URL scheme error: LaunchFavorite alert description (favorite name not found) */
"The variable in the ?name= query parameter could not be matched with any of your favorites." = "Переменная в параметре ?name= запроса не соответствует ни одному из ваших избранных.";
