/* Table Content : Remove Row : Result : Too Few : Part 3 : Row not deleted when using primary key for DELETE statement. */
" Check the Console for possible errors inside the primary key(s) of this table!" = " Zkontrolujte konzoli pro možné chyby uvnitř primá<PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON><PERSON><PERSON>) této tabulky!";

/* Table Content : Remove Row : Result : Too Many : Part 2 : Generic text */
" Please check the Console and inform the Sequel Ace team!" = " Prosím zkontrolujte konzoli a informujte tým Sequel Ace!";

/* Table Content : Remove Row : Result : Too Few : Part 2 : Generic help message */
" Reload the table to be sure that the contents have not changed in the meantime." = " Znovu načtěte tabulku a ujistěte se, že se mezitím obsah nezměnil.";

/* Table Content : Remove Row : Result : Too Few : Part 3 : no primary key in table generic message */
" You should also add a primary key to this table!" = " <PERSON><PERSON><PERSON> byste také přidat primární kl<PERSON> do této tabulky!";

/* Query snippet shell command syntax and placeholder */
"$(shell_command)" = "$(shell_command)";

/* text showing how many rows are selected */
"%@ %@ selected" = "%1$@ %2$@ vybráno";

/* History item filtered by values label */
"%@ (Filtered by %@)" = "%1$@ ( %2$@)";

/* History item with page number label */
"%@ (Page %lu)" = "%1$@ (strana %2$lu)";

/* Content Filter Manager : Initial name of copied filter
 Initial favourite name after duplicating a previous favourite */
"%@ Copy" = "%@ Kopírovat";

/* text showing a single row a partially loaded result */
"%@ row in partial load" = "%@ řádek s částečným zatížením";

/* text showing a single row in the result */
"%@ row in table" = "%@ řádek v tabulce";

/* text showing how a single rows matched filter */
"%@ row of %@%@ matches filter" = "%1$@ řádek z %2$@%3$@ odpovídá filtru";

/* text showing how many rows are in a partially loaded result */
"%@ rows in partial load" = "%@ řádků s částečným zatížením";

/* text showing how many rows are in the result */
"%@ rows in table" = "%@ řádků v tabulce";

/* text showing how many rows matched filter */
"%@ rows of %@%@ match filter" = "%1$@ řádků %2$@%3$@ odpovídající filtr";

/* text showing how many rows have been affected by a single query */
"%@; %ld rows affected" = "%1$@; postižení %2$ld řádky";

/* text showing how many rows have been affected by multiple queries */
"%@; %ld rows affected in total, by %ld queries taking %@" = "%1$@; %2$ld celkem ovlivněny %3$ld dotazy za %4$@";

/* text showing one row has been affected by a single query */
"%@; 1 row affected" = "%@; 1 řádek zasažen";

/* text showing one row has been affected by multiple queries */
"%@; 1 row affected in total, by %ld queries taking %@" = "%1$@; celkem 1 řádek ovlivněn, %2$ld dotazy užívající %3$@";

/* text showing a query was cancelled */
"%@; Cancelled after %@" = "%1$@; Zrušeno po %2$@";

/* text showing multiple queries were cancelled */
"%@; Cancelled in query %ld, after %@" = "%1$@; Zrušeno v dotazu %2$ld, po %3$@";

/* Error display text, showing original MySQL error */
"%@\n\nMySQL said: %@" = "%1$@\n\nMySQL řekl: %2$@";

/* favorite singular label (%d == 1) */
"%lu favorite" = "%lu oblíbené";

/* favorites plural label (%d != 1) */
"%lu favorites" = "%lu oblíbených";

/* favorite group singular label (%d == 1) */
"%lu group" = "%lu skupina";

/* favorite groups plural label (%d != 1) */
"%lu groups" = "%lu skupin";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+y (y!=1) rows instead of n selected were deleted. */
"%ld additional rows were removed!" = "%ld další řádky byly odstraněny!";

/* Label showing the index of the selected CSV row */
"%ld of %lu record(s)" = "%1$ld záznamu z %2$lu";

/* Label showing the index of the selected CSV row (csv partially loaded) */
"%ld of first %lu record(s)" = "%1$ld prvního %2$lu záznamu";

/* Table Content : Remove Row : Result : Too Few : Part 1 : n-x (x!=1) of n selected rows were deleted. */
"%ld rows were not removed." = "%ld řádky nebyly odstraněny.";

/* Export file already exists message */
"%lu files already exist. Do you want to replace them?" = "%lu soubory již existují. Chcete je nahradit?";

/* Export files creation error title */
"%lu files could not be created" = "%lu soubory nelze vytvořit";

/* Some export files already exist explanatory text */
"%lu files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "%lu soubory se stejnými názvy již existují v cílové složce. Jejich nahrazení přepíše jejich aktuální obsah.";

/* filtered item count */
"%lu of %lu" = "%1$lu z %2$lu";

/* Export folder not writable for some files explanatory text */
"%lu of the export files could not be created because their target export folder is not writable; please select a new export location and try again." = "%lu exportních souborů nelze vytvořit, protože jejich cílová exportní složka není zapisovatelná; prosím, vyberte nové místo exportu a zkuste to znovu.";

/* Export folder missing for some files explanatory text */
"%lu of the export files could not be created because their target export folder no longer exists; please select a new export location and try again." = "%lu exportních souborů nelze vytvořit, protože jejich cílová exportní složka již neexistuje. prosím, vyberte nové místo exportu a zkuste to znovu.";

/* History item title with nothing selected */
"(no selection)" = "(žádný výběr)";

/* value shown for hidden blob and text fields */
"(not loaded)" = "(nenačteno)";

/* Explanation for MySQL server has gone away error */
"(This usually indicates that the connection has been closed by the server after inactivity, but can also occur due to other conditions.  The connection has been restored; please try again if the query is safe to re-run.)" = "(To obvykle znamená, že připojení bylo uzavřeno serverem po neaktivitě, ale může k němu dojít také kvůli jiným podmínkám. Připojení bylo obnoveno; zkuste to prosím znovu, pokud je dotaz bezpečný pro opakovaný spuštění.)";

/* Custom Query : text appended to the “x row(s) affected” messages. $1 is a time interval */
", first row available after %1$@" = ", první řádek je k dispozici po %1$@";

/* Custom Query : text appended to the “x row(s) affected” messages (for update/delete queries). $1 is a time interval */
", taking %1$@" = ", beru %1$@";

/* warning shown in the console when no rows have been affected after writing to the db */
"/* WARNING: No rows have been affected */\n" = "/* VAROVÁNÍ: Žádné řádky nebyly ovlivněny */\n";

/* error text when multiple custom query failed */
"[ERROR in query %ld] %@\n" = "[CHYBA v dotazu %1$ld] %2$@\n";

/* error text when reading of csv file gave errors */
"[ERROR in row %ld] %@\n" = "[CHYBA v řádku %1$ld] %2$@\n";

/* [multiple selection] */
"[multiple selection]" = "[vícenásobný výběr]";

/* displayed when new content filter has empty Name field (ContentFilterManager) */
"[name required]" = "[vyžadováno jméno]";

/* [no selection] */
"[no selection]" = "[žádný výběr]";

/* Additional export file errors */
"\n\n(In addition, one or more errors occurred while attempting to create the export files: %lu could not be created. These files will be ignored.)" = "\n\n(Kromě toho došlo k jedné nebo více chybám při pokusu o vytvoření exportních souborů: %lu nelze vytvořit. Tyto soubory budou ignorovány.)";

/* \n\npress shift for binary search tooltip message */
"\n\nPress ⇧ for binary search (case-sensitive)." = "\n\nStiskněte <unk> pro binární vyhledávání (rozlište malá a velká písmena).";

/* description of bit */
"A bit-field type. M specifies the number of bits. If shorter values are inserted, they will be aligned on the least significant bit. See the SET type if you want to explicitly name each bit." = "Typ bitového pole. M určuje počet bitů. Pokud jsou vloženy kratší hodnoty, budou zarovnány na nejméně významném bitu. Pokud chcete explicitně pojmenovat každou část, podívejte se na typ SET.";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog message */
"A Bundle ‘%@’ is already installed. Do you want to update it?" = "Bundle ‘%@je již nainstalován. Chcete jej aktualizovat?";

/* description of binary */
"A byte array with fixed length. Shorter values will always be padded to the right with 0x00 until they fit M." = "Bajtové pole s pevnou délkou. Krátké hodnoty budou vždy umístěny vpravo s 0x00, dokud se nevejde M.";

/* description of varbinary */
"A byte array with variable length. The actual number of bytes is further limited by the values of other fields in the row." = "Pole bajtů s délkou proměnné. Skutečný počet bajtů je dále omezen hodnotami jiných polí v řadě.";

/* description of blob
 description of longblob
 description of mediumblob
 description of tinyblob */
"A byte array with variable length. Unlike VARBINARY this type does not count towards the maximum row length." = "Pole bajtů s proměnnou délkou. Na rozdíl od VARBINARY se tento typ nezapočítává do maximální délky řady.";

/* description of tinytext */
"A character string that can store up to 255 bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Řetězec znaků, který může ukládat až 255 bajtů, ale vyžaduje méně místa pro kratší hodnoty. Skutečný počet znaků je dále omezen použitým kódováním. Na rozdíl od VARCHAR se tento typ nezapočítává do maximální délky řádku.";

/* description of varchar */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding and the values of other fields in the row." = "Řetězec znaků, který může ukládat až M bajtů, ale vyžaduje méně místa pro kratší hodnoty. Skutečný počet znaků je dále omezen použitým kódováním a hodnotami jiných polí v řádku.";

/* description of text */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Řetězec znaků, který může ukládat až M bajtů, ale vyžaduje méně místa pro kratší hodnoty. Skutečný počet znaků je dále omezen použitým kódováním. Na rozdíl od VARCHAR se tento typ nezapočítává do maximální délky řádku.";

/* description of char */
"A character string that will require M×w bytes per row, independent of the actual content length. w is the maximum number of bytes a single character can occupy in the given encoding." = "Řetězec znaků, který bude vyžadovat M×w bytů na řádek, nezávisle na skutečné délce obsahu. w je maximální počet bytů, které může jeden znak obsadit v daném kódování.";

/* description of longtext
 description of mediumtext */
"A character string with variable length. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Řetězec znaků s délkou proměnné. Skutečný počet znaků je dále omezen použitým kódováním. Na rozdíl od VARCHAR se tento typ nezapočítává do maximální délky řady.";

/* description of json */
"A data type that validates JSON data on INSERT and internally stores it in a binary format that is both, more compact and faster to access than textual JSON.\nAvailable from MySQL 5.7.8." = "Datový typ, který ověřuje data JSON na INSERT a interně jej ukládá v binárním formátu, který je obojího, více kompaktní a rychlejší přístup než textový JSON.\nK dispozici z MySQL 5.7.8.";

/* Export file already exists explanatory text */
"A file with the same name already exists in the target folder. Replacing it will overwrite its current contents." = "Soubor se stejným názvem již existuje v cílové složce. Nahrazení přepíše jeho aktuální obsah.";

/* description of decimal */
"A fixed-point, exact decimal value. M is the maxium number of digits, of which D may be after the decimal point. When rounding, 0-4 is always rounded down, 5-9 up (“round towards nearest”)." = "Pevný bod, přesná desetinná hodnota. M je maximální počet číslic, z nichž D může být za desetinnou čárkou. Při zaokrouhlování je 0-4 vždy zaokrouhleno dolů, 5-9 nahoru („zaokrouhleno směrem k blízkosti“).";

/* table structure : indexes : delete index : error 1553 : title
 table structure : indexes : delete index : error 1553, no FK found : title */
"A foreign key needs this index" = "Externí klíč potřebuje tento index";

/* description of set */
"A SET can define up to 64 members (as strings) of which a field can use one or more using a comma-separated list. Upon insertion the order of members is automatically normalized and duplicate members will be eliminated. Assignment of numbers is supported using the same semantics as for BIT types." = "SET může definovat až 64 členů (jako řetězce), z nichž jedno nebo více polí může použít pomocí čárkami odděleného seznamu. Po vložení je pořadí členů automaticky normalizováno a duplicitní členové budou vyloučeni. Přiřazení čísel je podporováno pomocí stejných sémantik jako pro BIT typy.";

/* SSH key not found message */
"A SSH key location was specified, but no file was found in the specified location.  Please re-select the key and try again." = "SSH klíč byl zadán, ale v určeném umístění nebyl nalezen žádný soubor. Prosím znovu vyberte klíč a zkuste to znovu.";

/* SSL CA certificate file not found message */
"A SSL Certificate Authority certificate location was specified, but no file was found in the specified location.  Please re-select the Certificate Authority certificate and try again." = "Bylo zadáno umístění certifikátu SSL Certifikační autority, ale v určeném umístění nebyl nalezen žádný soubor. Prosím vyberte znovu certifikát Certifikační autority a zkuste to znovu.";

/* SSL certificate file not found message */
"A SSL certificate location was specified, but no file was found in the specified location.  Please re-select the certificate and try again." = "Bylo zadáno umístění SSL certifikátu, ale v určeném umístění nebyl nalezen žádný soubor. Prosím vyberte certifikát a zkuste to znovu.";

/* SSL key file not found message */
"A SSL key file location was specified, but no file was found in the specified location.  Please re-select the key file and try again." = "Byla zadána lokace souboru klíče SSL, ale v určeném umístění nebyl nalezen žádný soubor. Prosím znovu vyberte klíčový soubor a zkuste to znovu.";

/* duplicate host informative message */
"A user with the host '%@' already exists" = "Uživatel s hostitelem '%@' již existuje";

/* duplicate user informative message */
"A user with the name '%@' already exists" = "Uživatel s jménem '%@' již existuje";

/* table content : editing : error message description when parsing as hex string failed */
"A valid hex string may only contain the numbers 0-9 and letters A-F (a-f). It can optionally begin with „0x“ and spaces will be ignored.\nAlternatively the syntax X'val' is supported, too." = "Platný hex řetězec může obsahovat pouze čísla 0–9 a písmena A-F (a-f). Volitelně může začínat znakem „0x“ a mezery budou ignorovány.\nAlternativně je podporována syntaxe X'val.";

/* connection failed due to access denied title */
"Access denied!" = "Přístup odepřen!";

/* range of double */
"Accurate to approx. 15 decimal places" = "Přesná na přibližně 15 desetinných míst";

/* range of float */
"Accurate to approx. 7 decimal places" = "Přesná na přibližně 7 desetinných míst";

/* active connection window is busy. please wait and try again. tooltip */
"Active connection window is busy. Please wait and try again." = "Aktivní okno připojení je zaneprázdněno. Počkejte a zkuste to znovu.";

/* header for activities pane */
"ACTIVITIES" = "ČINNOSTI";

/* Add trigger button label */
"Add" = "Přidat";

/* menu item to add db */
"Add Database..." = "Přidat databázi...";

/* Add Host */
"Add Host" = "Přidat hostitele";

/* add global value or expression menu item */
"Add Value or Expression…" = "Přidat hodnotu nebo výraz…";

/* adding index task status message */
"Adding index..." = "Přidávání indexu...";

/* Advanced options short title */
"Advanced" = "Rozšířené";

/* notifications preference pane name */
"Alerts & Logs" = "Upozornění a logy";

/* notifications preference pane tooltip */
"Alerts & Logs Preferences" = "Nastavení upozornění a logů";

/* All databases placeholder */
"All Databases" = "Všechny databáze";

/* All databases (%) placeholder */
"All Databases (%)" = "Všechny databáze (%)";

/* All export files already exist message */
"All the export files already exist. Do you want to replace them?" = "Všechny exportované soubory již existují. Chcete je nahradit?";

/* An error for sequelace URL scheme command occurred. Probably no corresponding connection window found. */
"An error for sequelace URL scheme command occurred. Probably no corresponding connection window found." = "Došlo k chybě pro příkaz URL schéma. Pravděpodobně nebylo nalezeno odpovídající okno připojení.";

/* no connection available informatie message */
"An error has occurred and there doesn't seem to be a connection available." = "Došlo k chybě a zdá se, že není k dispozici připojení.";

/* an error occur while executing a scheme command. if the scheme command was invoked by a bundle command, it could be that the command still runs. you can try to terminate it by pressing ⌘+. or via the activities pane. */
"An error occur while executing a scheme command. If the scheme command was invoked by a Bundle command, it could be that the command still runs. You can try to terminate it by pressing ⌘+. or via the Activities pane." = "Při provádění příkazu schématu došlo k chybě. Pokud byl příkaz Bundle vyvolán příkazem Bundle, mohl by to být tak, že příkaz stále běží. Můžete se ho pokusit ukončit stisknutím klávesy <unk> +. nebo v panelu Aktivity.";

/* error killing query informative message */
"An error occurred while attempting to kill connection %lld.\n\nMySQL said: %@" = "Došlo k chybě při pokusu o ukončení spojení %1$lld.\n\nMySQL řekl: %2$@";

/* error killing query informative message */
"An error occurred while attempting to kill the query associated with connection %lld.\n\nMySQL said: %@" = "Došlo k chybě při pokusu o ukončení dotazu spojeného s připojením %1$lld.\n\nMySQL řekl: %2$@";

/* Error shown when unable to show create table syntax */
"An error occurred while creating table syntax.\n\n: %@" = "Došlo k chybě při vytváření syntaxe tabulky.\n\n: %@";

/* rename table error - no temporary name found */
"An error occurred while renaming '%@'. No temporary name could be found. Please try renaming to something else first." = "Došlo k chybě při přejmenování '%@'. Nebylo nalezeno žádné dočasné jméno. Prosím, zkuste nejdříve přejmenovat na něco jiného.";

/* rename table error informative message */
"An error occurred while renaming '%@'.\n\nMySQL said: %@" = "Došlo k chybě při přejmenování '%1$@'.\n\nMySQL řekl: %2$@";

/* rename error - don't know what type the renamed thing is */
"An error occurred while renaming. '%@' is of an unknown type." = "Došlo k chybě při přejmenování. '%@' je neznámý typ.";

/* rename precedure/function error - can't delete old procedure */
"An error occurred while renaming. I couldn't delete '%@'.\n\nMySQL said: %@" = "Došlo k chybě při přejmenování. Nemohu smazat '%1$@'.\n\nMySQL řekl: %2$@";

/* rename precedure/function error - can't recreate procedure */
"An error occurred while renaming. I couldn't recreate '%@'.\n\nMySQL said: %@" = "Došlo k chybě při přejmenování. Nemohu znovu vytvořit '%1$@'.\n\nMySQL řekl: %2$@";

/* rename precedure/function error - can't retrieve syntax */
"An error occurred while renaming. I couldn't retrieve the syntax for '%@'.\n\nMySQL said: %@" = "Při přejmenování došlo k chybě. Nemohu načíst syntaxi pro '%1$@'.\n\nMySQL řekl: %2$@";

/* rename error - invalid create syntax */
"An error occurred while renaming. The CREATE syntax of '%@' could not be parsed." = "Došlo k chybě při přejmenování. CREATE syntaxe '%@' nelze analyzovat.";

/* message of panel when retrieving view information failed */
"An error occurred while retrieving status data.\n\nMySQL said: %@" = "An error occurred while retrieving status data.\n\nMySQL said: %@";

/* message of panel when create syntax cannot be retrieved */
"An error occurred while retrieving the create syntax for '%@'.\nMySQL said: %@" = "Nastala chyba při získávání syntaxe pro '%1$@'.\nMySQL řekl: %2$@";

/* add index error informative message */
"An error occurred while trying to add the index.\n\nMySQL said: %@" = "Nastala chyba při přidávání indexu.\n\nMySQL řekl: %@";

/* error adding password to keychain informative message */
"An error occurred while trying to add the password to your Keychain. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Nastala chyba při přidávání hesla do klíče. Opravením klíče by se to mohlo vyřešit, ale pokud se to nepodaří, nahlaste to týmu Sequel Ace, který dodá chybový kód %i.";

/* unable to copy database message informative message */
"An error occurred while trying to copy the database '%@' to '%@'." = "Nastala chyba při kopírování databáze '%1$@' do '%2$@'.";

/* error deleting index informative message */
"An error occurred while trying to delete the index.\n\nMySQL said: %@" = "Nastala chyba při odstraňování indexu.\n\nMySQL řekl: %@";

/* table status : row count query failed : error message */
"An error occurred while trying to determine the number of rows for %@.\nMySQL said: %@ (%lu)" = "Nastala chyba při zjišťování počtu řádků pro „%1$@”.\nMySQL řekl: %2$@ (%3$lu)";

/* unable to rename database message informative message */
"An error occurred while trying to rename the database '%@' to '%@'." = "Nastala chyba při přejmenování databáze '%1$@' na '%2$@'.";

/* error finding keychain item to edit informative message */
"An error occurred while trying to retrieve the Keychain item you're trying to edit. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Nastala chyba při načítání položky klíče, kterou se pokoušíte upravit. Opravením klíče by se to mohlo vyřešit, ale pokud se to nepodaří, nahlaste to týmu Sequel Ace, který dodá chybový kód %i.";

/* error updating keychain item informative message */
"An error occurred while trying to update the Keychain item. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Nastala chyba při aktualizaci položky klíče. Opravením klíče by se to mohlo vyřešit, ale pokud se to nepodaří, nahlaste to týmu Sequel Ace, který dodá chybový kód %i.";

/* mysql error occurred message */
"An error occurred" = "Došlo k chybě";

/* MySQL table info retrieval error message */
"An error occurred retrieving table information.  MySQL said: %@" = "Došlo k chybě při načítání informací o tabulce. MySQL řekl: %@";

/* SQL encoding read error */
"An error occurred when reading the file, as it could not be read in the encoding you selected (%@).\n\nOnly %ld queries were executed." = "Při čtení souboru došlo k chybě, protože jej nelze přečíst ve vybraném kódování (%1$@).\n\nPouze %2$ld dotazy byly provedeny.";

/* CSV encoding read error */
"An error occurred when reading the file, as it could not be read using the encoding you selected (%@).\n\nOnly %ld rows were imported." = "Došlo k chybě při čtení souboru, protože nelze číst pomocí vybraného kódování (%1$@).\n\nPouze %2$ld řádky byly importovány.";

/* SQL read error, including detail from system */
"An error occurred when reading the file.\n\nOnly %ld queries were executed.\n\n(%@)" = "An error occurred when reading the file.\n\nOnly %1$ld queries were executed.\n\n(%2$@)";

/* CSV read error, including detail string from system */
"An error occurred when reading the file.\n\nOnly %ld rows were imported.\n\n(%@)" = "Při čtení souboru došlo k chybě.\n\nByly importovány pouze řádky %1$ld .\n\n(%2$@)";

/* error adding field informative message */
"An error occurred when trying to add the field '%@' via\n\n%@\n\nMySQL said: %@" = "Nastala chyba při přidávání pole '%1$@' přes\n\n%2$@\n\nMySQL řekl: %3$@";

/* error changing field informative message */
"An error occurred when trying to change the field '%@' via\n\n%@\n\nMySQL said: %@" = "Nastala chyba při pokusu změnit pole '%1$@' přes\n\n%2$@\n\nMySQL řekl: %3$@";

/* error changing table collation informative message */
"An error occurred when trying to change the table collation to '%@'.\n\nMySQL said: %@" = "Nastala chyba při pokusu změnit kollaci tabulky na '%1$@'.\n\nMySQL řekl: %2$@";

/* error changing table encoding informative message */
"An error occurred when trying to change the table encoding to '%@'.\n\nMySQL said: %@" = "Nastala chyba při pokusu změnit tabulkové kódování na '%1$@'.\n\nMySQL řekl: %2$@";

/* error changing table type informative message */
"An error occurred when trying to change the table type to '%@'.\n\nMySQL said: %@" = "Nastala chyba při pokusu o změnu typu tabulky na '%1$@'.\n\nMySQL řekl: %2$@";

/* error changing table comment informative message */
"An error occurred when trying to change the table's comment to '%@'.\n\nMySQL said: %@" = "Nastala chyba při pokusu změnit komentář tabulky na '%1$@'.\n\nMySQL řekl: %2$@";

/* an error occurred while analyzing the %@.\n\nMySQL said:%@ */
"An error occurred while analyzing the %@.\n\nMySQL said:%@" = "Došlo k chybě při analýze %1$@.\n\nMySQL řekl:%2$@";

/* an error occurred while fetching the optimized field type.\n\nMySQL said:%@ */
"An error occurred while fetching the optimized field type.\n\nMySQL said:%@" = "Při načítání optimalizovaného typu pole došlo k chybě.\n\nMySQL řekl:%@";

/* an error occurred while trying to flush the %@.\n\nMySQL said:%@ */
"An error occurred while flushing the %@.\n\nMySQL said:%@" = "Došlo k chybě při splachování %1$@.\n\nMySQL řekl:%2$@";

/* sql import error message */
"An error occurred while importing SQL" = "Při importu SQL došlo k chybě";

/* an error occurred while trying to optimze the %@.\n\nMySQL said:%@ */
"An error occurred while optimzing the %@.\n\nMySQL said:%@" = "Došlo k chybě při optimalizaci %1$@.\n\nMySQL řekl:%2$@";

/* an error occurred while performing the checksum on the %@.\n\nMySQL said:%@ */
"An error occurred while performing the checksum on %@.\n\nMySQL said:%@" = "Došlo k chybě při provádění kontrolního součtu na %1$@.\n\nMySQL řekl:%2$@";

/* an error occurred while trying to repair the %@.\n\nMySQL said:%@ */
"An error occurred while repairing the %@.\n\nMySQL said:%@" = "Došlo k chybě při opravě %1$@.\n\nMySQL řekl:%2$@";

/* message of panel when retrieving information failed */
"An error occurred while retrieving information.\nMySQL said: %@" = "Při načítání informací došlo k chybě.\nMySQL řekl: %@";

/* error retrieving table information informative message */
"An error occurred while retrieving the information for table '%@'. Please try again.\n\nMySQL said: %@" = "Při načítání informací pro tabulku '%1$@' došlo k chybě. Opakujte akci.\n\nMySQL řekl: %2$@";

/* error retrieving table information informative message */
"An error occurred while retrieving the trigger information for table '%@'. Please try again.\n\nMySQL said: %@" = "Došlo k chybě při načítání spouštěčích informací pro tabulku '%1$@'. Opakujte akci.\n\nMySQL řekl: %2$@";

/* error adding new column informative message */
"An error occurred while trying to add the new column '%@' by\n\n%@.\n\nMySQL said: %@" = "Nastala chyba při přidávání nového sloupce '%1$@' od\n\n%2$@.\n\nMySQL řekl: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@' by\n\n%@.\n\nMySQL said: %@" = "Nastala chyba při přidávání nové tabulky '%1$@' od\n\n%2$@.\n\nMySQL řekl: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@'.\n\nMySQL said: %@" = "Nastala chyba při přidávání nové tabulky '%1$@'.\n\nMySQL řekl: %2$@";

/* error while trying to alter table message */
"An error occurred while trying to alter table '%@'.\n\nMySQL said: %@" = "Nastala chyba při pokusu změnit tabulku '%1$@'.\n\nMySQL řekl: %2$@";

/* an error occurred while trying to check the %@.\n\nMySQL said:%@ */
"An error occurred while trying to check the %@.\n\nMySQL said:%@" = "Nastala chyba při pokusu o kontrolu %1$@.\n\nMySQL řekl:%2$@";

/* error deleting relation informative message */
"An error occurred while trying to delete the relation '%@'.\n\nMySQL said: %@" = "Nastala chyba při odstraňování relace '%1$@'.\n\nMySQL řekl: %2$@";

/* unable to get list of users informative message */
"An error occurred while trying to get the list of users. Please make sure you have the necessary privileges to perform user management, including access to the mysql.user table." = "Nastala chyba při pokusu získat seznam uživatelů. Ujistěte se, že máte potřebná oprávnění k provádění uživatelského řízení, včetně přístupu k mysql.user tabulce.";

/* error importing table informative message */
"An error occurred while trying to import a table via: \n%@\n\n\nMySQL said: %@" = "Nastala chyba při importu tabulky via: \n%1$@\n\n\nMySQL řekl: %2$@";

/* error moving field informative message */
"An error occurred while trying to move the field.\n\nMySQL said: %@" = "Nastala chyba při pokusu přesunout pole.\n\nMySQL řekl: %@";

/* error resetting auto_increment informative message */
"An error occurred while trying to reset AUTO_INCREMENT of table '%@'.\n\nMySQL said: %@" = "Nastala chyba při obnovování AUTO_INCREMENT tabulky '%1$@'.\n\nMySQL řekl: %2$@";

/* error truncating table informative message */
"An error occurred while trying to truncate the table '%@'.\n\nMySQL said: %@" = "Nastala chyba při pokusu o zkrácení tabulky '%1$@'.\n\nMySQL řekl: %2$@";

/* mysql error occurred informative message */
"An error occurred whilst trying to perform the operation.\n\nMySQL said: %@" = "Při pokusu o provedení operace došlo k chybě.\n\nMySQL řekl: %@";

/* mysql resource limits unsupported message */
"Resource Limits are not supported for your version of MySQL. Any Resouce Limits you specified have been discarded and not saved. MySQL said: %@" = "Limity zdrojů nejsou pro vaši verzi MySQL podporovány. Jakékoliv limity Resouce limit, které jste zadali, byly zahozeny a neuloženy. MySQL řekl: %@";

/* Export files creation error explanatory text */
"An unhandled error occurred when attempting to create %lu of the export files.  Please check the details and try again." = "Při pokusu o vytvoření %lu exportovaných souborů došlo k neošetřené chybě. Zkontrolujte podrobnosti a zkuste to znovu.";

/* All export files creation error explanatory text */
"An unhandled error occurred when attempting to create each of the export files.  Please check the details and try again." = "Při pokusu o vytvoření každého z exportních souborů došlo k neošetřené chybě. Zkontrolujte podrobnosti a zkuste to znovu.";

/* Export file creation error explanatory text */
"An unhandled error occurred when attempting to create the export file.  Please check the details and try again." = "Při pokusu o vytvoření exportního souboru došlo k neošetřené chybě. Zkontrolujte podrobnosti a zkuste to znovu.";

/* ANALYZE one or more tables - result title */
"Analyze %@" = "%@";

/* analyze selected items menu item */
"Analyze Selected Items" = "Analyzovat vybrané položky";

/* analyze table menu item */
"Analyze Table" = "Analyzovat tabulku";

/* analyze table failed message */
"Analyze table failed." = "Analyzovat tabulku selhala.";

/* change table type informative message */
"Are you sure you want to change this table's type to %@?\n\nPlease be aware that changing a table's type has the potential to cause the loss of some or all of its data. This action cannot be undone." = "Opravdu chcete změnit typ tabulky na %@?\n\nMějte prosím na paměti, že změna typu tabulky může způsobit ztrátu některých nebo všech jejích dat. Tuto akci nelze vrátit zpět.";

/* clear global history list informative message */
"Are you sure you want to clear the global history list? This action cannot be undone." = "Opravdu chcete vymazat globální seznam historie? Tuto akci nelze vrátit zpět.";

/* clear history list for %@ informative message */
"Are you sure you want to clear the history list for %@? This action cannot be undone." = "Opravdu chcete vymazat seznam historie pro %@? Tuto akci nelze vrátit zpět.";

/* truncate tables informative message */
"Are you sure you want to delete ALL records in the selected tables? This operation cannot be undone." = "Opravdu chcete odstranit VŠECHNY záznamy ve vybraných tabulkách? Tuto operaci nelze vrátit zpět.";

/* truncate table informative message */
"Are you sure you want to delete ALL records in the table '%@'? This operation cannot be undone." = "Opravdu chcete odstranit VŠECHNY záznamy v tabulce '%@'? Tuto operaci nelze vrátit zpět.";

/* delete all rows informative message */
"Are you sure you want to delete all the rows from this table? This action cannot be undone." = "Opravdu chcete odstranit všechny řádky z této tabulky? Tuto akci nelze vrátit zpět.";

/* delete table/view informative message */
"Are you sure you want to delete the %@ '%@'? This operation cannot be undone." = "Opravdu chcete odstranit %1$@ '%2$@'? Tuto operaci nelze vrátit zpět.";

/* delete database informative message */
"Are you sure you want to delete the database '%@'? This operation cannot be undone." = "Opravdu chcete odstranit databázi '%@'? Tuto operaci nelze vrátit zpět.";

/* delete database informative message */
"Are you sure you want to delete the favorite '%@'? This operation cannot be undone." = "Jste si jisti, že chcete odstranit oblíbené '%@'? Tuto operaci nelze vrátit zpět.";

/* delete field informative message */
"Are you sure you want to delete the field '%@'? This action cannot be undone." = "Opravdu chcete odstranit pole '%@'? Tuto akci nelze vrátit zpět.";

/* delete database informative message */
"Are you sure you want to delete the group '%@'? All groups and favorites within this group will also be deleted. This operation cannot be undone." = "Jste si jisti, že chcete odstranit skupinu '%@'? Všechny skupiny a oblíbené v této skupině budou také odstraněny. Tuto operaci nelze vrátit zpět.";

/* delete index informative message */
"Are you sure you want to delete the index '%@'? This action cannot be undone." = "Opravdu chcete smazat index '%@'? Tuto akci nelze vrátit zpět.";

/* delete tables/views informative message */
"Are you sure you want to delete the selected %@? This operation cannot be undone." = "Opravdu chcete odstranit vybranou %@? Tuto operaci nelze vrátit zpět.";

/* delete rows informative message */
"Are you sure you want to delete the selected %ld rows from this table? This action cannot be undone." = "Are you sure you want to delete the selected %ld rows from this table? This action cannot be undone.";

/* delete selected relation informative message */
"Are you sure you want to delete the selected relations? This action cannot be undone." = "Jste si jisti, že chcete odstranit vybrané vztahy? Tuto akci nelze vrátit zpět.";

/* delete selected row informative message */
"Are you sure you want to delete the selected row from this table? This action cannot be undone." = "Opravdu chcete odstranit vybraný řádek z této tabulky? Tuto akci nelze vrátit zpět.";

/* delete selected trigger informative message */
"Are you sure you want to delete the selected triggers? This action cannot be undone." = "Opravdu chcete odstranit vybrané spouštěče? Tuto akci nelze vrátit zpět.";

/* kill connection informative message */
"Are you sure you want to kill connection ID %lld?\n\nPlease be aware that continuing to kill this connection may result in data corruption. Please proceed with caution." = "Jste si jisti, že chcete ukončit připojení ID %lld?\n\nMějte prosím na paměti, že pokračování v ukončení tohoto připojení může mít za následek poškození dat. Pokračujte prosím opatrně.";

/* kill query informative message */
"Are you sure you want to kill the current query executing on connection ID %lld?\n\nPlease be aware that continuing to kill this query may result in data corruption. Please proceed with caution." = "Opravdu chcete ukončit aktuální dotaz spuštěný při připojení ID %lld?\n\nMějte prosím na paměti, že pokračování zabíjení tohoto dotazu může mít za následek poškození dat. Pokračujte prosím opatrně.";

/* Bundle Editor : Remove-Bundle: remove dialog message */
"Are you sure you want to move the selected Bundle to the Trash and remove them respectively?" = "Jste si jisti, že chcete přesunout vybraný balíček do koše a odstranit jej?";

/* continue to print informative message */
"Are you sure you want to print the current content view of the table '%@'?\n\nIt currently contains %@ rows, which may take a significant amount of time to print." = "Opravdu chcete vytisknout aktuální zobrazení obsahu v tabulce '%1$@'?\n\nV současné době obsahuje %2$@ řádky, které mohou trvat dlouho.";

/* remove all query favorites informative message */
"Are you sure you want to remove all of your saved query favorites? This action cannot be undone." = "Opravdu chcete odstranit všechny uložené oblíbené dotazy? Tuto akci nelze vrátit zpět.";

/* remove all selected content filters informative message */
"Are you sure you want to remove all selected content filters? This action cannot be undone." = "Opravdu chcete odstranit všechny vybrané filtry obsahu? Tuto akci nelze vrátit zpět.";

/* remove all selected query favorites informative message */
"Are you sure you want to remove all selected query favorites? This action cannot be undone." = "Opravdu chcete odstranit všechny vybrané oblíbené dotazy? Tuto akci nelze vrátit zpět.";

/* Table Info Section : current value of auto_increment */
"auto_increment: %@" = "auto_přírůstek: %@";

/* Encoding autodetect menu item */
"Autodetect" = "Autodetekovat";

/* background label for color table (Prefs > Editor) */
"Background" = "Pozadí";

/* backtick quote label for color table (Prefs > Editor) */
"Backtick Quote" = "Zpětná nabídka";

/* bash error */
"BASH Error" = "Chyba BASH";

/* toolbar item label for switching to the Table Content tab */
"Browse & Edit Table Content" = "Procházet a upravit obsah tabulky";

/* build label */
"build" = "stavět";

/* build label */
"Build" = "Postavit";

/* bundle editor menu item label */
"Bundle Editor" = "Editor balíčků";

/* bundle error */
"Bundle Error" = "Chyba balíčku";

/* bundles menu item label */
"Bundles" = "Bundles";

/* Bundle Editor : Outline View : 'BUNDLES' item */
"BUNDLES" = "BUNDLES";

/* Bundle Editor : Outline View : Menu Category item : tooltip */
"Bundles in category %@" = "Balíčky v kategorii %@";

/* bundles installation error */
"Bundles Installation Error" = "Chyba instalace balíčků";

/* bzip2 compression export summary - within a sentence */
"bzip2 compression" = "bzip2 komprese";

/* description of geometry */
"Can store a single spatial value of types POINT, LINESTRING or POLYGON. Spatial support in MySQL is based on the OpenGIS Geometry Model." = "Lze uložit jedinou prostorovou hodnotu typů POINT, LINESTRING nebo POLYGON. Prostorová podpora v MySQL je založena na modelu geometrie OpenGIS.";

/* Bundle Editor : Remove-Bundle: cancel button
 cancel
 cancel button
 Open Files : Bundle : Already-Installed : Cancel button
 Preferences : Network : Custom SSH client : warning dialog : cancel button
 shutdown server : confirmation dialog : cancel button */
"Cancel" = "Zrušit";

/* sql import : charset error alert : cancel button */
"Cancel Import" = "Zrušit import";

/* cancelling task status message */
"Cancelling..." = "Rušení...";

/* empty query informative message */
"Cannot save an empty query." = "Nelze uložit prázdný dotaz.";

/* caret label for color table (Prefs > Editor) */
"Caret" = "Péče";

/* change button */
"Change" = "Změnit";

/* change focus to table list menu item */
"Change Focus to Table List" = "Změnit zaměření na seznam tabulek";

/* change table type message */
"Change table type" = "Změnit typ tabulky";

/* unsaved changes informative message */
"Changes have been made, which will be lost if this window is closed. Are you sure you want to continue" = "Byly provedeny změny, které budou ztraceny, pokud bude toto okno uzavřeno. Jste si jisti, že chcete pokračovat";

/* quitting app informal alert title */
"Close the app?" = "Close the app?";

/* quitting app informal alert body */
"Are you sure you want to quit the app?" = "Are you sure you want to quit the app?";

/* character set client: %@ */
"character set client: %@" = "Klient sady znaků: %@";

/* CHECK one or more tables - result title */
"Check %@" = "Zkontrolovat %@";

/* check of all selected items successfully passed message */
"Check of all selected items successfully passed." = "Kontrola všech vybraných položek úspěšně prošla.";

/* check option: %@ */
"check option: %@" = "volba kontroly: %@";

/* check selected items menu item */
"Check Selected Items" = "Zkontrolovat vybrané položky";

/* check table menu item */
"Check Table" = "Kontrolní tabulka";

/* check table failed message */
"Check table failed." = "Zaškrtávací tabulka se nezdařila.";

/* check table successfully passed message */
"Check table successfully passed." = "Zaškrtávací tabulka úspěšně prošla.";

/* check view menu item */
"Check View" = "Zkontrolovat zobrazení";

/* checking field data for editing task description */
"Checking field data for editing..." = "Kontrola dat polí pro úpravy...";

/* checksum %@ message */
"Checksum %@" = "Kontrolní součet %@";

/* checksum selected items menu item */
"Checksum Selected Items" = "Vybrané položky kontrolního součtu";

/* checksum table menu item */
"Checksum Table" = "Tabulka kontrolního součtu";

/* Checksums of %@ message */
"Checksums of %@" = "Kontrolní součty %@";

/* menu item for choose db */
"Choose Database..." = "Vybrat databázi...";

/* cancelling export cleaning up message */
"Cleaning up..." = "Čištění...";

/* clear button */
"Clear" = "Vyčistit";

/* toolbar item for clear console */
"Clear Console" = "Vymazat konzoli";

/* clear global history menu item title */
"Clear Global History" = "Vymazat globální historii";

/* clear history for %@ menu title */
"Clear History for %@" = "Vymazat historii pro %@";

/* clear history message */
"Clear History?" = "Vymazat historii?";

/* tooltip for toolbar item for clear console */
"Clear the console which shows all MySQL commands performed by Sequel Ace" = "Vymaže konzoli zobrazující všechny MySQL příkazy provedené Sequel Ace";

/* clear the document-based history list tooltip message */
"Clear the document-based history list" = "Vymazat seznam historie dokumentů";

/* clear the global history list tooltip message */
"Clear the global history list" = "Vymazat seznam globálních dějin";

/* Close menu item */
"Close" = "Zavřít";

/* close tab context menu item */
"Close Tab" = "Zavřít kartu";

/* Close Window menu item */
"Close Window" = "Zavřít okno";

/* collation label (Navigator) */
"Collation" = "Kollace";

/* collation connection: %@ */
"collation connection: %@" = "spojení: %@";

/* comment label */
"Comment" = "Komentář";

/* Title of action menu item to comment line */
"Comment Line" = "Komentář";

/* Title of action menu item to comment selection */
"Comment Selection" = "Výběr komentáře";

/* connect button */
"Connect" = "Připojit";

/* Connect via socket button */
"Connect via socket" = "Připojit pomocí soketu";

/* connection established message */
"Connected" = "Připojeno";

/* description for connected notification */
"Connected to %@" = "Připojeno k %@";

/* message of panel when connection to db failed */
"Connected to host, but unable to connect to database %@.\n\nBe sure that the database exists and that you have the necessary privileges.\n\nMySQL said: %@" = "Připojeno k serveru, ale nelze se připojit k databázi %1$@.\n\nUjistěte se, že databáze existuje a že máte potřebná oprávnění.\n\nMySQL řekl: %2$@";

/* Generic connecting very short status message */
"Connecting..." = "Připojování...";

/* window title string indicating that sp is connecting */
"Connecting…" = "Připojování…";

/* error while reading connection data file */
"Connection data file couldn't be read." = "Datový soubor připojení nelze přečíst.";

/* message error while reading connection data file and suggesting to save it under a differnet name */
"Connection data file %@ couldn't be read. Please try to save the document under a different name." = "Datový soubor připojení %@ nelze přečíst. Zkuste uložit dokument pod jiným názvem.";

/* connection failed title */
"Connection failed!" = "Připojení se nezdařilo!";

/* Connection file is encrypted */
"Connection file is encrypted" = "Připojovací soubor je šifrován";

/* Connection success very short status message */
"Connection succeeded" = "Spojení bylo úspěšné";

/* Console */
"Console" = "Konzole";

/* Console : Save as : Initial filename */
"ConsoleLog" = "KonsoleLog";

/* toolbar item label for switching to the Table Content tab */
"Content" = "Obsah";

/* content filter clause is empty tooltip. */
"Content Filter clause is empty." = "Ustanovení o filtru obsahu je prázdné.";

/* continue button
 Continue button title */
"Continue" = "Pokračovat";

/* continue to print message */
"Continue to print?" = "Pokračovat v tisku?";

/* Copy as RTF */
"Copy as RTF" = "Kopírovat jako RTF";

/* copy create func syntax menu item
 Table List : Context Menu : copy CREATE FUNCTION syntax */
"Copy Create Function Syntax" = "Kopírovat syntaxi pro vytvoření funkce";

/* copy create proc syntax menu item
 Table List : Context Menu : Copy CREATE PROCEDURE syntax
 Table List : Gear Menu : Copy CREATE PROCEDURE syntax */
"Copy Create Procedure Syntax" = "Kopírovat vytvoření procedury syntaxe";

/* copy create syntaxes menu item
 Table List : Context Menu : Copy CREATE syntax (multiple selection)
 Table List : Gear Menu : Copy CREATE syntax (multiple selection) */
"Copy Create Syntaxes" = "Kopírovat Syntaxe";

/* copy create table syntax menu item
 Table List : Context Menu : Copy CREATE syntax (single table)
 Table List : Gear Menu : Copy CREATE syntax (single table) */
"Copy Create Table Syntax" = "Kopírovat syntaxi pro vytvoření tabulky";

/* copy create view syntax menu item
 Table List : Context Menu : Copy CREATE view statement
 Table List : Gear Menu : Copy CREATE view statement */
"Copy Create View Syntax" = "Kopírovat syntaxi vytvořit zobrazení";

/* copy server variable name menu item */
"Copy Variable Name" = "Kopírovat název proměnné";

/* copy server variable names menu item */
"Copy Variable Names" = "Kopírovat názvy proměnných";

/* copy server variable value menu item */
"Copy Variable Value" = "Kopírovat hodnotu proměnné";

/* copy server variable values menu item */
"Copy Variable Values" = "Kopírovat hodnoty proměnné";

/* Procedure/function export permission error */
"Could not export the %@ '%@' because of a permissions error.\n" = "Nelze exportovat %1$@ '%2$@' z důvodu chyby oprávnění.\n";

/* Error when we can't parse/split file as CSV */
"Could not parse file as CSV" = "Nepodařilo se naparsovat soubor jako CSV";

/* message when database selection failed */
"Could not select database" = "Nelze vybrat databázi";

/* Alter Database : Query Failed ($1 = mysql error message) */
"Couldn't alter database.\nMySQL said: %@" = "Nelze změnit databázi.\nMySQL řekl: %@";

/* Couldn't copy default themes to Application Support Theme folder!\nError: %@ */
"Couldn't copy default themes to Application Support Theme folder!\nError: %@" = "Nelze zkopírovat výchozí motivy do složky podpory aplikace!\nChyba: %@";

/* message of panel when table cannot be created */
"Couldn't create '%@'.\nMySQL said: %@" = "Nelze vytvořit '%1$@'.\nMySQL řekl: %2$@";

/* Couldn't create Application Support Bundle folder!\nError: %@ */
"Couldn't create Application Support Bundle folder!\nError: %@" = "Složka Balíček aplikací se nepodařilo vytvořit!\nChyba: %@";

/* Couldn't create Application Support Theme folder!\nError: %@ */
"Couldn't create Application Support Theme folder!\nError: %@" = "Složka podpory aplikací se nepodařilo vytvořit!\nChyba: %@";

/* message of panel when creation of db failed */
"Couldn't create database.\nMySQL said: %@" = "Nelze vytvořit databázi.\nMySQL řekl: %@";

/* message of panel when an item cannot be deleted */
"Couldn't delete '%@'.\n\nMySQL said: %@" = "Nelze odstranit '%1$@'.\n\nMySQL řekl: %2$@";

/* message of panel when an item cannot be deleted including informative message about using force deletion */
"Couldn't delete '%@'.\n\nSelecting the 'Force delete' option may prevent this issue, but may leave the database in an inconsistent state.\n\nMySQL said: %@" = "Nelze odstranit '%1$@'.\n\nVýběr možnosti 'Vynutit odstranění' může tomuto problému zabránit, ale může ponechat databázi v nekonzistentním stavu.\n\nMySQL řekl: %2$@";

/* message of panel when field cannot be deleted */
"Couldn't delete field %@.\nMySQL said: %@" = "Nelze odstranit pole %1$@.\nMySQL řekl: %2$@";

/* message when deleteing all rows failed */
"Couldn't delete rows.\n\nMySQL said: %@" = "Nelze odstranit řádky.\n\nMySQL řekl: %@";

/* message of panel when deleting db failed */
"Couldn't delete the database.\nMySQL said: %@" = "Nelze odstranit databázi.\nMySQL řekl: %@";

/* message of panel when an item cannot be renamed */
"Couldn't duplicate '%@'.\nMySQL said: %@" = "Nelze duplikovat '%1$@'.\nMySQL řekl: %2$@";

/* message of panel when flushing privs failed */
"Couldn't flush privileges.\nMySQL said: %@" = "Nelze vymazat oprávnění.\nMySQL řekl: %@";

/* message of panel when table information cannot be retrieved */
"Couldn't get create syntax.\nMySQL said: %@" = "Nelze vytvořit syntaxi.\nMySQL řekl: %@";

/* Custom Query result editing error - could not identify a corresponding column */
"Couldn't identify field origin unambiguously. The column '%@' contains data from more than one table." = "Nelze jednoznačně identifikovat počátek pole. Sloupec '%@' obsahuje data z více než jedné tabulky.";

/* message of panel when loading of row failed */
"Couldn't load the row. Reload the table to be sure that the row exists and use a primary key for your table." = "Řádek se nepodařilo načíst. Znovu načtěte tabulku a ujistěte se, že řádek existuje a použijte primární klíč pro tabulku.";

/* Couldn't read the file content of */
"Couldn't read the file content of" = "Nelze číst obsah souboru";

/* text shown if an error occurred while sorting the result table */
"Couldn't sort column." = "Řazení sloupce se nezdařilo.";

/* message of panel when sorting of table failed */
"Couldn't sort table. MySQL said: %@" = "Nelze seřadit tabulku. MySQL řekl: %@";

/* message of panel when error while updating field to db */
"Couldn't write field.\nMySQL said: %@" = "Couldn't write field.\nMySQL said: %@";

/* create syntax for table comment */
"Create syntax for" = "Vytvořit syntaxi pro";

/* Create syntax label */
"Create syntax for %@ '%@'" = "Vytvořit syntaxi pro %1$@ '%2$@'";

/* Create syntaxes for selected items label */
"Create syntaxes for selected items" = "Vytvořit syntaxe pro vybrané položky";

/* Table Info Section : table create options */
"create_options: %@" = "create_options: %@";

/* created: %@
 Table Info Section : time+date table was created at */
"created: %@" = "vytvořeno: %@";

/* description of polygon */
"Creates a surface by combining one LinearRing (ie. a LineString that is closed and simple) as the outside boundary with zero or more inner LinearRings acting as \"holes\"." = "Vytvoří povrch kombinací jedné linearringu. LineString, který je uzavřený a jednoduchý) jako vnější hranice s nulovou nebo více vnitřních linearingů, které působí jako \"dírky\".";

/* Creating table task string */
"Creating %@..." = "Vytváření %@...";

/* Bundle Editor : Fallback Input source dropdown : 'current line' item */
"Current Line" = "Aktuální řádek";

/* Bundle Editor : Fallback Input source dropdown : 'current query' item */
"Current Query" = "Aktuální dotaz";

/* header for current selection in filtered list */
"CURRENT SELECTION" = "AKTUÁLNÍ VÝBĚR";

/* Bundle Editor : Fallback Input source dropdown : 'current word' item */
"Current Word" = "Aktuální slovo";

/* SSH connection : debug header with user-defined ssh binary */
"Custom SSH binary enabled. Disable in Preferences to rule out incompatibilities!" = "Vlastní binární soubor SSH je povolen. Zakažte v nastavení pro vyloučení nekompatibility!";

/* customize file name label */
"Customize Filename (%@)" = "Přizpůsobit název souboru (%@)";

/* data access: %@ (%@) */
"data access: %@ (%@)" = "přístup k datům: %1$@ (%2$@)";

/* Bundle Editor : Scope dropdown : 'data table' item
 data table menu item label */
"Data Table" = "Tabulka údajů";

/* Bundle Editor : Outline View : 'Data Table' item : tooltip */
"Data Table Scope\ncommands will run on the Content and Query data tables" = "Příkazy s datovou tabulkou\nbudou spuštěny na tabulkách obsahu a dotazů";

/* export filename database token
 export header database label
 export source
 Table Structure : Collation dropdown : 'item is the same as the collation of database' marker
 Table Structure : Encoding dropdown : 'item is database default' marker */
"Database" = "Databáze";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'database changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'database changed' item */
"Database changed" = "Databáze změněna";

/* message of panel when no db name is given */
"Database must have a name." = "Databáze musí mít název.";

/* databsse rename unsupported message */
"Database Rename Unsupported" = "Přejmenování databáze není podporováno";

/* export filename date token */
"Date" = "Datum:";

/* export filename date token */
"Day" = "Den";

/* Add Table Sheet : Collation : Default (unknown)
 Collation Dropdown : Default (unknown)
 default label
 Table Structure : Collation dropdown : 'item is the same as the default collation of the row's charset' marker */
"Default" = "Výchozí";

/* Add Table : Collation : Default ($1 = collation name)
 Charset Dropdown : Default item ($1 = charset name)
 Collation Dropdown : Default ($1 = collation name)
 Collation Dropdown : Default collation for given charset ($1 = collation name)
 New Table Sheet : Table Engine Dropdown : Default */
"Default (%@)" = "Výchozí (%@)";

/* default bundles update */
"Default Bundles Update" = "Výchozí aktualizace balíčků";

/* import : csv field mapping : field default value */
"Default: %@" = "Výchozí: %@";

/* Query snippet default value placeholder */
"default_value" = "výchozí_hodnota";

/* definer label (Navigator) */
"Definer" = "Definátor";

/* definer: %@ */
"definer: %@" = "definice: %@";

/* description of enum */
"Defines a list of members, of which every field can use at most one. Values are sorted by their index number (starting at 0 for the first member)." = "Určuje seznam členů, kterých může každé pole použít nanejvýš jednou. Hodnoty jsou seřazeny podle jejich indexu (počínaje 0 pro prvního člena).";

/* delete button */
"Delete" = "Vymazat";

/* delete table/view message */
"Delete %@ '%@'?" = "Smazat %1$@ '%2$@'?";

/* table structure : indexes : delete index : error 1553 : delete index and FK button */
"Delete Both" = "Odstranit obojí";

/* delete database message */
"Delete database '%@'?" = "Smazat databázi '%@'?";

/* delete database message */
"Delete favorite '%@'?" = "Smazat oblíbené '%@'?";

/* delete field message */
"Delete field '%@'?" = "Smazat pole '%@'?";

/* delete func menu title */
"Delete Function" = "Odstranit funkci";

/* delete functions menu title */
"Delete Functions" = "Odstranit funkce";

/* delete database message */
"Delete group '%@'?" = "Odstranit skupinu '%@'?";

/* delete index message */
"Delete index '%@'?" = "Smazat index '%@'?";

/* delete items menu title */
"Delete Items" = "Odstranit položky";

/* delete proc menu title */
"Delete Procedure" = "Odstranit postup";

/* delete procedures menu title */
"Delete Procedures" = "Odstranit postupy";

/* delete relation menu item */
"Delete Relation" = "Odstranit vztah";

/* delete relation message */
"Delete relation" = "Odstranit vztah";

/* delete relations menu item */
"Delete Relations" = "Odstranit vztahy";

/* delete row menu item singular */
"Delete Row" = "Odstranit řádek";

/* delete rows menu item plural */
"Delete Rows" = "Odstranit řádky";

/* delete rows message */
"Delete rows?" = "Odstranit řádky?";

/* delete tables/views message */
"Delete selected %@?" = "Smazat vybranou %@?";

/* delete selected row message */
"Delete selected row?" = "Odstranit vybraný řádek?";

/* delete table menu title */
"Delete Table..." = "Odstranit tabulku...";

/* delete tables menu title */
"Delete Tables" = "Odstranit tabulky";

/* delete trigger menu item */
"Delete Trigger" = "Odstranit spouštěč";

/* delete trigger message */
"Delete trigger" = "Odstranit spouštěč";

/* delete triggers menu item */
"Delete Triggers" = "Odstranit spouštěče";

/* delete view menu title */
"Delete View" = "Odstranit zobrazení";

/* delete views menu title */
"Delete Views" = "Odstranit zobrazení";

/* Preferences : Network : SSL Chiper suites : List seperator */
"Disabled Cipher Suites" = "Zakázané Cipher Suites";

/* force table deltion button text tooltip */
"Disables foreign key checks (FOREIGN_KEY_CHECKS) before deletion and re-enables them afterwards." = "Zakáže kontroly cizího klíče (FOREIGN_KEY_CHECKS) před smazáním a poté je znovu povolí.";

/* discard changes button */
"Discard changes" = "Zrušit změny";

/* description for disconnected notification */
"Disconnected from %@" = "Odpojeno od %@";

/* do update operator tooltip */
"Do UPDATE where field contents match" = "AKTUALIZOVAT tam, kde obsah pole odpovídá";

/* message of panel asking for confirmation for loading large text into the query editor */
"Do you really want to load a SQL file with %@ of data into the Query Editor?" = "Opravdu chcete načíst SQL soubor s %@ dat do Editoru dotazů?";

/* message of panel asking for confirmation for inserting large text from dragging action */
"Do you really want to proceed with %@ of data?" = "Opravdu chceš pokračovat v %@ dat?";

/* shutdown server : confirmation dialog : title */
"Do you really want to shutdown the server?" = "Opravdu chcete vypnout server?";

/* dtd identifier label (Navigator) */
"DTD Identifier" = "DTD Identifikátor";

/* sql export dump of table label */
"Dump of table" = "Výpis tabulky";

/* sql export dump of view label */
"Dump of view" = "Výpis zobrazení";

/* text showing that app is writing dump */
"Dumping..." = "Vyčerpání...";

/* duplicate object message */
"Duplicate %@ '%@' to:" = "Duplikovat %1$@ '%2$@' do:";

/* duplicate func menu title */
"Duplicate Function..." = "Duplicitní funkce...";

/* duplicate host message */
"Duplicate Host" = "Duplikovat hostitele";

/* duplicate proc menu title */
"Duplicate Procedure..." = "Duplikovat postup...";

/* duplicate table menu title */
"Duplicate Table..." = "Duplikovat tabulku...";

/* duplicate user message */
"Duplicate User" = "Duplikovat uživatele";

/* duplicate view menu title */
"Duplicate View..." = "Duplikovat zobrazení...";

/* partial copy database support informative message */
"Duplicating the database '%@' is only partially supported as it contains objects other than tables (i.e. views, procedures, functions, etc.), which will not be copied.\n\nWould you like to continue?" = "Duplikování databáze '%@' je podporováno pouze částečně, protože obsahuje jiné objekty než tabulky (tj. zobrazení, postupy, funkce atd.), které nebudou kopírovány.\n\nChcete pokračovat?";

/* edit filter */
"Edit Filters…" = "Upravit filtry…";

/* Edit row button */
"Edit row" = "Upravit řádek";

/* toolbar item label for switching to the Table Structure tab */
"Edit Table Structure" = "Upravit strukturu tabulky";

/* edit theme list label */
"Edit Theme List…" = "Upravit seznam témat…";

/* edit user-defined filter */
"Edit user-defined Filters…" = "Upravit uživatelem definované filtry…";

/* empty query message */
"Empty query" = "Prázdný dotaz";

/* MySQL search language code - eg in http://search.mysql.com/search?q=select&site=refman-50&lr=lang_en */
"en" = "cs-cs";

/* encoding label (Navigator) */
"Encoding" = "Kódování";

/* Table Info Section : $1 = table charset */
"encoding: %1$@" = "kódování: %1$@";

/* Table Info Section : $1 = table charset, $2 = table collation */
"encoding: %1$@ (%2$@)" = "kódování: %1$@ (%2$@)";

/* Table Info Section : Table Engine */
"engine: %@" = "motor: %@";

/* enter connection details label */
"Enter connection details below, or choose a favorite" = "Níže zadejte podrobnosti o připojení nebo vyberte oblíbenou položku";

/* SSH key password prompt */
"Enter your password for the SSH key\n\"%@\"" = "Zadejte své heslo pro SSH klíč\n'%@";

/* Bundle Editor : Fallback Input source dropdown : 'entire content' item
 Bundle Editor : Scope=Field : Input source dropdown: 'entire content' item */
"Entire Content" = "Celý obsah";

/* Bundle Editor : Copy-Command-Error : error dialog title
 error */
"Error" = "Chyba";

/* error adding field message */
"Error adding field" = "Chyba při přidávání pole";

/* error adding new column message */
"Error adding new column" = "Chyba při přidávání nového sloupce";

/* error adding new table message */
"Error adding new table" = "Chyba při přidávání nové tabulky";

/* error adding password to keychain message */
"Error adding password to Keychain" = "Chyba při přidávání hesla do Klíčenky";

/* error changing field message */
"Error changing field" = "Chyba při změně pole";

/* error changing table collation message */
"Error changing table collation" = "Chyba při změně collace tabulky";

/* error changing table comment message */
"Error changing table comment" = "Chyba při změně komentáře tabulky";

/* error changing table encoding message */
"Error changing table encoding" = "Chyba při změně kódování tabulky";

/* error changing table type message */
"Error changing table type" = "Chyba při změně typu tabulky";

/* error creating relation message */
"Error creating relation" = "Chyba při vytváření vztahu";

/* error creating trigger message */
"Error creating trigger" = "Chyba při vytváření spouštěče";

/* error for message */
"Error for" = "Chyba pro";

/* error for bash command ($1), $2=message */
"Error for “%1$@”:\n%2$@" = "Chyba pro „%1$@”:\n%2$@";

/* error moving field message */
"Error moving field" = "Chyba při přesunu pole";

/* error occurred */
"error occurred" = "Došlo k chybě";

/* error reading import file */
"Error reading import file." = "Chyba při čtení importního souboru.";

/* error finding keychain item to edit message */
"Error retrieving Keychain item to edit" = "Chyba při načítání položky klíče k úpravě";

/* error retrieving table information message */
"Error retrieving table information" = "Chyba při načítání informací tabulky";

/* error retrieving trigger information message */
"Error retrieving trigger information" = "Chyba při načítání informací o spouštění";

/* error truncating table message */
"Error truncating table" = "Chyba zkrácení tabulky";

/* error updating keychain item message */
"Error updating Keychain item" = "Chyba při aktualizaci položky klíče";

/* error while analyzing selected items message */
"Error while analyzing selected items" = "Chyba při analýze vybraných položek";

/* error while checking selected items message */
"Error while checking selected items" = "Chyba při kontrole vybraných položek";

/* error while converting color scheme data */
"Error while converting color scheme data" = "Chyba při konverzi barevného schématu";

/* error while converting connection data */
"Error while converting connection data" = "Chyba při konverzi dat připojení";

/* Content filters could not be converted to plist upon export - message title (ContentFilterManager) */
"Error while converting content filter data" = "Chyba při konverzi dat filtru obsahu";

/* error while converting query favorite data */
"Error while converting query favorite data" = "Chyba při konverzi oblíbených dotazů";

/* error while converting session data */
"Error while converting session data" = "Chyba při převodu dat relace";

/* Error while deleting field */
"Error while deleting field" = "Chyba při mazání pole";

/* Bundle Editor : Copy-Command-Error : Copying failed error message */
"Error while duplicating Bundle content." = "Chyba při kopírování obsahu balíčku";

/* error while executing javascript bash command */
"Error while executing JavaScript BASH command" = "Chyba při provádění JavaScriptu BASH příkazu";

/* error while fetching the optimized field type message */
"Error while fetching the optimized field type" = "Chyba při načítání optimalizovaného typu pole";

/* error while flushing selected items message */
"Error while flushing selected items" = "Chyba při splachování vybraných položek";

/* error while importing table message */
"Error while importing table" = "Chyba při importu tabulky";

/* Open Files : Bundle : Install-Error : error dialog title
 Open Files : Bundle : UUID : Error dialog title */
"Error while installing Bundle" = "Chyba při instalaci balíčku";

/* error while installing color theme file */
"Error while installing color theme file" = "Chyba při instalaci barevného souboru motivu";

/* Bundle Editor : Trash-Bundle(s)-Error : error dialog title
 error while moving %@ to trash
 Open Files : Bundle : Already-Installed : Delete-Old-Error : Could not delete old bundle before installing new version. */
"Error while moving %@ to Trash." = "Chyba při přesouvání %@ do koše.";

/* error while optimizing selected items message */
"Error while optimizing selected items" = "Chyba při optimalizaci vybraných položek";

/* error while parsing CREATE TABLE syntax */
"Error while parsing CREATE TABLE syntax" = "Chyba při parsování TABULKA syntaxi";

/* error while reading connection data file */
"Error while reading connection data file" = "Chyba při čtení datového souboru";

/* error while reading data file */
"Error while reading data file" = "Chyba při čtení datového souboru";

/* error while repairing selected items message */
"Error while repairing selected items" = "Chyba při opravě vybraných položek";

/* Bundle Editor : Save-Bundle-Error : error dialog title */
"Error while saving the Bundle." = "Chyba při ukládání balíčku.";

/* Bundle Editor : Save-and-Close-Error : error dialog title */
"Error while saving %@." = "Chyba při ukládání %@.";

/* Errors title */
"Errors" = "Chyby";

/* Mysql Help Viewer : Help Topic: Example section title */
"Example:" = "Příklad:";

/* Bundle Editor : BLOB dropdown : 'exclude BLOB' item */
"exclude BLOB" = "vyloučit BLOB";

/* execution privilege label (Navigator) */
"Execution Privilege" = "Oprávnění exekuce";

/* execution privilege: %@ */
"execution privilege: %@" = "oprávnění exekuce: %@";

/* execution stopped message */
"Execution stopped!\n" = "Provedení zastaveno!\n";

/* export selected favorites menu item */
"Export Selected..." = "Exportovat vybrané...";

/* text showing that the application is importing a supplied format */
"Exporting %@" = "Exportuji %@";

/* text showing that the application is exporting a Dot file */
"Exporting Dot File" = "Exportování souboru s tečkou";

/* text showing that the application is exporting SQL */
"Exporting SQL" = "Exportování SQL";

/* extra label (Navigator) */
"Extra" = "Další";

/* table structure : indexes : delete index : no columns error : title */
"Failed to remove index '%@'" = "Odstranění indexu '%@ ' se nezdařilo";

/* fatal error */
"Fatal Error" = "Závažná chyba";

/* export filename favorite name token */
"Favorite" = "Oblíbená";

/* favorites label */
"Favorites" = "Oblíbené";

/* favorites export error message */
"Favorites export error" = "Chyba při exportu oblíbených";

/* favorites import error message */
"Favorites import error" = "Chyba při importu oblíbených";

/* export label showing that the app is fetching data */
"Fetching data..." = "Načítání dat...";

/* fetching database structure data in progress */
"fetching database structure data in progress" = "Probíhá načítání dat o struktuře databáze";

/* fetching database structure in progress */
"fetching database structure in progress" = "probíhá načítání databázové struktury";

/* fetching table data for completion in progress message */
"fetching table data…" = "Načítání údajů tabulky…";

/* popup menuitem for field (showing only if disabled) */
"field" = "pole";

/* Field */
"Field" = "Pole";

/* Query result editing error - could not match row being edited uniquely */
"Field is not editable. Couldn't identify field origin unambiguously (%ld matches)." = "Pole není upravitelné. Nelze jednoznačně identifikovat původ pole (%ld shody).";

/* field is not editable due to no table/database */
"Field is not editable. Field has no or multiple table or database origin(s)." = "Pole nelze upravit. Pole nemá žádnou nebo více tabulek nebo původ databáze.";

/* Custom Query result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload data, check encoding, or try to add\na primary key field or more fields\nin your SELECT statement for table '%@'\nto identify field origin unambiguously." = "Pole nelze upravit. Nebyl nalezen žádný odpovídající záznam.\nNačíst data, zkontrolovat kódování nebo zkuste přidat\nprimární pole klíče nebo více polí\nve vašem příkazu SELECT pro tabulku '%@'\npro jednoznačnou identifikaci původu polí.";

/* Table Content result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload table, check the encoding, or try to add\na primary key field or more fields\nin the view declaration of '%@' to identify\nfield origin unambiguously." = "Pole nelze upravit. Nebyl nalezen žádný odpovídající záznam.\nZnovu načíst tabulku, zkontrolujte kódování, nebo zkuste přidat\nprimární pole klíče nebo více polí\nv deklaraci zobrazení '%@' pro jednoznačnou identifikaci\npůvodu polí.";

/* error while reading data file */
"File couldn't be read." = "Soubor nelze přečíst.";
"File couldn't be read: %@\n\nIt will be deleted." = "Soubor nelze přečíst: %@\n\nBude smazán.";

/* File read error title (Import Dialog) */
"File read error" = "Chyba při čtení souboru";

/* All export files already exist explanatory text */
"Files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "Soubory se stejnými názvy již v cílové složce existují. Jejich nahrazením budou přepsány jejich aktuální obsah.";

/* filter label */
"Filter" = "Filtr";

/* apply filter label */
"Apply Filter(s)" = "Použít filtry";

/* filter tables menu item */
"Filter Tables" = "Filtrovat tabulky";

/* export source */
"Filtered table content" = "Filtrovaný obsah tabulky";

/* filtering failed. please try again. tooltip */
"Filtering failed. Please try again." = "Filtrování se nezdařilo. Zkuste to prosím znovu.";

/* Filtering table task description */
"Filtering table..." = "Filtrování tabulky...";

/* description for finished exporting notification */
"Finished exporting to %@" = "Export do %@";

/* description for finished importing notification */
"Finished importing %@" = "Dokončeno importování %@";

/* FLUSH one or more tables - result title */
"Flush %@" = "Flush %@";

/* flush selected items menu item */
"Flush Selected Items" = "Vyprázdnit vybrané položky";

/* flush table menu item */
"Flush Table" = "Vyprázdnit stůl";

/* flush table failed message */
"Flush table failed." = "Vyprázdnit stůl se nezdařilo.";

/* flush view menu item */
"Flush View" = "Vyprázdnit zobrazení";

/* title of panel when successfully flushed privs */
"Flushed Privileges" = "Rozmazaná práva";

/* For BIT fields only “1” or “0” are allowed. */
"For BIT fields only “1” or “0” are allowed." = "Pro pole BIT jsou povoleny pouze ‚1‘ nebo ‚0‘.";

/* force table deletion button text */
"Force delete (disables integrity checks)" = "Vynutit odstranění (zakáže kontroly integrity)";

/* full text index menu item title */
"FULLTEXT" = "FULLTEXT";

/* function */
"function" = "funkce";

/* header for function info pane */
"FUNCTION INFORMATION" = "INFORMACE O FUNKCI";

/* functions */
"functions" = "funkce";

/* Bundle Editor : Scope dropdown : 'general' item
 general menu item label
 general preference pane name */
"General" = "Obecná ustanovení";

/* general preference pane tooltip */
"General Preferences" = "Obecné předvolby";

/* Bundle Editor : Outline View : 'General' item : tooltip */
"General Scope\ncommands will run application-wide" = "Obecné příkazy rozsahu\nbudou spouštět celou aplikaci";

/* generating print document status message */
"Generating print document..." = "Generování tiskového dokumentu...";

/* export header generation time label */
"Generation Time" = "Generovat čas";

/* Content Filter Manager : Filter Entry List: 'Global' Header
 Query Favorites : List : Section Heading : global query favorites */
"Global" = "Globální";

/* Query Favorites : List : Section Heading : global : tooltip */
"Globally stored favorites" = "Globálně uložené oblíbené";

/* Preferences : Notifications : status text : notification setup */
"Notifications are delivered via the Notification Center." = "Oznámení se doručují prostřednictvím střediska oznámení.";

/* Gzip compression export summary - within a sentence */
"Gzip compression" = "Komprese Gzip";

/* MySQL Help Viewer : Results list : Page title */
"Help topics for %@" = "Témata nápovědy pro %@";

/* hide console */
"Hide Console" = "Skrýt konzoli";

/* hide navigator */
"Hide Navigator" = "Hide Navigator";

/* hide tab bar */
"Hide Tab Bar" = "Skrýt panel záložek";

/* Hide Toolbar menu item */
"Hide Toolbar" = "Skrýt lištu nástrojů";

/* export filename host token
 export header host label */
"Host" = "Hostitel";

/* description of double */
"IEEE 754 double-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "„Technologie“ ve smyslu všeobecné poznámky k technologii pro „vývoj“, „výrobu“ nebo „užití“ zařízení nebo „softwaru“ uvedených v položkách 1.A., 19.A.1. nebo 19.A.2. Poznámka: Mnoho desetinných čísel může být aproximováno pouze hodnotami s pohyblivou řádovou čárkou. Pokud potřebujete přesné výsledky, podívejte se DECIMAL.";

/* description of float */
"IEEE 754 single-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "\"Technologie\" ve smyslu všeobecné poznámky k technologii pro \"vývoj\", \"výrobu\" nebo \"užití\" zařízení nebo \"softwaru\" uvedených v položkách 1.A., 19.A.1. nebo 19.A.2. Poznámka: Mnoho desetinných čísel může být aproximováno pouze hodnotami s pohyblivou řádovou čárkou. Pokud potřebujete přesné výsledky, podívejte se DECIMAL.";

/* ignore button */
"Ignore" = "Ignorovat";

/* ignore errors button */
"Ignore All Errors" = "Ignorovat všechny chyby";

/* ignore all fields menu item */
"Ignore all Fields" = "Ignorovat všechna pole";

/* ignore field label */
"Ignore field" = "Ignorovat pole";

/* ignore field label */
"Ignore Field" = "Ignorovat pole";

/* import button */
"Import" = "Importovat";

/* import all fields menu item */
"Import all Fields" = "Importovat všechna pole";

/* sql import : charset error alert : continue button */
"Import Anyway" = "Přesto importovat";

/* import cancelled message */
"Import cancelled!\n" = "Import zrušen!\n";

/* Import Error title */
"Import Error" = "Chyba importu";

/* import field operator tooltip */
"Import field" = "Pole importu";

/* import file does not exist message */
"Import file does not exist." = "Importovaný soubor neexistuje.";

/* Export file format cannot be imported warning */
"Import of the selected data is currently not supported." = "Import vybraných dat není v současné době podporován.";

/* SQL import progress text */
"Imported %@ of %@" = "Importováno %1$@ z %2$@";

/* CSV import progress text where total size is unknown */
"Imported %@ of CSV data" = "Importováno %@ dat CSV";

/* SQL import progress text where total size is unknown */
"Imported %@ of SQL" = "Importováno %@ SQL";

/* text showing that the application is importing CSV */
"Importing CSV" = "Import CSV";

/* text showing that the application is importing SQL */
"Importing SQL" = "Importuji SQL";

/* Bundle Editor : BLOB dropdown : 'include BLOB' item */
"include BLOB" = "včetně BLOB";

/* include content table column tooltip */
"Include content" = "Zahrnout obsah";

/* sql import error message */
"Incompatible encoding in SQL file" = "Nekompatibilní kódování v SQL souboru";

/* header for blank info pane */
"INFORMATION" = "INFORMACE";

/* New Table Sheet : Table Collation Dropdown : Default inherited from database
 New Table Sheet : Table Encoding Dropdown : Default inherited from database */
"Inherit from database (%@)" = "Zdědit z databáze (%@)";

/* initializing export label */
"Initializing..." = "Inicializace...";

/* Bundle Editor : Scope dropdown : 'input field' item
 input field menu item label */
"Input Field" = "Vstupní pole";

/* input field  doesn't support insertion of snippets. */
"Input Field doesn't support insertion of snippets." = "Vstupní pole nepodporuje vložení snippetů.";

/* input field is not editable. */
"Input Field is not editable." = "Vstupní pole není upravitelné.";

/* Bundle Editor : Outline View : 'Input Field' item : tooltip */
"Input Field Scope\ncommands will run on each text input field" = "Příkazy rozsahu vstupního pole\nbudou spuštěny na každém vstupním poli";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as snippet' item */
"Insert as Snippet" = "Vložit jako snippet";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as text' item */
"Insert as Text" = "Vložit jako text";

/* Bundle Editor : Outline View : 'BUNDLES' item : tooltip */
"Installed Bundles" = "Nainstalované balíčky";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog title */
"Installing Bundle" = "Instalace balíčku Bundle";

/* insufficient details message */
"Insufficient connection details" = "Nedostatečné podrobnosti o připojení";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please enter at least the hostname." = "Nebyly poskytnuty dostatečné podrobnosti pro navázání připojení. Zadejte prosím alespoň název hostitele.";

/* insufficient SSH tunnel details informative message */
"Insufficient details provided to establish a connection. Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Nebyly poskytnuty dostatečné podrobnosti pro navázání připojení. Zadejte prosím název hostitele SSH tunelu nebo zakažte SSH tunel.";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please provide at least a host." = "Není k dispozici dostatek podrobností pro navázání připojení. Zadejte prosím alespoň hostitele.";

/* Interpret data as: */
"Interpret data as:" = "Interpret údaje jako:";

/* Invalid database very short status message */
"Invalid database" = "Neplatná databáze";

/* export : import settings : file error title */
"Invalid file supplied!" = "Byl zadán neplatný soubor!";

/* table content : editing : error message title when parsing as hex string failed */
"Invalid hexadecimal value" = "Neplatná hexadecimální hodnota";

/* is deterministic label (Navigator) */
"Is Deterministic" = "Je deterministický";

/* is nullable label (Navigator) */
"Is Nullable" = "Je nenulovatelný";

/* is updatable: %@ */
"is updatable: %@" = "je nahoru: %@";

/* items */
"items" = "položky";

/* javascript exception */
"JavaScript Exception" = "Výjimka JavaScriptu";

/* javascript parsing error */
"JavaScript Parsing Error" = "Chyba při analýze JavaScriptu";

/* key label (Navigator) */
"Key" = "Klíč";

/* keyword label for color table (Prefs > Editor) */
"Keyword" = "Keyword";

/* kill button */
"Kill" = "Ukončit";

/* kill connection message */
"Kill connection?" = "Ukončit připojení?";

/* kill query message */
"Kill query?" = "Ukončit dotaz?";

/* Last Error Message */
"Last Error Message" = "Poslední chybová zpráva";

/* Last Used entry in favorites menu */
"Last Used" = "Naposledy použito";

/* range for json type */
"Limited to @@max_allowed_packet" = "Omezeno na @@max_allowed_packet";

/* Loading table task string */
"Loading %@..." = "Loading %@...";

/* Loading database task string */
"Loading database '%@'..." = "Načítání databáze '%@'...";

/* Loading history entry task desc */
"Loading history entry..." = "Načítání historie...";

/* Loading table page task string */
"Loading page %lu..." = "Načítání stránky %lu...";

/* Loading referece task string */
"Loading reference..." = "Načítání odkazu...";

/* Loading table data string */
"Loading table data..." = "Načítání dat tabulky...";

/* Low memory export summary */
"Low memory" = "Nedostatečná paměť";

/* range of decimal */
"M (precision): Up to 65 digits\nD (scale): 0 to 30 digits" = "M (přesnost): až do 65 číslic\nD (stupnice): 0 až 30 číslic";

/* range for blob type
 range for varbinary type */
"M: %@ to %@ bytes" = "M: %1$@ až %2$@ bajtů";

/* range for text type
 range for varchar type */
"M: %@ to %@ characters" = "M: %1$@ až %2$@ znaky";

/* range for longtext type */
"M: %@ to %@ characters (4 GiB)" = "M: %1$@ až %2$@ znaků (4 GiB)";

/* range for binary type */
"M: 0 to 255 bytes" = "M: 0 až 255 bajtů";

/* range for char type */
"M: 0 to 255 characters" = "M: 0 až 255 znaků";

/* range for bit type */
"M: 1 (default) to 64" = "M: 1 (výchozí) až 64";

/* connection view : ssl : key file picker : wrong format error description */
"Make sure the file contains a RSA private key and is using PEM encoding." = "Ujistěte se, že soubor obsahuje RSA soukromý klíč a používá kódování PEM.";

/* connection view : ssl : client cert picker : wrong format error description */
"Make sure the file contains a X.509 client certificate and is using PEM encoding." = "Ujistěte se, že soubor obsahuje klientský certifikát X.509 a používá kódování PEM.";

/* match field menu item */
"Match Field" = "Odpovídající pole";

/* Shown when user inserts too many arguments (ContentFilterManager) */
"Maximum number of arguments is 2!" = "Maximální počet argumentů je 2!";

/* Maximum text length is set to %ld. */
"Maximum text length is set to %ld." = "Maximální délka textu je nastavena na %ld.";

/* Maximum text length is set to %ld. Inserted text was truncated. */
"Maximum text length is set to %ld. Inserted text was truncated." = "Maximální délka textu je nastavena na %ld. Vložený text byl zkrácen.";

/* Maximum text length is set to %llu. */
"Maximum text length is set to %llu." = "Maximální délka textu je nastavena na %llu.";

/* Maximum text length is set to %llu. Inserted text was truncated. */
"Maximum text length is set to %llu. Inserted text was truncated." = "Maximální délka textu je nastavena na %llu. Vložený text byl zkrácen.";

/* message column title */
"Message" = "Zpráva";

/* mgtemplateengine error */
"MGTemplateEngine Error" = "Chyba MGTemplateEngine";

/* export filename date token */
"Month" = "Měsíc";

/* multiple selection */
"multiple selection" = "vícenásobný výběr";

/* MySQL connecting very short status message */
"MySQL connecting..." = "Připojování k MySQL...";

/* mysql error message */
"MySQL Error" = "MySQL Error";

/* mysql help */
"MySQL Help" = "MySQL Help";

/* MySQL Help for Selection */
"MySQL Help for Selection" = "MySQL Nápověda pro výběr";

/* MySQL Help for Word */
"MySQL Help for Word" = "MySQL Nápověda pro slovo";

/* mysql help categories */
"MySQL Help – Categories" = "MySQL Nápověda - Kategorie";

/* mysql said message */
"MySQL said:" = "MySQL řekl:";

/* shutdown server : error dialog : message */
"MySQL said:\n%@" = "MySQL řekl:\n%@";

/* message of panel when error while adding row to db */
"MySQL said:\n\n%@" = "MySQL řekl:\n\n%@";

/* Preferences : Themes : Initial filename for 'Export' */
"MyTheme" = "Mymotiv";

/* network preference pane name */
"Network" = "Síť";

/* network preference pane tooltip */
"Network Preferences" = "Předvolby sítě";

/* file preference pane name */
"Files" = "Soubory";

/* file preference pane tooltip */
"File Preferences" = "Předvolby souboru";

/* Bundle Editor : Default name for new bundle in the list on the left */
"New Bundle" = "Nový balíček";

/* new column name placeholder string */
"New Column Name" = "Nový název sloupce";

/* new favorite name */
"New Favorite" = "Nová oblíbená";

/* Content Filter Manager : Initial name for new filter */
"New Filter" = "Nový filtr";

/* new folder placeholder name */
"New Folder" = "Nová složka";

/* Bundle Editor : Default name for a new bundle in the menu */
"New Name" = "Nový název";

/* new table menu item */
"New Table" = "Nová tabulka";

/* error that no color theme found */
"No color theme data found." = "Nebyla nalezena žádná barevná data motivu.";

/* No compression export summary - within a sentence */
"no compression" = "žádná komprese";

/* no connection available message */
"No connection available" = "Není k dispozici žádné připojení";

/* no connection data found */
"No connection data found." = "Nebyla nalezena žádná data o připojení.";

/* No content filters were found in file to import (ContentFilterManager) */
"No content filters found." = "Nebyly nalezeny žádné filtry obsahu.";

/* no data found */
"No data found." = "Nebyla nalezena žádná data.";

/* No errors title */
"No errors" = "Žádné chyby";

/* No favorites entry in favorites menu */
"No Favorties" = "Žádné oblíbené položky";

/* All export files creation error title */
"No files could be created" = "Nelze vytvořit žádné soubory";

/* no item found message */
"No item found" = "Nebyla nalezena žádná položka";

/* SSH tunnel could not be created because no local port could be allocated */
"No local port could be allocated for the SSH Tunnel." = "Pro SSH tunel nemohl být přidělen žádný místní přístav.";

/* header for no matches in filtered list */
"NO MATCHES" = "ŽÁDNÉ MATCHY";

/* no optimized field type found. message */
"No optimized field type found." = "Nebyl nalezen žádný optimalizovaný typ pole.";

/* error that no query favorites found */
"No query favorites found." = "Nebyly nalezeny žádné oblíbené dotazy.";

/* Mysql Help Viewer : Search : No results */
"No results found." = "Nebyly nalezeny žádné výsledky.";

/* Bundle Editor : Fallback Input source dropdown : 'none' item
 Bundle Editor : Scope=? : ? dropdown: 'None' item
 Bundle Editor : Scope=Data-Table : Input source dropdown: 'none' item
 Bundle Editor : Scope=Data-Table : Output dropdown : 'none' item
 Bundle Editor : Scope=Data-Table : Trigger dropdown : 'none' item
 Bundle Editor : Scope=Field : Input source dropdown: 'None' item
 Bundle Editor : Scope=Field : Output dropdown : 'none' item
 Bundle Editor : Scope=Field : Trigger dropdown : 'none' item
 Bundle Editor : Scope=General : Input source dropdown: 'None' item
 Bundle Editor : Scope=General : Output dropdown : 'none' item
 Bundle Editor : Scope=General : Trigger dropdown : 'none' item
 compression: none */
"None" = "Nic";

/* not available label */
"Not available" = "Není k dispozici";

/* Argument count (ContentFilterManager) */
"Number of arguments: %lu" = "Počet argumentů: %lu";

/* numeric label for color table (Prefs > Editor) */
"Numeric" = "Numeric";

/* Bundle Editor : Copy-Command-Error : OK button
 Bundle Editor : Save-and-Close-Error : OK button
 Bundle Editor : Save-Bundle-Error : OK button
 Bundle Editor : Trash-Bundle(s)-Error : OK button
 OK
 OK button
 Open Files : Bundle : Already-Installed : Delete-Old-Error : OK button
 Open Files : Bundle : Install-Error : OK button
 Open Files : Bundle : UUID : OK button
 Preferences : Network : Custom SSH client : warning dialog : accept button */
"OK" = "OK";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+1 rows instead of n selected were deleted. */
"One additional row was removed!" = "Byl odstraněn jeden další řádek!";

/* Table Content : Remove Row : Result : Too Few : Part 1 : Only n-1 of n selected rows were deleted. */
"One row was not removed." = "Jeden řádek nebyl odstraněn.";

/* Only one dragged item allowed. */
"Only one dragged item allowed." = "Povolena je pouze jedna přetahovaná položka.";

/* partial copy database support message */
"Only Partially Supported" = "Pouze částečně podporováno";

/* open function in new table title */
"Open Function in New Tab" = "Otevřít funkci v novém panelu";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Function in New Window" = "Otevřít funkci v novém okně";

/* open connection in new tab context menu item */
"Open in New Tab" = "Otevřít v nové kartě";

/* menu item open in new window */
"Open in New Window" = "Otevřít v novém okně";

/* open procedure in new table title */
"Open Procedure in New Tab" = "Otevřená procedura v novém panelu";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Procedure in New Window" = "Otevřená procedura v novém okně";

/* open table in new tab title
 open table in new table title */
"Open Table in New Tab" = "Otevřít tabulku v novém panelu";

/* Table List : Context Menu : Duplicate connection to new window
 Table List : Gear Menu : Duplicate connection to new window */
"Open Table in New Window" = "Otevřít tabulku v novém okně";

/* open view in new tab title
 open view in new table title */
"Open View in New Tab" = "Otevřít zobrazení v nové kartě";

/* Tables List : Context Menu : Duplicate connection to new window
 Tables List : Gear Menu : Duplicate connection to new window */
"Open View in New Window" = "Otevřít zobrazení v novém okně";

/* menu item open %@ in new window */
"Open %@ in New Window" = "Otevřít %@ v novém okně";

/* OPTIMIZE one or more tables - result title */
"Optimize %@" = "Optimalizovat %@";

/* optimize selected items menu item */
"Optimize Selected Items" = "Optimalizovat vybrané položky";

/* optimize table menu item */
"Optimize Table" = "Optimalizovat tabulku";

/* optimize table failed message */
"Optimize table failed." = "Optimalizace tabulky se nezdařila.";

/* Optimized type for field %@ */
"Optimized type for field '%@'" = "Optimalizovaný typ pro pole '%@'";

/* optional placeholder string */
"optional" = "volitelné";

/* Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed. */
"Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed." = "Prošlý parametr nelze interpretovat. Jsou povoleny pouze řetězec nebo pole (s 2 prvky).";

/* Permission Denied */
"Permission Denied" = "Oprávnění odepřeno";

/* please choose a favorite connection view label */
"Please choose a favorite" = "Vyberte si prosím oblíbené";

/* message of panel when ssh details are incomplete */
"Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Zadejte prosím název hostitele SSH Tunnel, nebo zakažte SSH tunel.";

/* Please enter the password */
"Please enter the password for ‘%@’:" = "Zadejte prosím heslo pro „%@“:";

/* print button */
"Print" = "Tisk";

/* print page menu item title */
"Print Page…" = "Vytisknout stránku…";

/* privileges label (Navigator) */
"Privileges" = "Práva";

/* procedure */
"procedure" = "postup";

/* header for procedure info pane */
"PROCEDURE INFORMATION" = "POSTUP INFORMACE";

/* procedures */
"procedures" = "postupy";

/* proceed button */
"Proceed" = "Pokračovat";

/* header for procs & funcs list */
"PROCS & FUNCS" = "PROCS a FONDY";

/* toolbar item label for switching to the Run Query tab */
"Query" = "Dotaz";

/* query background label for color table (Prefs > Editor) */
"Query Background" = "Pozadí dotazu";

/* Query cancelled error */
"Query cancelled." = "Dotaz zrušen.";

/* Query cancel by resetting connection error */
"Query cancelled.  Please note that to cancel the query the connection had to be reset; transactions and connection variables were reset." = "Dotaz zrušen. Pro zrušení dotazu muselo být připojení resetováno; transakce a proměnné připojení byly resetovány.";

/* query editor preference pane name */
"Query Editor" = "Editor dotazů";

/* query editor preference pane tooltip */
"Query Editor Preferences" = "Nastavení editoru dotazů";

/* query logging currently disabled label
 query logging disabled label */
"Query logging is currently disabled" = "Protokolování dotazů je v současné době zakázáno";

/* query result print heading */
"Query Result" = "Výsledek dotazu";

/* export source */
"Query results" = "Výsledky dotazu";

/* Query Status */
"Query Status" = "Stav dotazu";

/* table status : row count query failed : error title */
"Querying row count failed" = "Počet dotazů se nezdařil";

/* Quick connect item label */
"Quick Connect" = "Rychlé připojení";

/* quote label for color table (Prefs > Editor) */
"Quote" = "Cenová nabídka";

/* databsse rename unsupported informative message */
"Renaming the database '%@' is currently unsupported as it contains objects other than tables (i.e. views, procedures, functions, etc.).\n\nIf you would like to rename a database please use the 'Duplicate Database', move any non-table objects manually then drop the old database." = "Přejmenování databáze '%@' není v současné době podporováno, protože obsahuje jiné objekty než tabulky (např. zobrazení, postupy, funkce atd.).\n\nPokud chcete přejmenovat databázi, použijte prosím 'Duplicate Database', přesunout všechny nestolní objekty ručně a pak přetáhněte starou databázi.";

/* range for serial type */
"Range: %@ to %@" = "Rozsah: %1$@ až %2$@";

/* range for time type */
"Range: -838:59:59.0 to 838:59:59.0\nF (precision): 0 (1s) to 6 (1µs)" = "Rozsah: -838:59:59.0 až 838:59:59.0\nF (přesnost): 0 (1s) až 6 (1μs)";

/* range for year type */
"Range: 0000, 1901 to 2155" = "Rozsah: 0000, 1901 až 2155";

/* range for set type */
"Range: 1 to 64 members\n1, 2, 3, 4 or 8 bytes storage" = "Rozsah: 1 až 64 členů\n1, 2, 3, 4 nebo 8 bajtů";

/* range for datetime type */
"Range: 1000-01-01 00:00:00.0 to 9999-12-31 23:59:59.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Rozsah: 1000-01-01 00:00:00.0 až 9999-12-31 23:59:59.9999\nF (přesnost): 0 (1s) až 6 (1μs)";

/* range for date type */
"Range: 1000-01-01 to 9999-12-31" = "Rozsah: 1000-01-01 až 9999-12-31";

/* range for timestamp type */
"Range: 1970-01-01 00:00:01.0 to 2038-01-19 03:14:07.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Rozsah: 1970-01-01 00:00:01.0 až 2038-01-19 03:14:07.9999\nF (přesnost): 0 (1s) až 6 (1μs)";

/* text showing that app is reading dump */
"Reading..." = "Čtení...";

/* menu item to refresh databases */
"Refresh Databases" = "Aktualizovat databáze";

/* refresh list menu item */
"Refresh List" = "Aktualizovat seznam";

/* toolbar item label for switching to the Table Relations tab */
"Relations" = "Vztahy";

/* Relations tab subtitle showing table name */
"Relations for table: %@" = "Vztahy k tabulce: %@";

/* reload bundles menu item label */
"Reload Bundles" = "Znovu načíst balíčky";

/* Reloading data task description */
"Reloading data..." = "Načítání dat...";

/* Reloading table task string */
"Reloading..." = "Reloading...";

/* remote error */
"Remote Error" = "Vzdálená chyba";

/* Bundle Editor : Remove-Bundle: remove button
 remove button */
"Remove" = "Odebrat";

/* remove all button */
"Remove All" = "Odstranit vše";

/* remove all query favorites message */
"Remove all query favorites?" = "Odstranit všechny oblíbené dotazy?";

/* Bundle Editor : Remove-Bundle: remove dialog title */
"Remove selected Bundle?" = "Odstranit vybraný balík?";

/* remove selected content filters message */
"Remove selected content filters?" = "Odstranit vybrané filtry obsahu?";

/* remove selected query favorites message */
"Remove selected query favorites?" = "Odstranit vybrané oblíbené dotazy?";

/* removing field task status message */
"Removing field..." = "Odstraňování pole...";

/* removing index task status message */
"Removing index..." = "Odstraňování indexu...";

/* rename database message */
"Rename database '%@' to:" = "Přejmenovat databázi '%@' na:";

/* rename func menu title */
"Rename Function..." = "Přejmenovat funkci...";

/* rename proc menu title */
"Rename Procedure..." = "Přejmenovat postup...";

/* rename table menu title */
"Rename Table..." = "Přejmenovat tabulku...";

/* rename view menu title */
"Rename View..." = "Přejmenovat zobrazení...";

/* REPAIR one or more tables - result title */
"Repair %@" = "Repair %@";

/* repair selected items menu item */
"Repair Selected Items" = "Opravit vybrané položky";

/* repair table menu item */
"Repair Table" = "Opravovací tabulka";

/* repair table failed message */
"Repair table failed." = "Oprava tabulky se nezdařila.";

/* Replace button */
"Replace" = "Nahradit";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace entire content' item */
"Replace Entire Content" = "Nahradit celý obsah";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace selection' item */
"Replace Selection" = "Nahradit výběr";

/* description of year */
"Represents a 4 digit year value, stored as 1 byte. Invalid values are converted to 0000 and two digit values 0 to 69 will be converted to years 2000 to 2069, resp. values 70 to 99 to years 1970 to 1999.\nThe YEAR(2) type was removed in MySQL 5.7.5." = "Představuje čtyřmístnou hodnotu roku uloženou jako 1 byt. Neplatné hodnoty jsou převedeny na 0000 a dvě číslice hodnoty 0 až 69 budou převedeny na roky 2000 až 2069, resp. hodnoty 70 až 99 do let 1970 až 1999.\nTyp YEAR(2) byl odstraněn v MySQL 5.7.5.";

/* description of multilinestring */
"Represents a collection of LineStrings." = "Představuje sbírku LineStrings.";

/* description of geometrycollection */
"Represents a collection of objects of any other single- or multi-valued spatial type. The only restriction being, that all objects must share a common coordinate system." = "Představuje množinu objektů jakéhokoli jiného jednotlivého nebo vícenásobného prostorového typu. Jediné omezení, že všechny objekty musí sdílet společný souřadnicový systém.";

/* description of multipolygon */
"Represents a collection of Polygons. The Polygons making up the MultiPolygon must not intersect." = "Představuje sbírku polygonů. Polygony tvořící MultiPolygon nesmí křižovat.";

/* description of multipoint */
"Represents a set of Points without specifying any kind of relation and/or order between them." = "Představuje soubor bodů, aniž by mezi nimi specifikoval jakýkoli druh vztahu nebo příkazu.";

/* description of point */
"Represents a single location in coordinate space using X and Y coordinates. The point is zero-dimensional." = "Představuje jediné umístění v souřadnicovém prostoru pomocí souřadnic X a Y. Bod je nulový rozměr.";

/* description of linestring */
"Represents an ordered set of coordinates where each consecutive pair of two points is connected by a straight line." = "Představuje uspořádanou sadu souřadnic, kde je každý po sobě jdoucí pár dvou bodů spojen přímkou.";

/* required placeholder string */
"required" = "vyžadováno";

/* description of smallint */
"Requires 2 bytes storage space. M is the optional display width and does not affect the possible value range." = "Vyžaduje 2 bajty úložného prostoru. M je volitelná šířka zobrazení a nemá vliv na možný rozsah hodnot.";

/* description of mediumint */
"Requires 3 bytes storage space. M is the optional display width and does not affect the possible value range." = "Vyžaduje úložiště 3 bajtů. M je volitelná šířka displeje a nemá vliv na možný rozsah hodnot.";

/* description of int */
"Requires 4 bytes storage space. M is the optional display width and does not affect the possible value range. INTEGER is an alias to this type." = "Vyžaduje úložiště 4 bajtů. M je volitelná šířka displeje a nemá vliv na možný rozsah hodnot. INTEGER je alias tohoto typu.";

/* description of bigint */
"Requires 8 bytes storage space. M is the optional display width and does not affect the possible value range. Note: Arithmetic operations might fail for large numbers." = "Vyžaduje úložiště 8 bajtů. M je volitelná šířka displeje a nemá vliv na možný rozsah hodnot. Poznámka: Aritmetické operace mohou selhat u velkých čísel.";

/* reset auto_increment after deletion of all rows message */
"Reset AUTO_INCREMENT after deletion\n(only for Delete ALL ROWS IN TABLE)?" = "Obnovit AUTO_INCREMENT po smazání\n(pouze pro smazání VŠECHNY ROWY V OCHRANU)?";

/* delete selected row button */
"Delete Selected Row" = "Odstranit vybraný řádek";

/* delete selected rows button */
"Delete Selected Rows" = "Odstranit vybrané řádky";

/* delete all rows in table button */
"Delete ALL ROWS IN TABLE" = "Odstranit VŠECHNY ROWY v TABULCE";

/* Restoring session task description */
"Restoring session..." = "Obnovuji relaci...";

/* return type label (Navigator) */
"Return Type" = "Typ vratky";

/* return type: %@ */
"return type: %@" = "typ návratu: %@";

/* singular word for row */
"row" = "Řádek";

/* plural word for rows */
"rows" = "Řádky";

/* text showing how many rows are in the limited filter match */
"Rows %@ - %@ from filtered matches" = "Řádky %1$@ - %2$@ z filtrovaných zápasů";

/* text showing how many rows are in the limited result */
"Rows %@ - %@ of %@%@ from table" = "Řádky %1$@ - %2$@ z %3$@%4$@ z tabulky";

/* Table Info Section : number of rows (exact value) */
"rows: %@" = "řádek: %@";

/* Table Info Section : number of rows (estimated value) */
"rows: ~%@" = "řádek: ~%@";

/* run all button */
"Run All" = "Spustit vše";

/* Run All menu item title */
"Run All Queries" = "Spustit všechny dotazy";

/* Title of button to run current query in custom query view */
"Run Current" = "Spustit aktuální";

/* Title of action menu item to run current query in custom query view */
"Run Current Query" = "Spustit aktuální dotaz";

/* toolbar item label for switching to the Run Query tab */
"Run Custom Query" = "Spustit vlastní dotaz";

/* Title of button to run query just before text caret in custom query view */
"Run Previous" = "Spustit předchozí";

/* Title of action menu item to run query just before text caret in custom query view */
"Run Previous Query" = "Spustit předchozí dotaz";

/* Title of action menu item to run selected text in custom query view */
"Run Selected Text" = "Spustit vybraný text";

/* Title of button to run selected text in custom query view */
"Run Selection" = "Spustit výběr";

/* Running multiple queries string */
"Running query %i of %lu..." = "Probíhá dotaz %1$i z %2$lu...";

/* Running multiple queries string */
"Running query %ld of %lu..." = "Probíhá dotaz %1$ld z %2$lu...";

/* Running single query string */
"Running query..." = "Probíhá dotaz...";

/* Save trigger button label */
"Save" = "Uložit";

/* Save All to Favorites */
"Save All to Favorites" = "Uložit vše do oblíbených";

/* save as button title */
"Save As..." = "Uložit jako...";

/* Bundle Editor : BLOB dropdown : 'save BLOB as dat file' item */
"save BLOB as dat file" = "uložit BLOB jako datový soubor";

/* Bundle Editor : BLOB dropdown : 'save BLOB as image file' item */
"save BLOB as image file" = "uložit BLOB jako obrázek";

/* Save Current Query to Favorites */
"Save Current Query to Favorites" = "Uložit aktuální dotaz do oblíbených";

/* save page as menu item title */
"Save Page As…" = "Uložit stránku jako…";

/* Save Queries… */
"Save Queries…" = "Uložit dotazy…";

/* Save Query… */
"Save Query…" = "Uložit dotaz…";

/* Save Selection to Favorites */
"Save Selection to Favorites" = "Uložit výběr do oblíbených";

/* save view as button title */
"Save View As..." = "Uložit pohled jako...";

/* schema path header for completion tooltip */
"Schema path:" = "Cesta k schématu:";

/* Search in MySQL Documentation */
"Search in MySQL Documentation" = "Hledat v MySQL dokumentaci";

/* Search in MySQL Help */
"Search in MySQL Help" = "Hledat v MySQL nápovědě";

/* Select Active Query */
"Select Active Query" = "Vybrat aktivní dotaz";

/* toolbar item for selecting a db */
"Select Database" = "Vybrat databázi";

/* selected items */
"selected items" = "vybrané položky";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as comma-separated' item */
"Selected Rows (CSV)" = "Vybrané řádky (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as SQL' item */
"Selected Rows (SQL)" = "Vybrané řádky (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as tab-separated' item */
"Selected Rows (TSV)" = "Vybrané řádky (TSV)";

/* Bundle Editor : Scope=Field : Input source dropdown: 'selected text' item */
"Selected Text" = "Vybraný text";

/* selection label for color table (Prefs > Editor) */
"Selection" = "Výběr";

/* table structure : indexes : delete index : no columns error : description */
"Sequel Ace could not find any columns belonging to this index. Maybe it has been removed already?" = "Sequel Ace nemohl najít žádné sloupce náležející k tomuto indexu. Možná již byl odstraněn?";

/* Preferences : Network : Custom SSH client : warning dialog message */
"Sequel Ace only supports and is tested with the default OpenSSH client versions included with macOS. Using different clients might cause connection issues, security risks or not work at all.\n\nPlease be aware, that we cannot provide support for such configurations." = "Sequel Ace podporuje a testuje se s výchozími OpenSSH klientskými verzemi včetně macOS. Použití různých klientů může způsobit problémy s připojením, bezpečnostní rizika nebo vůbec nefungovat.\n\nMějte prosím na paměti, že pro takové konfigurace nemůžeme poskytnout podporu.";

/* sequelace URL scheme command not supported. */
"sequelace URL scheme command not supported." = "příkaz URL sekvenování není podporován.";

/* sequelace url Scheme Error */
"sequelace URL Scheme Error" = "chyba následného schématu URL";

/* Table Structure : Collation dropdown : 'item is the same as the collation of server' marker
 Table Structure : Encoding dropdown : 'item is server default' marker */
"Server" = "Server";

/* Add Database : Charset dropdown : default item ($1 = charset name)
 Add Database : Collation dropdown : default item ($1 = collation name) */
"Server Default (%@)" = "Výchozí nastavení serveru (%@)";

/* server processes window title (var = hostname) */
"Server Processes on %@" = "Procesy serveru na %@";

/* Initial filename for 'Save session' file */
"Session" = "Relace";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html' item */
"Show as HTML" = "Zobrazit jako HTML";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html tooltip' item */
"Show as HTML Tooltip" = "Zobrazit jako HTML tip";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as text tooltip' item */
"Show as Text Tooltip" = "Zobrazit jako textový nápovědu";

/* show console */
"Show Console" = "Zobrazit konzoli";

/* show create func syntax menu item */
"Show Create Function Syntax..." = "Zobrazit syntaxi pro vytvoření funkce...";

/* show create proc syntax menu item */
"Show Create Procedure Syntax..." = "Zobrazit syntaxi procedur...";

/* show create syntaxes menu item */
"Show Create Syntaxes..." = "Zobrazit syntaxi...";

/* show create table syntax menu item */
"Show Create Table Syntax..." = "Zobrazit syntaxi pro vytvoření tabulky...";

/* show create view syntax menu item */
"Show Create View Syntax..." = "Zobrazit syntaxi Vytvořit zobrazení...";

/* Show detail button */
"Show Detail" = "Zobrazit detaily";

/* MySQL Help Viewer : Results list : Link tooltip */
"Show MySQL help for %@" = "Zobrazit MySQL nápovědu pro %@";

/* show navigator */
"Show Navigator" = "Show Navigator";

/* show tab bar */
"Show Tab Bar" = "Zobrazit panel záložek";

/* tooltip for toolbar item for show console */
"Show the console which shows all MySQL commands performed by Sequel Ace" = "Zobrazit konzolu, která zobrazuje všechny MySQL příkazy provedené Sequel Ace";

/* Show Toolbar menu item */
"Show Toolbar" = "Zobrazit panel nástrojů";

/* filtered item count */
"Showing %lu of %lu processes" = "Zobrazuji %1$lu z %2$lu procesů";

/* shutdown server : confirmation dialog : shutdown button */
"Shutdown" = "Vypnout";

/* shutdown server : error dialog : title */
"Shutdown failed!" = "Vypnutí selhalo!";

/* range of integer types */
"Signed: %@ to %@\nUnsigned: %@ to %@" = "Podpis: %1$@ až %2$@\nNepodepsaný: %3$@ to %4$@";

/* Table Info Section : table size on disk */
"size: %@" = "velikost: %@";

/* skip existing button */
"Skip existing" = "Přeskočit existující";

/* skip problems button */
"Skip problems" = "Přeskočit problémy";

/* beta build label */
"Beta Build" = "Beta sestavení";

/* socket connection failed title */
"Socket connection failed!" = "Spojení se Socket selhalo!";

/* socket not found title */
"Socket not found!" = "Socket nenalezen!";

/* Some export folders not writable explanatory text */
"Some of the target export folders are not writable.  Please select a new export location and try again." = "Některé cílové složky exportu nejsou zapisovatelné. Zvolte prosím nové umístění exportu a zkuste to znovu.";

/* Some export folders missing explanatory text */
"Some of the target export folders no longer exist.  Please select a new export location and try again." = "Některé cílové složky pro export již neexistují. Zvolte prosím nové místo pro export a zkuste to znovu.";

/* Sorting table task description */
"Sorting table..." = "Řazení tabulky...";

/* spatial index menu item title */
"SPATIAL" = "SPATIAL";

/* sql data access label (Navigator) */
"SQL Data Access" = "Přístup k SQL datům";


/* Connection Secured via SSL information text */
"Connection Secured via SSL" = "Připojení zabezpečeno přes SSL";

/* SSH connected titlebar marker */
"SSH Connected" = "SSH připojeno";

/* SSH connecting very short status message */
"SSH connecting..." = "Připojování SSH...";

/* SSH connecting titlebar marker */
"SSH Connecting…" = "Připojování SSH…";

/* SSH connection failed title */
"SSH connection failed!" = "Připojení SSH selhalo!";

/* SSH disconnected titlebar marker */
"SSH Disconnected" = "SSH odpojeno";

/* SSH key check error */
"SSH Key not found" = "SSH klíč nebyl nalezen";

/* title when ssh tunnel port forwarding failed */
"SSH port forwarding failed" = "Odeslání SSH portu selhalo";

/* SSL certificate authority file check error */
"SSL Certificate Authority File not found" = "Soubor SSL Certifikační autority nebyl nalezen";

/* SSL certificate file check error */
"SSL Certificate File not found" = "Soubor certifikátu SSL nebyl nalezen";

/* SSL requested but not used title */
"SSL connection not established" = "Připojení SSL není navázáno";

/* SSL key file check error */
"SSL Key File not found" = "Soubor klíče SSL nebyl nalezen";

/* Standard memory export summary */
"Standard memory" = "Standardní paměť";

/* started */
"started" = "začal";

/* status file for sequelace url scheme command couldn't be written error message */
"Status file for sequelace url scheme command couldn't be written!" = "Stavový soubor pro příkaz url schéma sekvelace nelze zapsat!";

/* stop button */
"Stop" = "Zastavit";

/* Stop queries string */
"Stop queries" = "Zastavit dotazy";

/* Stop query string */
"Stop query" = "Ukončit dotaz";

/* description of timestamp */
"Stores a date and time of day as seconds since the beginning of the UNIX epoch (1970-01-01 00:00:00). The values displayed/stored are affected by the connection's @@time_zone setting.\nThe representation is the same as for DATETIME. Invalid values, as well as \"second zero\", are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F. Some additional rules may apply." = "Ukládá datum a denní dobu od začátku epochy UNIX (1970-01-01 00:00:00). Zobrazené/uložené hodnoty jsou ovlivněny nastavením připojení @@time_zone.\nZástupce je stejná jako v případě DATETIME. Neplatné hodnoty, stejně jako \"druhá nula\", jsou převedeny na 0000-00-00 00:00:00.0. Frakční sekundy byly přidány do MySQL 5. .4 s přesností na mikrosekundy (6), specifikovanou v F. mohou platit některá další pravidla.";

/* description of datetime */
"Stores a date and time of day. The representation is YYYY-MM-DD HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Uloží datum a čas dne. Zastupování je YYYY-MM-DD HH:MM:SS[. *], Jsem zlomek sekund. Hodnota není ovlivněna nastavením časového pásma. Neplatné hodnoty jsou převedeny na 0000-00-00 00:00:00.0. Frakční sekundy byly přidány do MySQL 5.6.4 s přesností dolů na microsekundy (6), zadané F.";

/* description of date */
"Stores a date without time information. The representation is YYYY-MM-DD. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00." = "Uloží datum bez časových informací. Reprezentace je YYYY-MM-DD. Hodnota není ovlivněna nastavením časového pásma. Neplatné hodnoty jsou převedeny na 0000-00-00.";

/* description of time */
"Stores a time of day, duration or time interval. The representation is HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 00:00:00. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Uloží denní dobu, dobu trvání nebo časový interval. Zastupování je HH:MM:SS[. *], Jsem zlomek sekund. Hodnota není ovlivněna nastavením časového pásma. Neplatné hodnoty jsou převedeny na 00:00:00. Frakční sekundy byly přidány do MySQL 5.6.4 s přesností na mikrosekundy (6), zadané F.";

/* toolbar item label for switching to the Table Structure tab */
"Structure" = "Struktura";

/* successfully analyzed all selected items message */
"Successfully analyzed all selected items." = "Všechny vybrané položky byly úspěšně analyzovány.";

/* analyze table successfully passed message */
"Successfully analyzed table." = "Tabulka byla úspěšně analyzována.";

/* successfully flushed all selected items message */
"Successfully flushed all selected items." = "Všechny vybrané položky úspěšně smazány.";

/* message of panel when successfully flushed privs */
"Successfully flushed privileges." = "Práva byla úspěšně smazána.";

/* flush table successfully passed message */
"Successfully flushed table." = "Stůl byl úspěšně smazán.";

/* successfully optimized all selected items message */
"Successfully optimized all selected items." = "Úspěšně optimalizováno všechny vybrané položky.";

/* optimize table successfully passed message */
"Successfully optimized table." = "Tabulka byla úspěšně optimalizována.";

/* successfully repaired all selected items message */
"Successfully repaired all selected items." = "Všechny vybrané položky byly úspěšně opraveny.";

/* repair table successfully passed message */
"Successfully repaired table." = "Tabulka byla úspěšně opravena.";

/* tooltip for toolbar item for switching to the Run Query tab */
"Switch to the Run Query tab" = "Přepnout na kartu Spustit dotaz";

/* tooltip for toolbar item for switching to the Table Content tab */
"Switch to the Table Content tab" = "Přepnout na záložku obsahu tabulky";

/* tooltip for toolbar item for switching to the Table Info tab */
"Switch to the Table Info tab" = "Přepnout na záložku Info tabulky";

/* tooltip for toolbar item for switching to the Table Relations tab */
"Switch to the Table Relations tab" = "Přepnout na záložku Vztahy tabulky";

/* tooltip for toolbar item for switching to the Table Structure tab */
"Switch to the Table Structure tab" = "Přepnout na záložku Struktura tabulky";

/* tooltip for toolbar item for switching to the Table Triggers tab */
"Switch to the Table Triggers tab" = "Přepnout na kartu Spouštěče tabulky";

/* tooltip for toolbar item for switching to the User Manager tab */
"Switch to the User Manager tab" = "Přepnout na kartu Správce uživatelů";

/* description for table syntax copied notification */
"Syntax for %@ table copied" = "Syntaxe pro %@ tabulka zkopírována";

/* table */
"table" = "Tabulka";

/* csv export table heading
 table
 Table Structure : Collation dropdown : 'item is the same as the collation of table' marker
 Table Structure : Encoding dropdown : 'item is table default' marker */
"Table" = "Stupeň úvěrové kvality 1";

/* export label showing that the app is fetching data for a specific table */
"Table %lu of %lu (%@): Fetching data..." = "Tabulka %1$lu z %2$lu (%3$@): Načítání dat...";

/* export label showing app is fetching relations data for a specific table */
"Table %lu of %lu (%@): Fetching relations data..." = "Tabulka %1$lu %2$lu (%3$@): Načítání údajů o vztazích...";

/* export label showing app if writing data for a specific table */
"Table %lu of %lu (%@): Writing data..." = "Tabulka %1$lu %2$lu (%3$@): Psaní...";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'table changed' item */
"Table changed" = "Tabulka změněna";

/* table checksum message */
"Table checksum" = "Kontrolní součet tabulky";

/* table checksum: %@ */
"Table checksum: %@" = "Kontrolní součet tabulky: %@";

/* table content print heading */
"Table Content" = "Obsah tabulky";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as comma-separated' item */
"Table Content (CSV)" = "Obsah tabulky (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as SQL' item */
"Table Content (SQL)" = "Obsah tabulky (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as tab-separated' item */
"Table Content (TSV)" = "Obsah tabulky (TSV)";

/* toolbar item for navigation history */
"Table History" = "Historie tabulky";

/* toolbar item label for switching to the Table Info tab */
"Table Info" = "Informace o tabulce";

/* header for table info pane */
"TABLE INFORMATION" = "TABULKA INFORMACÍ";

/* table information print heading */
"Table Information" = "Informace o tabulce";

/* message of panel when no name is given for table */
"Table must have a name." = "Tabulka musí mít název.";

/* general preference pane tooltip */
"Table Preferences" = "Předvolby tabulky";

/* toolbar item label for switching to the Table Relations tab */
"Table Relations" = "Vztahy k tabulkám";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table row changed' item */
"Table Row changed" = "Řádek tabulky změněn";

/* table structure print heading */
"Table Structure" = "Struktura tabulky";

/* toolbar item label for switching to the Table Triggers tab */
"Table Triggers" = "Stolní spouštěče";

/* Add Relation sheet title, showing table name */
"Table: %@" = "Tabulka: %@";

/* tables preference pane name */
"Tables" = "Tabulky";

/* tables */
"tables" = "Tabulky";

/* header for table list */
"TABLES" = "TABULKY";

/* header for table & views list */
"TABLES & VIEWS" = "TABULKY A ZOBRAZENÍ";

/* Connection test very short status message */
"Testing connection..." = "Testování připojení...";

/* MySQL connection test very short status message */
"Testing MySQL..." = "Testing MySQL...";

/* SSH testing very short status message */
"Testing SSH..." = "Testování SSH...";

/* text label for color table (Prefs > Editor) */
"Text" = "Text";

/* Text is too long. Maximum text length is set to %llu. */
"Text is too long. Maximum text length is set to %llu." = "Text je příliš dlouhý. Maximální délka textu je nastavena na %llu.";

/* Release notes dialog title thanking user for upgrade */
"Thanks for updating Sequel Ace!" = "Děkujeme za aktualizaci Sequel Ace!";

/* Open Files : Bundle : Install-Error : Destination path already exists error dialog message */
"The Bundle ‘%@’ already exists." = "Bundle ‘%@již existuje.";

/* Open Files : Bundle: UUID : UUID-Attribute is missing in bundle's command.plist file */
"The Bundle ‘%@’ has no UUID which is necessary to identify installed Bundles." = "Balíček „%@“ nemá žádný UUID, který je nezbytný k identifikaci nainstalovaných balíčků.";

"‘%@’ Bundle contains legacy components" = "„%@’ Bundle obsahuje starší složky";

"In these files:\n\n%@\n\nDo you still want to install the bundle?" = "V těchto souborech:\n\n%@\n\nChcete stále nainstalovat balíček?";

/* message while reading a spf file which matches non-supported formats. */
"The chosen file %@ contains ‘%@’ data." = "Zvolený soubor „%1$@“ obsahuje data „%2$@“.";

/* the color theme ‘%@’ already exists. */
"The color theme ‘%@’ already exists." = "Barevný motiv „%@“ již existuje.";

/* the connection is busy. please wait and try again tooltip */
"The connection is busy. Please wait and try again." = "Připojení je zaneprázdněno. Počkejte a zkuste to znovu.";

/* the connection of the active connection window is not identical tooltip */
"The connection of the active connection window is not identical." = "Připojení okna aktivního připojení není totožné.";

/* Connection lost during import error message */
"The connection to the server was lost during the import.  The import is only partially complete." = "Připojení k serveru bylo ztraceno během importu. Import je dokončen pouze částečně.";

/* Create syntax permission denied detail */
"The creation syntax could not be retrieved due to a permissions error.\n\nPlease check your user permissions with an administrator." = "Syntaxe vytvoření nelze načíst z důvodu chyby oprávnění.\n\nZkontrolujte svá uživatelská oprávnění správcem.";

/* CSV file open error */
"The CSV file you selected could not be found or read." = "Vybraný CSV soubor nebyl nalezen nebo načten.";

/* Error when CSV appears to have too many columns to import, probably due to line ending mismatch */
"The CSV was read as containing more than 512 columns, more than the maximum columns permitted for speed reasons by Sequel Ace.\n\nThis usually happens due to errors reading the CSV; please double-check the CSV to be imported and the line endings and escape characters at the bottom of the CSV selection dialog." = "CSV bylo vykázáno, že obsahuje více než 512 sloupců, více než maximální sloupce povolené z důvodů rychlosti Sequel Ace.\n\nK tomu obvykle dochází kvůli chybám čtení CSV; prosím dvakrát zkontrolujte CSV, který má být importován, konce řádků a únikové znaky ve spodní části dialogu pro výběr CSV.";

/* unsaved theme informative message */
"The current color theme is unsaved. Do you want to proceed without saving it?" = "Aktuální barevný motiv není uložen. Chcete pokračovat bez uložení?";

/* Short important release note for swap of custom query buttons */
"The Custom Query \"Run\" and \"Run All\" button positions and their shortcuts have been swapped." = "Vlastní Query run a Spustit všechny pozice tlačítek a jejich zkratky byly přeskočeny.";

/* the following default bundles were updated:\n%@\nyour modifications were stored as “(user)”. */
"The following default Bundles were updated:\n%@\nYour modifications were stored as “(user)”." = "Následující výchozí balíčky byly aktualizovány:\n%@\nTvé úpravy byly uloženy jako “(uživatel)”.";

/* favorites export error informative message */
"The following error occurred during the export process:\n\n%@" = "Při exportu došlo k následující chybě:\n\n%@";

/* favorites import error informative message */
"The following error occurred during the import process:\n\n%@" = "Během procesu importu došlo k následující chybě:\n\n%@";

/* table structure : indexes : delete index : error 1553 : description */
"The foreign key relationship '%@' has a dependency on index '%@'. This relationship must be removed before the index can be deleted.\n\nAre you sure you want to continue to delete the relationship and the index? This action cannot be undone." = "Vztah cizího klíče '%1$@' má závislost na indexu '%2$@'. Tento vztah musí být odstraněn před odstraněním indexu.\n\nJste si jisti, že chcete pokračovat v odstraňování vztahu a indexu? Tuto akci nelze vrátit zpět.";

/* table list change alert message */
"The list of tables has changed" = "Seznam tabulek se změnil";

/* message when trying to rename a table/view/proc/etc to an already used name */
"The name '%@' is already used." = "Název '%@' je již použit.";

/* table list change alert informative message */
"The number of tables in this database has changed since the export dialog was opened. There are now %lu additional table(s), most likely added by an external application.\n\nHow would you like to proceed?" = "Počet tabulek v této databázi se změnil od otevření dialogového okna. Nyní existuje %lu další tabulka(y), s největší pravděpodobností přidána externí aplikací.\n\nJak chcete pokračovat?";

/* message of panel when no rows have been affected after writing to the db */
"The row was not written to the MySQL database. You probably haven't changed anything.\nReload the table to be sure that the row exists and use a primary key for your table.\n(This error can be turned off in the preferences.)" = "Řádek nebyl zapsán do databáze MySQL. Pravděpodobně jste nic nezměnili.\nZnovu načtěte tabulku a ujistěte se, že řádek existuje a použijte primární klíč pro vaši tabulku.\n(Tuto chybu lze vypnout v nastavení.)";

/* export : import settings : file version error description ($1 = is version, $2 = list of supported versions); note: the u00A0 is a non-breaking space, do not add more whitespace. */
"The selected export settings were stored with version %1$ld, but only settings with the following versions can be imported: %2$@.\n\nEither save the settings in a backwards compatible way or update your version of Sequel Ace." = "Vybrané nastavení exportu bylo uloženo s verzí%1$ld, ale pouze nastavení s následujícími verzemi lze importovat: %2$@.\n\nBuď uložte nastavení zpět kompatibilním způsobem, nebo aktualizujte verzi Sequel Ace.";

/* export : import settings : spf content type error description */
"The selected file contains data of type “%1$@”, but type “%2$@” is needed. Please choose a different file." = "Vybraný soubor obsahuje data typu „%1$@”, ale je třeba napsat „%2$@“. Zvolte prosím jiný soubor.";

/* export : import settings : file error description */
"The selected file is either not a valid SPF file or severely corrupted." = "Vybraný soubor buď není platný SPF soubor nebo je vážně poškozen.";

/* error deleting relation informative message */
"The selected relation couldn't be deleted.\n\nMySQL said: %@" = "Vybrané relace nelze odstranit.\n\nMySQL řekl: %@";

/* error deleting trigger informative message */
"The selected trigger couldn't be deleted.\n\nMySQL said: %@" = "The selected trigger couldn't be deleted.\n\nMySQL said: %@";

/* description of tinyint */
"The smallest integer type, requires 1 byte storage space. M is the optional display width and does not affect the possible value range." = "Nejmenší celočíselný typ vyžaduje 1 bajtový úložný prostor. M je volitelná šířka displeje a nemá vliv na rozsah možných hodnot.";

/* message of panel when connection to socket failed because optional socket could not be found */
"The socket file could not be found in any common location. Please supply the correct socket location.\n\nMySQL said: %@" = "Soubor se soketem nebyl nalezen v žádném společném umístění. Zadejte prosím správné umístění socketu.\n\nMySQL řekl: %@";

/* error creating relation informative message */
"The specified relation could not be created.\n\nMySQL said: %@" = "Zadaná relace nemohla být vytvořena.\n\nMySQL řekl: %@";

/* error creating trigger informative message */
"The specified trigger was unable to be created.\n\nMySQL said: %@" = "Zadaný spouštěč nemohl být vytvořen.\n\nMySQL řekl: %@";

/* sql import : charset error alert : detail message */
"The SQL file uses utf8mb4 encoding, but your MySQL version only supports the limited utf8 subset.\n\nYou can continue the import, but any non-BMP characters in the SQL file (eg. some typographic and scientific special characters, archaic CJK logograms, emojis) will be unrecoverably lost!" = "SQL soubor používá kódování utf8mb4, ale vaše verze MySQL podporuje pouze omezenou podsadu utf8.\n\nMůžete pokračovat v importu, ale jakékoli jiné než BMP znaky v SQL souboru (např. některé typografické a vědecké speciální znaky, archaické CJK logy, emojis) budou nenávratně ztraceny!";

/* SQL file open error */
"The SQL file you selected could not be found or read." = "Vybraný SQL soubor nebyl nalezen nebo načten.";

/* Prompt for SSH password when keychain fetch failed */
"The SSH password could not be loaded from the keychain; please enter the SSH password for %@:" = "SSH heslo nelze načíst z klíče; zadejte prosím SSH heslo pro %@:";

/* Prompt for SSH password when direct fetch failed */
"The SSH password could not be loaded; please enter the SSH password for %@:" = "Nelze načíst SSH heslo; zadejte SSH heslo pro %@:";

/* SSH tunnel authentication failed message */
"The SSH Tunnel could not authenticate with the remote host. Please check your password and ensure you still have access." = "SSH tunel se nemohl autentizovat se vzdáleným serverem. Zkontrolujte prosím své heslo a ujistěte se, že máte stále přístup.";

/* SSH tunnel unexpectedly closed */
"The SSH Tunnel has unexpectedly closed." = "SSH tunel byl neočekávaně uzavřen.";

/* SSH tunnel was closed by remote host message */
"The SSH Tunnel was closed 'by the remote host'. This may indicate a networking issue or a network timeout." = "SSH tunel byl zavřen 'vzdáleným serverem'. To může naznačovat problém se sítí nebo časový limit sítě.";

/* SSH tunnel forwarding port connection refused message */
"The SSH Tunnel was established successfully, but could not forward data to the remote port as the remote port refused the connection." = "SSH tunel byl úspěšně vytvořen, ale nemohl předat data do vzdáleného portu, protože vzdálený port odmítl připojení.";

/* SSH tunnel unable to bind to local port message */
"The SSH Tunnel was unable to bind to the local port. This error may occur if you already have an SSH connection to the same server and are using a 'LocalForward' setting in your SSH configuration.\n\nWould you like to fall back to a standard connection to localhost in order to use the existing tunnel?" = "SSH tunel se nepodařilo navázat na místní port. Tato chyba může nastat v případě, že již máte SSH připojení ke stejnému serveru a používáte v konfiguraci SSH nastavení 'LocalForward'.\n\nChcete se vrátit ke standardnímu připojení k localhostu, abyste mohli používat existující tunel?";

/* SSH tunnel failed or timed out message */
"The SSH Tunnel was unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds)." = "SSH tunel se nepodařilo připojit k hostiteli %1$@nebo vypršel časový limit požadavku.\n\nUjistěte se, že adresa je správná a že máte potřebná oprávnění, nebo zkuste zvýšit časový limit připojení (v současné době %2$ld sekund).";

/* message of panel when loading of table failed and presumably due to used filter argument */
"The table data couldn't be loaded presumably due to used filter clause. \n\nMySQL said: %@" = "Data tabulky nemohla být načtena pravděpodobně z důvodu použité doložky filtru. \n\nMySQL řekl: %@";

/* message of panel when loading of table failed */
"The table data couldn't be loaded.\n\nMySQL said: %@" = "Data tabulky se nepodařilo načíst.\n\nMySQL řekl: %@";

/* Export folder not writable explanatory text */
"The target export folder is not writable.  Please select a new export location and try again." = "Do cílové složky pro export nelze zapisovat. Zvolte prosím nové umístění pro export a zkuste to znovu.";

/* Export folder not chosen by user explanatory text */
"No directory selected.  Please select a new export location and try again." = "Nebyl vybrán žádný adresář. Vyberte prosím nové místo exportu a zkuste to znovu.";
"No directory selected." = "Nebyl vybrán žádný adresář.";
"Please select a new export location and try again." = "Vyberte prosím nové místo exportu a zkuste to znovu.";

/* Export folder missing explanatory text */
"The target export folder no longer exists.  Please select a new export location and try again." = "Cílová složka pro export již neexistuje. Vyberte novou polohu pro export a zkuste to znovu.";

/* theme name label */
"Theme Name:" = "Název šablony:";

/* themes installation error */
"Themes Installation Error" = "Chyba instalace motivů";

/* message of panel when table content cannot be copied */
"There have been errors while copying table content. Please check the new table." = "Při kopírování obsahu tabulky došlo k chybám. Zkontrolujte prosím novou tabulku.";

/* Cannot duplicate a table with triggers to a different database */
"Cannot duplicate a table with triggers to a different database." = "Nelze duplikovat tabulku s spouštěči do jiné databáze.";

/* text shown when query was successfull */
"There were no errors." = "Neobjevily se žádné chyby.";

/* delete field and foreign key informative message */
"This field is part of a foreign key relationship with the table '%@'. This relationship must be removed before the field can be deleted.\n\nAre you sure you want to continue to delete the relationship and the field? This action cannot be undone." = "Toto pole je součástí zahraničního klíče vztahu s tabulkou '%@'. Tento vztah musí být odstraněn, než může být pole odstraněno.\n\nJste si jisti, že chcete pokračovat v odstraňování vztahu a pole? Tuto akci nelze vrátit zpět.";

/* table structure : indexes : delete index : error 1553, no FK found : description */
"This index cannot be deleted, because it is used by an existing foreign key relationship.\n\nPlease remove the relationship, before trying to remove this index.\n\nMySQL said: %@" = "Tento index nelze odstranit, protože je používán ve vztahu existujícím zahraničním klíčem.\n\nPřed odstraněním tohoto indexu odstraňte vztah.\n\nMySQL řekl: %@";

/* description of serial */
"This is an alias for BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE." = "Toto je alias pro BIGINT UNSIGNED NULL AUTO_INCREMENT UNIQUE.";

/* description of dec
 description of fixed
 description of numeric */
"This is an alias for DECIMAL." = "Toto je alias pro DECIMAL.";

/* description of double real */
"This is an alias for DOUBLE, unless REAL_AS_FLOAT is configured." = "Toto je alias pro DOUBLE, pokud není nastaven REAL_AS_FLOAT.";

/* description of double precision */
"This is an alias for DOUBLE." = "Toto je alias pro DOUBLE.";

/* description of bool
 description of boolean */
"This is an alias for TINYINT(1)." = "Toto je alias pro TINYINT(1).";

/* Table Structure : Collation dropdown : database marker tooltip */
"This is the default collation of database %@." = "Toto je výchozí porovnání databáze %@.";

/* Table Structure : Collation dropdown : default marker tooltip */
"This is the default collation of encoding %@." = "Toto je výchozí kolize kódování %@.";

/* Table Structure : Collation dropdown : table marker tooltip */
"This is the default collation of table %@." = "Toto je výchozí porovnání tabulky %@.";

/* Table Structure : Collation dropdown : server marker tooltip */
"This is the default collation of this server." = "Toto je výchozí porovnání tohoto serveru.";

/* Table Structure : Encoding dropdown : database marker tooltip */
"This is the default encoding of database %@." = "Toto je výchozí kódování databáze %@.";

/* Table Structure : Encoding dropdown : table marker tooltip */
"This is the default encoding of table %@." = "Toto je výchozí kódování tabulky %@.";

/* Table Structure : Encoding dropdown : server marker tooltip */
"This is the default encoding of this server." = "Toto je výchozí kódování tohoto serveru.";

/* This table currently does not support relations. Only tables that use the InnoDB storage engine support them. */
"This table currently does not support relations. Only tables that use the InnoDB storage engine support them." = "Tato tabulka v současné době nepodporuje vztahy. Podporují je pouze tabulky, které používají úložiště InnoDB.";

/* user has no hosts informative message */
"This user doesn't have any hosts associated with it. It will be deleted unless one is added" = "Tento uživatel nemá přiřazené žádné hostitele. Bude smazán, pokud nebude přidán";

/* error removing host informative message */
"This user doesn't seem to have any associated hosts and will be removed unless a host is added." = "Zdá se, že tento uživatel nemá žádné přidružené hostitele a bude odstraněn, dokud nebude přidán hostitele.";

/* shutdown server : confirmation dialog : message */
"This will wait for open transactions to complete and then quit the mysql daemon. Afterwards neither you nor anyone else can connect to this database!\n\nFull management access to the server's operating system is required to restart MySQL!" = "To bude čekat na otevřené transakce na dokončení a pak skončit mysql daemon. Poté se k této databázi nemůžete připojit ani vy, ani kdokoli jiný!\n\nÚplný přístup k operačnímu systému serveru je vyžadován pro restartování MySQL!";

/* export filename time token */
"Time" = "Čas";

/* toolbar item label for switching to the Table Triggers tab */
"Triggers" = "Spouštěče";

/* triggers for table label */
"Triggers for table: %@" = "Spouštěče pro tabulku: %@";

/* truncate button */
"Truncate" = "Zkrátit";

/* truncate tables message */
"Truncate selected tables?" = "Zkracovat vybrané tabulky?";

/* truncate table menu title */
"Truncate Table..." = "Zkrátit tabulku...";

/* truncate table message */
"Truncate table '%@'?" = "Zkrátit tabulku '%@'?";

/* truncate tables menu item */
"Truncate Tables" = "Zkrátit tabulky";

/* type label (Navigator) */
"Type" = "Typ";

/* type declaration header */
"Type Declaration:" = "Prohlášení o typu:";

/* add index error message */
"Unable to add index" = "Nelze přidat index";

/* unable to analyze selected items message */
"Unable to analyze selected items" = "Vybrané položky nelze analyzovat";

/* unable to analyze table message */
"Unable to analyze table" = "Nelze analyzovat tabulku";

/* unable to check selected items message */
"Unable to check selected items" = "Vybrané položky nelze zkontrolovat";

/* unable to check table message */
"Unable to check table" = "Nelze zkontrolovat tabulku";

/* message of panel when connection to host failed due to access denied error */
"Unable to connect to host %@ because access was denied.\n\nDouble-check your username and password and ensure that access from your current location is permitted.\n\nMySQL said: %@" = "Nelze se připojit k hostiteli %1$@ , protože přístup byl odepřen.\n\nZkontrolujte své uživatelské jméno a heslo a ujistěte se, že je povolen přístup z vaší aktuální polohy.\n\nMySQL řekl: %2$@";

/* message of panel when SSH port forwarding failed */
"Unable to connect to host %@ because the port connection via SSH was refused.\n\nPlease ensure that your MySQL host is set up to allow TCP/IP connections (no --skip-networking) and is configured to allow connections from the host you are tunnelling via.\n\nYou may also want to check the port is correct and that you have the necessary privileges.\n\nChecking the error detail will show the SSH debug log which may provide more details.\n\nMySQL said: %@" = "Nelze se připojit k hostování %1$@ , protože připojení k portu přes SSH bylo odmítnuto.\n\nUjistěte se, že Váš MySQL host je nastaven pro povolení TCP/IP připojení (ne --skip-networking) a je nastaven tak, aby bylo možné povolit připojení z hostitele, který tunelling via.\n\nMůžete také zkontrolovat, že port je správný a že máte potřebná oprávnění.\n\nKontrola detailu chyb zobrazí protokol SSH ladění, který může poskytnout více podrobností.\n\nMySQL řekl: %2$@";

/* message of panel when connection to host failed */
"Unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds).\n\nMySQL said: %@" = "Nelze se připojit k hostiteli %1$@nebo vypršel časový limit požadavku.\n\nUjistěte se, že adresa je správná a že máte potřebná oprávnění, nebo zkuste zvýšit časový limit připojení (v současné době %2$ld sekund).\n\nMySQL řekl: %3$@";

/* message of panel when connection to host failed */
"Unable to connect via the socket, or the request timed out.\n\nDouble-check that the socket path is correct and that you have the necessary privileges, and that the server is running.\n\nMySQL said: %@" = "Nelze se připojit pomocí socketu, nebo vypršel časový limit požadavku.\n\nZkontrolujte, zda je cesta k soketu správná a zda máte potřebná oprávnění a že server běží.\n\nMySQL řekl: %@";

/* unable to copy database message */
"Unable to copy database" = "Nelze zkopírovat databázi";

/* error deleting index message */
"Unable to delete index" = "Index nelze odstranit";

/* error deleting relation message */
"Unable to delete relation" = "Nelze odstranit relaci";

/* error deleting trigger message */
"Unable to delete trigger" = "Nelze odstranit spouštěč";

/* unable to flush selected items message */
"Unable to flush selected items" = "Vybrané položky nelze vymazat";

/* unable to flush table message */
"Unable to flush table" = "Nelze vymazat tabulku";

/* unable to get list of users message */
"Unable to get list of users" = "Nelze získat seznam uživatelů";

/* error killing connection message */
"Unable to kill connection" = "Nelze ukončit připojení";

/* error killing query message */
"Unable to kill query" = "Dotaz nelze ukončit";

/* unable to optimze selected items message */
"Unable to optimze selected items" = "Vybrané položky nelze optimalizovat";

/* unable to optimze table message */
"Unable to optimze table" = "Nelze optimalizovat tabulku";

/* unable to perform the checksum */
"Unable to perform the checksum" = "Kontrolní součet nelze provést";

/* error removing host message */
"Unable to remove host" = "Hostitele nelze odstranit";

/* unable to rename database message */
"Unable to rename database" = "Nelze přejmenovat databázi";

/* unable to repair selected items message */
"Unable to repair selected items" = "Vybrané položky nelze opravit";

/* unable to repair table message */
"Unable to repair table" = "Nelze opravit tabulku";

/* message of panel when connection to db failed after selecting from popupbutton */
"Unable to select database %@.\nPlease check you have the necessary privileges to view the database, and that the database still exists." = "Nelze vybrat databázi %@.\nZkontrolujte, zda máte potřebná oprávnění k prohlížení databáze a zda databáze stále existuje.";

/* Unable to write row error */
"Unable to write row" = "Nelze zapsat řádek";

/* Table Content : Remove Row : Result : n Error title */
"Unexpected number of rows removed!" = "Neočekávaný počet řádků odstraněn!";

/* warning */
"Unknown file format" = "Neznámý formát souboru";

/* unsaved changes message */
"Unsaved changes" = "Neuložené změny";

/* unsaved theme message */
"Unsaved Theme" = "Neuložená šablona";

/* Preferences : Network : Custom SSH client : warning dialog title */
"Unsupported configuration!" = "Nepodporovaná konfigurace!";

/* export : import settings : file version error title */
"Unsupported version for export settings!" = "Nepodporovaná verze pro export!";

/* Name for an untitled connection */
"Untitled" = "Bez názvu";

/* Title of a new Sequel Ace Document */
"Untitled %ld" = "Bez názvu %ld";

/* range for mediumblob type */
"Up to %@ bytes (16 MiB)" = "Až do %@ bajtů (16 MiB)";

/* range for longblob type */
"Up to %@ bytes (4 GiB)" = "Až do %@ bajtů (4 GiB)";

/* range for mediumtext type */
"Up to %@ characters (16 MiB)" = "Až do %@ znaků (16 MiB)";

/* range for enum type */
"Up to %@ distinct members (<%@ in practice)\n1-2 bytes storage" = "Až %1$@ různé členy (<%2$@ v praxi)\n1-2 bajtů";

/* range for tinyblob type */
"Up to 255 bytes" = "Až 255 bajtů";

/* range for tinytext type */
"Up to 255 characters" = "Až 255 znaků";

/* Open Files : Bundle : Already-Installed : Update button */
"Update" = "Aktualizovat";

/* updated: %@ */
"updated: %@" = "aktualizováno: %@";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field of table `%2$@` was changed." = "Aktualizace obsahu pole selhala. Nelze jednoznačně identifikovat původ pole (%1$ld shody). Je velmi pravděpodobné, že při úpravě tohoto pole tabulky `%2$@` bylo změněno.";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field the table `%2$@` was changed by an other user." = "Aktualizace obsahu pole selhala. Nelze jednoznačně identifikovat původ pole (%1$ld shody). Je velmi pravděpodobné, že během úpravy tohoto pole byla tabulka `%2$@` změněna jiným uživatelem.";

/* updating field task description */
"Updating field data..." = "Aktualizuji data polí...";

/* URL scheme command couldn't authenticated */
"URL scheme command couldn't authenticated" = "Příkaz URL schématu nebyl ověřen";

/* URL scheme command was terminated by user */
"URL scheme command was terminated by user" = "Příkaz URL schématu byl ukončen uživatelem";

/* URL scheme command %@ unsupported */
"URL scheme command %@ unsupported" = "Příkaz URL schématu %@ není podporován";

/* Use 127.0.0.1 button */
"Use 127.0.0.1" = "Použít 127.0.0.1";

/* use standard connection button */
"Use Standard Connection" = "Použít standardní připojení";

/* user has no hosts message */
"User has no hosts" = "Uživatel nemá žádné hostitele";

/* user-defined value */
"User-defined value" = "Uživatelsky definovaná hodnota";

/* toolbar item label for switching to the User Manager tab */
"Users" = "Uživatelé";

/* CSV Field Mapping : Table View : Tooltip for fields with NULL value */
"Value will be imported as MySQL NULL" = "Hodnota bude importována jako MySQL NULL";

/* variable label for color table (Prefs > Editor) */
"Variable" = "Proměnná";

/* version */
"version" = "verze";

/* export header version label */
"Version" = "Verze";

/* view */
"view" = "Zobrazit";

/* Release notes button title */
"View full release notes" = "Zobrazit poznámky k plnému vydání";

/* header for view info pane */
"VIEW INFORMATION" = "ZOBRAZIT INFORMACE";

/* view html source code menu item title */
"View Source" = "Zobrazit zdroj";

/* view structure print heading */
"View Structure" = "Zobrazit strukturu";

/* views */
"views" = "zobrazení";

/* warning */
"Warning" = "Varování";

/* Short important release note for why password prompts may occur */
"We've changed Sequel Ace's digital signature for GateKeeper compatibility; you'll have to allow access to your passwords again." = "Pro kompatibilitu s GateKeeper jsme změnili digitální podpis Sequel Ace; budete muset znovu povolit přístup ke svým heslům.";

/* Important release notes informational text, single change */
"We've made a few changes but we thought you should know about one particularly important one:" = "Provedli jsme několik změn, ale mysleli jsme si, že byste měli vědět o jednom zvláště důležitém:";

/* Important release notes informational text, multiple changes */
"We've made a few changes but we thought you should know about some particularly important ones:" = "Provedli jsme několik změn, ale mysleli jsme si, že byste měli vědět o některých zvláště důležitých změnách:";

/* WHERE clause not valid */
"WHERE clause not valid" = "VZHLEDEM k tomu, že ustanovení není platné";

/* Title of filter preview area when the query WHERE is negated */
"WHERE NOT query" = "KDE NENÍ dotaz";

/* Title of filter preview area when the query WHERE is normal */
"WHERE query" = "VÍCE dotaz";

/* Generic working description */
"Working..." = "Zpracovávám...";

/* export label showing app is writing data */
"Writing data..." = "Zapisování dat...";

/* text showing that app is writing text file */
"Writing..." = "Psaní...";

/* wrong data format or password */
"Wrong data format or password." = "Nesprávný datový formát nebo heslo.";

/* wrong data format */
"Wrong data format." = "Nesprávný formát dat.";

/* export : import settings : spf content type error title */
"Wrong SPF content type!" = "Nesprávný typ obsahu SPF!";

/* export filename date token */
"Year" = "Rok";

/* message of panel when trying to copy multiple rows */
"You can only copy single rows." = "Můžete kopírovat pouze jeden řádek.";

/* message of panel when trying to edit tables without index and with hidden blob/text fields */
"You can't hide blob and text fields when working with tables without index." = "Nemůžete skrýt blob a textová pole při práci s tabulkami bez indexu.";

/* You cannot delete the last field in a table. Delete the table instead. */
"You cannot delete the last field in a table. Delete the table instead." = "Poslední pole v tabulce nelze odstranit. Namísto toho odstraňte tabulku.";

/* SSL connection requested but not established error detail */
"You requested that the connection should be established using SSL, but MySQL made the connection without SSL.\n\nThis may be because the server does not support SSL connections, or has SSL disabled; or insufficient details were supplied to establish an SSL connection.\n\nThis connection is not encrypted." = "Požádali jste o navázání spojení pomocí SSL, ale MySQL se připojil bez SSL.\n\nMůže to být proto, že server nepodporuje připojení SSL nebo že je SSL zakázáno; nebo nebyly poskytnuty dostatečné podrobnosti pro navázání spojení SSL.\n\nToto připojení není šifrované.";

/* Query Favorites : List : Section Heading : current connection document : tooltip (arg is the name of the spf file) */
"‘%@’ based favorites" = "‘%@’ based favorites";

/* table column header. Read: 'Showing all content filters for fields of type %@' (ContentFilterManager) */
"‘%@’ Fields Content Filters" = "„%@“ Filtry obsahu polí";

/* Export file already exists message */
"%@ already exists. Do you want to replace it?" = "%@ již existuje. Chceš ho nahradit?";

/* Bundle Editor : Outline View : Bundle item : tooltip */
"%@ Bundle" = "%@ Balíček";

/* Export file creation error title */
"%@ could not be created" = "%@ nelze vytvořit";

/* %@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item. */
"%@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item." = "%@ nelze analyzovat. Můžete upravit nastavení sloupce, ale sloupec nebude zobrazen v zobrazení obsahu; prosím nahlaste tento problém týmu Sequel Ace pomocí položky nabídky Nápověda.";

/* connection view : ssl : client cert file picker : wrong format error title */
"%@ is not a valid client certificate file." = "%@ není platný soubor klientských certifikátů.";

/* connection view : ssl : key file picker : wrong format error title */
"%@ is not a valid private key file." = "%@ není platný soubor soukromého klíče.";

/* text shown when there are App Sandbox Issues */
"App Sandbox Issue" = "Problém se Sandboxem";

/* Stale Bookmarks error title */
"Stale Bookmarks" = "Zastaralé záložky";

/* App Sandbox info link text */
"App Sandbox Info" = "Informace o aplikaci Sandbox";

/* error while selecting file title */
"File Selection Error" = "Chyba výběru souboru";

/* error while selecting file message */
"The selected file is not a valid file.\n\nPlease try again.\n\nClass: %@" = "Vybraný soubor není platný soubor.\n\nZkuste to prosím znovu.\n\nTřída: %@";

/* known hosts not writable error message */
"The selected known hosts file is not writable.\n\n%@\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "Vybraný soubor hosts není zapisovatelný.\n\n%@\n\nProsím znovu vyberte soubor v předvolbách Sequel Ace a zkuste to znovu.";

/* known hosts is invalid message */
"The selected known hosts file is invalid.\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "Vybraný soubor hosts je neplatný.\n\nProsím znovu vyberte soubor v předvolbách Sequel Ace a zkuste to znovu.";

/* known hosts contains quote message */
"The selected known hosts file contains a quote (\") in its file path which is not supported.\n\n%@\n\nPlease select a different file in Sequel Ace's Preferences or rename the file/path to remove the quote." = "Vybraný soubor hosts obsahuje cenovou nabídku (\") ve své cestě k souboru, která není podporována.\n\n%@\n\nProsím vyberte jiný soubor v nastavení Sequel Ace, nebo přejmenujte soubor/cestu pro odstranění cenové nabídky.";

/* SSH Tunnel Debugging menu and window title */
"SSH Tunnel Debugging Info" = "Informace o ladění SSH tunelu";

/* Question to user to see if they would like to re-request access */
"You have stale secure bookmarks:\n\n%@\n\nWould you like to re-request access now?" = "Máte zastaralé bezpečné záložky:\n\n%@\n\nChcete nyní požádat o přístup?";

/* Question to user to see if they would like to request access to missing bookmarks */
"You have missing secure bookmarks:\n\n%@\n\nWould you like to request access now?" = "Chybí ti bezpečné záložky:\n\n%@\n\nChceš požádat o přístup?";

/* known hosts advance menu item title */
"Use known hosts from ssh config (ADVANCED)" = "Použít známé hostitele z konfigurace ssh (ADVANCED)";

/* The answer, yes */
"Yes" = "Ano";

/* Shown to remind users of their stale bookmarks */
"A reminder of your stale secure bookmarks:<br /><br />%@<br />" = "Připomenutí vaší skalní bezpečné záložky:<br /><br />%@<br />";

/* Title for Stale Secure Bookmarks help window */
"Stale Secure Bookmarks" = "Zastaralé zabezpečené záložky";

/* Title for Export Error alert */
"Export Error" = "Chyba exportu";

/* Message for Export Error alert */
"Error while writing to the export file. Could not open file: %@" = "Chyba při zápisu do exportního souboru. Nelze otevřít soubor: %@";

/* Message for User window error */
"Resultset from mysql.user contains neither 'Password' nor 'authentication_string' column." = "Výsledná sada mysql.user neobsahuje sloupec 'Heslo' ani 'authentication_string'.";

/* Title for User window error */
"User Data Error" = "Chyba uživatelských dat";

/* Title for Stale Bookmark file selection dialog */
"Please re-select the file '%@' in order to restore Sequel Ace's access." = "Prosím znovu vyberte soubor '%@' pro obnovení přístupu Sequel Ace.";

/* Title for File Preference file selection dialog */
"Please choose a file or folder to grant Sequel Ace access to." = "Vyberte soubor nebo složku, ke které chcete povolit přístup Sequel Ace.";

/* Title for Network Preference ssh config file selection dialog */
"Please choose your ssh config files(s)" = "Prosím, vyberte konfigurační soubory ssh";

/* Title for Network Preference ssh known hosts file selection dialog */
"Please choose your known hosts file" = "Vyberte prosím váš známý soubor hosts";

/* Message for field editor JSON segment when JSON is invalid */
"Invalid JSON" = "Neplatný JSON";

/* Applying syntax highlighting task description */
"Applying syntax highlighting..." = "Probíhá zvýrazňování syntaxi...";

/* Error message when a bash task fails to start */
"Couldn't launch task.\nException reason: %@\n ENV length: %lu" = "Nelze spustit úkol.\nDůvod: %@\n ENV délka: %lu";

/* Error message title when we failed to create new connection view */
"New Connection Error" = "Chyba nového připojení";

/* New Connection Error informative message */
"Failed to create new database connection window. Please restart Sequel Ace and try again." = "Nepodařilo se vytvořit nové okno pro připojení k databázi. Restartujte Sequel Ace a zkuste to znovu.";

/* new version is available alert title */
"A new version is available" = "Je dostupná nová verze";

/* new version is available download button title */
"Download" = "Stáhnout";

/* new version is available alert informativeText */
"Version %@ is available. You are currently running %@" = "Verze %@ je k dispozici. Momentálně používáte %@";

/* downloading new version window title */
"Download Progress" = "Průběh stahování";

/* downloading new version time remaining placeholder text */
"Calculating time remaining..." = "Výpočet zbývajícího času...";

/* downloading new version window message */
"Downloading Sequel Ace - %@" = "Stahování Sequel Ace - %@";

/* downloading new version time remaining estimate text */
"About %.1f seconds left" = "O %.1f sekund zbývá";

/* downloading new version failure alert title */
"Download Failed" = "Stahování se nezdařilo";

/* Tool tip for the Show alert when update is available preference on the Notifications pane */
"Only available for GitHub downloads" = "Dostupné pouze pro stažení GitHub";

/* Tool tip warning for when the user sets auto-complete delay to zero */
"WARNING: Setting the auto-complete delay to 0.0 can result in strange output." = "VAROVÁNÍ: Nastavení zpoždění automatického dokončování na 0.0 může mít za následek podivný výstup.";

/* downloading new version bytes of bytes done */
"%@ of %@" = "%@ z %@";

/* Time zone set failed informative message */
"\n\ntime_zone will be set to SYSTEM." = "Časová zóna \n\nbude nastavena na SYSTEM.";

/* Menu item title for checking for updates */
"Check for Updates..." = "Zkontrolovat aktualizace...";

/* Warning message during connection in case the variable skip-show-database is set to ON */
"The skip-show-database variable of the database server is set to ON. Thus, you won't be able to list databases unless you have the SHOW DATABASES privilege.\n\nHowever, the databases are still accessible directly through SQL queries depending on your privileges." = "Proměnná přeskakovací databáze databázového serveru je zapnutá. Takže nebudete moci vypisovat databáze, dokud nebudete mít oprávnění SHOW DATABASES.\n\nNicméně databáze jsou stále přístupné přímo prostřednictvím SQL dotazů v závislosti na vašich oprávněních.";

/* Error alert title when the request to GitHub fails */
"GitHub Request Failed" = "Žádost o GitHub selhala";

/* Info alert title when there are Newer Releases Available */
"No Newer Release Available" = "Žádné novější vydání není k dispozici";

/* Info alert message when there are Newer Releases Available */
"You are currently running the latest release." = "V současné době běžíte na nejnovější verzi.";

/* Alert button, disable the warning when skip-show-database is set to on */
"Never show this again" = "Znovu nezobrazovat";

/* Info alert when editing a generated column */
"The current field \"%@\" is a generated column and therefore cannot be edited." = "Aktuální pole \"%@\" je vygenerovaný sloupec, a proto nelze editovat.";

/* Release note / info modal about change in latest release about column behavior */
"The use of the \"default\" column has changed since the last version of Sequel ACE:\n\n- No default value : Leave it blank.\n- String value : Use single '' or double quotes \"\" if you want an empty string or to wrap a string\n- Expression : Use parentheses (). Except for the TIMESTAMP and DATETIME columns where you can specify the CURRENT_TIMESTAMP function without enclosing parentheses." = "Použití \"default\" sloupce se změnilo od poslední verze Sequel ACE:\n\n- Žádná výchozí hodnota: Ponechte prázdné.\n- Hodnota řetězce : Použijte jednoduchý '' nebo dvojité uvozovky \"\" pokud chcete prázdný řetězec nebo zalomit řetězec\n- Výraz : Použijte závorky (). S výjimkou sloupců TIMESTAMP a DATETIME, kde můžete specifikovat funkci CURRENT_TIMESTAMP bez uzavření závorek.";

/* Menu option to pin a view to the top of the list of tables */
"Pin View" = "Zobrazení PIN";

/* Menu option to pin a table to the top of the list of tables */
"Pin Table" = "Připnout tabulka";

/* Menu option to pin a procedure to the top of the list of tables */
"Pin Procedure" = "Připnout postup";

/* Menu option to pin a function to the top of the list of tables */
"Pin Function" = "Funkce PIN";

/* Menu option to unpin a view which is currently pinned at the top of the list of tables */
"Unpin View" = "Odepnout zobrazení";

/* Menu option to unpin a table which is currently pinned at the top of the list of tables */
"Unpin Table" = "Odepnout tabulku";

/* Menu option to unpin a procedure which is currently pinned at the top of the list of tables */
"Unpin Procedure" = "Odepnout postup";

/* Menu option to unpin a function which is currently pinned at the top of the list of tables */
"Unpin Function" = "Odepnout funkci";

/* header for pinned table list */
"PINNED" = "VYPNOUT";

/* Menu option to copy a table's name with database name in the list of tables */
"Copy Table Name" = "Kopírovat název tabulky";

/* sequelace URL scheme error: LaunchFavorite alert title (favorite name not found) */
"LaunchFavorite URL Scheme Error" = "Chyba URL schématu LaunchFavorite";

/* sequelace URL scheme error: LaunchFavorite alert description (favorite name not found) */
"The variable in the ?name= query parameter could not be matched with any of your favorites." = "Proměnnou v parametru dotazu ?name= nelze porovnat s žádnou z vašich oblíbených.";
