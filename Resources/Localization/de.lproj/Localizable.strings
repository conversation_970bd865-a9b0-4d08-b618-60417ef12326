/* Table Content : Remove Row : Result : Too Few : Part 3 : Row not deleted when using primary key for DELETE statement. */
" Check the Console for possible errors inside the primary key(s) of this table!" = " Überprüfen Sie die Konsole auf mögliche Fehler in Primärschlüssel(n) dieser Tabelle!";

/* Table Content : Remove Row : Result : Too Many : Part 2 : Generic text */
" Please check the Console and inform the Sequel Ace team!" = " Bitte überprüfe die Konsole und benachrichtige das Sequel Ace Team!";

/* Table Content : Remove Row : Result : Too Few : Part 2 : Generic help message */
" Reload the table to be sure that the contents have not changed in the meantime." = " Laden Sie die Tabelle erneut, um eventuelle zwischenzeitliche Änderungen zu sehen.";

/* Table Content : Remove Row : Result : Too Few : Part 3 : no primary key in table generic message */
" You should also add a primary key to this table!" = " Sie sollten der Tabelle auch einen Primärschlüssel hinzufügen!";

/* Query snippet shell command syntax and placeholder */
"$(shell_command)" = "$(shell_command)";

/* text showing how many rows are selected */
"%@ %@ selected" = "%1$@ %2$@ ausgewählt";

/* History item filtered by values label */
"%@ (Filtered by %@)" = "%1$@ (gefiltert durch %2$@)";

/* History item with page number label */
"%@ (Page %lu)" = "%1$@ (Seite %2$lu)";

/* Content Filter Manager : Initial name of copied filter
 Initial favourite name after duplicating a previous favourite */
"%@ Copy" = "%@ Kopie";

/* text showing a single row a partially loaded result */
"%@ row in partial load" = "%@ Datensatz teilweise geladen";

/* text showing a single row in the result */
"%@ row in table" = "%@ Datensatz der Tabelle";

/* text showing how a single rows matched filter */
"%@ row of %@%@ matches filter" = "%1$@ Datensatz von %2$@%3$@ trifft den Filter";

/* text showing how many rows are in a partially loaded result */
"%@ rows in partial load" = "%@ Datensätze teilweise geladen";

/* text showing how many rows are in the result */
"%@ rows in table" = "%@ Datensätze in der Tabelle";

/* text showing how many rows matched filter */
"%@ rows of %@%@ match filter" = "%1$@ Datensätze von %2$@%3$@ treffen den Filter";

/* text showing how many rows have been affected by a single query */
"%@; %ld rows affected" = "%1$@; %2$ld Datensätze betroffen";

/* text showing how many rows have been affected by multiple queries */
"%@; %ld rows affected in total, by %ld queries taking %@" = "%1$@; %2$ld Datensätze von %3$ld Abfragen, die %4$@ verwenden, betroffen";

/* text showing one row has been affected by a single query */
"%@; 1 row affected" = "%@; Ein Datensatz betroffen";

/* text showing one row has been affected by multiple queries */
"%@; 1 row affected in total, by %ld queries taking %@" = "%1$@; ein Datensatz durch %2$ld Abfragen mit %3$@ betroffen";

/* text showing a query was cancelled */
"%@; Cancelled after %@" = "%1$@; Nach %2$@ abgebrochen";

/* text showing multiple queries were cancelled */
"%@; Cancelled in query %ld, after %@" = "%1$@; Abgebrochen in Abfrage %2$ld, nach %3$@";

/* Error display text, showing original MySQL error */
"%@\n\nMySQL said: %@" = "%1$@\n\nMySQL Antwort: %2$@";

/* favorite singular label (%d == 1) */
"%lu favorite" = "%lu Favorit";

/* favorites plural label (%d != 1) */
"%lu favorites" = "%lu Favoriten";

/* favorite group singular label (%d == 1) */
"%lu group" = "%lu Gruppe";

/* favorite groups plural label (%d != 1) */
"%lu groups" = "%lu Gruppen";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+y (y!=1) rows instead of n selected were deleted. */
"%ld additional rows were removed!" = "%ld zusätzliche Datensätze wurden entfernt!";

/* Label showing the index of the selected CSV row */
"%ld of %lu record(s)" = "%1$ld von %2$lu Datensätzen";

/* Label showing the index of the selected CSV row (csv partially loaded) */
"%ld of first %lu record(s)" = "%1$ld der ersten %2$lu Datensätze";

/* Table Content : Remove Row : Result : Too Few : Part 1 : n-x (x!=1) of n selected rows were deleted. */
"%ld rows were not removed." = "%ld Datensätze wurden nicht entfernt.";

/* Export file already exists message */
"%lu files already exist. Do you want to replace them?" = "%lu Dateien sind bereits vorhanden. Sollen sie ersetzt werden?";

/* Export files creation error title */
"%lu files could not be created" = "%lu Dateien konnten nicht erstellt werden";

/* Some export files already exist explanatory text */
"%lu files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "%lu gleichnamige Dateien sind im Zielordner bereits enthalten. Ersetzen überschreibt den Inhalt der bestehenden Dateien.";

/* filtered item count */
"%lu of %lu" = "%1$lu von %2$lu";

/* Export folder not writable for some files explanatory text */
"%lu of the export files could not be created because their target export folder is not writable; please select a new export location and try again." = "%lu der Export-Dateien wurden nicht erzeugt, weil der Zielordner schreibgeschützt ist. Der Export sollte mit einem anderen Zielordner wiederholt werden.";

/* Export folder missing for some files explanatory text */
"%lu of the export files could not be created because their target export folder no longer exists; please select a new export location and try again." = "%lu der Export-Dateien wurden nicht erzeugt, weil der Zielordner nicht gefunden wurde. Der Export sollte mit einem anderen Zielordner wiederholt werden.";

/* History item title with nothing selected */
"(no selection)" = "(keine Auswahl)";

/* value shown for hidden blob and text fields */
"(not loaded)" = "(nicht geladen)";

/* Explanation for MySQL server has gone away error */
"(This usually indicates that the connection has been closed by the server after inactivity, but can also occur due to other conditions.  The connection has been restored; please try again if the query is safe to re-run.)" = "(Dies bedeutet normalerweise, dass der Server die Verbindung wegen Inaktivität beendet hat, allerdings können auch andere Ursachen vorliegen. Die Verbindung wurde wiederhergestellt, die Abfrage kann wiederholt werden, wenn keine Bedenken bestehen.)";

/* Custom Query : text appended to the “x row(s) affected” messages. $1 is a time interval */
", first row available after %1$@" = ", erster Datensatz nach %1$@ verfügbar";

/* Custom Query : text appended to the “x row(s) affected” messages (for update/delete queries). $1 is a time interval */
", taking %1$@" = ", benötigte %1$@";

/* warning shown in the console when no rows have been affected after writing to the db */
"/* WARNING: No rows have been affected */\n" = "/* ACHTUNG: Keine Datensätze betroffen */\n";

/* error text when multiple custom query failed */
"[ERROR in query %ld] %@\n" = "[FEHLER in der Abfrage %1$ld] %2$@\n";

/* error text when reading of csv file gave errors */
"[ERROR in row %ld] %@\n" = "[FEHLER in Zeile %1$ld] %2$@\n";

/* [multiple selection] */
"[multiple selection]" = "[Mehrfach-Auswahl]";

/* displayed when new content filter has empty Name field (ContentFilterManager) */
"[name required]" = "[Name erforderlich]";

/* [no selection] */
"[no selection]" = "[Keine Auswahl]";

/* Additional export file errors */
"\n\n(In addition, one or more errors occurred while attempting to create the export files: %lu could not be created. These files will be ignored.)" = "\n\n(Folgende(r) Fehler trat(en) bei der Erstellung der Export-Datei(en) auf: %lu konnte nicht erstellt werden. Diese Datei(en) werden übergangen.)";

/* \n\npress shift for binary search tooltip message */
"\n\nPress ⇧ for binary search (case-sensitive)." = "\n\n ⇧ für die binäre Suche mit Groß- und Kleinschreibung drücken.";

/* description of bit */
"A bit-field type. M specifies the number of bits. If shorter values are inserted, they will be aligned on the least significant bit. See the SET type if you want to explicitly name each bit." = "Ein Bit-Feld Typ. M gibt die Zahl der Bits an. Werden kürzere Werte eingetragen, erfolgt die Ausrichtung nach dem geringwertigsten Bit. Der SET Typ ermöglicht die Benennung jedes einzelnen Bits.";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog message */
"A Bundle ‘%@’ is already installed. Do you want to update it?" = "Ein Bundle ‘%@’ wurde bereits installiert. Soll es erneuert werden?";

/* description of binary */
"A byte array with fixed length. Shorter values will always be padded to the right with 0x00 until they fit M." = "Ein Byte-Array mit fester Länge. Kürzere Werte werden immer nach rechts mit 0x00 bis zum Erreichen von M aufgefüllt.";

/* description of varbinary */
"A byte array with variable length. The actual number of bytes is further limited by the values of other fields in the row." = "Ein Byte-Array mit variabler Länge. Die tatsächliche Zahl der Bytes wird weiters durch die Werte anderer Felder der Zeile begrenzt.";

/* description of blob
 description of longblob
 description of mediumblob
 description of tinyblob */
"A byte array with variable length. Unlike VARBINARY this type does not count towards the maximum row length." = "Ein Byte-Array mit variable Länge. Dieser Typ wird nicht zur maximalen Zeilenlänge aufgerechnet (anders als VARBINARY).";

/* description of tinytext */
"A character string that can store up to 255 bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Eine Zeichenkette, die bis zu 255 Bytes speichern kann, allerdings weniger Platz für kürzere Werte benötigt. Die tatsächliche Zahl der Zeichen wird weiters durch die verwendeten Zeichen-Codierung begrenzt. Anders als VARCHAR wird dieser Typ nicht gegen die maximale Zeilenlänge aufgerechnet.";

/* description of varchar */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding and the values of other fields in the row." = "Eine Zeichenkette, die bis zu M Bytes speichern kann, allerdings weniger Platz für kürzere Werte benötigt. Weiters wird die tatsächliche Zahl der Zeichen durch die verwendete Zeichen-Codierung und die übrigen Felder in der Zeile begrenzt.";

/* description of text */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Eine Zeichenkette, die bis zu M Bytes speichern kann, allerdings weniger Platz für kürzere Werte benötigt. Weiters wird die tatsächliche Zahl der Zeichen durch die verwendete Zeichen-Codierung. Anders als VARCHAR wird dieser Typ nicht gegen die Zeilenlänge aufgerechnet.";

/* description of char */
"A character string that will require M×w bytes per row, independent of the actual content length. w is the maximum number of bytes a single character can occupy in the given encoding." = "Eine Zeichenkette, die unabhängig von der tatsächlichen Länge des Inhalts M×w Bytes pro Zeile benötigt. w ist die Höchstzahl an Bytes, die ein einzelnes Zeichen gemäß der Zeichen-Codierung beansprucht.";

/* description of longtext
 description of mediumtext */
"A character string with variable length. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Eine Zeichenkette mit variabler Länge. Die Zahl der tatsächlichen Zeichen wird durch die verwendete Zeichen-Codierung begrenzt. Anders als VARCHAR wird dieser Typ nicht gegen die Zeilenlänge aufgerechnet.";

/* description of json */
"A data type that validates JSON data on INSERT and internally stores it in a binary format that is both, more compact and faster to access than textual JSON.\nAvailable from MySQL 5.7.8." = "Ein Typ, der JSON Daten beim INSERT validiert, platzsparend in binärem Format ablegt und schnellere Zugriffe als Text-basiertes JSON ermöglicht.\nVerfügbar seit MySQL 5.7.8.";

/* Export file already exists explanatory text */
"A file with the same name already exists in the target folder. Replacing it will overwrite its current contents." = "Eine Datei mit selbem Namen liegt bereits im Ziel-Ordner. Das Ersetzen überschreibt deren Inhalt.";

/* description of decimal */
"A fixed-point, exact decimal value. M is the maxium number of digits, of which D may be after the decimal point. When rounding, 0-4 is always rounded down, 5-9 up (“round towards nearest”)." = "Ein exakter Festkomma-Dezimalwert. Die maximale Zahl an Stellen ist M, davon können D Stellen rechts vom Komma stehen. Werte werden kaufmännisch (4/5) gerundet.";

/* table structure : indexes : delete index : error 1553 : title
 table structure : indexes : delete index : error 1553, no FK found : title */
"A foreign key needs this index" = "Ein Fremdschlüssel benötigt diesen Index";

/* description of set */
"A SET can define up to 64 members (as strings) of which a field can use one or more using a comma-separated list. Upon insertion the order of members is automatically normalized and duplicate members will be eliminated. Assignment of numbers is supported using the same semantics as for BIT types." = "Ein SET kann bis zu 64 Elemente (z.B. Zeichenketten) definieren, die von einem Feld in einer durch Komma getrennten Liste genutzt werden können. Beim Einfügen wird die Sortierung der Elemente automatisch normalisiert und Duplikate werden entfernt. Die Zuordnung von Ziffern wird gemäß der Semantik für BIT Typen unterstützt.";

/* SSH key not found message */
"A SSH key location was specified, but no file was found in the specified location.  Please re-select the key and try again." = "Ein SSH-Schlüssel wurde festgelegt, am angegebenen Ort wurde jedoch kein Schlüssel gefunden. Bitte erneut versuchen. ";

/* SSL CA certificate file not found message */
"A SSL Certificate Authority certificate location was specified, but no file was found in the specified location.  Please re-select the Certificate Authority certificate and try again." = "In der angegebenen Ablage der Autorisierungsstelle wurden keine SSL Zertifikate gefunden. Das Zertifikat sollte erneut gewählt werden. ";

/* SSL certificate file not found message */
"A SSL certificate location was specified, but no file was found in the specified location.  Please re-select the certificate and try again." = "An der angegebenen Ablage wurde kein SSL Zertifikat gefunden. Das Zertifikat sollte erneut gewählt werden.";

/* SSL key file not found message */
"A SSL key file location was specified, but no file was found in the specified location.  Please re-select the key file and try again." = "An der angegebenen Ablage konnte keine SSL-Schlüssel Datei gefunden werden. Bitte erneut versuchen.";

/* duplicate host informative message */
"A user with the host '%@' already exists" = "Der Benutzer am Host '%@' existiert bereits";

/* duplicate user informative message */
"A user with the name '%@' already exists" = "Der Benutzername '%@' existiert bereits";

/* table content : editing : error message description when parsing as hex string failed */
"A valid hex string may only contain the numbers 0-9 and letters A-F (a-f). It can optionally begin with „0x“ and spaces will be ignored.\nAlternatively the syntax X'val' is supported, too." = "Eine gültige hexadezimale Zeichenkette darf nur die Zeichen 0-9 und A-F enthalten. Als Präfix kann „0x“ benutzt werden, Leerzeichen werden übergangen. Als Alternative ist auch die Schreibweise X’wert’ zulässig.";

/* connection failed due to access denied title */
"Access denied!" = "Zugriff abgelehnt!";

/* range of double */
"Accurate to approx. 15 decimal places" = "Auf etwa 15 Dezimalstellen genau";

/* range of float */
"Accurate to approx. 7 decimal places" = "Auf etwa 7 Dezimalstellen genau";

/* active connection window is busy. please wait and try again. tooltip */
"Active connection window is busy. Please wait and try again." = "Das Fenster der aktiven Verbindung ist ausgelastet. Bitte später versuchen.";

/* header for activities pane */
"ACTIVITIES" = "AKTIVITÄTEN";

/* Add trigger button label */
"Add" = "Hinzufügen";

/* menu item to add db */
"Add Database..." = "Datenbank hinzufügen …";

/* Add Host */
"Add Host" = "Host hinzufügen";

/* add global value or expression menu item */
"Add Value or Expression…" = "Wert oder Ausdruck hinufügen …";

/* adding index task status message */
"Adding index..." = "Index hinzufügen …";

/* Advanced options short title */
"Advanced" = "Fortgeschritten";

/* notifications preference pane name */
"Alerts & Logs" = "Warnungen & Protokolle";

/* notifications preference pane tooltip */
"Alerts & Logs Preferences" = "Vorgaben für Warnungen & Protokolle";

/* All databases placeholder */
"All Databases" = "Alle Datenbanken";

/* All databases (%) placeholder */
"All Databases (%)" = "Alle Datenbanken (%)";

/* All export files already exist message */
"All the export files already exist. Do you want to replace them?" = "Alle Export-Dateien existieren bereits. Sollen sie überschrieben werden?";

/* An error for sequelace URL scheme command occurred. Probably no corresponding connection window found. */
"An error for sequelace URL scheme command occurred. Probably no corresponding connection window found." = "Beim Sequel Ace URL Schema Befehl trat ein Fehler auf. Offenbar wurde kein entsprechendes Verbindungsfenster gefunden.";

/* no connection available informatie message */
"An error has occurred and there doesn't seem to be a connection available." = "Ein Fehler ist scheinbar ohne verfügbare Verbindung aufgetreten.";

/* an error occur while executing a scheme command. if the scheme command was invoked by a bundle command, it could be that the command still runs. you can try to terminate it by pressing ⌘+. or via the activities pane. */
"An error occur while executing a scheme command. If the scheme command was invoked by a Bundle command, it could be that the command still runs. You can try to terminate it by pressing ⌘+. or via the Activities pane." = "Ein Fehler ist bei der Ausführung eines Schema Befehls aufgetreten. Wenn der Schema Befehl von einem Bundle Befehl ausgelöst wurde, könnte die Verarbeitung noch laufen. Sie können den Abbruch durch Drücken von ⌘+. oder über das Aktivitäten Paneel versuchen.";

/* error killing query informative message */
"An error occurred while attempting to kill connection %lld.\n\nMySQL said: %@" = "Beim Versuch, Verbindung %1$lld. zu beenden, trat ein Fehler auf.\n\nMySQL Ergebnis: %2$@";

/* error killing query informative message */
"An error occurred while attempting to kill the query associated with connection %lld.\n\nMySQL said: %@" = "Beim Versuch, eine Abfrage in Beziehung zur Verbindung %1$lld. zu beenden, trat ein Fehler auf\n\nMySQL Ergebnis: %2$@";

/* Error shown when unable to show create table syntax */
"An error occurred while creating table syntax.\n\n: %@" = "Beim Erstellen der Tabellen-Syntax trat ein Fehler auf.\n\n: %@";

/* rename table error - no temporary name found */
"An error occurred while renaming '%@'. No temporary name could be found. Please try renaming to something else first." = "Beim Umbenennen von '%@' trat ein Fehler auf, kein provisorischer Name wurde gefunden. Versuchen Sie, zunächst zu einem anderen Namen umzubenennen.";

/* rename table error informative message */
"An error occurred while renaming '%@'.\n\nMySQL said: %@" = "Beim Umbenennen von '%1$@' trat ein Fehler auf.\n\nMySQL Ergebnis: %2$@";

/* rename error - don't know what type the renamed thing is */
"An error occurred while renaming. '%@' is of an unknown type." = "Beim Umbenennen trat ein Fehler auf. '%@' hat einen unbekannten Typ.";

/* rename precedure/function error - can't delete old procedure */
"An error occurred while renaming. I couldn't delete '%@'.\n\nMySQL said: %@" = "Beim Umbenennen trat ein Fehler auf. '%1$@' konnte nicht entfernt werden.\n\nMySQL Ergebnis: %2$@";

/* rename precedure/function error - can't recreate procedure */
"An error occurred while renaming. I couldn't recreate '%@'.\n\nMySQL said: %@" = "Beim Umbenennen trat ein Fehler auf. '%1$@' konnte nicht wiederhergestellt werden.\n\nMySQL Ergebnis: %2$@";

/* rename precedure/function error - can't retrieve syntax */
"An error occurred while renaming. I couldn't retrieve the syntax for '%@'.\n\nMySQL said: %@" = "Beim Umbenennen trat ein Fehler auf. Die Schreibweise für '%1$@' konnte nicht verbessert werden.\n\nMySQL Ergebnis: %2$@";

/* rename error - invalid create syntax */
"An error occurred while renaming. The CREATE syntax of '%@' could not be parsed." = "Beim Umbenennen trat ein Fehler auf. Die Schreibweise für CREATE von '%@' konnte nicht geparst werden.";

/* message of panel when retrieving view information failed */
"An error occurred while retrieving status data.\n\nMySQL said: %@" = "Beim Abruf von Status Daten ist ein Fehler aufgetreten.\n\nMySQL Ergebnis: %@";

/* message of panel when create syntax cannot be retrieved */
"An error occurred while retrieving the create syntax for '%@'.\nMySQL said: %@" = "Beim Abruf Schreibweise für das CREATE von %1$@' ist ein Fehler aufgetreten.\nMySQL Ergebnis: %2$@";

/* add index error informative message */
"An error occurred while trying to add the index.\n\nMySQL said: %@" = "Beim Erstellen des Index ist ein Fehler aufgetreten.\n\nMySQL Ergebnis: %@";

/* error adding password to keychain informative message */
"An error occurred while trying to add the password to your Keychain. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Beim Aufnehmen des Passworts an den Schlüsselbund ist ein Fehler aufgetreten. Eine Reparatur des Schlüsselbunds könnte erfolgreich sein. Bleibt der Fehler bestehen, berichten Sie das Fall an das Sequel Ace Team mit dem Fehler-Code %i.";

/* unable to copy database message informative message */
"An error occurred while trying to copy the database '%@' to '%@'." = "Beim Kopieren der Datenbank '%1$@' nach '%2$@' trat ein Fehler auf.";

/* error deleting index informative message */
"An error occurred while trying to delete the index.\n\nMySQL said: %@" = "Beim Löschen des Index trat ein Fehler auf.\n\nMySQL Ergebnis: %@";

/* table status : row count query failed : error message */
"An error occurred while trying to determine the number of rows for %@.\nMySQL said: %@ (%lu)" = "Beim Versuch, die Anzahl der Zeilen für “%1$@” zu ermitteln, ist ein Fehler aufgetreten.\nMySQL meldet: %2$@ (%3$lu)";

/* unable to rename database message informative message */
"An error occurred while trying to rename the database '%@' to '%@'." = "Beim Umbenennen der Datenbank '%1$@' zu '%2$@' trat ein Fehler auf.";

/* error finding keychain item to edit informative message */
"An error occurred while trying to retrieve the Keychain item you're trying to edit. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Der Abruf des zu bearbeitenden Schlüssels vom Schlüsselbund schlug fehl. Eine Reparatur des Schlüsselbunds könnte erfolgreich sein. Bleibt der Fehler bestehen, berichten Sie das Fall an das Sequel Ace Team mit dem Fehler-Code %i.";

/* error updating keychain item informative message */
"An error occurred while trying to update the Keychain item. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Beim Erneuern des Schlüssels am Schlüsselbund trat ein Fehler auf. Eine Reparatur des Schlüsselbunds könnte erfolgreich sein. Bleibt der Fehler bestehen, berichten Sie das Fall an das Sequel Ace Team mit dem Fehler-Code %i.";

/* mysql error occurred message */
"An error occurred" = "Ein Fehler ist aufgetreten";

/* MySQL table info retrieval error message */
"An error occurred retrieving table information.  MySQL said: %@" = "Beim Abruf der Tabellen-Information trat ein Fehler auf.  MySQL Ergebnis: %@";

/* SQL encoding read error */
"An error occurred when reading the file, as it could not be read in the encoding you selected (%@).\n\nOnly %ld queries were executed." = "Die Datei konnte mit der gewählten Zeichen-Codierung (%1$@) nicht gelesen werden.\n\nNur %2$ld Abfragen wurden ausgeführt.";

/* CSV encoding read error */
"An error occurred when reading the file, as it could not be read using the encoding you selected (%@).\n\nOnly %ld rows were imported." = "Die Datei konnte mit der gewählten Zeichen-Codierung (%1$@) nicht gelesen werden.\n\nNur %2$ld Datensätze wurden importiert.";

/* SQL read error, including detail from system */
"An error occurred when reading the file.\n\nOnly %ld queries were executed.\n\n(%@)" = "Beim Lesen der Datei ist ein Fehler aufgetreten.\n\nNur %1$ld Abfragen wurden ausgeführt.\n\n(%2$@)";

/* CSV read error, including detail string from system */
"An error occurred when reading the file.\n\nOnly %ld rows were imported.\n\n(%@)" = "Beim Lesen der Datei ist ein Fehler aufgetreten.\n\nNur %1$ld Datensätze wurden importiert.\n\n(%2$@)";

/* error adding field informative message */
"An error occurred when trying to add the field '%@' via\n\n%@\n\nMySQL said: %@" = "Beim Hinzufügen des Feldes '%1$@' über\n\n%2$@ trat ein Fehler auf.\n\nMySQL Ergebnis: %3$@";

/* error changing field informative message */
"An error occurred when trying to change the field '%@' via\n\n%@\n\nMySQL said: %@" = "Beim Versuch das Feld '%1$@' über\n\n%2$@ zu ändern trat ein Fehler auf.\n\nMySQL Ergebnis: %3$@";

/* error changing table collation informative message */
"An error occurred when trying to change the table collation to '%@'.\n\nMySQL said: %@" = "Beim Versuch, die Tabellen-Collation auf '%1$@' zu ändern, trat ein Fehler auf.\n\nMySQL Ergebnis: %2$@";

/* error changing table encoding informative message */
"An error occurred when trying to change the table encoding to '%@'.\n\nMySQL said: %@" = "Beim Versuch, die Zeichen-Codierung der Tabelle auf '%1$@' zu ändern, trat ein Fehler auf.\n\nMySQL Ergebnis: %2$@";

/* error changing table type informative message */
"An error occurred when trying to change the table type to '%@'.\n\nMySQL said: %@" = "Beim Versuch, den Tabellentyp auf '%1$@' zu ändern, trat ein Fehler auf.\n\nMySQL Ergebnis: %2$@";

/* error changing table comment informative message */
"An error occurred when trying to change the table's comment to '%@'.\n\nMySQL said: %@" = "Beim Versuch, den Tabellen-Kommentar auf ’%1$@' zu ändern, trat ein Fehler auf.\n\nMySQL Ergebnis: %2$@";

/* an error occurred while analyzing the %@.\n\nMySQL said:%@ */
"An error occurred while analyzing the %@.\n\nMySQL said:%@" = "Beim Analysieren von %1$@ trat ein Fehler auf.\n\nMySQL Ergebnis:%2$@";

/* an error occurred while fetching the optimized field type.\n\nMySQL said:%@ */
"An error occurred while fetching the optimized field type.\n\nMySQL said:%@" = "Beim Abruf des optimierten Feld-Typs trat ein Fehler auf.\n\nMySQL Ergebnis:%@";

/* an error occurred while trying to flush the %@.\n\nMySQL said:%@ */
"An error occurred while flushing the %@.\n\nMySQL said:%@" = "Beim Abschließen von %1$@ trat ein Fehler auf.\n\nMySQL Ergebnis:%2$@";

/* sql import error message */
"An error occurred while importing SQL" = "Beim Import von SQL trat ein Fehler auf";

/* an error occurred while trying to optimze the %@.\n\nMySQL said:%@ */
"An error occurred while optimzing the %@.\n\nMySQL said:%@" = "Beim Optimieren von %1$@ trat ein Fehler auf.\n\nMySQL Ergebnis:%2$@";

/* an error occurred while performing the checksum on the %@.\n\nMySQL said:%@ */
"An error occurred while performing the checksum on %@.\n\nMySQL said:%@" = "Das Erstellen der Prüfsumme von %1$@ trat ein Fehler auf.\n\nMySQL Ergebnis:%2$@";

/* an error occurred while trying to repair the %@.\n\nMySQL said:%@ */
"An error occurred while repairing the %@.\n\nMySQL said:%@" = "Beim reparieren von %1$@ trat ein Fehler auf.\n\nMySQL Ergebnis:%2$@";

/* message of panel when retrieving information failed */
"An error occurred while retrieving information.\nMySQL said: %@" = "Beim Abruf trat ein Fehler auf.\nMySQL Ergebnis: %@";

/* error retrieving table information informative message */
"An error occurred while retrieving the information for table '%@'. Please try again.\n\nMySQL said: %@" = "Beim Abruf der Information für Tabelle '%1$@' trat ein Fehler auf; bitte erneut versuchen.\n\nMySQL Ergebnis: %2$@";

/* error retrieving table information informative message */
"An error occurred while retrieving the trigger information for table '%@'. Please try again.\n\nMySQL said: %@" = "Beim Abruf der Trigger-Information für Tabelle '%1$@' trat ein Fehler auf; bitte erneut versuchen.\n\nMySQL Ergebnis: %2$@";

/* error adding new column informative message */
"An error occurred while trying to add the new column '%@' by\n\n%@.\n\nMySQL said: %@" = "Beim Anlegen der Spalte '%1$@' durch\n\n%2$@ trat ein Fehler auf.\n\nMySQL Ergebnis: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@' by\n\n%@.\n\nMySQL said: %@" = "Beim Anlegen der Tabelle '%1$@' durch\n\n%2$@ trat ein Fehler auf.\n\nMySQL Ergebnis: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@'.\n\nMySQL said: %@" = "Beim Anlegen der Tabelle '%1$@' trat ein Fehler auf.\n\nMySQL Ergebnis: %2$@";

/* error while trying to alter table message */
"An error occurred while trying to alter table '%@'.\n\nMySQL said: %@" = "Beim Ändern der Tabelle '%1$@' trat ein Fehler auf.\n\nMySQL Ergebnis: %2$@";

/* an error occurred while trying to check the %@.\n\nMySQL said:%@ */
"An error occurred while trying to check the %@.\n\nMySQL said:%@" = "Beim Prüfen von %1$@ trat ein Fehler auf.\n\nMySQL Ergebnis:%2$@";

/* error deleting relation informative message */
"An error occurred while trying to delete the relation '%@'.\n\nMySQL said: %@" = "Beim Löschen der Beziehung '%1$@' trat ein Fehler auf.\n\nMySQL Ergebnis: %2$@";

/* unable to get list of users informative message */
"An error occurred while trying to get the list of users. Please make sure you have the necessary privileges to perform user management, including access to the mysql.user table." = "Beim Abruf der Benutzerliste trat ein Fehler auf. Stellen Sie sicher, dass die erforderlichen Rechte, einschließlich des Zugriffs auf die Tabelle mysql.user erteilt wurden.";

/* error importing table informative message */
"An error occurred while trying to import a table via: \n%@\n\n\nMySQL said: %@" = "Ein Fehler ist beim importieren der Tabelle über: \n%1$@\naufgetreten.\n\nMySQL Ergebnis: %2$@";

/* error moving field informative message */
"An error occurred while trying to move the field.\n\nMySQL said: %@" = "Beim Verschieben des Feldes trat ein Fehler auf.\n\nMySQL Ergebnis: %@";

/* error resetting auto_increment informative message */
"An error occurred while trying to reset AUTO_INCREMENT of table '%@'.\n\nMySQL said: %@" = "Beim Zurücksetzen von AUTO_INCREMENT der Tabelle '%1$@' trat ein Fehler auf.\n\nMySQL Ergebnis: %2$@";

/* error truncating table informative message */
"An error occurred while trying to truncate the table '%@'.\n\nMySQL said: %@" = "Beim Leeren der Tabelle '%1$@' trat ein Fehler auf.\n\nMySQL-Ergebnis: %2$@";

/* mysql error occurred informative message */
"An error occurred whilst trying to perform the operation.\n\nMySQL said: %@" = "Bei der Ausführung trat ein Fehler auf.\n\nMySQL Ergebnis: %@";

/* mysql resource limits unsupported message */
"Resource Limits are not supported for your version of MySQL. Any Resouce Limits you specified have been discarded and not saved. MySQL said: %@" = "Ressourcenlimits werden für Ihre MySQL-Version nicht unterstützt. Alle von Ihnen angegebenen Ressourcenlimits wurden verworfen und nicht gespeichert. MySQL sagte: %@";

/* Export files creation error explanatory text */
"An unhandled error occurred when attempting to create %lu of the export files.  Please check the details and try again." = "Beim Anlegen von %lu der Export-Dateien trat ein unklarer Fehler auf. Nach Prüfen der Umstände erneut versuchen.";

/* All export files creation error explanatory text */
"An unhandled error occurred when attempting to create each of the export files.  Please check the details and try again." = "Beim Anlegen der einzelnen Export-Dateien trat ein unklarer Fehler auf. Nach Prüfen der Umstände erneut versuchen.";

/* Export file creation error explanatory text */
"An unhandled error occurred when attempting to create the export file.  Please check the details and try again." = "Beim Anlegen der Export-Dateien trat ein unklarer Fehler auf. Nach Prüfen der Umstände erneut versuchen.";

/* ANALYZE one or more tables - result title */
"Analyze %@" = "Analysiere %@";

/* analyze selected items menu item */
"Analyze Selected Items" = "Analysiere die gewählten Items";

/* analyze table menu item */
"Analyze Table" = "Analysiere die Tabelle";

/* analyze table failed message */
"Analyze table failed." = "Fehler beim Analysieren der Tabelle.";

/* change table type informative message */
"Are you sure you want to change this table's type to %@?\n\nPlease be aware that changing a table's type has the potential to cause the loss of some or all of its data. This action cannot be undone." = "Soll der Tabellen-Typ tatsächlich auf %@ geändert werden?\n\nDas Ändern des Typs einer Tabelle ist unwiderruflich und kann Datenverlust zur Folge haben.";

/* clear global history list informative message */
"Are you sure you want to clear the global history list? This action cannot be undone." = "Soll das globale Protokoll wirklich unwiderruflich geleert werden?";

/* clear history list for %@ informative message */
"Are you sure you want to clear the history list for %@? This action cannot be undone." = "Sind Sie sicher, dass Sie die Verlaufsliste für %@ löschen wollen? Diese Aktion kann nicht rückgängig gemacht werden.";

/* truncate tables informative message */
"Are you sure you want to delete ALL records in the selected tables? This operation cannot be undone." = "Sollen wirklich ALLE Datensätze der gewählten Tabellen unwiderruflich gelöscht werden?";

/* truncate table informative message */
"Are you sure you want to delete ALL records in the table '%@'? This operation cannot be undone." = "Sollen wirklich alle Datensätze der Tabelle '%@' unwiderruflich gelöscht werden?";

/* delete all rows informative message */
"Are you sure you want to delete all the rows from this table? This action cannot be undone." = "Sollen wirklich alle Datensätze von dieser Tabelle unwiderruflich gelöscht werden?";

/* delete table/view informative message */
"Are you sure you want to delete the %@ '%@'? This operation cannot be undone." = "Sollen wirklich %1$@ '%2$@' unwiderruflich gelöscht werden?";

/* delete database informative message */
"Are you sure you want to delete the database '%@'? This operation cannot be undone." = "Soll die Datenbank '%@' wirklich unwiderruflich gelöscht werden?";

/* delete database informative message */
"Are you sure you want to delete the favorite '%@'? This operation cannot be undone." = "Soll der Favorit '%@' wirklich unwiderruflich gelöscht werden?";

/* delete field informative message */
"Are you sure you want to delete the field '%@'? This action cannot be undone." = "Soll das Feld %@ wirklich unwiderruflich gelöscht werden?";

/* delete database informative message */
"Are you sure you want to delete the group '%@'? All groups and favorites within this group will also be deleted. This operation cannot be undone." = "Soll die Gruppe '%@' und alle darin enthaltenen Gruppen und Favoriten unwiderruflich gelöscht werden?";

/* delete index informative message */
"Are you sure you want to delete the index '%@'? This action cannot be undone." = "Soll der Index %@‚ unwiderruflich gelöscht werden?";

/* delete tables/views informative message */
"Are you sure you want to delete the selected %@? This operation cannot be undone." = "Soll die Auswahl %@ unwiderruflich gelöscht werden?";

/* delete rows informative message */
"Are you sure you want to delete the selected %ld rows from this table? This action cannot be undone." = "Sollen die %ld ausgewählten Zeilen unwiderruflich aus dieser Tabelle gelöscht werden?";

/* delete selected relation informative message */
"Are you sure you want to delete the selected relations? This action cannot be undone." = "Sollen die ausgewählten Beziehungen wirklich unwiderruflich gelöscht werden?";

/* delete selected row informative message */
"Are you sure you want to delete the selected row from this table? This action cannot be undone." = "Soll der ausgewählte Datensatz wirklich unwiderruflich von der Tabelle gelöscht werden?";

/* delete selected trigger informative message */
"Are you sure you want to delete the selected triggers? This action cannot be undone." = "Sollen die ausgewählte Trigger wirklich unwiderruflich gelöscht werden?";

/* kill connection informative message */
"Are you sure you want to kill connection ID %lld?\n\nPlease be aware that continuing to kill this connection may result in data corruption. Please proceed with caution." = "Soll die Verbindung ID %lld wirklich gekappt werden?\n\nSeien Sie besonders vorsichtig, denn durch das Kappen der Verbindung könnte Datenverlust eintreten.";

/* kill query informative message */
"Are you sure you want to kill the current query executing on connection ID %lld?\n\nPlease be aware that continuing to kill this query may result in data corruption. Please proceed with caution." = "Soll die Abfrage über die Verbindung ID %lld wirklich abgebrochen werden?\n\nSeien Sie besonders vorsichtig, denn der Abbruch der Abfrage könnte Datenverlust verursachen.";

/* Bundle Editor : Remove-Bundle: remove dialog message */
"Are you sure you want to move the selected Bundle to the Trash and remove them respectively?" = "Soll das ausgewählte Bundle wirklich in den Papierkorb verschoben und entfernt werden?";

/* continue to print informative message */
"Are you sure you want to print the current content view of the table '%@'?\n\nIt currently contains %@ rows, which may take a significant amount of time to print." = "Soll die aktuelle Ansicht der Tabelle '%1$@' wirklich gedruckt werden?\n\nDer Umfang beträgt %2$@ Datensätze, deren Druck sehr viel Zeit erfordern wird.";

/* remove all query favorites informative message */
"Are you sure you want to remove all of your saved query favorites? This action cannot be undone." = "Sollen wirklich alle gespeicherten Abfragen unwiderruflich aus den Favoriten gelöscht werden?";

/* remove all selected content filters informative message */
"Are you sure you want to remove all selected content filters? This action cannot be undone." = "Sollen wirklich alle ausgewählten Inhalts-Filter unwiderruflich gelöscht werden?";

/* remove all selected query favorites informative message */
"Are you sure you want to remove all selected query favorites? This action cannot be undone." = "Sollen wirklich alle ausgewählten Abfragen unwiderruflich aus den Favoriten gelöscht werden?";

/* Table Info Section : current value of auto_increment */
"auto_increment: %@" = "auto_increment: %@";

/* Encoding autodetect menu item */
"Autodetect" = "Automatische Erkennung";

/* background label for color table (Prefs > Editor) */
"Background" = "Hintergrund";

/* backtick quote label for color table (Prefs > Editor) */
"Backtick Quote" = "`Backtick`";

/* bash error */
"BASH Error" = "BASH Fehler";

/* toolbar item label for switching to the Table Content tab */
"Browse & Edit Table Content" = "Tabelleninhalt sichten & bearbeiten";

/* build label */
"build" = "Bauen";

/* build label */
"Build" = "Erstellen";

/* bundle editor menu item label */
"Bundle Editor" = "Bundle-Editor";

/* bundle error */
"Bundle Error" = "Bundle-Fehler";

/* bundles menu item label */
"Bundles" = "Subtypen";

/* Bundle Editor : Outline View : 'BUNDLES' item */
"BUNDLES" = "Subtypen";

/* Bundle Editor : Outline View : Menu Category item : tooltip */
"Bundles in category %@" = "Bundles in Kategorie %@";

/* bundles installation error */
"Bundles Installation Error" = "Bundle-Installationsfehler";

/* bzip2 compression export summary - within a sentence */
"bzip2 compression" = "bzip2 Komprimierung";

/* description of geometry */
"Can store a single spatial value of types POINT, LINESTRING or POLYGON. Spatial support in MySQL is based on the OpenGIS Geometry Model." = "Kann einen einzelnen räumlichen Wert der Typen POINT, LINESTRING oder POLYGON speichern. Die räumliche Unterstützung in MySQL basiert auf dem OpenGIS-Geometriemodell.";

/* Bundle Editor : Remove-Bundle: cancel button
 cancel
 cancel button
 Open Files : Bundle : Already-Installed : Cancel button
 Preferences : Network : Custom SSH client : warning dialog : cancel button
 shutdown server : confirmation dialog : cancel button */
"Cancel" = "Abbrechen";

/* sql import : charset error alert : cancel button */
"Cancel Import" = "Import abbrechen";

/* cancelling task status message */
"Cancelling..." = "Wird abgebrochen …";

/* empty query informative message */
"Cannot save an empty query." = "Eine leere Abfrage kann nicht gespeichert werden.";

/* caret label for color table (Prefs > Editor) */
"Caret" = "Caret (^)";

/* change button */
"Change" = "Wechsel";

/* change focus to table list menu item */
"Change Focus to Table List" = "Fokus auf Tabellenliste legen";

/* change table type message */
"Change table type" = "Tabellen Typ wechseln";

/* unsaved changes informative message */
"Changes have been made, which will be lost if this window is closed. Are you sure you want to continue" = "Sollen alle gemachten Änderungen durch Schließen des Fensters verworfen werden?";

/* quitting app informal alert title */
"Close the app?" = "Close the app?";

/* quitting app informal alert body */
"Are you sure you want to quit the app?" = "Are you sure you want to quit the app?";

/* character set client: %@ */
"character set client: %@" = "Zeichensatz des Client: % @";

/* CHECK one or more tables - result title */
"Check %@" = "Prüfung %@";

/* check of all selected items successfully passed message */
"Check of all selected items successfully passed." = "Die Prüfung alle gewählten Items war erfolgreich.";

/* check option: %@ */
"check option: %@" = "Auswahl treffen: %@";

/* check selected items menu item */
"Check Selected Items" = "Ausgewählte Items prüfen";

/* check table menu item */
"Check Table" = "Tabelle prüfen";

/* check table failed message */
"Check table failed." = "Fehler bei Prüfung der Tabelle.";

/* check table successfully passed message */
"Check table successfully passed." = "Prüfung der Tabelle erfolgreich.";

/* check view menu item */
"Check View" = "Ansicht prüfen";

/* checking field data for editing task description */
"Checking field data for editing..." = "Feld-Daten zur Bearbeitung wählen…";

/* checksum %@ message */
"Checksum %@" = "Prüfsumme %@";

/* checksum selected items menu item */
"Checksum Selected Items" = "Prüfsumme über die Auswahl bilden";

/* checksum table menu item */
"Checksum Table" = "Prüfsumme der Tabelle";

/* Checksums of %@ message */
"Checksums of %@" = "Prüfsummen von %@";

/* menu item for choose db */
"Choose Database..." = "Datenbank wählen …";

/* cancelling export cleaning up message */
"Cleaning up..." = "Aufräumen …";

/* clear button */
"Clear" = "Leeren";

/* toolbar item for clear console */
"Clear Console" = "Konsole leeren";

/* clear global history menu item title */
"Clear Global History" = "Globales Protokoll leeren";

/* clear history for %@ menu title */
"Clear History for %@" = "Protokoll leeren?";

/* clear history message */
"Clear History?" = "Protokoll leeren?";

/* tooltip for toolbar item for clear console */
"Clear the console which shows all MySQL commands performed by Sequel Ace" = "Die Konsole mit allen MySQL Befehlen, die zuvor von Sequel Ace ausgeführt wurden, leeren.";

/* clear the document-based history list tooltip message */
"Clear the document-based history list" = "Das Dokument-basierte Protokoll leeren";

/* clear the global history list tooltip message */
"Clear the global history list" = "Das globale Protokoll leeren";

/* Close menu item */
"Close" = "Schließen";

/* close tab context menu item */
"Close Tab" = "Reiter schließen";

/* Close Window menu item */
"Close Window" = "Fenster schließen";

/* collation label (Navigator) */
"Collation" = "Kollation";

/* collation connection: %@ */
"collation connection: %@" = "Kollation Verbindung: %@";

/* comment label */
"Comment" = "Kommentar";

/* Title of action menu item to comment line */
"Comment Line" = "Zeile kommentieren";

/* Title of action menu item to comment selection */
"Comment Selection" = "Auswahl kommentieren";

/* connect button */
"Connect" = "Verbinden";

/* Connect via socket button */
"Connect via socket" = "Über Socket verbinden";

/* connection established message */
"Connected" = "Verbunden";

/* description for connected notification */
"Connected to %@" = "Verbunden mit %@";

/* message of panel when connection to db failed */
"Connected to host, but unable to connect to database %@.\n\nBe sure that the database exists and that you have the necessary privileges.\n\nMySQL said: %@" = "Die Verbindung zur Datenbank %1$@ auf dem Host schlug fehl.\n\nDie Datenbank und die nötigen Zugriffsrechte müssen auf dem Host vorhanden sein.\n\nMySQL Ergebnis: %2$@";

/* Generic connecting very short status message */
"Connecting..." = "Verbinde …";

/* window title string indicating that sp is connecting */
"Connecting…" = "Verbinde…";

/* error while reading connection data file */
"Connection data file couldn't be read." = "Verbindungsdaten konnten nicht gelesen werden.";

/* message error while reading connection data file and suggesting to save it under a differnet name */
"Connection data file %@ couldn't be read. Please try to save the document under a different name." = "Die Verbindungsdatendatei \"%\" konnte nicht gelesen werden. Versuchen Sie, das Dokument unter einem anderen Namen zu speichern.";

/* connection failed title */
"Connection failed!" = "Verbindung fehlgeschlagen!";

/* Connection file is encrypted */
"Connection file is encrypted" = "Verschlüsselte Verbindung";

/* Connection success very short status message */
"Connection succeeded" = "Verbunden";

/* Console */
"Console" = "Konsole";

/* Console : Save as : Initial filename */
"ConsoleLog" = "Konsolen-lLog";

/* toolbar item label for switching to the Table Content tab */
"Content" = "Inhalt";

/* content filter clause is empty tooltip. */
"Content Filter clause is empty." = "Inhaltsfilter enthält keine Bedingung.";

/* continue button
 Continue button title */
"Continue" = "Fortsetzen";

/* continue to print message */
"Continue to print?" = "Druck fortsetzen?";

/* Copy as RTF */
"Copy as RTF" = "Als RTF kopieren";

/* copy create func syntax menu item
 Table List : Context Menu : copy CREATE FUNCTION syntax */
"Copy Create Function Syntax" = "Syntax ‚Funktion erstellen‘ kopieren";

/* copy create proc syntax menu item
 Table List : Context Menu : Copy CREATE PROCEDURE syntax
 Table List : Gear Menu : Copy CREATE PROCEDURE syntax */
"Copy Create Procedure Syntax" = "Syntax ‚Prozedur erstellen‘ kopieren";

/* copy create syntaxes menu item
 Table List : Context Menu : Copy CREATE syntax (multiple selection)
 Table List : Gear Menu : Copy CREATE syntax (multiple selection) */
"Copy Create Syntaxes" = "Syntaxen für ‚Erstellen‘ kopieren";

/* copy create table syntax menu item
 Table List : Context Menu : Copy CREATE syntax (single table)
 Table List : Gear Menu : Copy CREATE syntax (single table) */
"Copy Create Table Syntax" = "Syntax ‚Tabelle erstellen‘ kopieren";

/* copy create view syntax menu item
 Table List : Context Menu : Copy CREATE view statement
 Table List : Gear Menu : Copy CREATE view statement */
"Copy Create View Syntax" = "Syntax ‚Ansicht erstellen‘ kopieren";

/* copy server variable name menu item */
"Copy Variable Name" = "Variablen-Name kopieren";

/* copy server variable names menu item */
"Copy Variable Names" = "Variablen-Name kopieren";

/* copy server variable value menu item */
"Copy Variable Value" = "Wert der Variable kopieren";

/* copy server variable values menu item */
"Copy Variable Values" = "Werte der Variablen kopieren";

/* Procedure/function export permission error */
"Could not export the %@ '%@' because of a permissions error.\n" = "Wegen fehlender Zugriffsrechte wurde %1$@ '%2$@' nicht exportiert .\n";

/* Error when we can't parse/split file as CSV */
"Could not parse file as CSV" = "Die Datei konnte nicht als CSV verarbeitet werden";

/* message when database selection failed */
"Could not select database" = "Die Datenbank konnte nicht gewählt werden";

/* Alter Database : Query Failed ($1 = mysql error message) */
"Couldn't alter database.\nMySQL said: %@" = "Die Datenbank konnte nicht verändert werden.\nMySQL Ergebnis: %@";

/* Couldn't copy default themes to Application Support Theme folder!\nError: %@ */
"Couldn't copy default themes to Application Support Theme folder!\nError: %@" = "Das Standard-Theme konnte nicht in den Ordner Application Support Theme kopiert werden!\nFehler: %@";

/* message of panel when table cannot be created */
"Couldn't create '%@'.\nMySQL said: %@" = "'%1$@' konnte nicht angelegt werden.\nMySQL Ergebnis: %2$@";

/* Couldn't create Application Support Bundle folder!\nError: %@ */
"Couldn't create Application Support Bundle folder!\nError: %@" = "Der Ordner Application Support Bundle konnte nicht angelegt werden!\\Fehler: %@";

/* Couldn't create Application Support Theme folder!\nError: %@ */
"Couldn't create Application Support Theme folder!\nError: %@" = "Der Ordner Application Support Theme konnten nicht angelegt werden!\\Fehler: %@";

/* message of panel when creation of db failed */
"Couldn't create database.\nMySQL said: %@" = "Die Datenbank konnte nicht angelegt werden.\nMySQL Ergebnis: %@";

/* message of panel when an item cannot be deleted */
"Couldn't delete '%@'.\n\nMySQL said: %@" = "'%1$@' konnte nicht gelöscht werden.\n\nMySQL Ergebnis: %2$@";

/* message of panel when an item cannot be deleted including informative message about using force deletion */
"Couldn't delete '%@'.\n\nSelecting the 'Force delete' option may prevent this issue, but may leave the database in an inconsistent state.\n\nMySQL said: %@" = "'%1$@' konnte nicht gelöscht werden.\n\n’Erzwungenes Löschen’ könnte dem Abhilfe schaffen, aber die Datenbank in einen inkonsistenten Zustand versetzen.\n\nMySQL Ergebnis: %2$@";

/* message of panel when field cannot be deleted */
"Couldn't delete field %@.\nMySQL said: %@" = "Das Feld %1$@ konnte nicht gelöscht werden.\nMySQL Ergebnis: %2$@";

/* message when deleteing all rows failed */
"Couldn't delete rows.\n\nMySQL said: %@" = "Die Zeilen konnten nicht gelöscht werden.\n\nMySQL Ergebnis: %@";

/* message of panel when deleting db failed */
"Couldn't delete the database.\nMySQL said: %@" = "Die Datenbank konnte nicht gelöscht werden.\nMySQL Ergebnis: %@";

/* message of panel when an item cannot be renamed */
"Couldn't duplicate '%@'.\nMySQL said: %@" = "'%1$@' konnte nicht dupliziert werden.\nMySQL Ergebnis: %2$@";

/* message of panel when flushing privs failed */
"Couldn't flush privileges.\nMySQL said: %@" = "Die Zugriffsrechte konnten nicht festgeschrieben werden.\nMySQL Ergebnis: %@";

/* message of panel when table information cannot be retrieved */
"Couldn't get create syntax.\nMySQL said: %@" = "‚Erstellen‘ Syntax nicht gefunden.\nMySQL Ergebnis: %@";

/* Custom Query result editing error - could not identify a corresponding column */
"Couldn't identify field origin unambiguously. The column '%@' contains data from more than one table." = "Die Feld Herkunft ist mehrdeutig. Die Spalte '%@' enthält Daten von mehr als einer Tabelle. ";

/* message of panel when loading of row failed */
"Couldn't load the row. Reload the table to be sure that the row exists and use a primary key for your table." = "Zeile konnte nicht geladen werden. Tabelle erneut laden um sicherzustellen, dass die Zeile vorhanden ist. Einen Primärschlüssel für die Tabelle benutzen.";

/* Couldn't read the file content of */
"Couldn't read the file content of" = "Unlesbarer Datei-Inhalt von";

/* text shown if an error occurred while sorting the result table */
"Couldn't sort column." = "Spalte nicht sortierbar.";

/* message of panel when sorting of table failed */
"Couldn't sort table. MySQL said: %@" = "Tabelle nicht sortierbar. MySQL Ergebnis: %@";

/* message of panel when error while updating field to db */
"Couldn't write field.\nMySQL said: %@" = "Feld nicht geschrieben.\nMySQL Ergebnis: %@";

/* create syntax for table comment */
"Create syntax for" = "Syntax erstellen für";

/* Create syntax label */
"Create syntax for %@ '%@'" = "Erstelle Syntax für %1$@ '%2$@'";

/* Create syntaxes for selected items label */
"Create syntaxes for selected items" = "Syntaxen für ausgewählte Items erzeugen";

/* Table Info Section : table create options */
"create_options: %@" = "create_options: % @";

/* created: %@
 Table Info Section : time+date table was created at */
"created: %@" = "erstellt: %@";

/* description of polygon */
"Creates a surface by combining one LinearRing (ie. a LineString that is closed and simple) as the outside boundary with zero or more inner LinearRings acting as \"holes\"." = "Erstellt eine Oberfläche durch die Kombination eines linearen Ringes (ein LineString ist einfach und geschlossen) als äußere Grenze, innerhlb derer andere Lineare Ringe als „Löcher“ wirken.";

/* Creating table task string */
"Creating %@..." = "Erstelle %@...";

/* Bundle Editor : Fallback Input source dropdown : 'current line' item */
"Current Line" = "Aktuelle Zeile";

/* Bundle Editor : Fallback Input source dropdown : 'current query' item */
"Current Query" = "Aktuelle Abfrage";

/* header for current selection in filtered list */
"CURRENT SELECTION" = "AKTUELLE AUSWAHL";

/* Bundle Editor : Fallback Input source dropdown : 'current word' item */
"Current Word" = "Aktuelles Wort";

/* SSH connection : debug header with user-defined ssh binary */
"Custom SSH binary enabled. Disable in Preferences to rule out incompatibilities!" = "Angepasste binäre SSH aktiv. In den Einstellungen deaktiveren, um Unverträglichkeiten zu vermeiden!";

/* customize file name label */
"Customize Filename (%@)" = "Dateiname anpassen (%@)";

/* data access: %@ (%@) */
"data access: %@ (%@)" = "Datenzugriff: %1$@ (%2$@)";

/* Bundle Editor : Scope dropdown : 'data table' item
 data table menu item label */
"Data Table" = "Datentabelle";

/* Bundle Editor : Outline View : 'Data Table' item : tooltip */
"Data Table Scope\ncommands will run on the Content and Query data tables" = "Data Table Scope\nBefehle werden auf den Inhalt und Abfrageergebnis Tabellen angewandt.";

/* export filename database token
 export header database label
 export source
 Table Structure : Collation dropdown : 'item is the same as the collation of database' marker
 Table Structure : Encoding dropdown : 'item is database default' marker */
"Database" = "Datenbank";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'database changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'database changed' item */
"Database changed" = "Datenbank geändert";

/* message of panel when no db name is given */
"Database must have a name." = "Die Datenbank muss benannt werden.";

/* databsse rename unsupported message */
"Database Rename Unsupported" = "Das Umbennen der Datenbank wird nicht unterstützt.";

/* export filename date token */
"Date" = "Datum";

/* export filename date token */
"Day" = "Tag";

/* Add Table Sheet : Collation : Default (unknown)
 Collation Dropdown : Default (unknown)
 default label
 Table Structure : Collation dropdown : 'item is the same as the default collation of the row's charset' marker */
"Default" = "Standard";

/* Add Table : Collation : Default ($1 = collation name)
 Charset Dropdown : Default item ($1 = charset name)
 Collation Dropdown : Default ($1 = collation name)
 Collation Dropdown : Default collation for given charset ($1 = collation name)
 New Table Sheet : Table Engine Dropdown : Default */
"Default (%@)" = "Standard (%@)";

/* default bundles update */
"Default Bundles Update" = "Standard-Bundles-Update";

/* import : csv field mapping : field default value */
"Default: %@" = "Standard: %@";

/* Query snippet default value placeholder */
"default_value" = "default_value";

/* definer label (Navigator) */
"Definer" = "Bezeichner: %@";

/* definer: %@ */
"definer: %@" = "Bezeichner: %@";

/* description of enum */
"Defines a list of members, of which every field can use at most one. Values are sorted by their index number (starting at 0 for the first member)." = "Definiert eine Liste von Einträgen, von denen jedes Feld höchstens eines verwenden kann. Die Werte werden nach Indexnummer sortiert, bei 0 beginnend\\.";

/* delete button */
"Delete" = "Löschen";

/* delete table/view message */
"Delete %@ '%@'?" = "%1$@ '%2$@' löschen?";

/* table structure : indexes : delete index : error 1553 : delete index and FK button */
"Delete Both" = "Beide löschen";

/* delete database message */
"Delete database '%@'?" = "Datenbank '%@' löschen?";

/* delete database message */
"Delete favorite '%@'?" = "Favorit '%@' löschen?";

/* delete field message */
"Delete field '%@'?" = "Feld '%@' löschen?";

/* delete func menu title */
"Delete Function" = "Funktion löschen";

/* delete functions menu title */
"Delete Functions" = "Funktionen löschen";

/* delete database message */
"Delete group '%@'?" = "Gruppe '%@' löschen?";

/* delete index message */
"Delete index '%@'?" = "Index '%@' löschen?";

/* delete items menu title */
"Delete Items" = "Items löschen";

/* delete proc menu title */
"Delete Procedure" = "Prozedur löschen";

/* delete procedures menu title */
"Delete Procedures" = "Prozeduren löschen";

/* delete relation menu item */
"Delete Relation" = "Beziehung löschen";

/* delete relation message */
"Delete relation" = "Beziehung  löschen";

/* delete relations menu item */
"Delete Relations" = "Beziehungen löschen";

/* delete row menu item singular */
"Delete Row" = "Zeile löschen";

/* delete rows menu item plural */
"Delete Rows" = "Zeilen löschen";

/* delete rows message */
"Delete rows?" = "Zeilen löschen?";

/* delete tables/views message */
"Delete selected %@?" = "Auswahl %@ löschen?";

/* delete selected row message */
"Delete selected row?" = "Ausgewählte Zeile löschen?";

/* delete table menu title */
"Delete Table..." = "Tabelle löschen...";

/* delete tables menu title */
"Delete Tables" = "Tabellen löschen";

/* delete trigger menu item */
"Delete Trigger" = "Trigger löschen";

/* delete trigger message */
"Delete trigger" = "Trigger löschen";

/* delete triggers menu item */
"Delete Triggers" = "Trigger löschen";

/* delete view menu title */
"Delete View" = "Ansicht löschen";

/* delete views menu title */
"Delete Views" = "Ansichten löschen";

/* Preferences : Network : SSL Chiper suites : List seperator */
"Disabled Cipher Suites" = "Deaktivierte Chiffre-Suiten";

/* force table deltion button text tooltip */
"Disables foreign key checks (FOREIGN_KEY_CHECKS) before deletion and re-enables them afterwards." = "Verhindert die Prüfung von Fremdschlüsseln (FOREIGN_KEY_CHECKS) vor dem Löschen und erlaubt sie danach wieder.";

/* discard changes button */
"Discard changes" = "Änderungen verwerfen";

/* description for disconnected notification */
"Disconnected from %@" = "Verbindung mit %@ beenden";

/* do update operator tooltip */
"Do UPDATE where field contents match" = "UPDATE bei übereinstimmenden Feldinhalten";

/* message of panel asking for confirmation for loading large text into the query editor */
"Do you really want to load a SQL file with %@ of data into the Query Editor?" = "Soll wirklich eine SQL Datei mit %@ Daten zur Bearbeitung geladen werden?";

/* message of panel asking for confirmation for inserting large text from dragging action */
"Do you really want to proceed with %@ of data?" = "Soll wirklich mit %@ an Daten fortgefahren werden?";

/* shutdown server : confirmation dialog : title */
"Do you really want to shutdown the server?" = "Soll der Server wirklich abgeschaltet werden?";

/* dtd identifier label (Navigator) */
"DTD Identifier" = "DTD-Bezeichner";

/* sql export dump of table label */
"Dump of table" = "Tabellen-Dump";

/* sql export dump of view label */
"Dump of view" = "Dump of view";

/* text showing that app is writing dump */
"Dumping..." = "Dump läuft…";

/* duplicate object message */
"Duplicate %@ '%@' to:" = "Dupliziere %1$@ '%2$@' nach:";

/* duplicate func menu title */
"Duplicate Function..." = "Duplicate Function…";

/* duplicate host message */
"Duplicate Host" = "Doppelter Host";

/* duplicate proc menu title */
"Duplicate Procedure..." = "Prozedur duplizieren…";

/* duplicate table menu title */
"Duplicate Table..." = "Tabelle duplizieren...";

/* duplicate user message */
"Duplicate User" = "Doppelter User";

/* duplicate view menu title */
"Duplicate View..." = "Ansicht duplizieren";

/* partial copy database support informative message */
"Duplicating the database '%@' is only partially supported as it contains objects other than tables (i.e. views, procedures, functions, etc.), which will not be copied.\n\nWould you like to continue?" = "Das Duplizieren der Datenbank '%@' wird nur teilweise unterstützt, da sie andere Objekte als Tabellen enthält (z.B. Ansichten, Prozeduren, Funktionen, usw.), die nicht kopiert werden.\n\nMöchten Sie fortfahren?";

/* edit filter */
"Edit Filters…" = "Filter bearbeiten…";

/* Edit row button */
"Edit row" = "Zeile bearbeiten";

/* toolbar item label for switching to the Table Structure tab */
"Edit Table Structure" = "Tabellen-Struktur bearbeiten";

/* edit theme list label */
"Edit Theme List…" = "Themenliste bearbeiten...";

/* edit user-defined filter */
"Edit user-defined Filters…" = "Benutzer-definierte Filter bearbeiten…";

/* empty query message */
"Empty query" = "Abfrage leeren";

/* MySQL search language code - eg in http://search.mysql.com/search?q=select&site=refman-50&lr=lang_en */
"en" = "de";

/* encoding label (Navigator) */
"Encoding" = "Die %driver Datenbank muss eine %encoding Kodierung verwenden, um mit Drupal zu funktionieren. Erstellen Sie die Datenbank mit %encoding Kodierung neu. Siehe <a href=\"INSTALL.pgsql.txt\">INSTALL.pgsql.txt</a> für mehr Details.";

/* Table Info Section : $1 = table charset */
"encoding: %1$@" = "Codierung: %1 @";

/* Table Info Section : $1 = table charset, $2 = table collation */
"encoding: %1$@ (%2$@)" = "Codierung: %1 - (%2)";

/* Table Info Section : Table Engine */
"engine: %@" = "Suchmaschine";

/* enter connection details label */
"Enter connection details below, or choose a favorite" = "Verbindungsdetails unten eingeben oder aus den Favoriten wählen";

/* SSH key password prompt */
"Enter your password for the SSH key\n\"%@\"" = "Geben Sie Ihr Kennwort für den SSH-Schlüssel ein. @";

/* Bundle Editor : Fallback Input source dropdown : 'entire content' item
 Bundle Editor : Scope=Field : Input source dropdown: 'entire content' item */
"Entire Content" = "Gesamter Inhalt";

/* Bundle Editor : Copy-Command-Error : error dialog title
 error */
"Error" = "Fehler";

/* error adding field message */
"Error adding field" = "Fehler bei Feld-Erstellung";

/* error adding new column message */
"Error adding new column" = "Fehler bei Spalten-Erstellung";

/* error adding new table message */
"Error adding new table" = "Fehler bei Tabellen-Erstelllung";

/* error adding password to keychain message */
"Error adding password to Keychain" = "Fehler beim Hinzufügen des Passworts an den Schlüsselbund";

/* error changing field message */
"Error changing field" = "Fehler beim Ändern des Feldes";

/* error changing table collation message */
"Error changing table collation" = "Fehler beim Ändern der Kollation der Tabelle";

/* error changing table comment message */
"Error changing table comment" = "Fehler beim Ändern des Tabellen-Kommentars";

/* error changing table encoding message */
"Error changing table encoding" = "Fehler beim Ändern des Zeichensatzes der Tabelle";

/* error changing table type message */
"Error changing table type" = "Fehler beim Ändern des Tabellen-Typs";

/* error creating relation message */
"Error creating relation" = "Fehler beim Erstellen der Relation";

/* error creating trigger message */
"Error creating trigger" = "Fehler beim Erstellen des Triggers";

/* error for message */
"Error for" = "Fehler bei";

/* error for bash command ($1), $2=message */
"Error for “%1$@”:\n%2$@" = "Fehler bei “%1$@”:\n%2$@";

/* error moving field message */
"Error moving field" = "Fehler beim Verschieben des Feldes";

/* error occurred */
"error occurred" = "Fehler aufgetreten";

/* error reading import file */
"Error reading import file." = "Fehler beim Lesen der Import-Datei.";

/* error finding keychain item to edit message */
"Error retrieving Keychain item to edit" = "Fehler beim Abruf des Passworts";

/* error retrieving table information message */
"Error retrieving table information" = "Fehler beim Abruf der Tabellen-Information";

/* error retrieving trigger information message */
"Error retrieving trigger information" = "Fehler beim Abruf der Trigger-Information";

/* error truncating table message */
"Error truncating table" = "Fehler beim Leeren der Tabelle";

/* error updating keychain item message */
"Error updating Keychain item" = "Fehler bei der Aktualisierung des Schlüsselbundes";

/* error while analyzing selected items message */
"Error while analyzing selected items" = "Fehler bei der Analyse der gewählten Items";

/* error while checking selected items message */
"Error while checking selected items" = "Fehler beim Prüfen der gewählten Items";

/* error while converting color scheme data */
"Error while converting color scheme data" = "Fehler beim Umwandeln des Farbschemas";

/* error while converting connection data */
"Error while converting connection data" = "Fehler beim Umwandeln der Verbindungsdaten";

/* Content filters could not be converted to plist upon export - message title (ContentFilterManager) */
"Error while converting content filter data" = "Fehler beim Umwandeln der Inhalts-Filter ";

/* error while converting query favorite data */
"Error while converting query favorite data" = "Fehler beim Umwandeln des Query Favoriten";

/* error while converting session data */
"Error while converting session data" = "Fehler beim Umwandeln der Sitzungs-Daten";

/* Error while deleting field */
"Error while deleting field" = "Feld löschen fehlerhaft";

/* Bundle Editor : Copy-Command-Error : Copying failed error message */
"Error while duplicating Bundle content." = "Bundle-Inhalt duplizieren fehlerhaft.";

/* error while executing javascript bash command */
"Error while executing JavaScript BASH command" = "Fehler beim Ausführen des JavaScript BASH Befehls";

/* error while fetching the optimized field type message */
"Error while fetching the optimized field type" = "Abruf des optimierten Feld-Typs fehlgeschlagen";

/* error while flushing selected items message */
"Error while flushing selected items" = "Flush der gewählten Items fehlgeschlagen";

/* error while importing table message */
"Error while importing table" = "Fehler beim Tabellen-Import";

/* Open Files : Bundle : Install-Error : error dialog title
 Open Files : Bundle : UUID : Error dialog title */
"Error while installing Bundle" = "Fehler bei der Bundle-Installation";

/* error while installing color theme file */
"Error while installing color theme file" = "Installation des Farb-Themas fehlgeschlagen";

/* Bundle Editor : Trash-Bundle(s)-Error : error dialog title
 error while moving %@ to trash
 Open Files : Bundle : Already-Installed : Delete-Old-Error : Could not delete old bundle before installing new version. */
"Error while moving %@ to Trash." = "Fehler beim Verschieben von %- in den Papierkorb.";

/* error while optimizing selected items message */
"Error while optimizing selected items" = "Optimierung der gewählten Items fehlgeschlagen";

/* error while parsing CREATE TABLE syntax */
"Error while parsing CREATE TABLE syntax" = "Syntaxfehler bei CREATE TABLE";

/* error while reading connection data file */
"Error while reading connection data file" = "Datei der Verbindungsdaten fehlerhaft";

/* error while reading data file */
"Error while reading data file" = "Fehler beim Lesen der Datei";

/* error while repairing selected items message */
"Error while repairing selected items" = "Reparieren der gewählten Items fehlgeschlagen";

/* Bundle Editor : Save-Bundle-Error : error dialog title */
"Error while saving the Bundle." = "Bundle konnte nicht gespeichert werden.";

/* Bundle Editor : Save-and-Close-Error : error dialog title */
"Error while saving %@." = "Bundle konnte nicht gespeichert werden.";

/* Errors title */
"Errors" = "Fehler";

/* Mysql Help Viewer : Help Topic: Example section title */
"Example:" = "Beispiel:";

/* Bundle Editor : BLOB dropdown : 'exclude BLOB' item */
"exclude BLOB" = "BLOB ausschließen";

/* execution privilege label (Navigator) */
"Execution Privilege" = "Ausführungs-Rechte";

/* execution privilege: %@ */
"execution privilege: %@" = "Ausführungs-Recht: %@";

/* execution stopped message */
"Execution stopped!\n" = "Ausführung angehalten!\n";

/* export selected favorites menu item */
"Export Selected..." = "Ausgewählte exportieren …";

/* text showing that the application is importing a supplied format */
"Exporting %@" = "Exportiere %@";

/* text showing that the application is exporting a Dot file */
"Exporting Dot File" = "DOT Datei wird exportiert";

/* text showing that the application is exporting SQL */
"Exporting SQL" = "Exportiere SQL";

/* extra label (Navigator) */
"Extra" = "Extra";

/* table structure : indexes : delete index : no columns error : title */
"Failed to remove index '%@'" = "Entfernen von Index '%@' fehlerhaft";

/* fatal error */
"Fatal Error" = "Schwerer Fehler";

/* export filename favorite name token */
"Favorite" = "Favorit";

/* favorites label */
"Favorites" = "Favoriten";

/* favorites export error message */
"Favorites export error" = "Fehler beim Export der Favoriten";

/* favorites import error message */
"Favorites import error" = "Fehler beim Import der Favoriten";

/* export label showing that the app is fetching data */
"Fetching data..." = "Datenabruf …";

/* fetching database structure data in progress */
"fetching database structure data in progress" = "Abruf der Daten der Datenbankstruktur läuft";

/* fetching database structure in progress */
"fetching database structure in progress" = "Abruf der Datenbankstruktur läuft";

/* fetching table data for completion in progress message */
"fetching table data…" = "Abruf der Tabellen-Daten…";

/* popup menuitem for field (showing only if disabled) */
"field" = "Feld";

/* Field */
"Field" = "Feld";

/* Query result editing error - could not match row being edited uniquely */
"Field is not editable. Couldn't identify field origin unambiguously (%ld matches)." = "Feld nicht bearbeitbar. Die Herkunft konnte nicht zweifelsfrei festgestellt werden (%ld Übereinstimmungen).";

/* field is not editable due to no table/database */
"Field is not editable. Field has no or multiple table or database origin(s)." = "Das Feld kann nicht bearbeitet werden, der Speicherort ist unklar.";

/* Custom Query result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload data, check encoding, or try to add\na primary key field or more fields\nin your SELECT statement for table '%@'\nto identify field origin unambiguously." = "Das Feld kann nicht bearbeitet werden, kein entsprechender Datensatz gefunden.\nDaten neu laden, Zeichensatz prüfen oder \nPrimärschlüssel oder weitere Felder\nim SELECT Ausdruck für Tabelle '%@' benutzen,\num den Speicherort des Feldes eindeutig zu bestimmen.";

/* Table Content result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload table, check the encoding, or try to add\na primary key field or more fields\nin the view declaration of '%@' to identify\nfield origin unambiguously." = "Das Feld kann nicht bearbeitet werden, kein entsprechender Datensatz gefunden.\nTabelle neu laden, Zeichensatz prüfen oder \nPrimärschlüssel oder weitere Felder\nin die Definition der Ansicht von %@ aufnehmen,\num das Feldes eindeutig zu bestimmen.";

/* error while reading data file */
"File couldn't be read." = "Datei nicht lesbar.";
"File couldn't be read: %@\n\nIt will be deleted." = "Datei nicht lesbar: %@\n\nund wird gelöscht.";

/* File read error title (Import Dialog) */
"File read error" = "Datei-Lesefehler";

/* All export files already exist explanatory text */
"Files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "Dateien mit denselben Namen im Zielordner vorhanden; Ersetzen überschreibt die bestehenden Inhalte.";

/* filter label */
"Filter" = "Filtern";

/* apply filter label */
"Apply Filter(s)" = "Filter anwenden";

/* filter tables menu item */
"Filter Tables" = "Tabellen filtern";

/* export source */
"Filtered table content" = "Gefilterter Tabellen-Inhalt";

/* filtering failed. please try again. tooltip */
"Filtering failed. Please try again." = "Filtern fehlgeschlagen; bitte erneut versuchen.";

/* Filtering table task description */
"Filtering table..." = "Tabelle wird gefiltert …";

/* description for finished exporting notification */
"Finished exporting to %@" = "Export nach %@ abgeschlossen";

/* description for finished importing notification */
"Finished importing %@" = "Import von %@ abgeschlossen";

/* FLUSH one or more tables - result title */
"Flush %@" = "%@ leeren";

/* flush selected items menu item */
"Flush Selected Items" = "Flush der ausgewählten Items";

/* flush table menu item */
"Flush Table" = "Flush der Tabelle";

/* flush table failed message */
"Flush table failed." = "Flush der Tabelle fehlgeschlagen.";

/* flush view menu item */
"Flush View" = "Flush der Ansicht";

/* title of panel when successfully flushed privs */
"Flushed Privileges" = "Flush der Zugriffsrechte erfolgreich";

/* For BIT fields only “1” or “0” are allowed. */
"For BIT fields only “1” or “0” are allowed." = "Bei BIT Feldern sind nur “1” oder “0” zulässig.";

/* force table deletion button text */
"Force delete (disables integrity checks)" = "Löschen erzwingen (Integritätsprüfung überspringen)";

/* full text index menu item title */
"FULLTEXT" = "VOLLTEXT";

/* function */
"function" = "Funktion";

/* header for function info pane */
"FUNCTION INFORMATION" = "FUNKTIONS-INFORMATION";

/* functions */
"functions" = "Funktionen";

/* Bundle Editor : Scope dropdown : 'general' item
 general menu item label
 general preference pane name */
"General" = "Allgemein";

/* general preference pane tooltip */
"General Preferences" = "Allgemeine Präferenzen";

/* Bundle Editor : Outline View : 'General' item : tooltip */
"General Scope\ncommands will run application-wide" = "Allgemeiner Bereich
Befehle werden anwendungsweit ausgeführt";

/* generating print document status message */
"Generating print document..." = "Druckdokument wird erzeugt…";

/* export header generation time label */
"Generation Time" = "Verarbeitungszeit";

/* Content Filter Manager : Filter Entry List: 'Global' Header
 Query Favorites : List : Section Heading : global query favorites */
"Global" = "Global";

/* Query Favorites : List : Section Heading : global : tooltip */
"Globally stored favorites" = "Global gespeicherte Favoriten";

/* Preferences : Notifications : status text : notification setup */
"Notifications are delivered via the Notification Center." = "Benachrichtigungen werden über das Notification Center übermittelt.";

/* Gzip compression export summary - within a sentence */
"Gzip compression" = "Gzip Komprimierung";

/* MySQL Help Viewer : Results list : Page title */
"Help topics for %@" = "Hilfethemen anzeigen";

/* hide console */
"Hide Console" = "Konsole verbergen";

/* hide navigator */
"Hide Navigator" = "Navigator verbergen";

/* hide tab bar */
"Hide Tab Bar" = "Reiter-Leiste verbergen";

/* Hide Toolbar menu item */
"Hide Toolbar" = "Werkzeugleiste verbergen";

/* export filename host token
 export header host label */
"Host" = "Host";

/* description of double */
"IEEE 754 double-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 Gleitkommawert mit doppelter Genauigkeit. M ist die maximale Anzahl von Ziffern, mit D Dezimalen. Hinweis: Viele Dezimalzahlen können nur durch Gleitkommawerte angenähert werden. DECIMAL benutzen, wenn genaue Ergebnisse benötigt werden.";

/* description of float */
"IEEE 754 single-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 Gleitkommawert mit einfacher Genauigkeit. M ist die maximale Anzahl von Ziffern, mit D Dezimalen. Hinweis: Viele Dezimalzahlen können nur durch Gleitkommawerte angenähert werden. DECIMAL verwenden, wenn genaue Ergebnisse benötigt werden.";

/* ignore button */
"Ignore" = "Übergehen";

/* ignore errors button */
"Ignore All Errors" = "Alle Fehler übergehen";

/* ignore all fields menu item */
"Ignore all Fields" = "Alle Felder übergehen";

/* ignore field label */
"Ignore field" = "Feld übergehen";

/* ignore field label */
"Ignore Field" = "Feld übergehen";

/* import button */
"Import" = "Importieren";

/* import all fields menu item */
"Import all Fields" = "Alle Felder importieren";

/* sql import : charset error alert : continue button */
"Import Anyway" = "Trotzdem importieren";

/* import cancelled message */
"Import cancelled!\n" = "Import abgebrochen!\n";

/* Import Error title */
"Import Error" = "Fehler beim Import";

/* import field operator tooltip */
"Import field" = "Feld importieren";

/* import file does not exist message */
"Import file does not exist." = "Import-Datei fehlt.";

/* Export file format cannot be imported warning */
"Import of the selected data is currently not supported." = "Import der ausgewählten Daten wird derzeit nicht unterstützt.";

/* SQL import progress text */
"Imported %@ of %@" = "%1$@ von %2$@ importiert";

/* CSV import progress text where total size is unknown */
"Imported %@ of CSV data" = "%@ CSV Daten importiert";

/* SQL import progress text where total size is unknown */
"Imported %@ of SQL" = "%@ SQL importiert";

/* text showing that the application is importing CSV */
"Importing CSV" = "CSV Import";

/* text showing that the application is importing SQL */
"Importing SQL" = "SQL Import";

/* Bundle Editor : BLOB dropdown : 'include BLOB' item */
"include BLOB" = "BLOB einbeziehen";

/* include content table column tooltip */
"Include content" = "Inhalt einbeziehen";

/* sql import error message */
"Incompatible encoding in SQL file" = "Inkompatibler Zeichensatz der SQL Datei";

/* header for blank info pane */
"INFORMATION" = "INFORMATIONEN";

/* New Table Sheet : Table Collation Dropdown : Default inherited from database
 New Table Sheet : Table Encoding Dropdown : Default inherited from database */
"Inherit from database (%@)" = "Von Datenbank (%@) übernehmen";

/* initializing export label */
"Initializing..." = "Initialisierung...";

/* Bundle Editor : Scope dropdown : 'input field' item
 input field menu item label */
"Input Field" = "Eingabefeld";

/* input field  doesn't support insertion of snippets. */
"Input Field doesn't support insertion of snippets." = "Eingabefeld lässt kein Einfügen von Snippets zu.";

/* input field is not editable. */
"Input Field is not editable." = "Eingabe-Feld nicht bearbeitbar.";

/* Bundle Editor : Outline View : 'Input Field' item : tooltip */
"Input Field Scope\ncommands will run on each text input field" = "Bereich Eingabefelder\nBefehle werden auf jedes Eingabefeld angewendet";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as snippet' item */
"Insert as Snippet" = "Als Snippet einfügen";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as text' item */
"Insert as Text" = "Als Text einfügen";

/* Bundle Editor : Outline View : 'BUNDLES' item : tooltip */
"Installed Bundles" = "Installierte Bundles";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog title */
"Installing Bundle" = "Bundle wird installiert";

/* insufficient details message */
"Insufficient connection details" = "Details zur Verbindung magelhaft";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please enter at least the hostname." = "Mangelhafte Verbindungsdetails. Wenigstens der Host-Name muss angegeben werden.";

/* insufficient SSH tunnel details informative message */
"Insufficient details provided to establish a connection. Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Mangelhafte Verbindungsdetails. Der Host-Name für den SSH-Tunnel muss angegeben oder der Tunnel deaktiviert werden.";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please provide at least a host." = "Mangelhafte Verbindungsdetails. Wenigstens ein Host muss angegeben werden.";

/* Interpret data as: */
"Interpret data as:" = "Interpretiere Daten als:";

/* Invalid database very short status message */
"Invalid database" = "Ungültige Datenbank";

/* export : import settings : file error title */
"Invalid file supplied!" = "Ungültige Datei!";

/* table content : editing : error message title when parsing as hex string failed */
"Invalid hexadecimal value" = "Ungültiger Hexadezimal-Wert";

/* is deterministic label (Navigator) */
"Is Deterministic" = "Ist deterministisch";

/* is nullable label (Navigator) */
"Is Nullable" = "Kann NULL sein";

/* is updatable: %@ */
"is updatable: %@" = "Neuere Version vorhanden: % @";

/* items */
"items" = "Items";

/* javascript exception */
"JavaScript Exception" = "JavaScript Exception";

/* javascript parsing error */
"JavaScript Parsing Error" = "JavaScript Parse-Fehler";

/* key label (Navigator) */
"Key" = "Schlüssel";

/* keyword label for color table (Prefs > Editor) */
"Keyword" = "Schlüsselwort";

/* kill button */
"Kill" = "Abbrechen";

/* kill connection message */
"Kill connection?" = "Verbindung abbrechen?";

/* kill query message */
"Kill query?" = "Query abbrechen?";

/* Last Error Message */
"Last Error Message" = "Letzte Fehlermeldung";

/* Last Used entry in favorites menu */
"Last Used" = "Zuletzt benutzt";

/* range for json type */
"Limited to @@max_allowed_packet" = "Auf @@max_allowed_packet beschränkt";

/* Loading table task string */
"Loading %@..." = "Lädt %@...";

/* Loading database task string */
"Loading database '%@'..." = "Lädt Datenbank '%@'...";

/* Loading history entry task desc */
"Loading history entry..." = "Lädt Protokoll-Eintrag …";

/* Loading table page task string */
"Loading page %lu..." = "Lädt Seite %lu...";

/* Loading referece task string */
"Loading reference..." = "Lädt Referenz …";

/* Loading table data string */
"Loading table data..." = "Lädt Tabellen-Daten …";

/* Low memory export summary */
"Low memory" = "Knapper Speicher";

/* range of decimal */
"M (precision): Up to 65 digits\nD (scale): 0 to 30 digits" = "M (Genauigkeit): Bis zu 65 Ziffern\nD (Dezimale): 0 bis 30 Stellen";

/* range for blob type
 range for varbinary type */
"M: %@ to %@ bytes" = "M: %1$@ bis %2$@ Bytes";

/* range for text type
 range for varchar type */
"M: %@ to %@ characters" = "M: %1$@ bis %2$@ Zeichen";

/* range for longtext type */
"M: %@ to %@ characters (4 GiB)" = "M: %1$@ bis %2$@ Zeichen (4 GiB)";

/* range for binary type */
"M: 0 to 255 bytes" = "M: 0 bis 255 Bytes";

/* range for char type */
"M: 0 to 255 characters" = "M: 0 bis 255 Zeichen";

/* range for bit type */
"M: 1 (default) to 64" = "M: 1 (Default) bis 64";

/* connection view : ssl : key file picker : wrong format error description */
"Make sure the file contains a RSA private key and is using PEM encoding." = "Sicherstellen, dass die Datei einen RSA Private Key in PEM Kodierung enthält.";

/* connection view : ssl : client cert picker : wrong format error description */
"Make sure the file contains a X.509 client certificate and is using PEM encoding." = "Sicherstellen, dass die Datei ein X.509 Client Zertifikat in PEM Kodierung enthält.";

/* match field menu item */
"Match Field" = "Spielfeld";

/* Shown when user inserts too many arguments (ContentFilterManager) */
"Maximum number of arguments is 2!" = "Nur zwei Argumente zulässig!";

/* Maximum text length is set to %ld. */
"Maximum text length is set to %ld." = "Maximale Textlänge ist %ld.";

/* Maximum text length is set to %ld. Inserted text was truncated. */
"Maximum text length is set to %ld. Inserted text was truncated." = "Der eingefügte Text wurde auf die zulässige Länge von %ld eingekürzt.";

/* Maximum text length is set to %llu. */
"Maximum text length is set to %llu." = "Maximale Textlänge ist %llu.";

/* Maximum text length is set to %llu. Inserted text was truncated. */
"Maximum text length is set to %llu. Inserted text was truncated." = "Der eingefügte Text wurde auf die zulässige Länge von %llu eingekürzt.";

/* message column title */
"Message" = "Nachricht";

/* mgtemplateengine error */
"MGTemplateEngine Error" = "Fehler in MGTemplateEngine";

/* export filename date token */
"Month" = "Monat";

/* multiple selection */
"multiple selection" = "Mehrfach-Auswahl";

/* MySQL connecting very short status message */
"MySQL connecting..." = "Verbinde MySQL ...";

/* mysql error message */
"MySQL Error" = "MySQL Fehler";

/* mysql help */
"MySQL Help" = "MySQL Hilfe";

/* MySQL Help for Selection */
"MySQL Help for Selection" = "MySQL Hilfe zur Auswahl";

/* MySQL Help for Word */
"MySQL Help for Word" = "MySQL Hilfe für Word";

/* mysql help categories */
"MySQL Help – Categories" = "MySQL Hilfe – Kategorien";

/* mysql said message */
"MySQL said:" = "MySQL Ergebnis:";

/* shutdown server : error dialog : message */
"MySQL said:\n%@" = "MySQL Ergebnis:\n%@";

/* message of panel when error while adding row to db */
"MySQL said:\n\n%@" = "MySQL Ergebnis:\n\n%@";

/* Preferences : Themes : Initial filename for 'Export' */
"MyTheme" = "MyTheme";

/* network preference pane name */
"Network" = "Netzwerk";

/* network preference pane tooltip */
"Network Preferences" = "Netzwerk-Vorgaben";

/* file preference pane name */
"Files" = "Dateien";

/* file preference pane tooltip */
"File Preferences" = "Datei-Vorgaben";

/* Bundle Editor : Default name for new bundle in the list on the left */
"New Bundle" = "Neues Bundle";

/* new column name placeholder string */
"New Column Name" = "Neuer Spaltenname";

/* new favorite name */
"New Favorite" = "Neuer Favorit";

/* Content Filter Manager : Initial name for new filter */
"New Filter" = "Neuer Filter";

/* new folder placeholder name */
"New Folder" = "Neuer Ordner";

/* Bundle Editor : Default name for a new bundle in the menu */
"New Name" = "Neuer Name";

/* new table menu item */
"New Table" = "NeueTable";

/* error that no color theme found */
"No color theme data found." = "Keine Daten zum Farbthema gefunden.";

/* No compression export summary - within a sentence */
"no compression" = "Ohne Komprimierung";

/* no connection available message */
"No connection available" = "Keine Verbindung verfügbar";

/* no connection data found */
"No connection data found." = "Keine Verbindungsdaten.";

/* No content filters were found in file to import (ContentFilterManager) */
"No content filters found." = "Keine Inhaltsfilter.";

/* no data found */
"No data found." = "Keine Daten.";

/* No errors title */
"No errors" = "Fehlerfrei";

/* No favorites entry in favorites menu */
"No Favorties" = "Keine Favoriten";

/* All export files creation error title */
"No files could be created" = "Keine Dateien angelegt";

/* no item found message */
"No item found" = "Keine Einträge gefunden";

/* SSH tunnel could not be created because no local port could be allocated */
"No local port could be allocated for the SSH Tunnel." = "Der lokale Port für den SSH Tunnel konnte nicht geöffnet werden.";

/* header for no matches in filtered list */
"NO MATCHES" = "KEINE TREFFER";

/* no optimized field type found. message */
"No optimized field type found." = "Kein optimierter Feldtyp.";

/* error that no query favorites found */
"No query favorites found." = "Keine Query-Favoriten.";

/* Mysql Help Viewer : Search : No results */
"No results found." = "Ohne Ergebnis.";

/* Bundle Editor : Fallback Input source dropdown : 'none' item
 Bundle Editor : Scope=? : ? dropdown: 'None' item
 Bundle Editor : Scope=Data-Table : Input source dropdown: 'none' item
 Bundle Editor : Scope=Data-Table : Output dropdown : 'none' item
 Bundle Editor : Scope=Data-Table : Trigger dropdown : 'none' item
 Bundle Editor : Scope=Field : Input source dropdown: 'None' item
 Bundle Editor : Scope=Field : Output dropdown : 'none' item
 Bundle Editor : Scope=Field : Trigger dropdown : 'none' item
 Bundle Editor : Scope=General : Input source dropdown: 'None' item
 Bundle Editor : Scope=General : Output dropdown : 'none' item
 Bundle Editor : Scope=General : Trigger dropdown : 'none' item
 compression: none */
"None" = "Kein";

/* not available label */
"Not available" = "Nicht verfügbar";

/* Argument count (ContentFilterManager) */
"Number of arguments: %lu" = "Anzahl der Argumente: %lu";

/* numeric label for color table (Prefs > Editor) */
"Numeric" = "Numerisch";

/* Bundle Editor : Copy-Command-Error : OK button
 Bundle Editor : Save-and-Close-Error : OK button
 Bundle Editor : Save-Bundle-Error : OK button
 Bundle Editor : Trash-Bundle(s)-Error : OK button
 OK
 OK button
 Open Files : Bundle : Already-Installed : Delete-Old-Error : OK button
 Open Files : Bundle : Install-Error : OK button
 Open Files : Bundle : UUID : OK button
 Preferences : Network : Custom SSH client : warning dialog : accept button */
"OK" = "Okay";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+1 rows instead of n selected were deleted. */
"One additional row was removed!" = "Eine zusätzliche Zeile wurde entfernt!";

/* Table Content : Remove Row : Result : Too Few : Part 1 : Only n-1 of n selected rows were deleted. */
"One row was not removed." = "Eine Zeile wurde nicht entfernt.";

/* Only one dragged item allowed. */
"Only one dragged item allowed." = "Nur ein Item darf gezogen werden.";

/* partial copy database support message */
"Only Partially Supported" = "Nur teilweise unterstützt.";

/* open function in new table title */
"Open Function in New Tab" = "Funktion in neuem Reiter öffnen";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Function in New Window" = "Funktion in neuem Fenster öffnen";

/* open connection in new tab context menu item */
"Open in New Tab" = "In neuem Reiter öffnen";

/* menu item open in new window */
"Open in New Window" = "In neuem Fenster öffnen";

/* open procedure in new table title */
"Open Procedure in New Tab" = "Prozedur in neuem Reiter öffnen";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Procedure in New Window" = "Prozedur in neuem Fenster öffnen";

/* open table in new tab title
 open table in new table title */
"Open Table in New Tab" = "Tabelle in neuem Reiter öffnen";

/* Table List : Context Menu : Duplicate connection to new window
 Table List : Gear Menu : Duplicate connection to new window */
"Open Table in New Window" = "Tabelle in neuem Fenster öffnen";

/* open view in new tab title
 open view in new table title */
"Open View in New Tab" = "Ansicht in neuem Reiter öffnen";

/* Tables List : Context Menu : Duplicate connection to new window
 Tables List : Gear Menu : Duplicate connection to new window */
"Open View in New Window" = "Ansicht in neuem Fenster öffnen";

/* menu item open %@ in new window */
"Open %@ in New Window" = "In neuem Fenster öffnen";

/* OPTIMIZE one or more tables - result title */
"Optimize %@" = "%@ optimieren";

/* optimize selected items menu item */
"Optimize Selected Items" = "Gewählte Items optimieren";

/* optimize table menu item */
"Optimize Table" = "Tabelle optimieren";

/* optimize table failed message */
"Optimize table failed." = "Tabellen-Optimierung fehlgeschlagen.";

/* Optimized type for field %@ */
"Optimized type for field '%@'" = "Feld-Typ für '%@' optimiert";

/* optional placeholder string */
"optional" = "optional";

/* Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed. */
"Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed." = "Die Verarbeitung des übergebenen Parameters schlug fehl. Nur Zeichenketten oder Arrays (mit 2 Elementen) sind zulässig.";

/* Permission Denied */
"Permission Denied" = "Zugriff verwehrt";

/* please choose a favorite connection view label */
"Please choose a favorite" = "Favoriten wählen";

/* message of panel when ssh details are incomplete */
"Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Host-Name für den SSH Tunnel angeben, oder den SSH-Tunnel abschalten.";

/* Please enter the password */
"Please enter the password for ‘%@’:" = "Passwort für ‘%@’ angeben:";

/* print button */
"Print" = "Drucken";

/* print page menu item title */
"Print Page…" = "Seite drucken …";

/* privileges label (Navigator) */
"Privileges" = "Zugriffsrechte";

/* procedure */
"procedure" = "Prozedur";

/* header for procedure info pane */
"PROCEDURE INFORMATION" = "PROZEDUR-INFORMATION";

/* procedures */
"procedures" = "Prozeduren";

/* proceed button */
"Proceed" = "Fortsetzen";

/* header for procs & funcs list */
"PROCS & FUNCS" = "PROZ. & FUNKT.";

/* toolbar item label for switching to the Run Query tab */
"Query" = "Query";

/* query background label for color table (Prefs > Editor) */
"Query Background" = "Query Hintergrund";

/* Query cancelled error */
"Query cancelled." = "Query abgebrochen.";

/* Query cancel by resetting connection error */
"Query cancelled.  Please note that to cancel the query the connection had to be reset; transactions and connection variables were reset." = "Query abgebrochen. Um die Query abzubrechen, mussten die Verbindung, Verbindungs-Variable und Transaktionen zurückgesetzt werden.";

/* query editor preference pane name */
"Query Editor" = "Query-Editor";

/* query editor preference pane tooltip */
"Query Editor Preferences" = "Query Editor Einstellungen";

/* query logging currently disabled label
 query logging disabled label */
"Query logging is currently disabled" = "Query Protokollierung derzeit abgeschaltet";

/* query result print heading */
"Query Result" = "Query Ergebnis";

/* export source */
"Query results" = "Query Ergebnisse";

/* Query Status */
"Query Status" = "Query-Status";

/* table status : row count query failed : error title */
"Querying row count failed" = "Abfrage der Zeilenzahl schlug fehl";

/* Quick connect item label */
"Quick Connect" = "Schnell-Verbindung";

/* quote label for color table (Prefs > Editor) */
"Quote" = "Zitat";

/* databsse rename unsupported informative message */
"Renaming the database '%@' is currently unsupported as it contains objects other than tables (i.e. views, procedures, functions, etc.).\n\nIf you would like to rename a database please use the 'Duplicate Database', move any non-table objects manually then drop the old database." = "Die Datenbank '%@' kann derzeit nicht umbenannt werden. Sie enthält andere Objekte als Tabellen (Ansichten, Prozeduren, Funktionen, etc.).\n\nZum Umbenennen zuerst ein Duplikat der Datenbank erstellen, alle Objekte außer Tabellen verschieben und danach die alte Datenbank löschen.";

/* range for serial type */
"Range: %@ to %@" = "Bereich: %1$@ bis %2$@";

/* range for time type */
"Range: -838:59:59.0 to 838:59:59.0\nF (precision): 0 (1s) to 6 (1µs)" = "Bereich: -838:59:59.0 to 838:59:59.0\nF (Genauigkeit): 0 (1s) bis 6 (1µs)";

/* range for year type */
"Range: 0000, 1901 to 2155" = "Bereich: 0000, 1901 bis 2155";

/* range for set type */
"Range: 1 to 64 members\n1, 2, 3, 4 or 8 bytes storage" = "Bereich: 1 bis 64 Einträge\n1, 2, 3, 4 oder 8 Bytes Speicher";

/* range for datetime type */
"Range: 1000-01-01 00:00:00.0 to 9999-12-31 23:59:59.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Bereich: 1000-01-01 00:00:00.0 bis 9999-12-31 23:59:59.999999\nF (Genauigkeit): 0 (1s) bis 6 (1µs)";

/* range for date type */
"Range: 1000-01-01 to 9999-12-31" = "Bereich: 1000-01-01 bis 9999-12-31";

/* range for timestamp type */
"Range: 1970-01-01 00:00:01.0 to 2038-01-19 03:14:07.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Bereich: 1970-01-01 00:00:01.0 bis 2038-01-19 03:14:07.999999\nF (Genauigkeit): 0 (1s) bis 6 (1µs)";

/* text showing that app is reading dump */
"Reading..." = "Lese …";

/* menu item to refresh databases */
"Refresh Databases" = "Datenbanken auffrischen";

/* refresh list menu item */
"Refresh List" = "Liste auffrischen";

/* toolbar item label for switching to the Table Relations tab */
"Relations" = "Relationen";

/* Relations tab subtitle showing table name */
"Relations for table: %@" = "Relationen der Tabelle: %@";

/* reload bundles menu item label */
"Reload Bundles" = "Bundles erneut laden";

/* Reloading data task description */
"Reloading data..." = "Daten werden erneut geladen …";

/* Reloading table task string */
"Reloading..." = "Erneut laden …";

/* remote error */
"Remote Error" = "Remote-Fehler";

/* Bundle Editor : Remove-Bundle: remove button
 remove button */
"Remove" = "Entfernen";

/* remove all button */
"Remove All" = "Alle entfernen";

/* remove all query favorites message */
"Remove all query favorites?" = "Alle Query Favoriten entfernen?";

/* Bundle Editor : Remove-Bundle: remove dialog title */
"Remove selected Bundle?" = "Gewähltes Bundle entfernen?";

/* remove selected content filters message */
"Remove selected content filters?" = "Gewählte Inhalts-Filter entfernen?";

/* remove selected query favorites message */
"Remove selected query favorites?" = "Gewählte Query Favoriten entfernen?";

/* removing field task status message */
"Removing field..." = "Feld wird entfernt …";

/* removing index task status message */
"Removing index..." = "Index wird entfernt …";

/* rename database message */
"Rename database '%@' to:" = "Datenbank '%@' umbenennen auf:";

/* rename func menu title */
"Rename Function..." = "Funktion umbenennen …";

/* rename proc menu title */
"Rename Procedure..." = "Prozedur umbenennen …";

/* rename table menu title */
"Rename Table..." = "Tabelle umbenennen …";

/* rename view menu title */
"Rename View..." = "Ansicht umbenennen …";

/* REPAIR one or more tables - result title */
"Repair %@" = "%@ reparieren";

/* repair selected items menu item */
"Repair Selected Items" = "Gewählte Items reparieren";

/* repair table menu item */
"Repair Table" = "Tabelle reparieren";

/* repair table failed message */
"Repair table failed." = "Tabellen-Reparatur schlug fehl.";

/* Replace button */
"Replace" = "Ersetzen";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace entire content' item */
"Replace Entire Content" = "Gesamten Inhalt ersetzen";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace selection' item */
"Replace Selection" = "Auswahl ersetzen";

/* description of year */
"Represents a 4 digit year value, stored as 1 byte. Invalid values are converted to 0000 and two digit values 0 to 69 will be converted to years 2000 to 2069, resp. values 70 to 99 to years 1970 to 1999.\nThe YEAR(2) type was removed in MySQL 5.7.5." = "Eine 4-stellige Jahreszahl, als 1 Byte gespeichert. Ungültige Werte werden zu 0000, 2-stellige Werte 0 to 69 zu den Jahreszahlen 2000 to 2069, die Werte 70 bis 99 zu den Jahren 1970 bis 1999 gewandelt.\nDer YEAR(2) Type wurde in MySQL 5.7.5 entfernt.";

/* description of multilinestring */
"Represents a collection of LineStrings." = "Eine Sammlung von Polylinien.";

/* description of geometrycollection */
"Represents a collection of objects of any other single- or multi-valued spatial type. The only restriction being, that all objects must share a common coordinate system." = "Eine Sammlung von Objekten mit ein- oder mehrfachen räumlichen Werten. Alle Objekte müssen das selbe Koordinatensystem benutzen.";

/* description of multipolygon */
"Represents a collection of Polygons. The Polygons making up the MultiPolygon must not intersect." = "Eine Sammlung von Polygonen. Polygone, die das Multi-Polygon darstellen, dürfen sich nicht überschneiden.";

/* description of multipoint */
"Represents a set of Points without specifying any kind of relation and/or order between them." = "Eine Sammlung von Punkten, die zueinander keine spezifische Beziehung oder Ordnung aufweisen.";

/* description of point */
"Represents a single location in coordinate space using X and Y coordinates. The point is zero-dimensional." = "Ein einzelner, dimensionsloser Punkt einer Ebene mit x- und y-Koordinaten.";

/* description of linestring */
"Represents an ordered set of coordinates where each consecutive pair of two points is connected by a straight line." = "Eine geordnete Sammlung von Koordinaten, wobei aufeinander folgende Punkte durch eine Gerade verbunden werden.";

/* required placeholder string */
"required" = "erforderlich";

/* description of smallint */
"Requires 2 bytes storage space. M is the optional display width and does not affect the possible value range." = "Erfordert 2 Bytes Speicherplatz. M ist die fakultative Darstellungsbreite ohne Einfluss auf den möglichen Wertebereich.";

/* description of mediumint */
"Requires 3 bytes storage space. M is the optional display width and does not affect the possible value range." = "Erfordert 3 Bytes Speicherplatz. M ist die fakultative Darstellungsbreite ohne Einfluss auf den möglichen Wertebereich.";

/* description of int */
"Requires 4 bytes storage space. M is the optional display width and does not affect the possible value range. INTEGER is an alias to this type." = "Erfordert 4 Bytes Speicherplatz. M ist die fakultative Darstellungsbreite ohne Einfluss auf den möglichen Wertebereich. INTEGER ist ein Alias dieses Typs.";

/* description of bigint */
"Requires 8 bytes storage space. M is the optional display width and does not affect the possible value range. Note: Arithmetic operations might fail for large numbers." = "Erfordert 8 Bytes Speicherplatz. M ist die fakultative Darstellungsbreite ohne Einfluss auf den möglichen Wertebereich. Hinweis: Arithmetische Operationen könnten bei großen Zahlen fehlschlagen.";

/* reset auto_increment after deletion of all rows message */
"Reset AUTO_INCREMENT after deletion\n(only for Delete ALL ROWS IN TABLE)?" = "Nach dem Löschen AUTO_INCREMENT zurücksetzen\n(nur für Löschen ALLER ZEILEN IN DER TABELLE)?";

/* delete selected row button */
"Delete Selected Row" = "Ausgewählte Zeile löschen";

/* delete selected rows button */
"Delete Selected Rows" = "Ausgewählte Zeilen löschen";

/* delete all rows in table button */
"Delete ALL ROWS IN TABLE" = "Lösche ALLE ZEILEN IN DER TABELLE";

/* Restoring session task description */
"Restoring session..." = "Sitzung wiederherstellen …";

/* return type label (Navigator) */
"Return Type" = "Rückgabewert";

/* return type: %@ */
"return type: %@" = "Rückgabewert: %@";

/* singular word for row */
"row" = "Zeile";

/* plural word for rows */
"rows" = "Zeilen";

/* text showing how many rows are in the limited filter match */
"Rows %@ - %@ from filtered matches" = "Zeilen %1$@ - %2$@ gefilterter Treffer";

/* text showing how many rows are in the limited result */
"Rows %@ - %@ of %@%@ from table" = "Zeilen %1$@ - %2$@ aus %3$@%4$@ der Tabelle";

/* Table Info Section : number of rows (exact value) */
"rows: %@" = "Zeilen: %@";

/* Table Info Section : number of rows (estimated value) */
"rows: ~%@" = "Zeilen: ~%@";

/* run all button */
"Run All" = "Alle ausführen";

/* Run All menu item title */
"Run All Queries" = "Alle Abfragen ausführen";

/* Title of button to run current query in custom query view */
"Run Current" = "Aktuelle ausführen";

/* Title of action menu item to run current query in custom query view */
"Run Current Query" = "Aktuelle Query ausführen";

/* toolbar item label for switching to the Run Query tab */
"Run Custom Query" = "Eigene Query ausführen";

/* Title of button to run query just before text caret in custom query view */
"Run Previous" = "Vorige ausführen";

/* Title of action menu item to run query just before text caret in custom query view */
"Run Previous Query" = "Vorige Query ausführen";

/* Title of action menu item to run selected text in custom query view */
"Run Selected Text" = "Ausgewählten Text ausführen";

/* Title of button to run selected text in custom query view */
"Run Selection" = "Auswahl ausführen";

/* Running multiple queries string */
"Running query %i of %lu..." = "Query %1$i von %2$lu läuft …";

/* Running multiple queries string */
"Running query %ld of %lu..." = "Query %1$ld von %2$lu läuft …";

/* Running single query string */
"Running query..." = "Query läuft …";

/* Save trigger button label */
"Save" = "Speichern";

/* Save All to Favorites */
"Save All to Favorites" = "Alle als Favoriten speichern";

/* save as button title */
"Save As..." = "Speichern unter …";

/* Bundle Editor : BLOB dropdown : 'save BLOB as dat file' item */
"save BLOB as dat file" = "BLOB als DAT Datei speichern";

/* Bundle Editor : BLOB dropdown : 'save BLOB as image file' item */
"save BLOB as image file" = "BLOB als Bilddatei speichern";

/* Save Current Query to Favorites */
"Save Current Query to Favorites" = "Aktuelle Query als Favorit speichern";

/* save page as menu item title */
"Save Page As…" = "Seite speichern als …";

/* Save Queries… */
"Save Queries…" = "Abfragen speichern…";

/* Save Query… */
"Save Query…" = "Query speichern…";

/* Save Selection to Favorites */
"Save Selection to Favorites" = "Auswahl als Favorit speichern";

/* save view as button title */
"Save View As..." = "Ansicht speichern als …";

/* schema path header for completion tooltip */
"Schema path:" = "Schema Pfad:";

/* Search in MySQL Documentation */
"Search in MySQL Documentation" = "In MySQL Dokumentation suchen";

/* Search in MySQL Help */
"Search in MySQL Help" = "In MySQL Hilfe suchen";

/* Select Active Query */
"Select Active Query" = "Aktive Query wählen";

/* toolbar item for selecting a db */
"Select Database" = "Datenbank wählen";

/* selected items */
"selected items" = "Ausgewählte Items";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as comma-separated' item */
"Selected Rows (CSV)" = "Ausgewählte Zeilen (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as SQL' item */
"Selected Rows (SQL)" = "Ausgewählte Zeilen (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as tab-separated' item */
"Selected Rows (TSV)" = "Ausgewählte Zeilen (TSV)";

/* Bundle Editor : Scope=Field : Input source dropdown: 'selected text' item */
"Selected Text" = "Ausgewählter Text";

/* selection label for color table (Prefs > Editor) */
"Selection" = "Auswahl";

/* table structure : indexes : delete index : no columns error : description */
"Sequel Ace could not find any columns belonging to this index. Maybe it has been removed already?" = "Sequel Ace findet keine Spalten zu diesem Index. Eventuell wurden sie bereits entfernt?";

/* Preferences : Network : Custom SSH client : warning dialog message */
"Sequel Ace only supports and is tested with the default OpenSSH client versions included with macOS. Using different clients might cause connection issues, security risks or not work at all.\n\nPlease be aware, that we cannot provide support for such configurations." = "Sequel Ace unterstützt und wird nur mit den standardmäßigen OpenSSH-Client-Versionen getestet, die in macOS enthalten sind. Die Verwendung unterschiedlicher Clients kann zu Verbindungsproblemen oder Sicherheitsrisiken führen oder gar nicht funktionieren.\n\nBitte beachten Sie, dass wir für solche Konfigurationen keinen Support leisten können.";

/* sequelace URL scheme command not supported. */
"sequelace URL scheme command not supported." = "Sequelace URL Schema-Befehl wird nicht unterstützt.";

/* sequelace url Scheme Error */
"sequelace URL Scheme Error" = "Sequelace URL Schema Fehler";

/* Table Structure : Collation dropdown : 'item is the same as the collation of server' marker
 Table Structure : Encoding dropdown : 'item is server default' marker */
"Server" = "Server";

/* Add Database : Charset dropdown : default item ($1 = charset name)
 Add Database : Collation dropdown : default item ($1 = collation name) */
"Server Default (%@)" = "Server Default (%@)";

/* server processes window title (var = hostname) */
"Server Processes on %@" = "Server Prozesse auf %@";

/* Initial filename for 'Save session' file */
"Session" = "Sitzung";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html' item */
"Show as HTML" = "Als HTML darstellen";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html tooltip' item */
"Show as HTML Tooltip" = "Als HTML Tooltip darstellen";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as text tooltip' item */
"Show as Text Tooltip" = "Als Text Tooltip darstellen";

/* show console */
"Show Console" = "Konsole anzeigen";

/* show create func syntax menu item */
"Show Create Function Syntax..." = "Zeige die Syntax zu Create Function …";

/* show create proc syntax menu item */
"Show Create Procedure Syntax..." = "Zeige die Syntax zu Create Procedure ...";

/* show create syntaxes menu item */
"Show Create Syntaxes..." = "Zeige die Syntax zu Create ...";

/* show create table syntax menu item */
"Show Create Table Syntax..." = "Zeige die Syntax zu Create Table ...";

/* show create view syntax menu item */
"Show Create View Syntax..." = "Zeige die Syntax zu Create View ...";

/* Show detail button */
"Show Detail" = "Detail anzeigen";

/* MySQL Help Viewer : Results list : Link tooltip */
"Show MySQL help for %@" = "MySQL-Hilfe für % anzeigen @";

/* show navigator */
"Show Navigator" = "Navigator anzeigen";

/* show tab bar */
"Show Tab Bar" = "Tab-Leiste anzeigen";

/* tooltip for toolbar item for show console */
"Show the console which shows all MySQL commands performed by Sequel Ace" = "Die Konsole mit allen MySQL Befehlen, die von Sequel Ace ausgeführt wurden, anzeigen";

/* Show Toolbar menu item */
"Show Toolbar" = "Werkzeugleiste anzeigen";

/* filtered item count */
"Showing %lu of %lu processes" = "%1$lu von %2$lu Prozessen";

/* shutdown server : confirmation dialog : shutdown button */
"Shutdown" = "Herunterfahren";

/* shutdown server : error dialog : title */
"Shutdown failed!" = "Shutdown fehlgeschlagen!";

/* range of integer types */
"Signed: %@ to %@\nUnsigned: %@ to %@" = "Signiert: %1 - bis %2 - nicht signiert: %3- bis %4$ @";

/* Table Info Section : table size on disk */
"size: %@" = "Größe: %@";

/* skip existing button */
"Skip existing" = "Vorhandene überspringen";

/* skip problems button */
"Skip problems" = "Probleme überspringen";

/* beta build label */
"Beta Build" = "Beta-Build";

/* socket connection failed title */
"Socket connection failed!" = "Socket Verbindung fehlgeschlagen!";

/* socket not found title */
"Socket not found!" = "Socket nicht gefunden!";

/* Some export folders not writable explanatory text */
"Some of the target export folders are not writable.  Please select a new export location and try again." = "Einige Zielordner sind schreibgeschützt. Ein anderes Ziel für den Export wählen und erneut versuchen.";

/* Some export folders missing explanatory text */
"Some of the target export folders no longer exist.  Please select a new export location and try again." = "Einige Zielordner sind nicht mehr vorhanden. Ein anderes Ziel für den Export wählen und erneut versuchen.";

/* Sorting table task description */
"Sorting table..." = "Tabelle wird sortiert …";

/* spatial index menu item title */
"SPATIAL" = "RÄUMLICH";

/* sql data access label (Navigator) */
"SQL Data Access" = "SQL Datenzugriff";


/* Connection Secured via SSL information text */
"Connection Secured via SSL" = "Gesicherte Verbindung via SSL";

/* SSH connected titlebar marker */
"SSH Connected" = "SSH verbunden";

/* SSH connecting very short status message */
"SSH connecting..." = "Verbinde SSH …";

/* SSH connecting titlebar marker */
"SSH Connecting…" = "Verbinde SSH …";

/* SSH connection failed title */
"SSH connection failed!" = "SSH Verbindung fehlgeschlagen!";

/* SSH disconnected titlebar marker */
"SSH Disconnected" = "SSH beendet";

/* SSH key check error */
"SSH Key not found" = "SSH Schlüssel nicht gefunden";

/* title when ssh tunnel port forwarding failed */
"SSH port forwarding failed" = "SSH Port Forwarding fehlgeschlagen";

/* SSL certificate authority file check error */
"SSL Certificate Authority File not found" = "Die Datei des SSL Zertifikat-Ausstellers fehlt";

/* SSL certificate file check error */
"SSL Certificate File not found" = "SSL Zertifikats-Datei fehlt";

/* SSL requested but not used title */
"SSL connection not established" = "Keine SSL Verbindung";

/* SSL key file check error */
"SSL Key File not found" = "Die Datei mit dem SSL Schlüssel fehlt";

/* Standard memory export summary */
"Standard memory" = "Basisspeicher";

/* started */
"started" = "gestartet";

/* status file for sequelace url scheme command couldn't be written error message */
"Status file for sequelace url scheme command couldn't be written!" = "Statusdatei für Sequelace URL Schema Befehl konnte nicht geschrieben werden!";

/* stop button */
"Stop" = "Anhalten";

/* Stop queries string */
"Stop queries" = "Abfragen beenden";

/* Stop query string */
"Stop query" = "Abfrage beenden";

/* description of timestamp */
"Stores a date and time of day as seconds since the beginning of the UNIX epoch (1970-01-01 00:00:00). The values displayed/stored are affected by the connection's @@time_zone setting.\nThe representation is the same as for DATETIME. Invalid values, as well as \"second zero\", are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F. Some additional rules may apply." = "Speichert ein Datum und eine Uhrzeit als Sekunden seit Beginn der UNIX-Epoche (1970-01-01 00:00:00). Die angezeigten/gespeicherten Werte werden durch die @@time_zone Einstellung der Verbindung beeinflusst. Ungültige Werte sowie \"zweite Null\" werden in 0000-00-00 00:00:00.0 konvertiert. Bruchsekunden wurden in MySQL 5.6.4 mit einer Genauigkeit bis zu Mikrosekunden (6) hinzugefügt, die durch F angegeben wurden. Möglicherweise gelten zusätzliche Regeln.";

/* description of datetime */
"Stores a date and time of day. The representation is YYYY-MM-DD HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Speichert ein Datum und eine Uhrzeit. Die Darstellung ist YYYY-MM-DD HH:MM:SS[. I*], wobei I Bruchteile von Sekunden sind. Der Wert ist von der eingestellten Zeitzone unbeeinflusst. Ungültige Werte werden in 0000-00-00 00:00:00.0 konvertiert. Sekundenbruchteile wurden in MySQL 5.6.4 mit einer Genauigkeit bis zu Mikrosekunden (6) hinzugefügt, die durch F angegeben wurden.";

/* description of date */
"Stores a date without time information. The representation is YYYY-MM-DD. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00." = "Speichert ein Datum ohne Zeitangaben. Die Darstellung ist YYYY-MM-DD. Der Wert wird von der eingestellten Zeitzonen nicht beeinflusst. Ungültige Werte werden in 0000-00-00 konvertiert.";

/* description of time */
"Stores a time of day, duration or time interval. The representation is HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 00:00:00. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Speichert eine Tageszeit, Dauer oder ein Zeitintervall. Die Darstellung ist HH:MM:SS[. I*], wobei I Bruchteile von Sekunden darstellt. Der Wert wird von keiner Zeitzoneneinstellung beeinflusst. Ungültige Werte werden in 00:00:00 konvertiert. Sekundenbruchteile wurden in MySQL 5.6.4 mit einer Genauigkeit bis zu Mikrosekunden (6) hinzugefügt, die durch F angegeben werden.";

/* toolbar item label for switching to the Table Structure tab */
"Structure" = "Struktur";

/* successfully analyzed all selected items message */
"Successfully analyzed all selected items." = "Ausgewählte Elemente erfolgreich analysiert.";

/* analyze table successfully passed message */
"Successfully analyzed table." = "Tabelle erfolgreich analysiert.";

/* successfully flushed all selected items message */
"Successfully flushed all selected items." = "Die ausgewählten Elemente wurden geleert.";

/* message of panel when successfully flushed privs */
"Successfully flushed privileges." = "Berechtigungen geleert.";

/* flush table successfully passed message */
"Successfully flushed table." = "Tabelle geleert.";

/* successfully optimized all selected items message */
"Successfully optimized all selected items." = "Die ausgewählten Items wurden optimiert.";

/* optimize table successfully passed message */
"Successfully optimized table." = "Tabelle optimiert.";

/* successfully repaired all selected items message */
"Successfully repaired all selected items." = "Alle ausgewählten Items wurden erfolgreich repariert.";

/* repair table successfully passed message */
"Successfully repaired table." = "Tabelle erfolgreich repariert.";

/* tooltip for toolbar item for switching to the Run Query tab */
"Switch to the Run Query tab" = "Zum Reiter „Abfrage ausführen“ wechseln";

/* tooltip for toolbar item for switching to the Table Content tab */
"Switch to the Table Content tab" = "Zum Reiter „Tabelleninhalt“ wechseln";

/* tooltip for toolbar item for switching to the Table Info tab */
"Switch to the Table Info tab" = "Zum Reiter „Tabelleninfo“ wechseln";

/* tooltip for toolbar item for switching to the Table Relations tab */
"Switch to the Table Relations tab" = "Zum Reiter „Tabellenbeziehungen“ wechseln";

/* tooltip for toolbar item for switching to the Table Structure tab */
"Switch to the Table Structure tab" = "Zum Reiter „Tabellenstruktur“ wechseln";

/* tooltip for toolbar item for switching to the Table Triggers tab */
"Switch to the Table Triggers tab" = "Zum Reiter „Tabellen-Trigger“ wechseln";

/* tooltip for toolbar item for switching to the User Manager tab */
"Switch to the User Manager tab" = "Zum Reiter „Benutzer Verwaltung“ wechseln";

/* description for table syntax copied notification */
"Syntax for %@ table copied" = "Syntax für die Tabelle \"%\" kopiert";

/* table */
"table" = "Tabelle";

/* csv export table heading
 table
 Table Structure : Collation dropdown : 'item is the same as the collation of table' marker
 Table Structure : Encoding dropdown : 'item is table default' marker */
"Table" = "Tabelle";

/* export label showing that the app is fetching data for a specific table */
"Table %lu of %lu (%@): Fetching data..." = "Tabelle %1$lu von %2$lu (%3) : Abrufen von Daten...";

/* export label showing app is fetching relations data for a specific table */
"Table %lu of %lu (%@): Fetching relations data..." = "Tabelle %1$lu von %2$lu (%3) : Abrufen von Relationen …";

/* export label showing app if writing data for a specific table */
"Table %lu of %lu (%@): Writing data..." = "Tabelle %1$lu von %2$lu (%3) : Daten schreiben...";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'table changed' item */
"Table changed" = "Tabelle geändert";

/* table checksum message */
"Table checksum" = "Tabellenprüfsumme";

/* table checksum: %@ */
"Table checksum: %@" = "Tabelle Prüfsumme: % @";

/* table content print heading */
"Table Content" = "Tabellen-Inhalte";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as comma-separated' item */
"Table Content (CSV)" = "Tabelleninhalt (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as SQL' item */
"Table Content (SQL)" = "Tabelleninhalt (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as tab-separated' item */
"Table Content (TSV)" = "Tabelleninhalt (TSV)";

/* toolbar item for navigation history */
"Table History" = "Tabellenverlauf";

/* toolbar item label for switching to the Table Info tab */
"Table Info" = "Tabelleninfo";

/* header for table info pane */
"TABLE INFORMATION" = "TABELLENINFORMATIONEN";

/* table information print heading */
"Table Information" = "Tabelleninformationen";

/* message of panel when no name is given for table */
"Table must have a name." = "Die Tabelle muss einen Namen haben.";

/* general preference pane tooltip */
"Table Preferences" = "Tabelleneinstellungen";

/* toolbar item label for switching to the Table Relations tab */
"Table Relations" = "Tabellenbeziehungen";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table row changed' item */
"Table Row changed" = "Tabellenzeile geändert";

/* table structure print heading */
"Table Structure" = "Tabellen-Struktur bearbeiten";

/* toolbar item label for switching to the Table Triggers tab */
"Table Triggers" = "Tabellen-Trigger";

/* Add Relation sheet title, showing table name */
"Table: %@" = "Tabelle: % @";

/* tables preference pane name */
"Tables" = "Tabellen";

/* tables */
"tables" = "Tabellen";

/* header for table list */
"TABLES" = "TABELLEN";

/* header for table & views list */
"TABLES & VIEWS" = "TABELLEN & ANSICHTEN";

/* Connection test very short status message */
"Testing connection..." = "Verbindung wird getestet...";

/* MySQL connection test very short status message */
"Testing MySQL..." = "MySQL wird geprüft… ";

/* SSH testing very short status message */
"Testing SSH..." = "SSH wird geprüft…";

/* text label for color table (Prefs > Editor) */
"Text" = "Text";

/* Text is too long. Maximum text length is set to %llu. */
"Text is too long. Maximum text length is set to %llu." = "Der Text ist zu lang. Die maximale Textlänge beträgt %llu.";

/* Release notes dialog title thanking user for upgrade */
"Thanks for updating Sequel Ace!" = "Vielen Dank für die Aktualisierung von Sequel Ace!";

/* Open Files : Bundle : Install-Error : Destination path already exists error dialog message */
"The Bundle ‘%@’ already exists." = "Das Bundle '%' ist bereits vorhanden.";

/* Open Files : Bundle: UUID : UUID-Attribute is missing in bundle's command.plist file */
"The Bundle ‘%@’ has no UUID which is necessary to identify installed Bundles." = "Das Bundle '%' hat keine UUID, die zum Identifizieren installierter Bundles erforderlich ist.";

"‘%@’ Bundle contains legacy components" = "'%'-Bundle enthält ältere Komponenten";

"In these files:\n\n%@\n\nDo you still want to install the bundle?" = "In diesen Dateien:'n'n%'n'n'nMöchten Sie das Paket trotzdem installieren?";

/* message while reading a spf file which matches non-supported formats. */
"The chosen file %@ contains ‘%@’ data." = "Die ausgewählte Datei \"%1\" enthält '%2''-Daten.";

/* the color theme ‘%@’ already exists. */
"The color theme ‘%@’ already exists." = "Das Farbthema '%' ist bereits vorhanden.";

/* the connection is busy. please wait and try again tooltip */
"The connection is busy. Please wait and try again." = "Die Verbindung ist ausgelastet. Versuchen Sie es nach kurzer Wartezeit erneut.";

/* the connection of the active connection window is not identical tooltip */
"The connection of the active connection window is not identical." = "Die Verbindung des aktiven Verbindungsfensters ist nicht identisch.";

/* Connection lost during import error message */
"The connection to the server was lost during the import.  The import is only partially complete." = "Die Verbindung zum Server ging während des Imports verloren.  Der Import ist nur teilweise abgeschlossen.";

/* Create syntax permission denied detail */
"The creation syntax could not be retrieved due to a permissions error.\n\nPlease check your user permissions with an administrator." = "Die Erstellungssyntax konnte aufgrund eines Berechtigungsfehlers nicht abgerufen werden.\n\nDie Berechtigungen mit dem Administrator überprüfen.";

/* CSV file open error */
"The CSV file you selected could not be found or read." = "Die ausgewählte CSV-Datei konnte nicht gefunden oder gelesen werden.";

/* Error when CSV appears to have too many columns to import, probably due to line ending mismatch */
"The CSV was read as containing more than 512 columns, more than the maximum columns permitted for speed reasons by Sequel Ace.\n\nThis usually happens due to errors reading the CSV; please double-check the CSV to be imported and the line endings and escape characters at the bottom of the CSV selection dialog." = "Die CSV wurde enthält mehr als 512 gelesene Spalten, mehr als die maximalen Anzahl, die für Sequel Ace aus Geschwindigkeitsgründen sind.\n\nDas passiert meist bei Lesefehlern, daher sollten die zu importierende CSV. die Zeilenenden und die Escape-Zeichen am unteren Rand des CSV-Auswahldialogs geprüft werden.";

/* unsaved theme informative message */
"The current color theme is unsaved. Do you want to proceed without saving it?" = "Soll das aktuelle Farbdesign wirklich nicht gespeichert werden?";

/* Short important release note for swap of custom query buttons */
"The Custom Query \"Run\" and \"Run All\" button positions and their shortcuts have been swapped." = "Die Schaltflächenpositionen für benutzerdefinierte Abfragen ausführen und ausführen Alle und ihre Verknüpfungen wurden ausgetauscht.";

/* the following default bundles were updated:\n%@\nyour modifications were stored as “(user)”. */
"The following default Bundles were updated:\n%@\nYour modifications were stored as “(user)”." = "Die folgenden Standard-Bundles wurden aktualisiert: \n%@\nDie Änderungen wurden unter “(user)” gespeichert.";

/* favorites export error informative message */
"The following error occurred during the export process:\n\n%@" = "Beim Export ist der folgende Fehler aufgetreten: \n\n%@";

/* favorites import error informative message */
"The following error occurred during the import process:\n\n%@" = "Beim Import ist der folgende Fehler aufgetreten:\n\n%@";

/* table structure : indexes : delete index : error 1553 : description */
"The foreign key relationship '%@' has a dependency on index '%@'. This relationship must be removed before the index can be deleted.\n\nAre you sure you want to continue to delete the relationship and the index? This action cannot be undone." = "Die Fremdschlüsselbeziehung '%1' ist abhängig vom Index '%2''. Diese Beziehung muss entfernt werden, bevor der Index gelöscht werden kann. Diese Aktion kann nicht rückgängig gemacht werden.";

/* table list change alert message */
"The list of tables has changed" = "Die Liste der Tabellen hat sich geändert.";

/* message when trying to rename a table/view/proc/etc to an already used name */
"The name '%@' is already used." = "Der Name '%' wird bereits verwendet.";

/* table list change alert informative message */
"The number of tables in this database has changed since the export dialog was opened. There are now %lu additional table(s), most likely added by an external application.\n\nHow would you like to proceed?" = "Die Anzahl der Tabellen in dieser Datenbank hat sich seit dem Öffnen des Exportdialogs geändert. Es gibt jetzt %lu zusätzliche Tabellen, die höchstwahrscheinlich von einer externen Anwendung hinzugefügt werden.";

/* message of panel when no rows have been affected after writing to the db */
"The row was not written to the MySQL database. You probably haven't changed anything.\nReload the table to be sure that the row exists and use a primary key for your table.\n(This error can be turned off in the preferences.)" = "Die Zeile wurde nicht in die MySQL-Datenbank geschrieben. Sie haben wahrscheinlich nichts geändert. .";

/* export : import settings : file version error description ($1 = is version, $2 = list of supported versions); note: the u00A0 is a non-breaking space, do not add more whitespace. */
"The selected export settings were stored with version %1$ld, but only settings with the following versions can be imported: %2$@.\n\nEither save the settings in a backwards compatible way or update your version of Sequel Ace." = "Die ausgewählten Exporteinstellungen wurden mit version %1$ld gespeichert, aber es können nur Einstellungen mit den folgenden Versionen importiert werden: %2.";

/* export : import settings : spf content type error description */
"The selected file contains data of type “%1$@”, but type “%2$@” is needed. Please choose a different file." = "Die ausgewählte Datei enthält Daten vom Typ \"%1\", aber die Eingabe \"%2\" wird benötigt. Wählen Sie eine andere Datei aus.";

/* export : import settings : file error description */
"The selected file is either not a valid SPF file or severely corrupted." = "Die ausgewählte Datei ist entweder keine gültige SPF-Datei oder stark beschädigt.";

/* error deleting relation informative message */
"The selected relation couldn't be deleted.\n\nMySQL said: %@" = "Die ausgewählte Beziehung konnte nicht gelöscht werden. @";

/* error deleting trigger informative message */
"The selected trigger couldn't be deleted.\n\nMySQL said: %@" = "Der ausgewählte Trigger konnte nicht gelöscht werden. @";

/* description of tinyint */
"The smallest integer type, requires 1 byte storage space. M is the optional display width and does not affect the possible value range." = "Der kleinste Ganzzahltyp benötigt 1 Byte Speicherplatz. M ist die optionale Anzeigebreite und hat keinen Einfluss auf den möglichen Wertebereich.";

/* message of panel when connection to socket failed because optional socket could not be found */
"The socket file could not be found in any common location. Please supply the correct socket location.\n\nMySQL said: %@" = "Die Socketdatei wurde an keinem gemeinsamen Speicherort gefunden. Geben Sie bitte den richtigen Socketspeicherort an. . . @";

/* error creating relation informative message */
"The specified relation could not be created.\n\nMySQL said: %@" = "Die angegebene Beziehung konnte nicht erstellt werden. . . @";

/* error creating trigger informative message */
"The specified trigger was unable to be created.\n\nMySQL said: %@" = "Der angegebene Trigger konnte nicht erstellt werden. @";

/* sql import : charset error alert : detail message */
"The SQL file uses utf8mb4 encoding, but your MySQL version only supports the limited utf8 subset.\n\nYou can continue the import, but any non-BMP characters in the SQL file (eg. some typographic and scientific special characters, archaic CJK logograms, emojis) will be unrecoverably lost!" = "Die SQL-Datei verwendet utf8mb4-Codierung, aber Ihre MySQL-Version unterstützt nur die begrenzte utf8-Untermenge. Sie können den Import fortsetzen, aber alle Nicht-BMP-Zeichen in der SQL-Datei (z. B. einige typografische und wissenschaftliche Sonderzeichen, archaische CJK-Logogramme, Emojis) gehen unwiederbringlich verloren!";

/* SQL file open error */
"The SQL file you selected could not be found or read." = "Die ausgewählte SQL-Datei konnte nicht gefunden oder gelesen werden.";

/* Prompt for SSH password when keychain fetch failed */
"The SSH password could not be loaded from the keychain; please enter the SSH password for %@:" = "Das SSH-Kennwort konnte nicht aus dem Schlüsselbund geladen werden. Geben Sie bitte das SSH-Kennwort für %' ein:";

/* Prompt for SSH password when direct fetch failed */
"The SSH password could not be loaded; please enter the SSH password for %@:" = "Das SSH-Kennwort konnte nicht geladen werden. Geben Sie bitte das SSH-Kennwort für %' ein:";

/* SSH tunnel authentication failed message */
"The SSH Tunnel could not authenticate with the remote host. Please check your password and ensure you still have access." = "Der SSH-Tunnel konnte sich nicht beim Remotehost authentifizieren. Bitte überprüfen Sie Ihr Passwort und stellen Sie sicher, dass Sie noch Zugriff haben.";

/* SSH tunnel unexpectedly closed */
"The SSH Tunnel has unexpectedly closed." = "Der SSH-Tunnel wurde unerwartet geschlossen.";

/* SSH tunnel was closed by remote host message */
"The SSH Tunnel was closed 'by the remote host'. This may indicate a networking issue or a network timeout." = "Der SSH-Tunnel wurde \"vom Remote-Host\" geschlossen. Dies kann auf ein Netzwerkproblem oder ein Netzwerktimeout hinweisen.";

/* SSH tunnel forwarding port connection refused message */
"The SSH Tunnel was established successfully, but could not forward data to the remote port as the remote port refused the connection." = "Der SSH-Tunnel wurde erfolgreich eingerichtet, konnte jedoch keine Daten an den Remoteport weiterleiten, da der Remoteport die Verbindung verweigerte.";

/* SSH tunnel unable to bind to local port message */
"The SSH Tunnel was unable to bind to the local port. This error may occur if you already have an SSH connection to the same server and are using a 'LocalForward' setting in your SSH configuration.\n\nWould you like to fall back to a standard connection to localhost in order to use the existing tunnel?" = "Der SSH-Tunnel konnte nicht an den lokalen Port gebunden werden. Dieser Fehler kann auftreten, wenn Sie bereits über eine SSH-Verbindung mit demselben Server verfügen und in Ihrer SSH-Konfiguration eine Einstellung \"LocalForward\" verwenden.";

/* SSH tunnel failed or timed out message */
"The SSH Tunnel was unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds)." = "Der SSH-Tunnel konnte keine Verbindung mit dem Host %1 herstellen, oder die Anforderung wurde zeitimiert %2$ld.";

/* message of panel when loading of table failed and presumably due to used filter argument */
"The table data couldn't be loaded presumably due to used filter clause. \n\nMySQL said: %@" = "Die Tabellendaten konnten vermutlich aufgrund der verwendeten Filterklausel nicht geladen werden. Nn-MySQL Sagte: % @";

/* message of panel when loading of table failed */
"The table data couldn't be loaded.\n\nMySQL said: %@" = "Die Tabellendaten konnten nicht geladen werden. @";

/* Export folder not writable explanatory text */
"The target export folder is not writable.  Please select a new export location and try again." = "%lu der Export-Dateien wurden nicht erzeugt, weil der Zielordner schreibgeschützt ist. Der Export sollte mit einem anderen Zielordner wiederholt werden.";

/* Export folder not chosen by user explanatory text */
"No directory selected.  Please select a new export location and try again." = "Es wurde kein Verzeichnis ausgewählt.  Wählen Sie einen neuen Exportspeicherort aus, und versuchen Sie es erneut.";
"No directory selected." = "Es wurde kein Verzeichnis ausgewählt.";
"Please select a new export location and try again." = "%lu der Export-Dateien wurden nicht erzeugt, weil der Zielordner schreibgeschützt ist. Der Export sollte mit einem anderen Zielordner wiederholt werden.";

/* Export folder missing explanatory text */
"The target export folder no longer exists.  Please select a new export location and try again." = "%lu der Export-Dateien wurden nicht erzeugt, weil der Zielordner nicht gefunden wurde. Der Export sollte mit einem anderen Zielordner wiederholt werden.";

/* theme name label */
"Theme Name:" = "Design Name:";

/* themes installation error */
"Themes Installation Error" = "Themes-Installationsfehler";

/* message of panel when table content cannot be copied */
"There have been errors while copying table content. Please check the new table." = "Beim Kopieren von Tabelleninhalten sind Fehler aufgetreten. Bitte überprüfen Sie die neue Tabelle.";

/* Cannot duplicate a table with triggers to a different database */
"Cannot duplicate a table with triggers to a different database." = "Eine Tabelle mit Triggern kann nicht in eine andere Datenbank dupliziert werden.";

/* text shown when query was successfull */
"There were no errors." = "Es gab keine Fehler.";

/* delete field and foreign key informative message */
"This field is part of a foreign key relationship with the table '%@'. This relationship must be removed before the field can be deleted.\n\nAre you sure you want to continue to delete the relationship and the field? This action cannot be undone." = "Dieses Feld ist Teil einer Fremdschlüsselbeziehung mit der Tabelle '%'. Diese Beziehung muss entfernt werden, bevor das Feld gelöscht werden kann. Diese Aktion kann nicht rückgängig gemacht werden.";

/* table structure : indexes : delete index : error 1553, no FK found : description */
"This index cannot be deleted, because it is used by an existing foreign key relationship.\n\nPlease remove the relationship, before trying to remove this index.\n\nMySQL said: %@" = "Dieser Index kann nicht gelöscht werden, da er von einer vorhandenen Fremdschlüsselbeziehung verwendet wird. @";

/* description of serial */
"This is an alias for BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE." = "Dies ist ein Alias für BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE.";

/* description of dec
 description of fixed
 description of numeric */
"This is an alias for DECIMAL." = "Dies ist ein Alias für DECIMAL.";

/* description of double real */
"This is an alias for DOUBLE, unless REAL_AS_FLOAT is configured." = "Dies ist ein Alias für DOUBLE, es sei denn, REAL_AS_FLOAT konfiguriert ist.";

/* description of double precision */
"This is an alias for DOUBLE." = "Dies ist ein Alias für DOUBLE.";

/* description of bool
 description of boolean */
"This is an alias for TINYINT(1)." = "Dies ist ein Alias für TINYINT(1).";

/* Table Structure : Collation dropdown : database marker tooltip */
"This is the default collation of database %@." = "Dies ist die Standardsortierung der Datenbank \"%\" .";

/* Table Structure : Collation dropdown : default marker tooltip */
"This is the default collation of encoding %@." = "Dies ist die Standardsortierung der Codierung \"%\" .";

/* Table Structure : Collation dropdown : table marker tooltip */
"This is the default collation of table %@." = "Dies ist die Standardsortierung der Tabelle \"%\" .";

/* Table Structure : Collation dropdown : server marker tooltip */
"This is the default collation of this server." = "Dies ist die Standardsortierung dieses Servers.";

/* Table Structure : Encoding dropdown : database marker tooltip */
"This is the default encoding of database %@." = "Dies ist die Standardcodierung der Datenbank %.";

/* Table Structure : Encoding dropdown : table marker tooltip */
"This is the default encoding of table %@." = "Dies ist die Standardcodierung der Tabelle \"%\" .";

/* Table Structure : Encoding dropdown : server marker tooltip */
"This is the default encoding of this server." = "Dies ist die Standardcodierung dieses Servers.";

/* This table currently does not support relations. Only tables that use the InnoDB storage engine support them. */
"This table currently does not support relations. Only tables that use the InnoDB storage engine support them." = "Diese Tabelle unterstützt derzeit keine Beziehungen. Nur Tabellen, die das InnoDB-Speichermodul verwenden, unterstützen sie.";

/* user has no hosts informative message */
"This user doesn't have any hosts associated with it. It will be deleted unless one is added" = "Diesem Benutzer sind keine Hosts zugeordnet. Sie wird gelöscht, es sei denn, sie wird hinzugefügt";

/* error removing host informative message */
"This user doesn't seem to have any associated hosts and will be removed unless a host is added." = "Dieser Benutzer scheint keine zugeordneten Hosts zu haben und wird entfernt, es sei denn, ein Host wird hinzugefügt.";

/* shutdown server : confirmation dialog : message */
"This will wait for open transactions to complete and then quit the mysql daemon. Afterwards neither you nor anyone else can connect to this database!\n\nFull management access to the server's operating system is required to restart MySQL!" = "Dies wartet, bis geöffnete Transaktionen abgeschlossen sind, und beendet dann den mysql-Daemon. Danach können weder Sie noch irgendjemand sonst eine Verbindung zu dieser Datenbank herstellen!";

/* export filename time token */
"Time" = "Zeit";

/* toolbar item label for switching to the Table Triggers tab */
"Triggers" = "Auslöser";

/* triggers for table label */
"Triggers for table: %@" = "Auslöser für Tabelle: % @";

/* truncate button */
"Truncate" = "Leeren";

/* truncate tables message */
"Truncate selected tables?" = "Ausgewählte Tabellen leeren?";

/* truncate table menu title */
"Truncate Table..." = "Tabelle leeren...";

/* truncate table message */
"Truncate table '%@'?" = "Tabelle '%@' leeren?";

/* truncate tables menu item */
"Truncate Tables" = "Tabellen leeren";

/* type label (Navigator) */
"Type" = "Typ";

/* type declaration header */
"Type Declaration:" = "Typdeklaration:";

/* add index error message */
"Unable to add index" = "Index kann nicht hinzugefügt werden";

/* unable to analyze selected items message */
"Unable to analyze selected items" = "Ausgewählte Elemente können nicht analysiert werden";

/* unable to analyze table message */
"Unable to analyze table" = "Tabelle kann nicht analysiert werden";

/* unable to check selected items message */
"Unable to check selected items" = "Ausgewählte Elemente können nicht überprüft werden";

/* unable to check table message */
"Unable to check table" = "Tabelle kann nicht überprüft werden";

/* message of panel when connection to host failed due to access denied error */
"Unable to connect to host %@ because access was denied.\n\nDouble-check your username and password and ensure that access from your current location is permitted.\n\nMySQL said: %@" = "Verbindung zum Host %1$@ konnte nicht hergestellt werden, da der Zugriff verweigert wurde.\n\nÜberprüfen Sie Ihren Benutzernamen und Ihr Passwort und stellen Sie sicher, dass der Zugriff von Ihrem aktuellen Standort aus erlaubt ist.\n\nMySQL sagte: %2$@";

/* message of panel when SSH port forwarding failed */
"Unable to connect to host %@ because the port connection via SSH was refused.\n\nPlease ensure that your MySQL host is set up to allow TCP/IP connections (no --skip-networking) and is configured to allow connections from the host you are tunnelling via.\n\nYou may also want to check the port is correct and that you have the necessary privileges.\n\nChecking the error detail will show the SSH debug log which may provide more details.\n\nMySQL said: %@" = "Verbindung zum Host %1$@ konnte nicht hergestellt werden, da die Port-Verbindung über SSH abgelehnt wurde.\n\nBitte stellen Sie sicher, dass Ihr MySQL-Host so eingerichtet ist, dass er TCP/IP-Verbindungen erlaubt (kein --skip-networking) und so konfiguriert ist, dass Verbindungen von dem Host über den Sie tunneln.\n\nSie können auch überprüfen, ob der Port korrekt ist und dass Sie die nötigen Berechtigungen haben.\n\nWenn Sie die Fehlerdetails überprüfen, wird das SSH-Debug-Log angezeigt, das weitere Details enthalten kann.\n\nMySQL sagte: %2$@";

/* message of panel when connection to host failed */
"Unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds).\n\nMySQL said: %@" = "Keine Verbindung zu Host %1$@oder Zeitüberschreitung möglich.\n\nVergewissern Sie sich, dass die Adresse korrekt ist und dass Sie die nötigen Berechtigungen haben, oder versuchen Sie die Zeitüberschreitung der Verbindung zu erhöhen (derzeit %2$ld Sekunden).\n\nMySQL sagte: %3$@";

/* message of panel when connection to host failed */
"Unable to connect via the socket, or the request timed out.\n\nDouble-check that the socket path is correct and that you have the necessary privileges, and that the server is running.\n\nMySQL said: %@" = "Keine Verbindung über den Socket oder Zeitüberschreitung möglich.\n\nÜberprüfen Sie, ob der Socket-Pfad korrekt ist und ob Sie die erforderlichen Berechtigungen haben und ob der Server läuft.\n\nMySQL sagte: %@";

/* unable to copy database message */
"Unable to copy database" = "Datenbank kann nicht kopiert werden";

/* error deleting index message */
"Unable to delete index" = "Index kann nicht gelöscht werden";

/* error deleting relation message */
"Unable to delete relation" = "Beziehung kann nicht gelöscht werden";

/* error deleting trigger message */
"Unable to delete trigger" = "Trigger kann nicht gelöscht werden";

/* unable to flush selected items message */
"Unable to flush selected items" = "Ausgewählte Artikel können nicht gelöscht werden";

/* unable to flush table message */
"Unable to flush table" = "Tabelle kann nicht geleert werden";

/* unable to get list of users message */
"Unable to get list of users" = "Liste der Benutzer kann nicht abrufen";

/* error killing connection message */
"Unable to kill connection" = "Verbindung kann nicht beendet werden";

/* error killing query message */
"Unable to kill query" = "Abfrage kann nicht beendet werden";

/* unable to optimze selected items message */
"Unable to optimze selected items" = "Ausgewählte Elemente können nicht optimiert werden";

/* unable to optimze table message */
"Unable to optimze table" = "Tabelle kann nicht optimiert werden";

/* unable to perform the checksum */
"Unable to perform the checksum" = "Die Prüfsumme kann nicht ausgeführt werden.";

/* error removing host message */
"Unable to remove host" = "Host kann nicht entfernt werden";

/* unable to rename database message */
"Unable to rename database" = "Datenbank kann nicht umbenannt werden";

/* unable to repair selected items message */
"Unable to repair selected items" = "Ausgewählte Artikel können nicht repariert werden";

/* unable to repair table message */
"Unable to repair table" = "Tabelle kann nicht repariert werden";

/* message of panel when connection to db failed after selecting from popupbutton */
"Unable to select database %@.\nPlease check you have the necessary privileges to view the database, and that the database still exists." = "Die Datenbank kann nicht ausgewählt werden.";

/* Unable to write row error */
"Unable to write row" = "Zeile kann nicht geschrieben werden";

/* Table Content : Remove Row : Result : n Error title */
"Unexpected number of rows removed!" = "Unerwartete Anzahl von Zeilen entfernt!";

/* warning */
"Unknown file format" = "Unbekanntes Dateiformat";

/* unsaved changes message */
"Unsaved changes" = "Sie haben nicht gespeicherte Änderungen";

/* unsaved theme message */
"Unsaved Theme" = "Nicht gespeichertes Thema";

/* Preferences : Network : Custom SSH client : warning dialog title */
"Unsupported configuration!" = "Nicht unterstützte Konfiguration!";

/* export : import settings : file version error title */
"Unsupported version for export settings!" = "Nicht unterstützte Version für Exporteinstellungen!";

/* Name for an untitled connection */
"Untitled" = "Ohne Titel";

/* Title of a new Sequel Ace Document */
"Untitled %ld" = "Ohne Titel %ld";

/* range for mediumblob type */
"Up to %@ bytes (16 MiB)" = "Bis zu %-Bytes (16 MiB)";

/* range for longblob type */
"Up to %@ bytes (4 GiB)" = "Bis zu %-Bytes (4 GiB)";

/* range for mediumtext type */
"Up to %@ characters (16 MiB)" = "Bis zu %-Zeichen (16 MiB)";

/* range for enum type */
"Up to %@ distinct members (<%@ in practice)\n1-2 bytes storage" = "Bis zu %1- verschiedene Member (<%2- in der Praxis)";

/* range for tinyblob type */
"Up to 255 bytes" = "Bis zu 255 Bytes";

/* range for tinytext type */
"Up to 255 characters" = "Bis zu 255 Zeichen";

/* Open Files : Bundle : Already-Installed : Update button */
"Update" = "Aktualisieren";

/* updated: %@ */
"updated: %@" = "Aktualisiert";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field of table `%2$@` was changed." = "Fehler beim Aktualisieren des Feldinhalts. Der Feldursprung konnte nicht eindeutig identifiziert werden (%1$ld Übereinstimmungen). Es ist sehr wahrscheinlich, dass beim Bearbeiten dieses Feldes der Tabelle '%2' geändert wurde.";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field the table `%2$@` was changed by an other user." = "Fehler beim Aktualisieren des Feldinhalts. Der Feldursprung konnte nicht eindeutig identifiziert werden (%1$ld Übereinstimmungen). Es ist sehr wahrscheinlich, dass beim Bearbeiten dieses Feldes die Tabelle '%2' von einem anderen Benutzer geändert wurde.";

/* updating field task description */
"Updating field data..." = "Felddaten werden aktualisiert...";

/* URL scheme command couldn't authenticated */
"URL scheme command couldn't authenticated" = "URL-Schemabefehl konnte nicht authentifiziert werden";

/* URL scheme command was terminated by user */
"URL scheme command was terminated by user" = "DER Befehl URL Scheme wurde vom Benutzer beendet";

/* URL scheme command %@ unsupported */
"URL scheme command %@ unsupported" = "URL-Schemabefehl %, nicht unterstützt";

/* Use 127.0.0.1 button */
"Use 127.0.0.1" = "Verwenden 127.0.0.1";

/* use standard connection button */
"Use Standard Connection" = "Verwenden der Standardverbindung";

/* user has no hosts message */
"User has no hosts" = "Benutzer hat keine Hosts";

/* user-defined value */
"User-defined value" = "Benutzerdefinierter Wert";

/* toolbar item label for switching to the User Manager tab */
"Users" = "Benutzer";

/* CSV Field Mapping : Table View : Tooltip for fields with NULL value */
"Value will be imported as MySQL NULL" = "Wert wird als MySQL NULL importiert";

/* variable label for color table (Prefs > Editor) */
"Variable" = "Variable";

/* version */
"version" = "Version";

/* export header version label */
"Version" = "Version";

/* view */
"view" = "Ansicht";

/* Release notes button title */
"View full release notes" = "Vollständige Versionshinweise anzeigen";

/* header for view info pane */
"VIEW INFORMATION" = "INFORMATIONEN ANZEIGEN";

/* view html source code menu item title */
"View Source" = "Quelle anschauen";

/* view structure print heading */
"View Structure" = "Ansichtsstruktur";

/* views */
"views" = "Ansichten";

/* warning */
"Warning" = "Warnung";

/* Short important release note for why password prompts may occur */
"We've changed Sequel Ace's digital signature for GateKeeper compatibility; you'll have to allow access to your passwords again." = "Wir haben die digitale Signatur von Sequel Ace für GateKeeper-Kompatibilität geändert; Sie müssen den Zugriff auf Ihre Kennwörter erneut zulassen.";

/* Important release notes informational text, single change */
"We've made a few changes but we thought you should know about one particularly important one:" = "Wir haben ein paar Änderungen vorgenommen, aber wir dachten, Sie sollten über eine besonders wichtige wissen:";

/* Important release notes informational text, multiple changes */
"We've made a few changes but we thought you should know about some particularly important ones:" = "Wir haben ein paar Änderungen vorgenommen, aber wir dachten, Sie sollten über einige besonders wichtige wissen:";

/* WHERE clause not valid */
"WHERE clause not valid" = "WHERE-Klausel ungültig";

/* Title of filter preview area when the query WHERE is negated */
"WHERE NOT query" = "WHERE NOT-Abfrage";

/* Title of filter preview area when the query WHERE is normal */
"WHERE query" = "WHERE-Abfrage";

/* Generic working description */
"Working..." = "In Bearbeitung…";

/* export label showing app is writing data */
"Writing data..." = "Daten werden geschrieben...";

/* text showing that app is writing text file */
"Writing..." = "Einen alternativen Pfad angeben über den auf diese Daten zugegriffen werden kann. Zum Beispiel „/ueber“ eingeben, wenn eine \"Über uns\"-Seite erstellt werden soll.";

/* wrong data format or password */
"Wrong data format or password." = "Falsches Datenformat oder Passwort.";

/* wrong data format */
"Wrong data format." = "Falsches Datenformat.";

/* export : import settings : spf content type error title */
"Wrong SPF content type!" = "Falscher SPF-Inhaltstyp!";

/* export filename date token */
"Year" = "Jahr";

/* message of panel when trying to copy multiple rows */
"You can only copy single rows." = "Sie können nur einzelne Zeilen kopieren.";

/* message of panel when trying to edit tables without index and with hidden blob/text fields */
"You can't hide blob and text fields when working with tables without index." = "Sie können Blob- und Textfelder nicht ausblenden, wenn Sie mit Tabellen ohne Index arbeiten.";

/* You cannot delete the last field in a table. Delete the table instead. */
"You cannot delete the last field in a table. Delete the table instead." = "Sie können das letzte Feld in einer Tabelle nicht löschen. Löschen Sie stattdessen die Tabelle.";

/* SSL connection requested but not established error detail */
"You requested that the connection should be established using SSL, but MySQL made the connection without SSL.\n\nThis may be because the server does not support SSL connections, or has SSL disabled; or insufficient details were supplied to establish an SSL connection.\n\nThis connection is not encrypted." = "Sie haben angefordert, dass die Verbindung mit SSL hergestellt werden soll, aber MySQL hat die Verbindung ohne SSL hergestellt. oder es wurden nicht genügend Details angegeben, um eine SSL-Verbindung herzustellen.";

/* Query Favorites : List : Section Heading : current connection document : tooltip (arg is the name of the spf file) */
"‘%@’ based favorites" = "Favoriten auf '%'-Basis";

/* table column header. Read: 'Showing all content filters for fields of type %@' (ContentFilterManager) */
"‘%@’ Fields Content Filters" = "Inhaltsfilter für '%'-Felder";

/* Export file already exists message */
"%@ already exists. Do you want to replace it?" = "%@ bereits vorhanden. Ersetzen?";

/* Bundle Editor : Outline View : Bundle item : tooltip */
"%@ Bundle" = "%@ Bundle";

/* Export file creation error title */
"%@ could not be created" = "%@ konnte nicht angelegt werden";

/* %@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item. */
"%@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item." = "%- konnte nicht analysiert werden. Sie können die Spalteneinrichtung bearbeiten, aber die Spalte wird nicht in der Inhaltsansicht angezeigt. Bitte melden Sie dieses Problem dem Sequel Ace-Team über den Menüpunkt Hilfe.";

/* connection view : ssl : client cert file picker : wrong format error title */
"%@ is not a valid client certificate file." = "%@ enthält kein gültiges Benutzer-Zertifikat.";

/* connection view : ssl : key file picker : wrong format error title */
"%@ is not a valid private key file." = "%@ enthält keinen privaten Schlüssel.";

/* text shown when there are App Sandbox Issues */
"App Sandbox Issue" = "App Sandbox Problem";

/* Stale Bookmarks error title */
"Stale Bookmarks" = "Veraltete Lesezeichen";

/* App Sandbox info link text */
"App Sandbox Info" = "App Sandbox Info";

/* error while selecting file title */
"File Selection Error" = "Fehler bei Dateiauswahl";

/* error while selecting file message */
"The selected file is not a valid file.\n\nPlease try again.\n\nClass: %@" = "Die gewählte Datei ist ungültig.\n\nBitte erneut versuchen.\n\nKlasse: %@";

/* known hosts not writable error message */
"The selected known hosts file is not writable.\n\n%@\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "Die ausgewählte known_hosts-Datei ist nicht beschreibbar.\n\n%@\n\nBitte wählen Sie die Datei erneut in den Einstellungen von Sequel Ace aus und versuchen Sie es erneut.";

/* known hosts is invalid message */
"The selected known hosts file is invalid.\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "Die ausgewählte Known-Hosts-Datei ist ungültig.\n\nBitte wählen Sie die Datei in den Einstellungen von Sequel Ace erneut aus und versuchen Sie es erneut.";

/* known hosts contains quote message */
"The selected known hosts file contains a quote (\") in its file path which is not supported.\n\n%@\n\nPlease select a different file in Sequel Ace's Preferences or rename the file/path to remove the quote." = "Die ausgewählte Datei bekannter Hosts enthält ein Anführungszeichen (\") im Dateipfad, das nicht unterstützt wird.\n\n%@\n\nBitte wählen Sie eine andere Datei in den Einstellungen von Sequel Ace aus oder benennen Sie die Datei/den Pfad um, um das Anführungszeichen zu entfernen.";

/* SSH Tunnel Debugging menu and window title */
"SSH Tunnel Debugging Info" = "SSH-Tunnel-Debugging-Informationen";

/* Question to user to see if they would like to re-request access */
"You have stale secure bookmarks:\n\n%@\n\nWould you like to re-request access now?" = "Einige „sichere Lesezeichen“ sind veraltet:\n\n%@\n\nSoll der Zugriff jetzt erneut angefordert werden?";

/* Question to user to see if they would like to request access to missing bookmarks */
"You have missing secure bookmarks:\n\n%@\n\nWould you like to request access now?" = "Es fehlen sichere Lesezeichen:\n\n%@\n\nSoll der Zugriff jetzt angefordert werden?";

/* known hosts advance menu item title */
"Use known hosts from ssh config (ADVANCED)" = "Bekannte Hosts aus der SSH Konfiguration verwenden (FORTGESCHRITTEN)";

/* The answer, yes */
"Yes" = "Ja";

/* Shown to remind users of their stale bookmarks */
"A reminder of your stale secure bookmarks:<br /><br />%@<br />" = "Ihre veralteten „sicheren Lesezeichen“:<br /><br />%@<br />";

/* Title for Stale Secure Bookmarks help window */
"Stale Secure Bookmarks" = "Veraltete „sichere Lesezeichen“";

/* Title for Export Error alert */
"Export Error" = "Export Fehler";

/* Message for Export Error alert */
"Error while writing to the export file. Could not open file: %@" = "Fehler beim Schreiben der Export-Datei. Datei %@ konnte nicht geöffnet werden";

/* Message for User window error */
"Resultset from mysql.user contains neither 'Password' nor 'authentication_string' column." = "Das Ergebnis aus mysql.user enthält weder eine ‚Password' noch eine ‚authentication_string' Spalte.";

/* Title for User window error */
"User Data Error" = "Benutzer-Daten-Fehler";

/* Title for Stale Bookmark file selection dialog */
"Please re-select the file '%@' in order to restore Sequel Ace's access." = "Wählen Sie die Datei '%@' erneut, um den Zugriff für Sequel Ace wieder herzustellen.";

/* Title for File Preference file selection dialog */
"Please choose a file or folder to grant Sequel Ace access to." = "Wählen Sie eine Datei oder einen Ordner, um Sequel Ace Zugriff zu gewähren.";

/* Title for Network Preference ssh config file selection dialog */
"Please choose your ssh config files(s)" = "Wählen Sie die SSH Konfigurationsdatei(en)";

/* Title for Network Preference ssh known hosts file selection dialog */
"Please choose your known hosts file" = "Wählen Sie die Datei der ‚Known Hosts‘";

/* Message for field editor JSON segment when JSON is invalid */
"Invalid JSON" = "Ungültiges JSON";

/* Applying syntax highlighting task description */
"Applying syntax highlighting..." = "Syntax Hervorhebung wird angewendet …";

/* Error message when a bash task fails to start */
"Couldn't launch task.\nException reason: %@\n ENV length: %lu" = "Verarbeitung konnte nicht begonnen werden.\nGrund für die Exception: %@\n ENV Länge: %lu";

/* Error message title when we failed to create new connection view */
"New Connection Error" = "Fehler bei neuer Verbindung";

/* New Connection Error informative message */
"Failed to create new database connection window. Please restart Sequel Ace and try again." = "Ein neues Fenster für die Datenbankverbindung konnte nicht erstellt werden. Nach Neustart von Sequel Ace wieder versuchen.";

/* new version is available alert title */
"A new version is available" = "Eine neue Version ist verfügbar";

/* new version is available download button title */
"Download" = "Herunterladen";

/* new version is available alert informativeText */
"Version %@ is available. You are currently running %@" = "Version %@ ist verfügbar. Die gegenwärtige Version ist %@";

/* downloading new version window title */
"Download Progress" = "Download Fortschritt";

/* downloading new version time remaining placeholder text */
"Calculating time remaining..." = "Verbleibende Zeit wird berechnet …";

/* downloading new version window message */
"Downloading Sequel Ace - %@" = "Sequel Ace holen - %@";

/* downloading new version time remaining estimate text */
"About %.1f seconds left" = "Noch etwa %.1f Sekunden";

/* downloading new version failure alert title */
"Download Failed" = "Download gescheitert";

/* Tool tip for the Show alert when update is available preference on the Notifications pane */
"Only available for GitHub downloads" = "Nur für GitHub Downloads verfügbar";

/* Tool tip warning for when the user sets auto-complete delay to zero */
"WARNING: Setting the auto-complete delay to 0.0 can result in strange output." = "ACHTUNG: Eine auto-complete Verzögerung von 0.0 kann seltsame Anzeigen zur Folge haben.";

/* downloading new version bytes of bytes done */
"%@ of %@" = "%@ von %@";

/* Time zone set failed informative message */
"\n\ntime_zone will be set to SYSTEM." = "\n\ntime_zone wird gemäß SYSTEM gesetzt.";

/* Menu item title for checking for updates */
"Check for Updates..." = "Auf neuere Version prüfen...";

/* Warning message during connection in case the variable skip-show-database is set to ON */
"The skip-show-database variable of the database server is set to ON. Thus, you won't be able to list databases unless you have the SHOW DATABASES privilege.\n\nHowever, the databases are still accessible directly through SQL queries depending on your privileges." = "Die skip-show-databse Variable des Datenbank-Servers steht auf EIN. Daher werden Listen von Datenbanken nur gezeigt, wenn der Benutzer den Zugriff SHOW DATABASES eingeräumt erhielt.\n\nAuf die Datenbanken gemäß den anderen Zugriffsrechten per SQL zugriffen werden.";

/* Error alert title when the request to GitHub fails */
"GitHub Request Failed" = "GitHub Anfrage gescheitert";

/* Info alert title when there are Newer Releases Available */
"No Newer Release Available" = "Keine neuere Version verfügbar";

/* Info alert message when there are Newer Releases Available */
"You are currently running the latest release." = "Sie verwenden die aktuelle Version.";

/* Alert button, disable the warning when skip-show-database is set to on */
"Never show this again" = "Nicht erneut anzeigen";

/* Info alert when editing a generated column */
"The current field \"%@\" is a generated column and therefore cannot be edited." = "Das aktuelle Feld \"%@\" ist eine generierte Spalte und kann daher nicht bearbeitet werden.";

/* Release note / info modal about change in latest release about column behavior */
"The use of the \"default\" column has changed since the last version of Sequel ACE:\n\n- No default value : Leave it blank.\n- String value : Use single '' or double quotes \"\" if you want an empty string or to wrap a string\n- Expression : Use parentheses (). Except for the TIMESTAMP and DATETIME columns where you can specify the CURRENT_TIMESTAMP function without enclosing parentheses." = "Die Verwendung der \"default\" Spalte hat sich seit der letzten Version der Sequel ACE geändert:\n\n- Kein Standardwert: Lassen Sie sie leer.\n- String-Wert: Benutzen Sie einfache '' oder doppelte Anführungszeichen \"\", wenn Sie einen leeren String oder einen String\n- Ausdruck : Klammern verwenden (). Mit Ausnahme der Spalten TIMESTAMP und DATETIME können Sie die Funktion CURRENT_TIMESTAMP ohne Klammern angeben.";

/* Menu option to pin a view to the top of the list of tables */
"Pin View" = "Ansicht anpinnen";

/* Menu option to pin a table to the top of the list of tables */
"Pin Table" = "Tabelle anpinnen";

/* Menu option to pin a procedure to the top of the list of tables */
"Pin Procedure" = "Pin Prozedur";

/* Menu option to pin a function to the top of the list of tables */
"Pin Function" = "Funktion anpinnen";

/* Menu option to unpin a view which is currently pinned at the top of the list of tables */
"Unpin View" = "Ansicht loslösen";

/* Menu option to unpin a table which is currently pinned at the top of the list of tables */
"Unpin Table" = "Tabelle loslösen";

/* Menu option to unpin a procedure which is currently pinned at the top of the list of tables */
"Unpin Procedure" = "Prozedur lösen";

/* Menu option to unpin a function which is currently pinned at the top of the list of tables */
"Unpin Function" = "Funktion loslösen";

/* header for pinned table list */
"PINNED" = "ANGEPINNT";

/* Menu option to copy a table's name with database name in the list of tables */
"Copy Table Name" = "Tabellennamen kopieren";

/* sequelace URL scheme error: LaunchFavorite alert title (favorite name not found) */
"LaunchFavorite URL Scheme Error" = "Fehler beim LaunchFavoriten URL-Schema";

/* sequelace URL scheme error: LaunchFavorite alert description (favorite name not found) */
"The variable in the ?name= query parameter could not be matched with any of your favorites." = "Die Variable im Parameter ?name= stimmt mit keinem Ihrer Favoriten überein.";
