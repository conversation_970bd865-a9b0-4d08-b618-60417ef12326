/* Table Content : Remove Row : Result : Too Few : Part 3 : Row not deleted when using primary key for DELETE statement. */
" Check the Console for possible errors inside the primary key(s) of this table!" = " Controlla la Console per possibili errori relativi alla o alle chiavi primarie di questa tabella!";

/* Table Content : Remove Row : Result : Too Many : Part 2 : Generic text */
" Please check the Console and inform the Sequel Ace team!" = " Per favore controllare la Console e informare it team di Sequel Ace!";

/* Table Content : Remove Row : Result : Too Few : Part 2 : Generic help message */
" Reload the table to be sure that the contents have not changed in the meantime." = " Ricarica la tabella per essere sicuri che i contenuti non siano cambiati nel frattempo.";

/* Table Content : Remove Row : Result : Too Few : Part 3 : no primary key in table generic message */
" You should also add a primary key to this table!" = " Dovresti anche aggiungere una chiave primaria a questa tabella!";

/* Query snippet shell command syntax and placeholder */
"$(shell_command)" = "$(shell_command)";

/* text showing how many rows are selected */
"%@ %@ selected" = "%1$@ %2$@ selezionati";

/* History item filtered by values label */
"%@ (Filtered by %@)" = "%1$@ (Filtrato da %2$@)";

/* History item with page number label */
"%@ (Page %lu)" = "%1$@ (Pagina %2$lu)";

/* Content Filter Manager : Initial name of copied filter
 Initial favourite name after duplicating a previous favourite */
"%@ Copy" = "%@ Copia";

/* text showing a single row a partially loaded result */
"%@ row in partial load" = "%@ riga in caricamento";

/* text showing a single row in the result */
"%@ row in table" = "%@ riga nella tabella";

/* text showing how a single rows matched filter */
"%@ row of %@%@ matches filter" = "%1$@ riga di %2$@%3$@ corrisponde al filtro";

/* text showing how many rows are in a partially loaded result */
"%@ rows in partial load" = "%@ righe in caricamento";

/* text showing how many rows are in the result */
"%@ rows in table" = "%@ righe nella tabella";

/* text showing how many rows matched filter */
"%@ rows of %@%@ match filter" = "%1$@ righe di %2$@%3$@ filtro corrispondenza";

/* text showing how many rows have been affected by a single query */
"%@; %ld rows affected" = "%1$@; %2$ld righe interessate";

/* text showing how many rows have been affected by multiple queries */
"%@; %ld rows affected in total, by %ld queries taking %@" = "%1$@; %2$ld righe colpite in totale, da %3$ld query che interessano %4$@";

/* text showing one row has been affected by a single query */
"%@; 1 row affected" = "%@; righe interessate";

/* text showing one row has been affected by multiple queries */
"%@; 1 row affected in total, by %ld queries taking %@" = "%1$@; 1 righe colpite in totale, da %2$ld query che interessano %3$@";

/* text showing a query was cancelled */
"%@; Cancelled after %@" = "%1$@; Annullato dopo %2$@";

/* text showing multiple queries were cancelled */
"%@; Cancelled in query %ld, after %@" = "%1$@; Cancellato nella query %2$ld, dopo %3$@";

/* Error display text, showing original MySQL error */
"%@\n\nMySQL said: %@" = "%1$@\n\nMySQL dice: %2$@";

/* favorite singular label (%d == 1) */
"%lu favorite" = "%lu preferito";

/* favorites plural label (%d != 1) */
"%lu favorites" = "%lu preferiti";

/* favorite group singular label (%d == 1) */
"%lu group" = "%lu gruppo";

/* favorite groups plural label (%d != 1) */
"%lu groups" = "%lu gruppi";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+y (y!=1) rows instead of n selected were deleted. */
"%ld additional rows were removed!" = "%ld colonne addizionali sono state rimosse!";

/* Label showing the index of the selected CSV row */
"%ld of %lu record(s)" = "%1$ld di %2$lu risultato/i";

/* Label showing the index of the selected CSV row (csv partially loaded) */
"%ld of first %lu record(s)" = "%1$ld dei primi record di %2$lu";

/* Table Content : Remove Row : Result : Too Few : Part 1 : n-x (x!=1) of n selected rows were deleted. */
"%ld rows were not removed." = "%ld righe non sono state eliminate.";

/* Export file already exists message */
"%lu files already exist. Do you want to replace them?" = "I file %lu esistono già. Vuoi sostituirli?";

/* Export files creation error title */
"%lu files could not be created" = "Impossibile creare i file %lu";

/* Some export files already exist explanatory text */
"%lu files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "%lu file con lo stesso nome esistono già nella cartella di destinazione. Sostituendoli, sovrascriverai il loro attuale contenuto.";

/* filtered item count */
"%lu of %lu" = "%1$lu di %2$lu";

/* Export folder not writable for some files explanatory text */
"%lu of the export files could not be created because their target export folder is not writable; please select a new export location and try again." = "%lu dei file di esportazione non può essere creato perché la cartella di esportazione di destinazione non è scrivibile; selezionare una nuova posizione di esportazione e riprovare.";

/* Export folder missing for some files explanatory text */
"%lu of the export files could not be created because their target export folder no longer exists; please select a new export location and try again." = "%lu dei file di esportazione non può essere creato perché la cartella di esportazione di destinazione non esiste più; selezionare una nuova posizione di esportazione e riprovare.";

/* History item title with nothing selected */
"(no selection)" = "(nessuna selezione)";

/* value shown for hidden blob and text fields */
"(not loaded)" = "(non caricato)";

/* Explanation for MySQL server has gone away error */
"(This usually indicates that the connection has been closed by the server after inactivity, but can also occur due to other conditions.  The connection has been restored; please try again if the query is safe to re-run.)" = "(Questo di solito indica che la connessione è stata chiusa dal server dopo l'inattività, ma può verificarsi anche a causa di altre condizioni. La connessione è stata ripristinata; riprova se la query è sicura da rieseguire.)";

/* Custom Query : text appended to the “x row(s) affected” messages. $1 is a time interval */
", first row available after %1$@" = ", prima riga disponibile dopo %1$@";

/* Custom Query : text appended to the “x row(s) affected” messages (for update/delete queries). $1 is a time interval */
", taking %1$@" = ", prendendo %1$@";

/* warning shown in the console when no rows have been affected after writing to the db */
"/* WARNING: No rows have been affected */\n" = "/* ATTENZIONE: Nessuna riga è stata colpita */\n";

/* error text when multiple custom query failed */
"[ERROR in query %ld] %@\n" = "[ERRORE nella query %1$ld] %2$@\n";

/* error text when reading of csv file gave errors */
"[ERROR in row %ld] %@\n" = "[ERRORE alla riga %1$ld] %2$@\n";

/* [multiple selection] */
"[multiple selection]" = "[selezione multipla]";

/* displayed when new content filter has empty Name field (ContentFilterManager) */
"[name required]" = "Nome richiesto";

/* [no selection] */
"[no selection]" = "nessuna selezione";

/* Additional export file errors */
"\n\n(In addition, one or more errors occurred while attempting to create the export files: %lu could not be created. These files will be ignored.)" = "\n\n(Inoltre, si sono verificati uno o più errori durante il tentativo di creare i file di esportazione: %lu non può essere creato. Questi file verranno ignorati.)";

/* \n\npress shift for binary search tooltip message */
"\n\nPress ⇧ for binary search (case-sensitive)." = "\n\nPremi ⇧ per la ricerca binaria (maiuscole e minuscole).";

/* description of bit */
"A bit-field type. M specifies the number of bits. If shorter values are inserted, they will be aligned on the least significant bit. See the SET type if you want to explicitly name each bit." = "Un tipo di campo bit. M specifica il numero di bit. Se vengono inseriti valori più corti, essi saranno allineati al bit meno significativo. Vedere il tipo SET se si desidera nominare esplicitamente ogni bit.";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog message */
"A Bundle ‘%@’ is already installed. Do you want to update it?" = "Un Bundle ‘%@’ è già installato. Vuoi aggiornarlo?";

/* description of binary */
"A byte array with fixed length. Shorter values will always be padded to the right with 0x00 until they fit M." = "Un array di byte con lunghezza fissa. I valori più brevi saranno sempre riempiti a destra con 0x00 finché non si adattano a M.";

/* description of varbinary */
"A byte array with variable length. The actual number of bytes is further limited by the values of other fields in the row." = "Un array di byte con lunghezza variabile. Il numero effettivo di byte è ulteriormente limitato dai valori di altri campi nella riga.";

/* description of blob
 description of longblob
 description of mediumblob
 description of tinyblob */
"A byte array with variable length. Unlike VARBINARY this type does not count towards the maximum row length." = "Un array di byte con lunghezza variabile. A differenza di VARBINARIO questo tipo non conta verso la lunghezza massima della riga.";

/* description of tinytext */
"A character string that can store up to 255 bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Una stringa di caratteri che può memorizzare fino a 255 byte, ma richiede meno spazio per valori più brevi. Il numero effettivo di caratteri è ulteriormente limitato dalla codifica usata. A differenza di VARCHAR questo tipo non conta verso la lunghezza massima della riga.";

/* description of varchar */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding and the values of other fields in the row." = "Una stringa di caratteri che può memorizzare fino a M byte, ma richiede meno spazio per valori più brevi. Il numero effettivo di caratteri è ulteriormente limitato dalla codifica utilizzata e dai valori di altri campi nella riga.";

/* description of text */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Una stringa di caratteri che può memorizzare fino a M byte, ma richiede meno spazio per valori più brevi. Il numero effettivo di caratteri è ulteriormente limitato dalla codifica usata. A differenza di VARCHAR questo tipo non conta verso la lunghezza massima della riga.";

/* description of char */
"A character string that will require M×w bytes per row, independent of the actual content length. w is the maximum number of bytes a single character can occupy in the given encoding." = "Una stringa di caratteri che richiederà byte M×w per riga, indipendentemente dalla lunghezza effettiva del contenuto. w è il numero massimo di byte che un singolo carattere può occupare nella codifica data.";

/* description of longtext
 description of mediumtext */
"A character string with variable length. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Una stringa di caratteri con lunghezza variabile. Il numero effettivo di caratteri è ulteriormente limitato dalla codifica usata. A differenza di VARCHAR questo tipo non conta verso la lunghezza massima della riga.";

/* description of json */
"A data type that validates JSON data on INSERT and internally stores it in a binary format that is both, more compact and faster to access than textual JSON.\nAvailable from MySQL 5.7.8." = "Un tipo di dati che convalida i dati JSON su INSERT e li memorizza internamente in un formato binario che è entrambi, più compatto e più veloce per accedere rispetto a JSON testuale.\nDisponibile da MySQL 5.7.8.";

/* Export file already exists explanatory text */
"A file with the same name already exists in the target folder. Replacing it will overwrite its current contents." = "Un file con lo stesso nome esiste già nella cartella di destinazione. Sostituirlo sovrascriverà il suo contenuto corrente.";

/* description of decimal */
"A fixed-point, exact decimal value. M is the maxium number of digits, of which D may be after the decimal point. When rounding, 0-4 is always rounded down, 5-9 up (“round towards nearest”)." = "Un punto fisso, un valore decimale esatto. M è il numero massimo di cifre, di cui D può essere dopo il punto decimale. Durante l’arrotondamento, 0-4 è sempre arrotondato verso il basso, 5-9 verso l’alto (“rotondo verso più vicino”).";

/* table structure : indexes : delete index : error 1553 : title
 table structure : indexes : delete index : error 1553, no FK found : title */
"A foreign key needs this index" = "Una chiave esterna ha bisogno di questo indice";

/* description of set */
"A SET can define up to 64 members (as strings) of which a field can use one or more using a comma-separated list. Upon insertion the order of members is automatically normalized and duplicate members will be eliminated. Assignment of numbers is supported using the same semantics as for BIT types." = "Un SET può definire fino a 64 membri (come stringhe) di cui un campo può usare uno o più utilizzando una lista separata da virgole. Al momento dell'inserimento l'ordine dei membri viene automaticamente normalizzato e i membri duplicati verranno eliminati. L'assegnazione di numeri è supportata utilizzando la stessa semantica dei tipi BIT.";

/* SSH key not found message */
"A SSH key location was specified, but no file was found in the specified location.  Please re-select the key and try again." = "È stata specificata una posizione della chiave SSH, ma non è stato trovato alcun file nella posizione specificata. Si prega di riselezionare la chiave e riprovare.";

/* SSL CA certificate file not found message */
"A SSL Certificate Authority certificate location was specified, but no file was found in the specified location.  Please re-select the Certificate Authority certificate and try again." = "È stata specificata una posizione del certificato dell'autorità di certificazione SSL, ma non è stato trovato alcun file nella posizione specificata. Selezionare nuovamente il certificato dell'autorità di certificazione e riprovare.";

/* SSL certificate file not found message */
"A SSL certificate location was specified, but no file was found in the specified location.  Please re-select the certificate and try again." = "È stata specificata una posizione del certificato SSL, ma non è stato trovato alcun file nella posizione specificata. Si prega di ri
selezionare il certificato e riprovare.";

/* SSL key file not found message */
"A SSL key file location was specified, but no file was found in the specified location.  Please re-select the key file and try again." = "È stata specificata una posizione del file chiave SSL, ma non è stato trovato alcun file nella posizione specificata. Si prega di ri-selezionare il file chiave e riprovare.";

/* duplicate host informative message */
"A user with the host '%@' already exists" = "Un utente con l'host '%@' esiste già";

/* duplicate user informative message */
"A user with the name '%@' already exists" = "Un utente con il nome '%@' esiste già";

/* table content : editing : error message description when parsing as hex string failed */
"A valid hex string may only contain the numbers 0-9 and letters A-F (a-f). It can optionally begin with „0x“ and spaces will be ignored.\nAlternatively the syntax X'val' is supported, too." = "Una stringa esadecimale valida può contenere solo i numeri 0-9 e le lettere A-F (a-f). Opzionalmente può iniziare con „0x“ e gli spazi saranno ignorati.\nIn alternativa, è supportata anche la sintassi X'val.";

/* connection failed due to access denied title */
"Access denied!" = "Accesso negato!";

/* range of double */
"Accurate to approx. 15 decimal places" = "Precisione con ca. 15 decimali";

/* range of float */
"Accurate to approx. 7 decimal places" = "Precisione a circa 7 decimali";

/* active connection window is busy. please wait and try again. tooltip */
"Active connection window is busy. Please wait and try again." = "La finestra di connessione attiva è occupata. Si prega di attendere e riprovare.";

/* header for activities pane */
"ACTIVITIES" = "ATTIVITÀ";

/* Add trigger button label */
"Add" = "Aggiungi";

/* menu item to add db */
"Add Database..." = "Aggiungi Database...";

/* Add Host */
"Add Host" = "Aggiungi Host";

/* add global value or expression menu item */
"Add Value or Expression…" = "Aggiungi valore o espressione…";

/* adding index task status message */
"Adding index..." = "Aggiunta indice...";

/* Advanced options short title */
"Advanced" = "Avanzate";

/* notifications preference pane name */
"Alerts & Logs" = "Avvisi e Registri";

/* notifications preference pane tooltip */
"Alerts & Logs Preferences" = "Preferenze di Avvisi e Registri";

/* All databases placeholder */
"All Databases" = "Tutti I Database";

/* All databases (%) placeholder */
"All Databases (%)" = "Tutti I Database (%)";

/* All export files already exist message */
"All the export files already exist. Do you want to replace them?" = "Tutti i file di esportazione già esistono. Vuoi sostituirli?";

/* An error for sequelace URL scheme command occurred. Probably no corresponding connection window found. */
"An error for sequelace URL scheme command occurred. Probably no corresponding connection window found." = "Si è verificato un errore per il comando sequelace dello schema URL. Probabilmente non è stata trovata alcuna finestra di connessione corrispondente.";

/* no connection available informatie message */
"An error has occurred and there doesn't seem to be a connection available." = "Si è verificato un errore e non sembra esserci una connessione disponibile.";

/* an error occur while executing a scheme command. if the scheme command was invoked by a bundle command, it could be that the command still runs. you can try to terminate it by pressing ⌘+. or via the activities pane. */
"An error occur while executing a scheme command. If the scheme command was invoked by a Bundle command, it could be that the command still runs. You can try to terminate it by pressing ⌘+. or via the Activities pane." = "Si è verificato un errore durante l'esecuzione di un comando schema. Se il comando schema è stato invocato da un comando Bundle, potrebbe essere che il comando è ancora in esecuzione. Puoi provare a terminarlo premendo <unk> +. o tramite il riquadro Attività.";

/* error killing query informative message */
"An error occurred while attempting to kill connection %lld.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di terminare la connessione %1$lld.\n\nMySQL ha detto: %2$@";

/* error killing query informative message */
"An error occurred while attempting to kill the query associated with connection %lld.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di terminare la query associata alla connessione %1$lld.\n\nMySQL ha detto: %2$@";

/* Error shown when unable to show create table syntax */
"An error occurred while creating table syntax.\n\n: %@" = "Si è verificato un errore durante la creazione della sintassi della tabella.\n\n: %@";

/* rename table error - no temporary name found */
"An error occurred while renaming '%@'. No temporary name could be found. Please try renaming to something else first." = "Si è verificato un errore durante la rinominazione di '%@'. Non è stato trovato alcun nome temporaneo. Prova a rinominare prima qualcos'altro.";

/* rename table error informative message */
"An error occurred while renaming '%@'.\n\nMySQL said: %@" = "Si è verificato un errore nel rinominare '%1$@'.\n\nMySQL ha detto: %2$@";

/* rename error - don't know what type the renamed thing is */
"An error occurred while renaming. '%@' is of an unknown type." = "Si è verificato un errore durante la rinomina. '%@' è di tipo sconosciuto.";

/* rename precedure/function error - can't delete old procedure */
"An error occurred while renaming. I couldn't delete '%@'.\n\nMySQL said: %@" = "Si è verificato un errore durante la rinomina. Non ho potuto eliminare '%1$@'.\n\nMySQL ha detto: %2$@";

/* rename precedure/function error - can't recreate procedure */
"An error occurred while renaming. I couldn't recreate '%@'.\n\nMySQL said: %@" = "Si è verificato un errore durante la rinomina. Non ho potuto ricreare '%1$@'.\n\nMySQL ha detto: %2$@";

/* rename precedure/function error - can't retrieve syntax */
"An error occurred while renaming. I couldn't retrieve the syntax for '%@'.\n\nMySQL said: %@" = "Si è verificato un errore durante la rinomina. Non ho potuto recuperare la sintassi per '%1$@'.\n\nMySQL ha detto: %2$@";

/* rename error - invalid create syntax */
"An error occurred while renaming. The CREATE syntax of '%@' could not be parsed." = "Si è verificato un errore durante la rinomina. Impossibile analizzare la sintassi CREATE di '%@'.";

/* message of panel when retrieving view information failed */
"An error occurred while retrieving status data.\n\nMySQL said: %@" = "Si è verificato un errore durante il recupero dei dati di stato.\n\nMySQL ha detto: %@";

/* message of panel when create syntax cannot be retrieved */
"An error occurred while retrieving the create syntax for '%@'.\nMySQL said: %@" = "Si è verificato un errore durante il recupero della sintassi di creazione di '%1$@'.\nMySQL ha detto: %2$@";

/* add index error informative message */
"An error occurred while trying to add the index.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di aggiungere l'indice.\n\nMySQL ha detto: %@";

/* error adding password to keychain informative message */
"An error occurred while trying to add the password to your Keychain. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Si è verificato un errore durante il tentativo di aggiungere la password al tuo Keychain. Riparare il portachiavi potrebbe risolvere questo problema, ma se non lo segnala al team Sequel Ace, fornendo il codice di errore %i.";

/* unable to copy database message informative message */
"An error occurred while trying to copy the database '%@' to '%@'." = "Si è verificato un errore durante il tentativo di copiare il database '%1$@' in '%2$@'.";

/* error deleting index informative message */
"An error occurred while trying to delete the index.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di eliminare l'indice.\n\nMySQL ha detto: %@";

/* table status : row count query failed : error message */
"An error occurred while trying to determine the number of rows for %@.\nMySQL said: %@ (%lu)" = "Si è verificato un errore durante il tentativo di determinare il numero di righe per «%1$@».\nMySQL ha detto: %2$@ (%3$lu)";

/* unable to rename database message informative message */
"An error occurred while trying to rename the database '%@' to '%@'." = "Si è verificato un errore durante il tentativo di rinominare il database '%1$@' in '%2$@'.";

/* error finding keychain item to edit informative message */
"An error occurred while trying to retrieve the Keychain item you're trying to edit. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Si è verificato un errore durante il tentativo di recuperare l'elemento Keychain che stai cercando di modificare. Riparare il portachiavi potrebbe risolvere questo problema, ma se non lo segnala al team Sequel Ace, fornendo il codice di errore %i.";

/* error updating keychain item informative message */
"An error occurred while trying to update the Keychain item. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Si è verificato un errore durante il tentativo di aggiornare l'elemento Keychain. Riparare il portachiavi potrebbe risolvere questo problema, ma se non lo segnala al team Sequel Ace, fornendo il codice di errore %i.";

/* mysql error occurred message */
"An error occurred" = "Si è verificato un errore";

/* MySQL table info retrieval error message */
"An error occurred retrieving table information.  MySQL said: %@" = "Si è verificato un errore nel recupero delle informazioni della tabella. MySQL ha detto: %@";

/* SQL encoding read error */
"An error occurred when reading the file, as it could not be read in the encoding you selected (%@).\n\nOnly %ld queries were executed." = "Si è verificato un errore durante la lettura del file, in quanto non poteva essere letto nella codifica che hai selezionato (%1$@).\n\nSono state eseguite solo interrogazioni %2$ld.";

/* CSV encoding read error */
"An error occurred when reading the file, as it could not be read using the encoding you selected (%@).\n\nOnly %ld rows were imported." = "Si è verificato un errore durante la lettura del file, in quanto non poteva essere letto usando la codifica che hai selezionato (%1$@).\n\nSono state importate solo le righe %2$ld.";

/* SQL read error, including detail from system */
"An error occurred when reading the file.\n\nOnly %ld queries were executed.\n\n(%@)" = "Si è verificato un errore durante la lettura del file. Le query\n\nSolo %1$ld sono state eseguite.\n\n(%2$@)";

/* CSV read error, including detail string from system */
"An error occurred when reading the file.\n\nOnly %ld rows were imported.\n\n(%@)" = "Si è verificato un errore durante la lettura del file. Sono state importate solo righe\n\n %1$ld .\n\n(%2$@)";

/* error adding field informative message */
"An error occurred when trying to add the field '%@' via\n\n%@\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di aggiungere il campo '%1$@' via\n\n%2$@\n\nMySQL ha detto: %3$@";

/* error changing field informative message */
"An error occurred when trying to change the field '%@' via\n\n%@\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di cambiare il campo '%1$@' via\n\n%2$@\n\nMySQL ha detto: %3$@";

/* error changing table collation informative message */
"An error occurred when trying to change the table collation to '%@'.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di cambiare l'ordinamento della tabella in '%1$@'.\n\nMySQL ha detto: %2$@";

/* error changing table encoding informative message */
"An error occurred when trying to change the table encoding to '%@'.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di cambiare la codifica della tabella in '%1$@'.\n\nMySQL ha detto: %2$@";

/* error changing table type informative message */
"An error occurred when trying to change the table type to '%@'.\n\nMySQL said: %@" = "Si è verificato un errore nel tentativo di cambiare il tipo di tabella in '%1$@'.\n\nMySQL ha detto: %2$@";

/* error changing table comment informative message */
"An error occurred when trying to change the table's comment to '%@'.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di cambiare il commento della tabella in '%1$@'.\n\nMySQL ha detto: %2$@";

/* an error occurred while analyzing the %@.\n\nMySQL said:%@ */
"An error occurred while analyzing the %@.\n\nMySQL said:%@" = "Si è verificato un errore durante l'analisi di %1$@.\n\nMySQL ha detto:%2$@";

/* an error occurred while fetching the optimized field type.\n\nMySQL said:%@ */
"An error occurred while fetching the optimized field type.\n\nMySQL said:%@" = "Si è verificato un errore durante il recupero del tipo di campo ottimizzato.\n\nMySQL ha detto:%@";

/* an error occurred while trying to flush the %@.\n\nMySQL said:%@ */
"An error occurred while flushing the %@.\n\nMySQL said:%@" = "Si è verificato un errore durante il flushing di %1$@.\n\nMySQL ha detto:%2$@";

/* sql import error message */
"An error occurred while importing SQL" = "Si è verificato un errore durante l'importazione di SQL";

/* an error occurred while trying to optimze the %@.\n\nMySQL said:%@ */
"An error occurred while optimzing the %@.\n\nMySQL said:%@" = "Si è verificato un errore durante l'ottimizzazione di %1$@.\n\nMySQL ha detto:%2$@";

/* an error occurred while performing the checksum on the %@.\n\nMySQL said:%@ */
"An error occurred while performing the checksum on %@.\n\nMySQL said:%@" = "Si è verificato un errore durante l'esecuzione del checksum su %1$@.\n\nMySQL ha detto:%2$@";

/* an error occurred while trying to repair the %@.\n\nMySQL said:%@ */
"An error occurred while repairing the %@.\n\nMySQL said:%@" = "Si è verificato un errore durante la riparazione di %1$@.\n\nMySQL ha detto:%2$@";

/* message of panel when retrieving information failed */
"An error occurred while retrieving information.\nMySQL said: %@" = "Si è verificato un errore durante il recupero delle informazioni.\nMySQL ha detto: %@";

/* error retrieving table information informative message */
"An error occurred while retrieving the information for table '%@'. Please try again.\n\nMySQL said: %@" = "Si è verificato un errore durante il recupero delle informazioni per la tabella '%1$@'. Riprova.\n\nMySQL ha detto: %2$@";

/* error retrieving table information informative message */
"An error occurred while retrieving the trigger information for table '%@'. Please try again.\n\nMySQL said: %@" = "Si è verificato un errore durante il recupero delle informazioni di attivazione per la tabella '%1$@'. Si prega di riprovare.\n\nMySQL ha detto: %2$@";

/* error adding new column informative message */
"An error occurred while trying to add the new column '%@' by\n\n%@.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di aggiungere la nuova colonna '%1$@' di\n\n%2$@.\n\nMySQL ha detto: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@' by\n\n%@.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di aggiungere la nuova tabella '%1$@' di\n\n%2$@.\n\nMySQL ha detto: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@'.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di aggiunta della nuova tabella '%1$@'.\n\nMySQL ha detto: %2$@";

/* error while trying to alter table message */
"An error occurred while trying to alter table '%@'.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di modifica della struttura della tabella '%1$@'.\n\nMySQL ha detto: %2$@";

/* an error occurred while trying to check the %@.\n\nMySQL said:%@ */
"An error occurred while trying to check the %@.\n\nMySQL said:%@" = "Si è verificato un errore durante il tentativo di controllare %1$@.\n\nMySQL ha detto:%2$@";

/* error deleting relation informative message */
"An error occurred while trying to delete the relation '%@'.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di eliminazione della relazione '%1$@'.\n\nMySQL ha detto: %2$@";

/* unable to get list of users informative message */
"An error occurred while trying to get the list of users. Please make sure you have the necessary privileges to perform user management, including access to the mysql.user table." = "Si è verificato un errore durante il tentativo di ottenere l'elenco degli utenti. Assicurati di avere i privilegi necessari per eseguire la gestione dell'utente, incluso l'accesso alla tabella mysql.utente.";

/* error importing table informative message */
"An error occurred while trying to import a table via: \n%@\n\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di importare una tabella via: \n%1$@\n\n\nMySQL ha detto: %2$@";

/* error moving field informative message */
"An error occurred while trying to move the field.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di spostare il campo.\n\nMySQL ha detto: %@";

/* error resetting auto_increment informative message */
"An error occurred while trying to reset AUTO_INCREMENT of table '%@'.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di ripristinare AUTO_INCREMENT della tabella '%1$@'.\n\nMySQL ha detto: %2$@";

/* error truncating table informative message */
"An error occurred while trying to truncate the table '%@'.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di troncare la tabella '%1$@'.\n\nMySQL ha detto: %2$@";

/* mysql error occurred informative message */
"An error occurred whilst trying to perform the operation.\n\nMySQL said: %@" = "Si è verificato un errore durante il tentativo di eseguire l'operazione.\n\nMySQL ha detto: %@";

/* mysql resource limits unsupported message */
"Resource Limits are not supported for your version of MySQL. Any Resouce Limits you specified have been discarded and not saved. MySQL said: %@" = "I limiti di risorse non sono supportati per la tua versione di MySQL. Tutti i limiti di rialzo specificati sono stati scartati e non salvati. MySQL ha detto: %@";

/* Export files creation error explanatory text */
"An unhandled error occurred when attempting to create %lu of the export files.  Please check the details and try again." = "Si è verificato un errore non gestito durante la creazione di %lu dei file di esportazione. Si prega di controllare i dettagli e riprovare.";

/* All export files creation error explanatory text */
"An unhandled error occurred when attempting to create each of the export files.  Please check the details and try again." = "Si è verificato un errore non gestito durante la creazione di ciascuno dei file di esportazione. Si prega di controllare i dettagli e riprovare.";

/* Export file creation error explanatory text */
"An unhandled error occurred when attempting to create the export file.  Please check the details and try again." = "Si è verificato un errore non gestito durante la creazione del file di esportazione. Si prega di controllare i dettagli e riprovare.";

/* ANALYZE one or more tables - result title */
"Analyze %@" = "Analizza %@";

/* analyze selected items menu item */
"Analyze Selected Items" = "Analizza Gli Elementi Selezionati";

/* analyze table menu item */
"Analyze Table" = "Analizza Tabella";

/* analyze table failed message */
"Analyze table failed." = "Tabella di analisi non riuscita.";

/* change table type informative message */
"Are you sure you want to change this table's type to %@?\n\nPlease be aware that changing a table's type has the potential to cause the loss of some or all of its data. This action cannot be undone." = "Sei sicuro di voler cambiare il tipo di tabella in %@?\n\nSi prega di essere consapevoli che cambiare il tipo di tabella ha il potenziale di causare la perdita di alcuni o di tutti i suoi dati. Questa azione non può essere annullata.";

/* clear global history list informative message */
"Are you sure you want to clear the global history list? This action cannot be undone." = "Sei sicuro di voler cancellare la cronologia globale? Questa azione non può essere annullata.";

/* clear history list for %@ informative message */
"Are you sure you want to clear the history list for %@? This action cannot be undone." = "Sei sicuro di voler cancellare la lista della cronologia di %@? Questa azione non può essere annullata.";

/* truncate tables informative message */
"Are you sure you want to delete ALL records in the selected tables? This operation cannot be undone." = "Sei sicuro di voler eliminare TUTTI i record nelle tabelle selezionate? Questa operazione non può essere annullata.";

/* truncate table informative message */
"Are you sure you want to delete ALL records in the table '%@'? This operation cannot be undone." = "Sei sicuro di voler eliminare TUTTI i record nella tabella '%@'? Questa operazione non può essere annullata.";

/* delete all rows informative message */
"Are you sure you want to delete all the rows from this table? This action cannot be undone." = "Sei sicuro di voler eliminare tutte le righe da questa tabella? Questa azione non può essere annullata.";

/* delete table/view informative message */
"Are you sure you want to delete the %@ '%@'? This operation cannot be undone." = "Sei sicuro di voler eliminare %1$@ '%2$@'? Questa operazione non può essere annullata.";

/* delete database informative message */
"Are you sure you want to delete the database '%@'? This operation cannot be undone." = "Sei sicuro di voler eliminare il database '%@'? Questa operazione non può essere annullata.";

/* delete database informative message */
"Are you sure you want to delete the favorite '%@'? This operation cannot be undone." = "Sei sicuro di voler eliminare il preferito '%@'? Questa operazione non può essere annullata.";

/* delete field informative message */
"Are you sure you want to delete the field '%@'? This action cannot be undone." = "Sei sicuro di voler eliminare il campo '%@'? Questa azione non può essere annullata.";

/* delete database informative message */
"Are you sure you want to delete the group '%@'? All groups and favorites within this group will also be deleted. This operation cannot be undone." = "Sei sicuro di voler eliminare il gruppo '%@'? Tutti i gruppi e i preferiti all'interno di questo gruppo saranno anche eliminati. Questa operazione non può essere annullata.";

/* delete index informative message */
"Are you sure you want to delete the index '%@'? This action cannot be undone." = "Sei sicuro di voler eliminare l'indice '%@'? Questa azione non può essere annullata.";

/* delete tables/views informative message */
"Are you sure you want to delete the selected %@? This operation cannot be undone." = "Sei sicuro di voler eliminare l' %@selezionato? Questa operazione non può essere annullata.";

/* delete rows informative message */
"Are you sure you want to delete the selected %ld rows from this table? This action cannot be undone." = "Sei sicuro di voler eliminare le righe %ld selezionate da questa tabella? Questa azione non può essere annullata.";

/* delete selected relation informative message */
"Are you sure you want to delete the selected relations? This action cannot be undone." = "Sei sicuro di voler eliminare le relazioni selezionate? Questa azione non può essere annullata.";

/* delete selected row informative message */
"Are you sure you want to delete the selected row from this table? This action cannot be undone." = "Sei sicuro di voler eliminare la riga selezionata da questa tabella? Questa azione non può essere annullata.";

/* delete selected trigger informative message */
"Are you sure you want to delete the selected triggers? This action cannot be undone." = "Sei sicuro di voler eliminare i trigger selezionati? Questa azione non può essere annullata.";

/* kill connection informative message */
"Are you sure you want to kill connection ID %lld?\n\nPlease be aware that continuing to kill this connection may result in data corruption. Please proceed with caution." = "Sei sicuro di voler terminare l'ID di connessione %lld?\n\nTieni presente che continuare a terminare questa connessione potrebbe causare la corruzione dei dati. Si prega di procedere con cautela.";

/* kill query informative message */
"Are you sure you want to kill the current query executing on connection ID %lld?\n\nPlease be aware that continuing to kill this query may result in data corruption. Please proceed with caution." = "Sei sicuro di voler uccidere la query in corso sull'ID di connessione %lld?\n\nTieni presente che continuare a uccidere questa interrogazione potrebbe causare la corruzione dei dati. Si prega di procedere con cautela.";

/* Bundle Editor : Remove-Bundle: remove dialog message */
"Are you sure you want to move the selected Bundle to the Trash and remove them respectively?" = "Sei sicuro di voler spostare il pacchetto selezionato nel cestino e rimuoverli rispettivamente?";

/* continue to print informative message */
"Are you sure you want to print the current content view of the table '%@'?\n\nIt currently contains %@ rows, which may take a significant amount of time to print." = "Sei sicuro di voler stampare la vista attuale del contenuto della tabella '%1$@'?\n\nAl momento contiene righe di %2$@ , che potrebbero richiedere molto tempo per stampare.";

/* remove all query favorites informative message */
"Are you sure you want to remove all of your saved query favorites? This action cannot be undone." = "Sei sicuro di voler rimuovere tutti i preferiti di query salvati? Questa azione non può essere annullata.";

/* remove all selected content filters informative message */
"Are you sure you want to remove all selected content filters? This action cannot be undone." = "Sei sicuro di voler rimuovere tutti i filtri di contenuto selezionati? Questa azione non può essere annullata.";

/* remove all selected query favorites informative message */
"Are you sure you want to remove all selected query favorites? This action cannot be undone." = "Sei sicuro di voler rimuovere tutti i preferiti di query selezionati? Questa azione non può essere annullata.";

/* Table Info Section : current value of auto_increment */
"auto_increment: %@" = "auto_incremento: %@";

/* Encoding autodetect menu item */
"Autodetect" = "Rilevamento Automatico";

/* background label for color table (Prefs > Editor) */
"Background" = "Sfondo";

/* backtick quote label for color table (Prefs > Editor) */
"Backtick Quote" = "Preventivo Backtick";

/* bash error */
"BASH Error" = "Errore BASH";

/* toolbar item label for switching to the Table Content tab */
"Browse & Edit Table Content" = "Sfoglia E Modifica Contenuto Della Tabella";

/* build label */
"build" = "build";

/* build label */
"Build" = "Costruisci";

/* bundle editor menu item label */
"Bundle Editor" = "Editor Bundle";

/* bundle error */
"Bundle Error" = "Errore Bundle";

/* bundles menu item label */
"Bundles" = "Bundles";

/* Bundle Editor : Outline View : 'BUNDLES' item */
"BUNDLES" = "BUNDLES";

/* Bundle Editor : Outline View : Menu Category item : tooltip */
"Bundles in category %@" = "Pacchetti nella categoria %@";

/* bundles installation error */
"Bundles Installation Error" = "Errore Di Installazione Pacchetti";

/* bzip2 compression export summary - within a sentence */
"bzip2 compression" = "compressione bzip2";

/* description of geometry */
"Can store a single spatial value of types POINT, LINESTRING or POLYGON. Spatial support in MySQL is based on the OpenGIS Geometry Model." = "Può memorizzare un singolo valore spaziale di tipi POINT, LINESTRING o POLYGON. Il supporto spaziale in MySQL è basato sul modello di geometria OpenGIS.";

/* Bundle Editor : Remove-Bundle: cancel button
 cancel
 cancel button
 Open Files : Bundle : Already-Installed : Cancel button
 Preferences : Network : Custom SSH client : warning dialog : cancel button
 shutdown server : confirmation dialog : cancel button */
"Cancel" = "Annulla";

/* sql import : charset error alert : cancel button */
"Cancel Import" = "Annulla Importazione";

/* cancelling task status message */
"Cancelling..." = "Annullamento...";

/* empty query informative message */
"Cannot save an empty query." = "Impossibile salvare una query vuota.";

/* caret label for color table (Prefs > Editor) */
"Caret" = "Corsivo";

/* change button */
"Change" = "Cambia";

/* change focus to table list menu item */
"Change Focus to Table List" = "Cambia il Focus alla lista delle tabelle";

/* change table type message */
"Change table type" = "Cambia tipo tabella";

/* unsaved changes informative message */
"Changes have been made, which will be lost if this window is closed. Are you sure you want to continue" = "Sono state apportate modifiche, che andranno perse se questa finestra è chiusa. Sei sicuro di voler continuare";

/* quitting app informal alert title */
"Close the app?" = "Chiudi l'app?";

/* quitting app informal alert body */
"Are you sure you want to quit the app?" = "Sei sicuro di voler chiudere l'app?";

/* character set client: %@ */
"character set client: %@" = "client set caratteri: %@";

/* CHECK one or more tables - result title */
"Check %@" = "Controlla %@";

/* check of all selected items successfully passed message */
"Check of all selected items successfully passed." = "Verifica di tutti gli elementi selezionati con successo.";

/* check option: %@ */
"check option: %@" = "opzione di controllo: %@";

/* check selected items menu item */
"Check Selected Items" = "Controlla Gli Elementi Selezionati";

/* check table menu item */
"Check Table" = "Controlla la Tabella";

/* check table failed message */
"Check table failed." = "Controllo della Tabella fallito.";

/* check table successfully passed message */
"Check table successfully passed." = "Controllo della Tabella effettuato con successo.";

/* check view menu item */
"Check View" = "Controllo della Vista";

/* checking field data for editing task description */
"Checking field data for editing..." = "Controllo dei campi dei dati per la modifica...";

/* checksum %@ message */
"Checksum %@" = "Checksum %@";

/* checksum selected items menu item */
"Checksum Selected Items" = "Checksum Elementi Selezionati";

/* checksum table menu item */
"Checksum Table" = "Tabella Checksum";

/* Checksums of %@ message */
"Checksums of %@" = "Checksum di %@";

/* menu item for choose db */
"Choose Database..." = "Scegli Database...";

/* cancelling export cleaning up message */
"Cleaning up..." = "Pulizia in corso...";

/* clear button */
"Clear" = "Pulisci";

/* toolbar item for clear console */
"Clear Console" = "Pulisci Console";

/* clear global history menu item title */
"Clear Global History" = "Cancella La Cronologia Globale";

/* clear history for %@ menu title */
"Clear History for %@" = "Cancella cronologia per %@";

/* clear history message */
"Clear History?" = "Cancellare La Cronologia?";

/* tooltip for toolbar item for clear console */
"Clear the console which shows all MySQL commands performed by Sequel Ace" = "Cancella la console che mostra tutti i comandi MySQL eseguiti da Sequel Ace";

/* clear the document-based history list tooltip message */
"Clear the document-based history list" = "Pulisce l'elenco della cronologia basato su documenti";

/* clear the global history list tooltip message */
"Clear the global history list" = "Pulisce l'elenco globale della cronologia";

/* Close menu item */
"Close" = "Chiudi";

/* close tab context menu item */
"Close Tab" = "Chiudi Scheda";

/* Close Window menu item */
"Close Window" = "Chiudi Finestra";

/* collation label (Navigator) */
"Collation" = "Collazione";

/* collation connection: %@ */
"collation connection: %@" = "connessione di collazione: %@";

/* comment label */
"Comment" = "Commento";

/* Title of action menu item to comment line */
"Comment Line" = "Linea di Commento";

/* Title of action menu item to comment selection */
"Comment Selection" = "Selezione Commento";

/* connect button */
"Connect" = "Connetti";

/* Connect via socket button */
"Connect via socket" = "Connettiti tramite socket";

/* connection established message */
"Connected" = "Connesso";

/* description for connected notification */
"Connected to %@" = "Connesso ad %@";

/* message of panel when connection to db failed */
"Connected to host, but unable to connect to database %@.\n\nBe sure that the database exists and that you have the necessary privileges.\n\nMySQL said: %@" = "Connesso all'host, ma non è possibile connettersi al database %1$@.\n\nAssicurati che il database esista e che tu abbia i privilegi necessari.\n\nMySQL ha detto: %2$@";

/* Generic connecting very short status message */
"Connecting..." = "Connessione...";

/* window title string indicating that sp is connecting */
"Connecting…" = "Connessione…";

/* error while reading connection data file */
"Connection data file couldn't be read." = "Impossibile leggere il file dei dati di connessione.";

/* message error while reading connection data file and suggesting to save it under a differnet name */
"Connection data file %@ couldn't be read. Please try to save the document under a different name." = "Il file dati di connessione %@ non può essere letto. Prova a salvare il documento con un nome diverso.";

/* connection failed title */
"Connection failed!" = "Connessione fallita!";

/* Connection file is encrypted */
"Connection file is encrypted" = "Il file di connessione è cifrato";

/* Connection success very short status message */
"Connection succeeded" = "Connessione riuscita";

/* Console */
"Console" = "Console";

/* Console : Save as : Initial filename */
"ConsoleLog" = "ConsoleLog";

/* toolbar item label for switching to the Table Content tab */
"Content" = "Contenuto";

/* content filter clause is empty tooltip. */
"Content Filter clause is empty." = "La clausola Content Filter è vuota.";

/* continue button
 Continue button title */
"Continue" = "Continua";

/* continue to print message */
"Continue to print?" = "Continuare a stampare?";

/* Copy as RTF */
"Copy as RTF" = "Copia come RTF";

/* copy create func syntax menu item
 Table List : Context Menu : copy CREATE FUNCTION syntax */
"Copy Create Function Syntax" = "Copia Crea Sintassi Funzione";

/* copy create proc syntax menu item
 Table List : Context Menu : Copy CREATE PROCEDURE syntax
 Table List : Gear Menu : Copy CREATE PROCEDURE syntax */
"Copy Create Procedure Syntax" = "Copia Crea Sintassi Della Procedura";

/* copy create syntaxes menu item
 Table List : Context Menu : Copy CREATE syntax (multiple selection)
 Table List : Gear Menu : Copy CREATE syntax (multiple selection) */
"Copy Create Syntaxes" = "Copia Crea Sintassi";

/* copy create table syntax menu item
 Table List : Context Menu : Copy CREATE syntax (single table)
 Table List : Gear Menu : Copy CREATE syntax (single table) */
"Copy Create Table Syntax" = "Copia Crea Sintassi Della Tabella";

/* copy create view syntax menu item
 Table List : Context Menu : Copy CREATE view statement
 Table List : Gear Menu : Copy CREATE view statement */
"Copy Create View Syntax" = "Copia Crea Sintassi Vista";

/* copy server variable name menu item */
"Copy Variable Name" = "Copia Nome Variabile";

/* copy server variable names menu item */
"Copy Variable Names" = "Copia Nomi Variabili";

/* copy server variable value menu item */
"Copy Variable Value" = "Copia Valore Variabile";

/* copy server variable values menu item */
"Copy Variable Values" = "Copia Valori Variabili";

/* Procedure/function export permission error */
"Could not export the %@ '%@' because of a permissions error.\n" = "Impossibile esportare l' %1$@ '%2$@' a causa di un errore di autorizzazioni.\n";

/* Error when we can't parse/split file as CSV */
"Could not parse file as CSV" = "Impossibile analizzare il file come CSV";

/* message when database selection failed */
"Could not select database" = "Impossibile selezionare il database";

/* Alter Database : Query Failed ($1 = mysql error message) */
"Couldn't alter database.\nMySQL said: %@" = "Impossibile modificare il database.\nMySQL ha detto: %@";

/* Couldn't copy default themes to Application Support Theme folder!\nError: %@ */
"Couldn't copy default themes to Application Support Theme folder!\nError: %@" = "Impossibile copiare i temi predefiniti nella cartella del tema Supporto applicazioni!\nErrore: %@";

/* message of panel when table cannot be created */
"Couldn't create '%@'.\nMySQL said: %@" = "Impossibile creare '%1$@'.\nMySQL ha detto: %2$@";

/* Couldn't create Application Support Bundle folder!\nError: %@ */
"Couldn't create Application Support Bundle folder!\nError: %@" = "Impossibile creare la cartella Bundle Supporto applicazioni!\nErrore: %@";

/* Couldn't create Application Support Theme folder!\nError: %@ */
"Couldn't create Application Support Theme folder!\nError: %@" = "Impossibile creare la cartella Tema Supporto Applicazioni!\nErrore: %@";

/* message of panel when creation of db failed */
"Couldn't create database.\nMySQL said: %@" = "Impossibile creare il database.\nMySQL ha detto: %@";

/* message of panel when an item cannot be deleted */
"Couldn't delete '%@'.\n\nMySQL said: %@" = "Impossibile eliminare '%1$@'.\n\nMySQL ha detto: %2$@";

/* message of panel when an item cannot be deleted including informative message about using force deletion */
"Couldn't delete '%@'.\n\nSelecting the 'Force delete' option may prevent this issue, but may leave the database in an inconsistent state.\n\nMySQL said: %@" = "Impossibile eliminare '%1$@'.\n\nSelezionando l'opzione 'Forza eliminazione' potrebbe evitare questo problema, ma potrebbe lasciare il database in uno stato incoerente.\n\nMySQL ha detto: %2$@";

/* message of panel when field cannot be deleted */
"Couldn't delete field %@.\nMySQL said: %@" = "Impossibile eliminare il campo %1$@.\nMySQL ha detto: %2$@";

/* message when deleteing all rows failed */
"Couldn't delete rows.\n\nMySQL said: %@" = "Impossibile eliminare le righe.\n\nMySQL ha detto: %@";

/* message of panel when deleting db failed */
"Couldn't delete the database.\nMySQL said: %@" = "Impossibile eliminare il database.\nMySQL ha detto: %@";

/* message of panel when an item cannot be renamed */
"Couldn't duplicate '%@'.\nMySQL said: %@" = "Impossibile duplicare '%1$@'.\nMySQL ha detto: %2$@";

/* message of panel when flushing privs failed */
"Couldn't flush privileges.\nMySQL said: %@" = "Non è stato possibile risciacquare i privilegi.\nMySQL ha detto: %@";

/* message of panel when table information cannot be retrieved */
"Couldn't get create syntax.\nMySQL said: %@" = "Impossibile creare la sintassi.\nMySQL ha detto: %@";

/* Custom Query result editing error - could not identify a corresponding column */
"Couldn't identify field origin unambiguously. The column '%@' contains data from more than one table." = "Impossibile identificare il campo di origine senza ambiguità. La colonna '%@' contiene dati da più di una tabella.";

/* message of panel when loading of row failed */
"Couldn't load the row. Reload the table to be sure that the row exists and use a primary key for your table." = "Impossibile caricare la riga. Ricarica la tabella per essere sicuro che la riga esista e usa una chiave primaria per la tabella.";

/* Couldn't read the file content of */
"Couldn't read the file content of" = "Impossibile leggere il contenuto del file";

/* text shown if an error occurred while sorting the result table */
"Couldn't sort column." = "Impossibile ordinare la colonna.";

/* message of panel when sorting of table failed */
"Couldn't sort table. MySQL said: %@" = "Impossibile ordinare la tabella. MySQL ha detto: %@";

/* message of panel when error while updating field to db */
"Couldn't write field.\nMySQL said: %@" = "Impossibile scrivere il campo.\nMySQL ha detto: %@";

/* create syntax for table comment */
"Create syntax for" = "Crea sintassi per";

/* Create syntax label */
"Create syntax for %@ '%@'" = "Crea sintassi per %1$@ '%2$@'";

/* Create syntaxes for selected items label */
"Create syntaxes for selected items" = "Crea sintassi per gli elementi selezionati";

/* Table Info Section : table create options */
"create_options: %@" = "create_options: %@";

/* created: %@
 Table Info Section : time+date table was created at */
"created: %@" = "creato: %@";

/* description of polygon */
"Creates a surface by combining one LinearRing (ie. a LineString that is closed and simple) as the outside boundary with zero or more inner LinearRings acting as \"holes\"." = "Crea una superficie combinando un LinearRing (es. una LineString chiusa e semplice) come il confine esterno con zero o più LinearRings interni che agiscono come \"buchi\".";

/* Creating table task string */
"Creating %@..." = "Creazione %@...";

/* Bundle Editor : Fallback Input source dropdown : 'current line' item */
"Current Line" = "Riga Corrente";

/* Bundle Editor : Fallback Input source dropdown : 'current query' item */
"Current Query" = "Query Attuale";

/* header for current selection in filtered list */
"CURRENT SELECTION" = "SELEZIONE ATTUALE";

/* Bundle Editor : Fallback Input source dropdown : 'current word' item */
"Current Word" = "Parola Attuale";

/* SSH connection : debug header with user-defined ssh binary */
"Custom SSH binary enabled. Disable in Preferences to rule out incompatibilities!" = "Binario SSH personalizzato abilitato. Disabilita nelle Preferenze per escludere le incompatibilità!";

/* customize file name label */
"Customize Filename (%@)" = "Personalizza Nome File (%@)";

/* data access: %@ (%@) */
"data access: %@ (%@)" = "accesso ai dati: %1$@ (%2$@)";

/* Bundle Editor : Scope dropdown : 'data table' item
 data table menu item label */
"Data Table" = "Tabella Dei Dati";

/* Bundle Editor : Outline View : 'Data Table' item : tooltip */
"Data Table Scope\ncommands will run on the Content and Query data tables" = "I comandi\ndella tabella dati verranno eseguiti sulle tabelle dei dati contenuti e query";

/* export filename database token
 export header database label
 export source
 Table Structure : Collation dropdown : 'item is the same as the collation of database' marker
 Table Structure : Encoding dropdown : 'item is database default' marker */
"Database" = "Database";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'database changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'database changed' item */
"Database changed" = "Database modificato";

/* message of panel when no db name is given */
"Database must have a name." = "Il database deve avere un nome.";

/* databsse rename unsupported message */
"Database Rename Unsupported" = "Rinomina Database Non Supportata";

/* export filename date token */
"Date" = "Data";

/* export filename date token */
"Day" = "Giorno";

/* Add Table Sheet : Collation : Default (unknown)
 Collation Dropdown : Default (unknown)
 default label
 Table Structure : Collation dropdown : 'item is the same as the default collation of the row's charset' marker */
"Default" = "Predefinito";

/* Add Table : Collation : Default ($1 = collation name)
 Charset Dropdown : Default item ($1 = charset name)
 Collation Dropdown : Default ($1 = collation name)
 Collation Dropdown : Default collation for given charset ($1 = collation name)
 New Table Sheet : Table Engine Dropdown : Default */
"Default (%@)" = "Predefinito (%@)";

/* default bundles update */
"Default Bundles Update" = "Aggiornamento Pacchetti Predefinito";

/* import : csv field mapping : field default value */
"Default: %@" = "Predefinito: %@";

/* Query snippet default value placeholder */
"default_value" = "default_value";

/* definer label (Navigator) */
"Definer" = "Definitore";

/* definer: %@ */
"definer: %@" = "definitore: %@";

/* description of enum */
"Defines a list of members, of which every field can use at most one. Values are sorted by their index number (starting at 0 for the first member)." = "Definisce un elenco di membri, di cui ogni campo può usare al massimo uno. I valori sono ordinati in base al loro numero indice (a partire da 0 per il primo membro).";

/* delete button */
"Delete" = "Elimina";

/* delete table/view message */
"Delete %@ '%@'?" = "Eliminare %1$@ '%2$@'?";

/* table structure : indexes : delete index : error 1553 : delete index and FK button */
"Delete Both" = "Elimina Entrambi";

/* delete database message */
"Delete database '%@'?" = "Eliminare il database '%@'?";

/* delete database message */
"Delete favorite '%@'?" = "Eliminare il preferito '%@'?";

/* delete field message */
"Delete field '%@'?" = "Eliminare il campo '%@'?";

/* delete func menu title */
"Delete Function" = "Elimina Funzione";

/* delete functions menu title */
"Delete Functions" = "Elimina Funzioni";

/* delete database message */
"Delete group '%@'?" = "Eliminare il gruppo '%@'?";

/* delete index message */
"Delete index '%@'?" = "Eliminare indice '%@'?";

/* delete items menu title */
"Delete Items" = "Elimina Oggetti";

/* delete proc menu title */
"Delete Procedure" = "Elimina Procedura";

/* delete procedures menu title */
"Delete Procedures" = "Cancellare Le Procedure";

/* delete relation menu item */
"Delete Relation" = "Elimina Relazione";

/* delete relation message */
"Delete relation" = "Elimina relazione";

/* delete relations menu item */
"Delete Relations" = "Elimina Relazioni";

/* delete row menu item singular */
"Delete Row" = "Elimina Riga";

/* delete rows menu item plural */
"Delete Rows" = "Elimina Righe";

/* delete rows message */
"Delete rows?" = "Eliminare le righe?";

/* delete tables/views message */
"Delete selected %@?" = "Eliminare l' %@ selezionato?";

/* delete selected row message */
"Delete selected row?" = "Eliminare la riga selezionata?";

/* delete table menu title */
"Delete Table..." = "Elimina Tabella...";

/* delete tables menu title */
"Delete Tables" = "Elimina Tabelle";

/* delete trigger menu item */
"Delete Trigger" = "Elimina Trigger";

/* delete trigger message */
"Delete trigger" = "Elimina trigger";

/* delete triggers menu item */
"Delete Triggers" = "Elimina Attivatori";

/* delete view menu title */
"Delete View" = "Elimina Vista";

/* delete views menu title */
"Delete Views" = "Elimina Viste";

/* Preferences : Network : SSL Chiper suites : List seperator */
"Disabled Cipher Suites" = "Suite Cipher Disabilitate";

/* force table deltion button text tooltip */
"Disables foreign key checks (FOREIGN_KEY_CHECKS) before deletion and re-enables them afterwards." = "Disabilita i controlli delle chiavi esterne (FOREIGN_KEY_CHECKS) prima della cancellazione e li riabilita in seguito.";

/* discard changes button */
"Discard changes" = "Scarta le modifiche";

/* description for disconnected notification */
"Disconnected from %@" = "Disconnesso da %@";

/* do update operator tooltip */
"Do UPDATE where field contents match" = "Fare AGGIORNAMENTO dove il contenuto del campo corrisponde";

/* message of panel asking for confirmation for loading large text into the query editor */
"Do you really want to load a SQL file with %@ of data into the Query Editor?" = "Vuoi davvero caricare un file SQL con %@ di dati nell'editor di query?";

/* message of panel asking for confirmation for inserting large text from dragging action */
"Do you really want to proceed with %@ of data?" = "Vuoi davvero procedere con %@ di dati?";

/* shutdown server : confirmation dialog : title */
"Do you really want to shutdown the server?" = "Vuoi davvero spegnere il server?";

/* dtd identifier label (Navigator) */
"DTD Identifier" = "Identificatore DTD";

/* sql export dump of table label */
"Dump of table" = "Dump della tabella";

/* sql export dump of view label */
"Dump of view" = "Dump della vista";

/* text showing that app is writing dump */
"Dumping..." = "Scaricamento...";

/* duplicate object message */
"Duplicate %@ '%@' to:" = "Duplica %1$@ '%2$@' a:";

/* duplicate func menu title */
"Duplicate Function..." = "Duplica Funzione...";

/* duplicate host message */
"Duplicate Host" = "Duplica Host";

/* duplicate proc menu title */
"Duplicate Procedure..." = "Duplica Procedura...";

/* duplicate table menu title */
"Duplicate Table..." = "Duplica Tabella...";

/* duplicate user message */
"Duplicate User" = "Duplica Utente";

/* duplicate view menu title */
"Duplicate View..." = "Duplica Vista...";

/* partial copy database support informative message */
"Duplicating the database '%@' is only partially supported as it contains objects other than tables (i.e. views, procedures, functions, etc.), which will not be copied.\n\nWould you like to continue?" = "Duplicare il database '%@' è solo parzialmente supportato in quanto contiene oggetti diversi dalle tabelle (es. visualizzazioni, procedure, funzioni, ecc.), che non verranno copiate.\n\nVuoi continuare?";

/* edit filter */
"Edit Filters…" = "Modifica Filtri…";

/* Edit row button */
"Edit row" = "Modifica riga";

/* toolbar item label for switching to the Table Structure tab */
"Edit Table Structure" = "Modifica Struttura Della Tabella";

/* edit theme list label */
"Edit Theme List…" = "Modifica Elenco Temi…";

/* edit user-defined filter */
"Edit user-defined Filters…" = "Modifica Filtri definiti dall'utente…";

/* empty query message */
"Empty query" = "Interrogazione vuota";

/* MySQL search language code - eg in http://search.mysql.com/search?q=select&site=refman-50&lr=lang_en */
"en" = "it";

/* encoding label (Navigator) */
"Encoding" = "Codifica";

/* Table Info Section : $1 = table charset */
"encoding: %1$@" = "codifica: %1$@";

/* Table Info Section : $1 = table charset, $2 = table collation */
"encoding: %1$@ (%2$@)" = "codifica: %1$@ (%2$@)";

/* Table Info Section : Table Engine */
"engine: %@" = "motore: %@";

/* enter connection details label */
"Enter connection details below, or choose a favorite" = "Inserisci i dettagli di connessione qui sotto, o scegli un preferito";

/* SSH key password prompt */
"Enter your password for the SSH key\n\"%@\"" = "Inserisci la tua password per la chiave SSH\n'%@";

/* Bundle Editor : Fallback Input source dropdown : 'entire content' item
 Bundle Editor : Scope=Field : Input source dropdown: 'entire content' item */
"Entire Content" = "Intero Contenuto";

/* Bundle Editor : Copy-Command-Error : error dialog title
 error */
"Error" = "Errore";

/* error adding field message */
"Error adding field" = "Errore nell'aggiungere il campo";

/* error adding new column message */
"Error adding new column" = "Errore nell'aggiungere una nuova colonna";

/* error adding new table message */
"Error adding new table" = "Errore nell'aggiungere una nuova tabella";

/* error adding password to keychain message */
"Error adding password to Keychain" = "Errore nell'aggiungere la password al portachiavi";

/* error changing field message */
"Error changing field" = "Errore nel cambiare il campo";

/* error changing table collation message */
"Error changing table collation" = "Errore nel cambiare l'ordinamento della tabella";

/* error changing table comment message */
"Error changing table comment" = "Errore nel cambiare il commento della tabella";

/* error changing table encoding message */
"Error changing table encoding" = "Errore nel cambiare la codifica della tabella";

/* error changing table type message */
"Error changing table type" = "Errore nel cambiare il tipo di tabella";

/* error creating relation message */
"Error creating relation" = "Errore nella creazione della relazione";

/* error creating trigger message */
"Error creating trigger" = "Errore nella creazione del trigger";

/* error for message */
"Error for" = "Errore per";

/* error for bash command ($1), $2=message */
"Error for “%1$@”:\n%2$@" = "Errore per «%1$@»:\n%2$@";

/* error moving field message */
"Error moving field" = "Errore nel spostare il campo";

/* error occurred */
"error occurred" = "errore verificatosi";

/* error reading import file */
"Error reading import file." = "Errore nella lettura del file di importazione.";

/* error finding keychain item to edit message */
"Error retrieving Keychain item to edit" = "Errore nel recuperare l'elemento portachiavi da modificare";

/* error retrieving table information message */
"Error retrieving table information" = "Errore nel recupero delle informazioni della tabella";

/* error retrieving trigger information message */
"Error retrieving trigger information" = "Errore nel recupero delle informazioni del trigger";

/* error truncating table message */
"Error truncating table" = "Errore nel troncare la tabella";

/* error updating keychain item message */
"Error updating Keychain item" = "Errore nell'aggiornamento dell'elemento portachiavi";

/* error while analyzing selected items message */
"Error while analyzing selected items" = "Errore durante l'analisi degli elementi selezionati";

/* error while checking selected items message */
"Error while checking selected items" = "Errore durante il controllo degli elementi selezionati";

/* error while converting color scheme data */
"Error while converting color scheme data" = "Errore durante la conversione dei dati dello schema di colori";

/* error while converting connection data */
"Error while converting connection data" = "Errore durante la conversione dei dati di connessione";

/* Content filters could not be converted to plist upon export - message title (ContentFilterManager) */
"Error while converting content filter data" = "Errore durante la conversione dei dati del filtro contenuti";

/* error while converting query favorite data */
"Error while converting query favorite data" = "Errore durante la conversione dei dati preferiti della query";

/* error while converting session data */
"Error while converting session data" = "Errore durante la conversione dei dati della sessione";

/* Error while deleting field */
"Error while deleting field" = "Errore durante l'eliminazione del campo";

/* Bundle Editor : Copy-Command-Error : Copying failed error message */
"Error while duplicating Bundle content." = "Errore durante la duplicazione del contenuto del pacchetto.";

/* error while executing javascript bash command */
"Error while executing JavaScript BASH command" = "Errore durante l'esecuzione del comando JavaScript BASH";

/* error while fetching the optimized field type message */
"Error while fetching the optimized field type" = "Errore durante il recupero del tipo di campo ottimizzato";

/* error while flushing selected items message */
"Error while flushing selected items" = "Errore durante il flushing degli elementi selezionati";

/* error while importing table message */
"Error while importing table" = "Errore durante l'importazione della tabella";

/* Open Files : Bundle : Install-Error : error dialog title
 Open Files : Bundle : UUID : Error dialog title */
"Error while installing Bundle" = "Errore durante l'installazione del Bundle";

/* error while installing color theme file */
"Error while installing color theme file" = "Errore durante l'installazione del file tema colore";

/* Bundle Editor : Trash-Bundle(s)-Error : error dialog title
 error while moving %@ to trash
 Open Files : Bundle : Already-Installed : Delete-Old-Error : Could not delete old bundle before installing new version. */
"Error while moving %@ to Trash." = "Errore durante lo spostamento di %@ nel cestino.";

/* error while optimizing selected items message */
"Error while optimizing selected items" = "Errore durante l'ottimizzazione degli elementi selezionati";

/* error while parsing CREATE TABLE syntax */
"Error while parsing CREATE TABLE syntax" = "Errore durante l'analisi della sintassi CREATE TABLE";

/* error while reading connection data file */
"Error while reading connection data file" = "Errore durante la lettura del file dati di connessione";

/* error while reading data file */
"Error while reading data file" = "Errore durante la lettura del file dati";

/* error while repairing selected items message */
"Error while repairing selected items" = "Errore durante la riparazione degli elementi selezionati";

/* Bundle Editor : Save-Bundle-Error : error dialog title */
"Error while saving the Bundle." = "Errore durante il salvataggio del Bundle.";

/* Bundle Editor : Save-and-Close-Error : error dialog title */
"Error while saving %@." = "Errore nel salvataggio di %@.";

/* Errors title */
"Errors" = "Errori";

/* Mysql Help Viewer : Help Topic: Example section title */
"Example:" = "Esempio:";

/* Bundle Editor : BLOB dropdown : 'exclude BLOB' item */
"exclude BLOB" = "escludi BLOB";

/* execution privilege label (Navigator) */
"Execution Privilege" = "Privilegio Esecuzione";

/* execution privilege: %@ */
"execution privilege: %@" = "privilegio di esecuzione: %@";

/* execution stopped message */
"Execution stopped!\n" = "Esecuzione interrotta!\n";

/* export selected favorites menu item */
"Export Selected..." = "Esporta Selezionata...";

/* text showing that the application is importing a supplied format */
"Exporting %@" = "Esportazione Di %@";

/* text showing that the application is exporting a Dot file */
"Exporting Dot File" = "Esportazione Del File Dot";

/* text showing that the application is exporting SQL */
"Exporting SQL" = "Esportazione SQL";

/* extra label (Navigator) */
"Extra" = "Extra";

/* table structure : indexes : delete index : no columns error : title */
"Failed to remove index '%@'" = "Impossibile rimuovere l'indice '%@'";

/* fatal error */
"Fatal Error" = "Errore Fatale";

/* export filename favorite name token */
"Favorite" = "Preferito";

/* favorites label */
"Favorites" = "Preferiti";

/* favorites export error message */
"Favorites export error" = "Errore esportazione preferiti";

/* favorites import error message */
"Favorites import error" = "Errore importazione preferiti";

/* export label showing that the app is fetching data */
"Fetching data..." = "Recupero dati...";

/* fetching database structure data in progress */
"fetching database structure data in progress" = "recupero dei dati della struttura del database in corso";

/* fetching database structure in progress */
"fetching database structure in progress" = "recupero della struttura del database in corso";

/* fetching table data for completion in progress message */
"fetching table data…" = "recupero dati tabella…";

/* popup menuitem for field (showing only if disabled) */
"field" = "campo";

/* Field */
"Field" = "Campo";

/* Query result editing error - could not match row being edited uniquely */
"Field is not editable. Couldn't identify field origin unambiguously (%ld matches)." = "Il campo non è modificabile. Impossibile identificare l'origine del campo in modo univoco (%ld corrisponde).";

/* field is not editable due to no table/database */
"Field is not editable. Field has no or multiple table or database origin(s)." = "Il campo non è modificabile. Il campo non ha né origini di tabella o database multiple.";

/* Custom Query result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload data, check encoding, or try to add\na primary key field or more fields\nin your SELECT statement for table '%@'\nto identify field origin unambiguously." = "Il campo non è modificabile. Nessun record corrispondente trovato.\nRicarica i dati, controlla la codifica, o prova ad aggiungere\nun campo chiave primario o più campi\nnella tua istruzione SELECT per la tabella '%@'\nper identificare l'origine del campo senza ambiguità.";

/* Table Content result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload table, check the encoding, or try to add\na primary key field or more fields\nin the view declaration of '%@' to identify\nfield origin unambiguously." = "Il campo non è modificabile. Nessun record corrispondente trovato.\nRicarica tabella, controlla la codifica, o prova ad aggiungere\nun campo chiave primario o più campi\nnella dichiarazione di visualizzazione di '%@' per identificare in modo univoco l'origine del campo\n.";

/* error while reading data file */
"File couldn't be read." = "Il file non può essere letto.";
"File couldn't be read: %@\n\nIt will be deleted." = "Il file non può essere letto: %@\n\nSarà eliminato.";

/* File read error title (Import Dialog) */
"File read error" = "Errore di lettura file";

/* All export files already exist explanatory text */
"Files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "I file con gli stessi nomi esistono già nella cartella di destinazione. Sostituirli sovrascriverà il loro contenuto corrente.";

/* filter label */
"Filter" = "Filtro";

/* apply filter label */
"Apply Filter(s)" = "Applica Filtro(i)";

/* filter tables menu item */
"Filter Tables" = "Filtra Tabelle";

/* export source */
"Filtered table content" = "Contenuto della tabella filtrata";

/* filtering failed. please try again. tooltip */
"Filtering failed. Please try again." = "Filtraggio non riuscito. Riprova.";

/* Filtering table task description */
"Filtering table..." = "Filtrando la tabella...";

/* description for finished exporting notification */
"Finished exporting to %@" = "Esportazione terminata ad %@";

/* description for finished importing notification */
"Finished importing %@" = "Importazione di %@ terminata";

/* FLUSH one or more tables - result title */
"Flush %@" = "Colore %@";

/* flush selected items menu item */
"Flush Selected Items" = "Flush Selected Items";

/* flush table menu item */
"Flush Table" = "Tabella Di Flush";

/* flush table failed message */
"Flush table failed." = "Tabella di scarico non riuscita.";

/* flush view menu item */
"Flush View" = "Vista A Flush";

/* title of panel when successfully flushed privs */
"Flushed Privileges" = "Privilegi Di Flushed";

/* For BIT fields only “1” or “0” are allowed. */
"For BIT fields only “1” or “0” are allowed." = "Per i campi BIT sono ammessi solo “1” o “0”.";

/* force table deletion button text */
"Force delete (disables integrity checks)" = "Forza eliminazione (disabilita controlli di integrità)";

/* full text index menu item title */
"FULLTEXT" = "FULLTEXT";

/* function */
"function" = "funzione";

/* header for function info pane */
"FUNCTION INFORMATION" = "INFORMAZIONI SULLA FUNZIONE";

/* functions */
"functions" = "funzioni";

/* Bundle Editor : Scope dropdown : 'general' item
 general menu item label
 general preference pane name */
"General" = "Generale";

/* general preference pane tooltip */
"General Preferences" = "Preferenze Generali";

/* Bundle Editor : Outline View : 'General' item : tooltip */
"General Scope\ncommands will run application-wide" = "I comandi General Scope\neseguiranno a livello di applicazione";

/* generating print document status message */
"Generating print document..." = "Generazione documento di stampa...";

/* export header generation time label */
"Generation Time" = "Tempo Di Generazione";

/* Content Filter Manager : Filter Entry List: 'Global' Header
 Query Favorites : List : Section Heading : global query favorites */
"Global" = "Globale";

/* Query Favorites : List : Section Heading : global : tooltip */
"Globally stored favorites" = "Preferiti memorizzati globalmente";

/* Preferences : Notifications : status text : notification setup */
"Notifications are delivered via the Notification Center." = "Le notifiche sono consegnate tramite il Centro Notifiche.";

/* Gzip compression export summary - within a sentence */
"Gzip compression" = "Compressione gzip";

/* MySQL Help Viewer : Results list : Page title */
"Help topics for %@" = "Argomenti di aiuto per %@";

/* hide console */
"Hide Console" = "Nascondi Console";

/* hide navigator */
"Hide Navigator" = "Hide Navigator";

/* hide tab bar */
"Hide Tab Bar" = "Nascondi Barra Delle Schede";

/* Hide Toolbar menu item */
"Hide Toolbar" = "Nascondi Barra Strumenti";

/* export filename host token
 export header host label */
"Host" = "Host";

/* description of double */
"IEEE 754 double-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 valore in virgola mobile a doppia precisione. M è il numero massimo di cifre, di cui D può essere dopo il punto decimale. Nota: Molti numeri decimali possono essere approssimati solo dai valori in virgola mobile. Vedi DECIMAL se hai bisogno di risultati esatti.";

/* description of float */
"IEEE 754 single-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 valore in virgola mobile mono-precisione. M è il numero massimo di cifre, di cui D può essere dopo il punto decimale. Nota: Molti numeri decimali possono essere approssimati solo dai valori in virgola mobile. Vedi DECIMAL se hai bisogno di risultati esatti.";

/* ignore button */
"Ignore" = "Ignora";

/* ignore errors button */
"Ignore All Errors" = "Ignora Tutti Gli Errori";

/* ignore all fields menu item */
"Ignore all Fields" = "Ignora tutti i campi";

/* ignore field label */
"Ignore field" = "Ignora campo";

/* ignore field label */
"Ignore Field" = "Ignora Campo";

/* import button */
"Import" = "Importa";

/* import all fields menu item */
"Import all Fields" = "Importa tutti i campi";

/* sql import : charset error alert : continue button */
"Import Anyway" = "Importa Comunque";

/* import cancelled message */
"Import cancelled!\n" = "Importazione annullata!\n";

/* Import Error title */
"Import Error" = "Errore Di Importazione";

/* import field operator tooltip */
"Import field" = "Importa campo";

/* import file does not exist message */
"Import file does not exist." = "Importa file non esiste.";

/* Export file format cannot be imported warning */
"Import of the selected data is currently not supported." = "L'importazione dei dati selezionati non è attualmente supportata.";

/* SQL import progress text */
"Imported %@ of %@" = "%1$@ importato di %2$@";

/* CSV import progress text where total size is unknown */
"Imported %@ of CSV data" = "%@ dei dati CSV importati";

/* SQL import progress text where total size is unknown */
"Imported %@ of SQL" = "%@ importato di SQL";

/* text showing that the application is importing CSV */
"Importing CSV" = "Importazione CSV";

/* text showing that the application is importing SQL */
"Importing SQL" = "Importazione SQL";

/* Bundle Editor : BLOB dropdown : 'include BLOB' item */
"include BLOB" = "include BLOB";

/* include content table column tooltip */
"Include content" = "Includi contenuto";

/* sql import error message */
"Incompatible encoding in SQL file" = "Codifica incompatibile in file SQL";

/* header for blank info pane */
"INFORMATION" = "INFORMAZIONI";

/* New Table Sheet : Table Collation Dropdown : Default inherited from database
 New Table Sheet : Table Encoding Dropdown : Default inherited from database */
"Inherit from database (%@)" = "Eredita dal database (%@)";

/* initializing export label */
"Initializing..." = "Inizializzazione...";

/* Bundle Editor : Scope dropdown : 'input field' item
 input field menu item label */
"Input Field" = "Campo Di Input";

/* input field  doesn't support insertion of snippets. */
"Input Field doesn't support insertion of snippets." = "Campo di input non supporta l'inserimento di snippet.";

/* input field is not editable. */
"Input Field is not editable." = "Campo di input non modificabile.";

/* Bundle Editor : Outline View : 'Input Field' item : tooltip */
"Input Field Scope\ncommands will run on each text input field" = "I comandi Input Campo Ambito\nverranno eseguiti su ogni campo di immissione testo";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as snippet' item */
"Insert as Snippet" = "Inserisci come Snippet";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as text' item */
"Insert as Text" = "Inserisci come testo";

/* Bundle Editor : Outline View : 'BUNDLES' item : tooltip */
"Installed Bundles" = "Pacchetti Installati";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog title */
"Installing Bundle" = "Installazione Bundle";

/* insufficient details message */
"Insufficient connection details" = "Informazioni di connessione insufficienti";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please enter at least the hostname." = "Informazioni non sufficienti per stabilire una connessione. Inserisci almeno il nome host.";

/* insufficient SSH tunnel details informative message */
"Insufficient details provided to establish a connection. Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Informazioni non sufficienti per stabilire una connessione. Inserisci il nome host per il tunnel SSH o disabilita il tunnel SSH.";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please provide at least a host." = "Informazioni non sufficienti per stabilire una connessione. Si prega di fornire almeno un host.";

/* Interpret data as: */
"Interpret data as:" = "Interpretare i dati come:";

/* Invalid database very short status message */
"Invalid database" = "Database non valido";

/* export : import settings : file error title */
"Invalid file supplied!" = "File fornito non valido!";

/* table content : editing : error message title when parsing as hex string failed */
"Invalid hexadecimal value" = "Valore esadecimale non valido";

/* is deterministic label (Navigator) */
"Is Deterministic" = "È Deterministico";

/* is nullable label (Navigator) */
"Is Nullable" = "È Nullable";

/* is updatable: %@ */
"is updatable: %@" = "è updatabase: %@";

/* items */
"items" = "elementi";

/* javascript exception */
"JavaScript Exception" = "Eccezione JavaScript";

/* javascript parsing error */
"JavaScript Parsing Error" = "Errore Di Analisi JavaScript";

/* key label (Navigator) */
"Key" = "Chiave";

/* keyword label for color table (Prefs > Editor) */
"Keyword" = "Keyword";

/* kill button */
"Kill" = "Uccidi";

/* kill connection message */
"Kill connection?" = "Terminare la connessione?";

/* kill query message */
"Kill query?" = "Uccidi la domanda?";

/* Last Error Message */
"Last Error Message" = "Ultimo Messaggio Di Errore";

/* Last Used entry in favorites menu */
"Last Used" = "Ultimo Utilizzato";

/* range for json type */
"Limited to @@max_allowed_packet" = "Limitato a @@max_allowed_packet";

/* Loading table task string */
"Loading %@..." = "Caricamento %@...";

/* Loading database task string */
"Loading database '%@'..." = "Caricamento database '%@'...";

/* Loading history entry task desc */
"Loading history entry..." = "Caricamento della cronologia...";

/* Loading table page task string */
"Loading page %lu..." = "Caricamento pagina %lu...";

/* Loading referece task string */
"Loading reference..." = "Caricamento riferimento...";

/* Loading table data string */
"Loading table data..." = "Caricamento dati tabella...";

/* Low memory export summary */
"Low memory" = "Memoria bassa";

/* range of decimal */
"M (precision): Up to 65 digits\nD (scale): 0 to 30 digits" = "M (precisione): fino a 65 cifre\nD (scala): da 0 a 30 cifre";

/* range for blob type
 range for varbinary type */
"M: %@ to %@ bytes" = "M: %1$@ a %2$@ bytes";

/* range for text type
 range for varchar type */
"M: %@ to %@ characters" = "M: da %1$@ a %2$@ caratteri";

/* range for longtext type */
"M: %@ to %@ characters (4 GiB)" = "M: da %1$@ a %2$@ caratteri (4 GiB)";

/* range for binary type */
"M: 0 to 255 bytes" = "M: da 0 a 255 byte";

/* range for char type */
"M: 0 to 255 characters" = "M: da 0 a 255 caratteri";

/* range for bit type */
"M: 1 (default) to 64" = "M: 1 (predefinito) a 64";

/* connection view : ssl : key file picker : wrong format error description */
"Make sure the file contains a RSA private key and is using PEM encoding." = "Assicurati che il file contenga una chiave privata RSA e che stia usando la codifica PEM.";

/* connection view : ssl : client cert picker : wrong format error description */
"Make sure the file contains a X.509 client certificate and is using PEM encoding." = "Assicurati che il file contenga un certificato client X.509 e che stia usando la codifica PEM.";

/* match field menu item */
"Match Field" = "Campo Partita";

/* Shown when user inserts too many arguments (ContentFilterManager) */
"Maximum number of arguments is 2!" = "Il numero massimo di argomenti è 2!";

/* Maximum text length is set to %ld. */
"Maximum text length is set to %ld." = "La lunghezza massima del testo è impostata su %ld.";

/* Maximum text length is set to %ld. Inserted text was truncated. */
"Maximum text length is set to %ld. Inserted text was truncated." = "La lunghezza massima del testo è impostata a %ld. Il testo inserito è stato troncato.";

/* Maximum text length is set to %llu. */
"Maximum text length is set to %llu." = "La lunghezza massima del testo è impostata su %llu.";

/* Maximum text length is set to %llu. Inserted text was truncated. */
"Maximum text length is set to %llu. Inserted text was truncated." = "La lunghezza massima del testo è impostata a %llu. Il testo inserito è stato troncato.";

/* message column title */
"Message" = "Messaggio";

/* mgtemplateengine error */
"MGTemplateEngine Error" = "Errore MGTemplateEngine";

/* export filename date token */
"Month" = "Mese";

/* multiple selection */
"multiple selection" = "selezione multipla";

/* MySQL connecting very short status message */
"MySQL connecting..." = "Connessione MySQL...";

/* mysql error message */
"MySQL Error" = "MySQL Error";

/* mysql help */
"MySQL Help" = "MySQL Help";

/* MySQL Help for Selection */
"MySQL Help for Selection" = "Aiuto MySQL per la selezione";

/* MySQL Help for Word */
"MySQL Help for Word" = "Aiuto MySQL per Word";

/* mysql help categories */
"MySQL Help – Categories" = "Aiuto MySQL – Categorie";

/* mysql said message */
"MySQL said:" = "MySQL ha detto:";

/* shutdown server : error dialog : message */
"MySQL said:\n%@" = "MySQL ha detto:\n%@";

/* message of panel when error while adding row to db */
"MySQL said:\n\n%@" = "MySQL ha detto:\n\n%@";

/* Preferences : Themes : Initial filename for 'Export' */
"MyTheme" = "MyTheme";

/* network preference pane name */
"Network" = "Rete";

/* network preference pane tooltip */
"Network Preferences" = "Preferenze Di Rete";

/* file preference pane name */
"Files" = "File";

/* file preference pane tooltip */
"File Preferences" = "Preferenze Del File";

/* Bundle Editor : Default name for new bundle in the list on the left */
"New Bundle" = "Nuovo Pacchetto";

/* new column name placeholder string */
"New Column Name" = "Nome Nuova Colonna";

/* new favorite name */
"New Favorite" = "Nuovo Preferito";

/* Content Filter Manager : Initial name for new filter */
"New Filter" = "Nuovo Filtro";

/* new folder placeholder name */
"New Folder" = "Nuova Cartella";

/* Bundle Editor : Default name for a new bundle in the menu */
"New Name" = "Nuovo Nome";

/* new table menu item */
"New Table" = "Nuova Tabella";

/* error that no color theme found */
"No color theme data found." = "Nessun dato tema colore trovato.";

/* No compression export summary - within a sentence */
"no compression" = "nessuna compressione";

/* no connection available message */
"No connection available" = "Nessuna connessione disponibile";

/* no connection data found */
"No connection data found." = "Nessun dato di connessione trovato.";

/* No content filters were found in file to import (ContentFilterManager) */
"No content filters found." = "Nessun filtro di contenuto trovato.";

/* no data found */
"No data found." = "Nessun dato trovato.";

/* No errors title */
"No errors" = "Nessun errore";

/* No favorites entry in favorites menu */
"No Favorties" = "Nessun Preferito";

/* All export files creation error title */
"No files could be created" = "Nessun file può essere creato";

/* no item found message */
"No item found" = "Nessun elemento trovato";

/* SSH tunnel could not be created because no local port could be allocated */
"No local port could be allocated for the SSH Tunnel." = "Non è stato possibile assegnare alcun porto locale per il tunnel SSH.";

/* header for no matches in filtered list */
"NO MATCHES" = "NESSUN MATCHES";

/* no optimized field type found. message */
"No optimized field type found." = "Nessun tipo di campo ottimizzato trovato.";

/* error that no query favorites found */
"No query favorites found." = "Nessuna query preferita trovata.";

/* Mysql Help Viewer : Search : No results */
"No results found." = "Nessun risultato trovato.";

/* Bundle Editor : Fallback Input source dropdown : 'none' item
 Bundle Editor : Scope=? : ? dropdown: 'None' item
 Bundle Editor : Scope=Data-Table : Input source dropdown: 'none' item
 Bundle Editor : Scope=Data-Table : Output dropdown : 'none' item
 Bundle Editor : Scope=Data-Table : Trigger dropdown : 'none' item
 Bundle Editor : Scope=Field : Input source dropdown: 'None' item
 Bundle Editor : Scope=Field : Output dropdown : 'none' item
 Bundle Editor : Scope=Field : Trigger dropdown : 'none' item
 Bundle Editor : Scope=General : Input source dropdown: 'None' item
 Bundle Editor : Scope=General : Output dropdown : 'none' item
 Bundle Editor : Scope=General : Trigger dropdown : 'none' item
 compression: none */
"None" = "Nessuno";

/* not available label */
"Not available" = "Non disponibile";

/* Argument count (ContentFilterManager) */
"Number of arguments: %lu" = "Numero di argomenti: %lu";

/* numeric label for color table (Prefs > Editor) */
"Numeric" = "Numeric";

/* Bundle Editor : Copy-Command-Error : OK button
 Bundle Editor : Save-and-Close-Error : OK button
 Bundle Editor : Save-Bundle-Error : OK button
 Bundle Editor : Trash-Bundle(s)-Error : OK button
 OK
 OK button
 Open Files : Bundle : Already-Installed : Delete-Old-Error : OK button
 Open Files : Bundle : Install-Error : OK button
 Open Files : Bundle : UUID : OK button
 Preferences : Network : Custom SSH client : warning dialog : accept button */
"OK" = "OK";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+1 rows instead of n selected were deleted. */
"One additional row was removed!" = "Una riga aggiuntiva è stata rimossa!";

/* Table Content : Remove Row : Result : Too Few : Part 1 : Only n-1 of n selected rows were deleted. */
"One row was not removed." = "Una riga non è stata rimossa.";

/* Only one dragged item allowed. */
"Only one dragged item allowed." = "Solo un elemento trascinato permesso.";

/* partial copy database support message */
"Only Partially Supported" = "Solo Parzialmente Supportato";

/* open function in new table title */
"Open Function in New Tab" = "Apri funzione in una nuova scheda";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Function in New Window" = "Apri la funzione in una nuova finestra";

/* open connection in new tab context menu item */
"Open in New Tab" = "Apri in una nuova scheda";

/* menu item open in new window */
"Open in New Window" = "Apri in una nuova finestra";

/* open procedure in new table title */
"Open Procedure in New Tab" = "Procedura aperta in una nuova scheda";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Procedure in New Window" = "Apri la procedura in una nuova finestra";

/* open table in new tab title
 open table in new table title */
"Open Table in New Tab" = "Apri tabella in una nuova scheda";

/* Table List : Context Menu : Duplicate connection to new window
 Table List : Gear Menu : Duplicate connection to new window */
"Open Table in New Window" = "Apri tabella in una nuova finestra";

/* open view in new tab title
 open view in new table title */
"Open View in New Tab" = "Apri vista in una nuova scheda";

/* Tables List : Context Menu : Duplicate connection to new window
 Tables List : Gear Menu : Duplicate connection to new window */
"Open View in New Window" = "Apri vista in una nuova finestra";

/* menu item open %@ in new window */
"Open %@ in New Window" = "Apri %@ in una nuova finestra";

/* OPTIMIZE one or more tables - result title */
"Optimize %@" = "Ottimizza %@";

/* optimize selected items menu item */
"Optimize Selected Items" = "Ottimizza Gli Elementi Selezionati";

/* optimize table menu item */
"Optimize Table" = "Ottimizza Tabella";

/* optimize table failed message */
"Optimize table failed." = "Ottimizzazione tabella non riuscita.";

/* Optimized type for field %@ */
"Optimized type for field '%@'" = "Tipo ottimizzato per il campo '%@'";

/* optional placeholder string */
"optional" = "opzionale";

/* Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed. */
"Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed." = "Il parametro superato non può essere interpretato. Sono permessi solo stringa o array (con 2 elementi).";

/* Permission Denied */
"Permission Denied" = "Permesso Negato";

/* please choose a favorite connection view label */
"Please choose a favorite" = "Scegli un preferito";

/* message of panel when ssh details are incomplete */
"Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Inserisci il nome host per il tunnel SSH o disabilita il tunnel SSH.";

/* Please enter the password */
"Please enter the password for ‘%@’:" = "Inserisci la password per «%@»:";

/* print button */
"Print" = "Stampa";

/* print page menu item title */
"Print Page…" = "Stampa Pagina…";

/* privileges label (Navigator) */
"Privileges" = "Privilegi";

/* procedure */
"procedure" = "procedura";

/* header for procedure info pane */
"PROCEDURE INFORMATION" = "INFORMAZIONI SUL PROCEDIMENTO";

/* procedures */
"procedures" = "procedure";

/* proceed button */
"Proceed" = "Procedere";

/* header for procs & funcs list */
"PROCS & FUNCS" = "PROCS & FUNCS";

/* toolbar item label for switching to the Run Query tab */
"Query" = "Interrogazione";

/* query background label for color table (Prefs > Editor) */
"Query Background" = "Sfondo Query";

/* Query cancelled error */
"Query cancelled." = "Interrogazione annullata.";

/* Query cancel by resetting connection error */
"Query cancelled.  Please note that to cancel the query the connection had to be reset; transactions and connection variables were reset." = "Interrogazione annullata. Si prega di notare che per annullare la query è stato necessario reimpostare la connessione; le transazioni e le variabili di connessione sono state reimpostate.";

/* query editor preference pane name */
"Query Editor" = "Editor Query";

/* query editor preference pane tooltip */
"Query Editor Preferences" = "Preferenze Dell'Editor Delle Interrogazioni";

/* query logging currently disabled label
 query logging disabled label */
"Query logging is currently disabled" = "Il registro delle interrogazioni è attualmente disabilitato";

/* query result print heading */
"Query Result" = "Risultato Query";

/* export source */
"Query results" = "Risultati delle interrogazioni";

/* Query Status */
"Query Status" = "Stato Query";

/* table status : row count query failed : error title */
"Querying row count failed" = "Interrogazione conteggio righe fallita";

/* Quick connect item label */
"Quick Connect" = "Connessione Rapida";

/* quote label for color table (Prefs > Editor) */
"Quote" = "Preventivo";

/* databsse rename unsupported informative message */
"Renaming the database '%@' is currently unsupported as it contains objects other than tables (i.e. views, procedures, functions, etc.).\n\nIf you would like to rename a database please use the 'Duplicate Database', move any non-table objects manually then drop the old database." = "Rinominare il database '%@' è attualmente non supportato in quanto contiene oggetti diversi dalle tabelle (cioè visualizzazioni, procedure, funzioni, ecc.).\n\nSe si desidera rinominare un database si prega di utilizzare 'Duplicate Database', sposta manualmente qualsiasi oggetto non tabella e rilascia il vecchio database.";

/* range for serial type */
"Range: %@ to %@" = "Intervallo: %1$@ a %2$@";

/* range for time type */
"Range: -838:59:59.0 to 838:59:59.0\nF (precision): 0 (1s) to 6 (1µs)" = "Intervallo: da -838:59:59.0 a 838:59:59.0\nF (precisione): da 0 (1s) a 6 (1μs)";

/* range for year type */
"Range: 0000, 1901 to 2155" = "Gamma: 0000, 1901 a 2155";

/* range for set type */
"Range: 1 to 64 members\n1, 2, 3, 4 or 8 bytes storage" = "Intervallo: da 1 a 64 membri\n1, 2, 3, 4 o 8 byte";

/* range for datetime type */
"Range: 1000-01-01 00:00:00.0 to 9999-12-31 23:59:59.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Intervallo 1000-01-01 00:00:00.0 a 9999-12-31 23:59:59.999999\nF (precisione): da 0 (1s) a 6 (1μs)";

/* range for date type */
"Range: 1000-01-01 to 9999-12-31" = "Gamma: da 1000-01-01 a 9999-12-31";

/* range for timestamp type */
"Range: 1970-01-01 00:00:01.0 to 2038-01-19 03:14:07.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Gamma: 1970-01-01 00:00:01.0 a 2038-01-19 03:14:07.999999\nF (precisione): 0 (1s) a 6 (1μs)";

/* text showing that app is reading dump */
"Reading..." = "Lettura...";

/* menu item to refresh databases */
"Refresh Databases" = "Aggiorna Database";

/* refresh list menu item */
"Refresh List" = "Aggiorna Lista";

/* toolbar item label for switching to the Table Relations tab */
"Relations" = "Relazioni";

/* Relations tab subtitle showing table name */
"Relations for table: %@" = "Relazioni per la tabella: %@";

/* reload bundles menu item label */
"Reload Bundles" = "Ricarica Pacchetti";

/* Reloading data task description */
"Reloading data..." = "Ricarica dati...";

/* Reloading table task string */
"Reloading..." = "Reloading...";

/* remote error */
"Remote Error" = "Errore Remoto";

/* Bundle Editor : Remove-Bundle: remove button
 remove button */
"Remove" = "Rimuovi";

/* remove all button */
"Remove All" = "Rimuovi Tutto";

/* remove all query favorites message */
"Remove all query favorites?" = "Rimuovere tutti i preferiti della query?";

/* Bundle Editor : Remove-Bundle: remove dialog title */
"Remove selected Bundle?" = "Rimuovere il pacchetto selezionato?";

/* remove selected content filters message */
"Remove selected content filters?" = "Rimuovere i filtri di contenuto selezionati?";

/* remove selected query favorites message */
"Remove selected query favorites?" = "Rimuovere i preferiti della query selezionata?";

/* removing field task status message */
"Removing field..." = "Rimozione campo...";

/* removing index task status message */
"Removing index..." = "Rimozione indice...";

/* rename database message */
"Rename database '%@' to:" = "Rinomina database '%@' in:";

/* rename func menu title */
"Rename Function..." = "Rinomina Funzione...";

/* rename proc menu title */
"Rename Procedure..." = "Rinomina Procedura...";

/* rename table menu title */
"Rename Table..." = "Rinomina Tabella...";

/* rename view menu title */
"Rename View..." = "Rinomina Vista...";

/* REPAIR one or more tables - result title */
"Repair %@" = "%@ Riparazione";

/* repair selected items menu item */
"Repair Selected Items" = "Ripara Oggetti Selezionati";

/* repair table menu item */
"Repair Table" = "Ripara Tavolo";

/* repair table failed message */
"Repair table failed." = "Riparazione tabella fallita.";

/* Replace button */
"Replace" = "Sostituisci";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace entire content' item */
"Replace Entire Content" = "Sostituisci Intero Contenuto";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace selection' item */
"Replace Selection" = "Sostituisci Selezione";

/* description of year */
"Represents a 4 digit year value, stored as 1 byte. Invalid values are converted to 0000 and two digit values 0 to 69 will be converted to years 2000 to 2069, resp. values 70 to 99 to years 1970 to 1999.\nThe YEAR(2) type was removed in MySQL 5.7.5." = "Rappresenta un valore di 4 cifre, memorizzato come 1 byte. I valori non validi sono convertiti in 0000 e due valori di cifra da 0 a 69 saranno convertiti in anni 2000 a 2069, resp. valori da 70 a 99 anni dal 1970 al 1999.\nIl tipo YEAR(2) è stato rimosso in MySQL 5.7.5.";

/* description of multilinestring */
"Represents a collection of LineStrings." = "Rappresenta una raccolta di LineStrings.";

/* description of geometrycollection */
"Represents a collection of objects of any other single- or multi-valued spatial type. The only restriction being, that all objects must share a common coordinate system." = "Rappresenta una raccolta di oggetti di qualsiasi altro tipo spaziale singolo o multi-valore. L'unico limite è che tutti gli oggetti devono condividere un sistema di coordinate comune.";

/* description of multipolygon */
"Represents a collection of Polygons. The Polygons making up the MultiPolygon must not intersect." = "Rappresenta una collezione di poligoni. I poligoni che compongono il poligono multiplo non devono intersecarsi.";

/* description of multipoint */
"Represents a set of Points without specifying any kind of relation and/or order between them." = "Rappresenta un insieme di Punti senza specificare alcun tipo di relazione e/o ordine tra di essi.";

/* description of point */
"Represents a single location in coordinate space using X and Y coordinates. The point is zero-dimensional." = "Rappresenta una singola posizione nello spazio di coordinate usando le coordinate X e Y. Il punto è zero-dimensionale.";

/* description of linestring */
"Represents an ordered set of coordinates where each consecutive pair of two points is connected by a straight line." = "Rappresenta un insieme ordinato di coordinate in cui ogni coppia consecutiva di due punti è collegata da una linea retta.";

/* required placeholder string */
"required" = "obbligatorio";

/* description of smallint */
"Requires 2 bytes storage space. M is the optional display width and does not affect the possible value range." = "Richiede 2 byte spazio di archiviazione. M è la larghezza di visualizzazione opzionale e non influisce sull'intervallo di valori possibile.";

/* description of mediumint */
"Requires 3 bytes storage space. M is the optional display width and does not affect the possible value range." = "Richiede 3 byte spazio di archiviazione. M è la larghezza di visualizzazione opzionale e non influisce sull'intervallo di valori possibile.";

/* description of int */
"Requires 4 bytes storage space. M is the optional display width and does not affect the possible value range. INTEGER is an alias to this type." = "Richiede 4 byte spazio di archiviazione. M è la larghezza di visualizzazione opzionale e non influisce sull'intervallo di valori possibile. INTEGER è un alias di questo tipo.";

/* description of bigint */
"Requires 8 bytes storage space. M is the optional display width and does not affect the possible value range. Note: Arithmetic operations might fail for large numbers." = "Richiede 8 byte spazio di archiviazione. M è la larghezza di visualizzazione opzionale e non influisce sull'intervallo di valori possibile. Nota: le operazioni aritmetiche potrebbero fallire per grandi numeri.";

/* reset auto_increment after deletion of all rows message */
"Reset AUTO_INCREMENT after deletion\n(only for Delete ALL ROWS IN TABLE)?" = "Reimpostare AUTO_INCREMENT dopo la cancellazione\n(solo per eliminare TUTTI I ROWS IN TABLE)?";

/* delete selected row button */
"Delete Selected Row" = "Elimina Riga Selezionata";

/* delete selected rows button */
"Delete Selected Rows" = "Elimina Righe Selezionate";

/* delete all rows in table button */
"Delete ALL ROWS IN TABLE" = "Eliminare TUTTI I ROWS NELLA TAVOLA";

/* Restoring session task description */
"Restoring session..." = "Ripristino sessione...";

/* return type label (Navigator) */
"Return Type" = "Tipo Di Reso";

/* return type: %@ */
"return type: %@" = "tipo di ritorno: %@";

/* singular word for row */
"row" = "riga";

/* plural word for rows */
"rows" = "righe";

/* text showing how many rows are in the limited filter match */
"Rows %@ - %@ from filtered matches" = "Righe %1$@ - %2$@ dalle corrispondenze filtrate";

/* text showing how many rows are in the limited result */
"Rows %@ - %@ of %@%@ from table" = "Righe %1$@ - %2$@ di %3$@%4$@ dalla tabella";

/* Table Info Section : number of rows (exact value) */
"rows: %@" = "righe: %@";

/* Table Info Section : number of rows (estimated value) */
"rows: ~%@" = "righe: ~%@";

/* run all button */
"Run All" = "Esegui Tutto";

/* Run All menu item title */
"Run All Queries" = "Esegui Tutte Le Query";

/* Title of button to run current query in custom query view */
"Run Current" = "Esegui Corrente";

/* Title of action menu item to run current query in custom query view */
"Run Current Query" = "Esegui Query Attuale";

/* toolbar item label for switching to the Run Query tab */
"Run Custom Query" = "Esegui Query Personalizzata";

/* Title of button to run query just before text caret in custom query view */
"Run Previous" = "Esegui Precedente";

/* Title of action menu item to run query just before text caret in custom query view */
"Run Previous Query" = "Esegui Query Precedente";

/* Title of action menu item to run selected text in custom query view */
"Run Selected Text" = "Esegui Il Testo Selezionato";

/* Title of button to run selected text in custom query view */
"Run Selection" = "Esegui Selezione";

/* Running multiple queries string */
"Running query %i of %lu..." = "Esecuzione della query %1$i di %2$lu...";

/* Running multiple queries string */
"Running query %ld of %lu..." = "Esecuzione della query %1$ld di %2$lu...";

/* Running single query string */
"Running query..." = "Interrogazione in corso...";

/* Save trigger button label */
"Save" = "Salva";

/* Save All to Favorites */
"Save All to Favorites" = "Salva tutto nei preferiti";

/* save as button title */
"Save As..." = "Salva Come...";

/* Bundle Editor : BLOB dropdown : 'save BLOB as dat file' item */
"save BLOB as dat file" = "salva BLOB come file dat";

/* Bundle Editor : BLOB dropdown : 'save BLOB as image file' item */
"save BLOB as image file" = "salva BLOB come file immagine";

/* Save Current Query to Favorites */
"Save Current Query to Favorites" = "Salva la Query Corrente nei Preferiti";

/* save page as menu item title */
"Save Page As…" = "Salva Pagina Come…";

/* Save Queries… */
"Save Queries…" = "Salva Query…";

/* Save Query… */
"Save Query…" = "Salva Query…";

/* Save Selection to Favorites */
"Save Selection to Favorites" = "Salva selezione nei preferiti";

/* save view as button title */
"Save View As..." = "Salva Vista Come...";

/* schema path header for completion tooltip */
"Schema path:" = "Percorso schema:";

/* Search in MySQL Documentation */
"Search in MySQL Documentation" = "Cerca nella documentazione MySQL";

/* Search in MySQL Help */
"Search in MySQL Help" = "Cerca nella Guida di MySQL";

/* Select Active Query */
"Select Active Query" = "Seleziona Interrogazione Attiva";

/* toolbar item for selecting a db */
"Select Database" = "Seleziona Database";

/* selected items */
"selected items" = "elementi selezionati";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as comma-separated' item */
"Selected Rows (CSV)" = "Righe Selezionate (Csv)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as SQL' item */
"Selected Rows (SQL)" = "Righe Selezionate (Sql)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as tab-separated' item */
"Selected Rows (TSV)" = "Righe Selezionate (Tsv)";

/* Bundle Editor : Scope=Field : Input source dropdown: 'selected text' item */
"Selected Text" = "Testo Selezionato";

/* selection label for color table (Prefs > Editor) */
"Selection" = "Selezione";

/* table structure : indexes : delete index : no columns error : description */
"Sequel Ace could not find any columns belonging to this index. Maybe it has been removed already?" = "Sequel Ace non ha trovato alcuna colonna appartenente a questo indice. Forse è già stato rimosso?";

/* Preferences : Network : Custom SSH client : warning dialog message */
"Sequel Ace only supports and is tested with the default OpenSSH client versions included with macOS. Using different clients might cause connection issues, security risks or not work at all.\n\nPlease be aware, that we cannot provide support for such configurations." = "Sequel Ace supporta solo ed è testato con le versioni client OpenSSH predefinite incluse con macOS. L'utilizzo di clienti diversi potrebbe causare problemi di connessione, rischi di sicurezza o non funzionare affatto.\n\nSi prega di essere consapevoli, che non possiamo fornire supporto per tali configurazioni.";

/* sequelace URL scheme command not supported. */
"sequelace URL scheme command not supported." = "comando schema URL sequelace non supportato.";

/* sequelace url Scheme Error */
"sequelace URL Scheme Error" = "sequelace URL Scheme Error";

/* Table Structure : Collation dropdown : 'item is the same as the collation of server' marker
 Table Structure : Encoding dropdown : 'item is server default' marker */
"Server" = "Server";

/* Add Database : Charset dropdown : default item ($1 = charset name)
 Add Database : Collation dropdown : default item ($1 = collation name) */
"Server Default (%@)" = "Server Predefinito (%@)";

/* server processes window title (var = hostname) */
"Server Processes on %@" = "Processi del server su %@";

/* Initial filename for 'Save session' file */
"Session" = "Sessione";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html' item */
"Show as HTML" = "Mostra come HTML";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html tooltip' item */
"Show as HTML Tooltip" = "Mostra come suggerimento HTML";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as text tooltip' item */
"Show as Text Tooltip" = "Mostra come suggerimento di testo";

/* show console */
"Show Console" = "Mostra Console";

/* show create func syntax menu item */
"Show Create Function Syntax..." = "Mostra Crea Sintassi Funzione...";

/* show create proc syntax menu item */
"Show Create Procedure Syntax..." = "Mostra Sintassi Della Procedura...";

/* show create syntaxes menu item */
"Show Create Syntaxes..." = "Mostra Crea Sintassi...";

/* show create table syntax menu item */
"Show Create Table Syntax..." = "Mostra Crea Sintassi Della Tabella...";

/* show create view syntax menu item */
"Show Create View Syntax..." = "Mostra Crea Sintassi Vista...";

/* Show detail button */
"Show Detail" = "Mostra Dettaglio";

/* MySQL Help Viewer : Results list : Link tooltip */
"Show MySQL help for %@" = "Mostra l'aiuto MySQL per %@";

/* show navigator */
"Show Navigator" = "Show Navigator";

/* show tab bar */
"Show Tab Bar" = "Mostra Barra Delle Schede";

/* tooltip for toolbar item for show console */
"Show the console which shows all MySQL commands performed by Sequel Ace" = "Mostra la console che mostra tutti i comandi MySQL eseguiti da Sequel Ace";

/* Show Toolbar menu item */
"Show Toolbar" = "Mostra Barra Strumenti";

/* filtered item count */
"Showing %lu of %lu processes" = "Mostrando %1$lu dei processi di %2$lu";

/* shutdown server : confirmation dialog : shutdown button */
"Shutdown" = "Spegnimento";

/* shutdown server : error dialog : title */
"Shutdown failed!" = "Arresto fallito!";

/* range of integer types */
"Signed: %@ to %@\nUnsigned: %@ to %@" = "Firmato: %1$@ a %2$@\nNon firmato: %3$@ a %4$@";

/* Table Info Section : table size on disk */
"size: %@" = "Dimensioni: %@";

/* skip existing button */
"Skip existing" = "Salta esistente";

/* skip problems button */
"Skip problems" = "Salta problemi";

/* beta build label */
"Beta Build" = "Build Beta";

/* socket connection failed title */
"Socket connection failed!" = "Connessione socket fallita!";

/* socket not found title */
"Socket not found!" = "Socket non trovato!";

/* Some export folders not writable explanatory text */
"Some of the target export folders are not writable.  Please select a new export location and try again." = "Alcune delle cartelle di esportazione di destinazione non sono scrivibili. Seleziona una nuova posizione di esportazione e riprova.";

/* Some export folders missing explanatory text */
"Some of the target export folders no longer exist.  Please select a new export location and try again." = "Alcune delle cartelle di esportazione di destinazione non esistono più. Seleziona una nuova posizione di esportazione e riprova.";

/* Sorting table task description */
"Sorting table..." = "Ordinamento tabella...";

/* spatial index menu item title */
"SPATIAL" = "SPATIAL";

/* sql data access label (Navigator) */
"SQL Data Access" = "Accesso Dati Sql";


/* Connection Secured via SSL information text */
"Connection Secured via SSL" = "Connessione protetta tramite SSL";

/* SSH connected titlebar marker */
"SSH Connected" = "SSH Connesso";

/* SSH connecting very short status message */
"SSH connecting..." = "Connessione SSH...";

/* SSH connecting titlebar marker */
"SSH Connecting…" = "Connessione SSH…";

/* SSH connection failed title */
"SSH connection failed!" = "Connessione SSH fallita!";

/* SSH disconnected titlebar marker */
"SSH Disconnected" = "SSH Disconnesso";

/* SSH key check error */
"SSH Key not found" = "Chiave SSH non trovata";

/* title when ssh tunnel port forwarding failed */
"SSH port forwarding failed" = "Inoltro porta SSH non riuscito";

/* SSL certificate authority file check error */
"SSL Certificate Authority File not found" = "File Autorità di Certificazione SSL non trovato";

/* SSL certificate file check error */
"SSL Certificate File not found" = "File certificato SSL non trovato";

/* SSL requested but not used title */
"SSL connection not established" = "Connessione SSL non stabilita";

/* SSL key file check error */
"SSL Key File not found" = "File Chiave SSL non trovato";

/* Standard memory export summary */
"Standard memory" = "Memoria standard";

/* started */
"started" = "iniziato";

/* status file for sequelace url scheme command couldn't be written error message */
"Status file for sequelace url scheme command couldn't be written!" = "Impossibile scrivere il file di stato per il comando dello schema url sequelace!";

/* stop button */
"Stop" = "Ferma";

/* Stop queries string */
"Stop queries" = "Ferma le query";

/* Stop query string */
"Stop query" = "Interrompi interrogazione";

/* description of timestamp */
"Stores a date and time of day as seconds since the beginning of the UNIX epoch (1970-01-01 00:00:00). The values displayed/stored are affected by the connection's @@time_zone setting.\nThe representation is the same as for DATETIME. Invalid values, as well as \"second zero\", are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F. Some additional rules may apply." = "Memorizza una data e un'ora del giorno come secondi dall'inizio dell'epoca UNIX (1970-01-01 00:00:00). I valori visualizzati/memorizzati sono influenzati dall'impostazione @@time_zone della connessione.\nLa rappresentazione è la stessa di DATETIME. Valori non validi, così come \"secondo zero\", vengono convertiti in 0000-00-00 00:00:00.0. Secondi frazionali sono stati aggiunti in MySQL 5. .4 con una precisione fino a microsecondi (6), specificata da F. Possono applicarsi alcune regole supplementari.";

/* description of datetime */
"Stores a date and time of day. The representation is YYYY-MM-DD HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Memorizza una data e un'ora del giorno. La rappresentazione è AAAA-MM-GG HH:MM:SS[. *], Sono secondi frazionati. Il valore non è influenzato da nessuna impostazione di fuso orario. I valori non validi sono convertiti in 0000-00-00 00:00:00.0. I secondi frazionali sono stati aggiunti in MySQL 5.6.4 con una precisione fino a microsecondi (6), specificati da F.";

/* description of date */
"Stores a date without time information. The representation is YYYY-MM-DD. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00." = "Memorizza una data senza informazioni sull'ora. La rappresentazione è AAAA-MM-GD. Il valore non è influenzato da nessuna impostazione del fuso orario. Valori non validi sono convertiti in 0000-00-00.";

/* description of time */
"Stores a time of day, duration or time interval. The representation is HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 00:00:00. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Memorizza un'ora del giorno, una durata o un intervallo di tempo. La rappresentazione è HH:MM:SS[. *], Sono secondi frazionati. Il valore non è influenzato da nessuna impostazione di fuso orario. I valori non validi sono convertiti in 00:00:00. I secondi frazionali sono stati aggiunti in MySQL 5.6.4 con una precisione fino a microsecondi (6), specificata da F.";

/* toolbar item label for switching to the Table Structure tab */
"Structure" = "Struttura";

/* successfully analyzed all selected items message */
"Successfully analyzed all selected items." = "Analizzato con successo tutti gli elementi selezionati.";

/* analyze table successfully passed message */
"Successfully analyzed table." = "Tabella analizzata con successo.";

/* successfully flushed all selected items message */
"Successfully flushed all selected items." = "Tutti gli elementi selezionati sono stati scaricati con successo.";

/* message of panel when successfully flushed privs */
"Successfully flushed privileges." = "Privilegi risciacquati con successo.";

/* flush table successfully passed message */
"Successfully flushed table." = "Tavolo lavato con successo.";

/* successfully optimized all selected items message */
"Successfully optimized all selected items." = "Ottimizzato con successo tutti gli elementi selezionati.";

/* optimize table successfully passed message */
"Successfully optimized table." = "Tavolo ottimizzato con successo.";

/* successfully repaired all selected items message */
"Successfully repaired all selected items." = "Hai riparato con successo tutti gli elementi selezionati.";

/* repair table successfully passed message */
"Successfully repaired table." = "Tabella riparata con successo.";

/* tooltip for toolbar item for switching to the Run Query tab */
"Switch to the Run Query tab" = "Passa alla scheda Esegui query";

/* tooltip for toolbar item for switching to the Table Content tab */
"Switch to the Table Content tab" = "Passa alla scheda Contenuto tabella";

/* tooltip for toolbar item for switching to the Table Info tab */
"Switch to the Table Info tab" = "Passa alla scheda Informazioni tabella";

/* tooltip for toolbar item for switching to the Table Relations tab */
"Switch to the Table Relations tab" = "Passa alla scheda Relazioni tabella";

/* tooltip for toolbar item for switching to the Table Structure tab */
"Switch to the Table Structure tab" = "Passa alla scheda Struttura tabella";

/* tooltip for toolbar item for switching to the Table Triggers tab */
"Switch to the Table Triggers tab" = "Passa alla scheda Trigger tabella";

/* tooltip for toolbar item for switching to the User Manager tab */
"Switch to the User Manager tab" = "Passa alla scheda Gestione utenti";

/* description for table syntax copied notification */
"Syntax for %@ table copied" = "Sintassi per la tabella %@ copiata";

/* table */
"table" = "tabella";

/* csv export table heading
 table
 Table Structure : Collation dropdown : 'item is the same as the collation of table' marker
 Table Structure : Encoding dropdown : 'item is table default' marker */
"Table" = "Tabella";

/* export label showing that the app is fetching data for a specific table */
"Table %lu of %lu (%@): Fetching data..." = "Tabella %1$lu di %2$lu (%3$@): Recupero dati...";

/* export label showing app is fetching relations data for a specific table */
"Table %lu of %lu (%@): Fetching relations data..." = "Tabella %1$lu di %2$lu (%3$@): Recupero dati relazioni...";

/* export label showing app if writing data for a specific table */
"Table %lu of %lu (%@): Writing data..." = "Tabella %1$lu di %2$lu (%3$@): Scrittura dei dati...";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'table changed' item */
"Table changed" = "Tabella modificata";

/* table checksum message */
"Table checksum" = "Checksum tabella";

/* table checksum: %@ */
"Table checksum: %@" = "Checksum tabella: %@";

/* table content print heading */
"Table Content" = "Contenuto Tabella";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as comma-separated' item */
"Table Content (CSV)" = "Contenuto Tabella (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as SQL' item */
"Table Content (SQL)" = "Contenuto Tabella (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as tab-separated' item */
"Table Content (TSV)" = "Contenuto Tabella (TSV)";

/* toolbar item for navigation history */
"Table History" = "Cronologia Tabella";

/* toolbar item label for switching to the Table Info tab */
"Table Info" = "Info Tabella";

/* header for table info pane */
"TABLE INFORMATION" = "INFORMAZIONI SULLA TABELLA";

/* table information print heading */
"Table Information" = "Informazioni Sulla Tabella";

/* message of panel when no name is given for table */
"Table must have a name." = "La tabella deve avere un nome.";

/* general preference pane tooltip */
"Table Preferences" = "Preferenze Tabella";

/* toolbar item label for switching to the Table Relations tab */
"Table Relations" = "Relazioni Sulla Tabella";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table row changed' item */
"Table Row changed" = "Riga tabella modificata";

/* table structure print heading */
"Table Structure" = "Struttura Della Tabella";

/* toolbar item label for switching to the Table Triggers tab */
"Table Triggers" = "Trigger Tabella";

/* Add Relation sheet title, showing table name */
"Table: %@" = "Tabella: %@";

/* tables preference pane name */
"Tables" = "Tabelle";

/* tables */
"tables" = "tabelle";

/* header for table list */
"TABLES" = "TABELLE";

/* header for table & views list */
"TABLES & VIEWS" = "TABELLE E VISIONI";

/* Connection test very short status message */
"Testing connection..." = "Verifica connessione...";

/* MySQL connection test very short status message */
"Testing MySQL..." = "Testing MySQL...";

/* SSH testing very short status message */
"Testing SSH..." = "Testando SSH...";

/* text label for color table (Prefs > Editor) */
"Text" = "Testo";

/* Text is too long. Maximum text length is set to %llu. */
"Text is too long. Maximum text length is set to %llu." = "Testo troppo lungo. La lunghezza massima del testo è impostata su %llu.";

/* Release notes dialog title thanking user for upgrade */
"Thanks for updating Sequel Ace!" = "Grazie per aver aggiornato Sequel Ace!";

/* Open Files : Bundle : Install-Error : Destination path already exists error dialog message */
"The Bundle ‘%@’ already exists." = "Il Bundle ‘%@’ esiste già.";

/* Open Files : Bundle: UUID : UUID-Attribute is missing in bundle's command.plist file */
"The Bundle ‘%@’ has no UUID which is necessary to identify installed Bundles." = "Il Bundle ‘%@’ non ha UUID che è necessario per identificare i pacchetti installati.";

"‘%@’ Bundle contains legacy components" = "‘%@’ Bundle contiene componenti legacy";

"In these files:\n\n%@\n\nDo you still want to install the bundle?" = "In questi file:\n\n%@\n\nVuoi ancora installare il pacchetto?";

/* message while reading a spf file which matches non-supported formats. */
"The chosen file %@ contains ‘%@’ data." = "Il file scelto «%1$@» contiene «%2$@» dati.";

/* the color theme ‘%@’ already exists. */
"The color theme ‘%@’ already exists." = "Il tema dei colori ‘%@’ esiste già.";

/* the connection is busy. please wait and try again tooltip */
"The connection is busy. Please wait and try again." = "La connessione è occupata. Si prega di attendere e riprovare.";

/* the connection of the active connection window is not identical tooltip */
"The connection of the active connection window is not identical." = "La connessione della finestra di connessione attiva non è identica.";

/* Connection lost during import error message */
"The connection to the server was lost during the import.  The import is only partially complete." = "La connessione al server è stata persa durante l'importazione. L'importazione è completata solo parzialmente.";

/* Create syntax permission denied detail */
"The creation syntax could not be retrieved due to a permissions error.\n\nPlease check your user permissions with an administrator." = "La sintassi di creazione non può essere recuperata a causa di un errore di autorizzazioni.\n\nSi prega di controllare i permessi utente con un amministratore.";

/* CSV file open error */
"The CSV file you selected could not be found or read." = "Il file CSV selezionato non può essere trovato o letto.";

/* Error when CSV appears to have too many columns to import, probably due to line ending mismatch */
"The CSV was read as containing more than 512 columns, more than the maximum columns permitted for speed reasons by Sequel Ace.\n\nThis usually happens due to errors reading the CSV; please double-check the CSV to be imported and the line endings and escape characters at the bottom of the CSV selection dialog." = "Il CSV è stato letto come contenente più di 512 colonne, più delle colonne massime consentite per ragioni di velocità da Sequel Ace.\n\nQuesto accade solitamente a causa di errori nella lettura del CSV; si prega di controllare due volte il CSV da importare e le terminazioni della riga e i caratteri di escape in fondo alla finestra di selezione CSV.";

/* unsaved theme informative message */
"The current color theme is unsaved. Do you want to proceed without saving it?" = "Il tema di colore corrente non è salvato. Vuoi procedere senza salvarlo?";

/* Short important release note for swap of custom query buttons */
"The Custom Query \"Run\" and \"Run All\" button positions and their shortcuts have been swapped." = "Le posizioni dei pulsanti di Query Personalizzate Esegui ed Esegui Tutte e le loro scorciatoie sono state scambiate.";

/* the following default bundles were updated:\n%@\nyour modifications were stored as “(user)”. */
"The following default Bundles were updated:\n%@\nYour modifications were stored as “(user)”." = "I seguenti pacchetti di default sono stati aggiornati:\n%@\nLe modifiche sono state memorizzate come “(utente)”.";

/* favorites export error informative message */
"The following error occurred during the export process:\n\n%@" = "Si è verificato il seguente errore durante il processo di esportazione:\n\n%@";

/* favorites import error informative message */
"The following error occurred during the import process:\n\n%@" = "Si è verificato il seguente errore durante il processo di importazione:\n\n%@";

/* table structure : indexes : delete index : error 1553 : description */
"The foreign key relationship '%@' has a dependency on index '%@'. This relationship must be removed before the index can be deleted.\n\nAre you sure you want to continue to delete the relationship and the index? This action cannot be undone." = "La relazione chiave estera '%1$@' dipende dall'indice '%2$@'. Questa relazione deve essere rimossa prima che l'indice possa essere cancellato.\n\nSei sicuro di voler continuare a eliminare la relazione e l'indice? Questa azione non può essere annullata.";

/* table list change alert message */
"The list of tables has changed" = "La lista delle tabelle è cambiata";

/* message when trying to rename a table/view/proc/etc to an already used name */
"The name '%@' is already used." = "Il nome '%@' è già in uso.";

/* table list change alert informative message */
"The number of tables in this database has changed since the export dialog was opened. There are now %lu additional table(s), most likely added by an external application.\n\nHow would you like to proceed?" = "Il numero di tabelle in questo database è cambiato dalla finestra di esportazione aperta. Ci sono ora tabelle aggiuntive %lu , molto probabilmente aggiunte da un'applicazione esterna.\n\nCome vuoi procedere?";

/* message of panel when no rows have been affected after writing to the db */
"The row was not written to the MySQL database. You probably haven't changed anything.\nReload the table to be sure that the row exists and use a primary key for your table.\n(This error can be turned off in the preferences.)" = "La riga non è stata scritta nel database MySQL. Probabilmente non hai cambiato nulla.\nRicarica la tabella per essere sicuri che la riga esista e usa una chiave primaria per la tua tabella.\n(Questo errore può essere disattivato nelle preferenze.)";

/* export : import settings : file version error description ($1 = is version, $2 = list of supported versions); note: the u00A0 is a non-breaking space, do not add more whitespace. */
"The selected export settings were stored with version %1$ld, but only settings with the following versions can be imported: %2$@.\n\nEither save the settings in a backwards compatible way or update your version of Sequel Ace." = "Le impostazioni di esportazione selezionate sono state salvate con la versione%1$ld, ma solo le impostazioni con le seguenti versioni possono essere importate: %2$@.\n\nSalva le impostazioni in un modo compatibile all'indietro o aggiorna la tua versione di Sequel Ace.";

/* export : import settings : spf content type error description */
"The selected file contains data of type “%1$@”, but type “%2$@” is needed. Please choose a different file." = "Il file selezionato contiene dati di tipo «%1$@», ma è necessario digitare «%2$@». Si prega di scegliere un file diverso.";

/* export : import settings : file error description */
"The selected file is either not a valid SPF file or severely corrupted." = "Il file selezionato non è un file SPF valido o gravemente danneggiato.";

/* error deleting relation informative message */
"The selected relation couldn't be deleted.\n\nMySQL said: %@" = "La relazione selezionata non può essere eliminata.\n\nMySQL ha detto: %@";

/* error deleting trigger informative message */
"The selected trigger couldn't be deleted.\n\nMySQL said: %@" = "Il trigger selezionato non può essere eliminato.\n\nMySQL ha detto: %@";

/* description of tinyint */
"The smallest integer type, requires 1 byte storage space. M is the optional display width and does not affect the possible value range." = "Il tipo intero più piccolo, richiede 1 byte di spazio di archiviazione. M è la larghezza di visualizzazione opzionale e non influisce sull'intervallo di valori possibile.";

/* message of panel when connection to socket failed because optional socket could not be found */
"The socket file could not be found in any common location. Please supply the correct socket location.\n\nMySQL said: %@" = "Il file socket non può essere trovato in nessuna posizione comune. Si prega di fornire la posizione corretta del socket.\n\nMySQL ha detto: %@";

/* error creating relation informative message */
"The specified relation could not be created.\n\nMySQL said: %@" = "La relazione specificata non può essere creata.\n\nMySQL ha detto: %@";

/* error creating trigger informative message */
"The specified trigger was unable to be created.\n\nMySQL said: %@" = "Impossibile creare il trigger specificato.\n\nMySQL ha detto: %@";

/* sql import : charset error alert : detail message */
"The SQL file uses utf8mb4 encoding, but your MySQL version only supports the limited utf8 subset.\n\nYou can continue the import, but any non-BMP characters in the SQL file (eg. some typographic and scientific special characters, archaic CJK logograms, emojis) will be unrecoverably lost!" = "Il file SQL utilizza la codifica utf8mb4, ma la versione MySQL supporta solo il sottoinsieme utf8 limitato.\n\nÈ possibile continuare l'importazione, ma qualsiasi carattere non BMP nel file SQL (ad es. alcuni caratteri tipografici e scientifici speciali, arcaici logogrammi CJK, emojis) andranno persi irrimediabilmente!";

/* SQL file open error */
"The SQL file you selected could not be found or read." = "Il file SQL selezionato non può essere trovato o letto.";

/* Prompt for SSH password when keychain fetch failed */
"The SSH password could not be loaded from the keychain; please enter the SSH password for %@:" = "La password SSH non può essere caricata dal portachiavi; inserisci la password SSH per %@:";

/* Prompt for SSH password when direct fetch failed */
"The SSH password could not be loaded; please enter the SSH password for %@:" = "La password SSH non può essere caricata; inserisci la password SSH per %@:";

/* SSH tunnel authentication failed message */
"The SSH Tunnel could not authenticate with the remote host. Please check your password and ensure you still have access." = "Il tunnel SSH non è stato in grado di autenticarsi con l'host remoto. Controlla la password e assicurati di avere ancora accesso.";

/* SSH tunnel unexpectedly closed */
"The SSH Tunnel has unexpectedly closed." = "Il tunnel SSH è stato chiuso inaspettatamente.";

/* SSH tunnel was closed by remote host message */
"The SSH Tunnel was closed 'by the remote host'. This may indicate a networking issue or a network timeout." = "Il tunnel SSH è stato chiuso 'dall'host remoto'. Questo può indicare un problema di rete o un timeout di rete.";

/* SSH tunnel forwarding port connection refused message */
"The SSH Tunnel was established successfully, but could not forward data to the remote port as the remote port refused the connection." = "Il tunnel SSH è stato istituito con successo, ma non è stato possibile inoltrare i dati alla porta remota in quanto la porta remota ha rifiutato la connessione.";

/* SSH tunnel unable to bind to local port message */
"The SSH Tunnel was unable to bind to the local port. This error may occur if you already have an SSH connection to the same server and are using a 'LocalForward' setting in your SSH configuration.\n\nWould you like to fall back to a standard connection to localhost in order to use the existing tunnel?" = "Il tunnel SSH non è stato in grado di legarsi al porto locale. Questo errore può verificarsi se si dispone già di una connessione SSH allo stesso server e si sta utilizzando un'impostazione 'LocalForward' nella configurazione SSH.\n\nVuoi tornare a una connessione standard a localhost per utilizzare il tunnel esistente?";

/* SSH tunnel failed or timed out message */
"The SSH Tunnel was unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds)." = "Il tunnel SSH non è stato in grado di connettersi all'host %1$@, o la richiesta è scaduta.\n\nAssicurati che l'indirizzo sia corretto e che tu abbia i privilegi necessari, o prova ad aumentare il timeout della connessione (attualmente %2$ld secondi).";

/* message of panel when loading of table failed and presumably due to used filter argument */
"The table data couldn't be loaded presumably due to used filter clause. \n\nMySQL said: %@" = "I dati della tabella non possono essere caricati presumibilmente a causa della clausola di filtro utilizzata. \n\nMySQL ha detto: %@";

/* message of panel when loading of table failed */
"The table data couldn't be loaded.\n\nMySQL said: %@" = "I dati della tabella non possono essere caricati.\n\nMySQL ha detto: %@";

/* Export folder not writable explanatory text */
"The target export folder is not writable.  Please select a new export location and try again." = "La cartella di esportazione di destinazione non è scrivibile. Si prega di selezionare una nuova posizione di esportazione e riprovare.";

/* Export folder not chosen by user explanatory text */
"No directory selected.  Please select a new export location and try again." = "Nessuna directory selezionata. Seleziona una nuova posizione di esportazione e riprova.";
"No directory selected." = "Nessuna directory selezionata.";
"Please select a new export location and try again." = "Seleziona una nuova posizione di esportazione e riprova.";

/* Export folder missing explanatory text */
"The target export folder no longer exists.  Please select a new export location and try again." = "La cartella di esportazione di destinazione non esiste più. Si prega di selezionare una nuova posizione di esportazione e riprovare.";

/* theme name label */
"Theme Name:" = "Nome Tema:";

/* themes installation error */
"Themes Installation Error" = "Errore Di Installazione Temi";

/* message of panel when table content cannot be copied */
"There have been errors while copying table content. Please check the new table." = "Ci sono stati errori durante la copia del contenuto della tabella. Si prega di controllare la nuova tabella.";

/* Cannot duplicate a table with triggers to a different database */
"Cannot duplicate a table with triggers to a different database." = "Impossibile duplicare una tabella con trigger in un database diverso.";

/* text shown when query was successfull */
"There were no errors." = "Non ci sono stati errori.";

/* delete field and foreign key informative message */
"This field is part of a foreign key relationship with the table '%@'. This relationship must be removed before the field can be deleted.\n\nAre you sure you want to continue to delete the relationship and the field? This action cannot be undone." = "Questo campo fa parte di una relazione chiave straniera con la tabella '%@'. Questa relazione deve essere rimossa prima che il campo possa essere eliminato.\n\nSei sicuro di voler continuare a eliminare la relazione e il campo? Questa azione non può essere annullata.";

/* table structure : indexes : delete index : error 1553, no FK found : description */
"This index cannot be deleted, because it is used by an existing foreign key relationship.\n\nPlease remove the relationship, before trying to remove this index.\n\nMySQL said: %@" = "Questo indice non può essere cancellato, perché è utilizzato da una relazione di chiave straniera esistente.\n\nSi prega di rimuovere la relazione, prima di provare a rimuovere questo indice.\n\nMySQL ha detto: %@";

/* description of serial */
"This is an alias for BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE." = "Questo è un alias per BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE.";

/* description of dec
 description of fixed
 description of numeric */
"This is an alias for DECIMAL." = "Questo è un alias per DECIMAL.";

/* description of double real */
"This is an alias for DOUBLE, unless REAL_AS_FLOAT is configured." = "Questo è un alias per DOUBLE, a meno che REAL_AS_FLOAT non sia configurato.";

/* description of double precision */
"This is an alias for DOUBLE." = "Questo è un alias per DOUBLE.";

/* description of bool
 description of boolean */
"This is an alias for TINYINT(1)." = "Questo è un alias per TINYINT(1).";

/* Table Structure : Collation dropdown : database marker tooltip */
"This is the default collation of database %@." = "Questo è l'ordinamento predefinito del database %@.";

/* Table Structure : Collation dropdown : default marker tooltip */
"This is the default collation of encoding %@." = "Questo è l'ordinamento predefinito della codifica %@.";

/* Table Structure : Collation dropdown : table marker tooltip */
"This is the default collation of table %@." = "Questo è l'ordinamento predefinito della tabella %@.";

/* Table Structure : Collation dropdown : server marker tooltip */
"This is the default collation of this server." = "Questo è l'ordinamento predefinito di questo server.";

/* Table Structure : Encoding dropdown : database marker tooltip */
"This is the default encoding of database %@." = "Questa è la codifica predefinita del database %@.";

/* Table Structure : Encoding dropdown : table marker tooltip */
"This is the default encoding of table %@." = "Questa è la codifica predefinita della tabella %@.";

/* Table Structure : Encoding dropdown : server marker tooltip */
"This is the default encoding of this server." = "Questa è la codifica predefinita di questo server.";

/* This table currently does not support relations. Only tables that use the InnoDB storage engine support them. */
"This table currently does not support relations. Only tables that use the InnoDB storage engine support them." = "Questa tabella al momento non supporta le relazioni. Solo le tabelle che utilizzano il motore di archiviazione InnoDB le supportano.";

/* user has no hosts informative message */
"This user doesn't have any hosts associated with it. It will be deleted unless one is added" = "Questo utente non ha alcun host ad esso associato. Sarà eliminato a meno che non venga aggiunto uno";

/* error removing host informative message */
"This user doesn't seem to have any associated hosts and will be removed unless a host is added." = "Questo utente non sembra avere host associati e verrà rimosso a meno che non venga aggiunto un host.";

/* shutdown server : confirmation dialog : message */
"This will wait for open transactions to complete and then quit the mysql daemon. Afterwards neither you nor anyone else can connect to this database!\n\nFull management access to the server's operating system is required to restart MySQL!" = "Questo aspetterà che le transazioni vengano aperte per completare e poi uscire dal demone mysql. In seguito, né tu né nessun altro puoi connetterti a questo database!\n\nL'accesso completo alla gestione del sistema operativo del server è necessario per riavviare MySQL!";

/* export filename time token */
"Time" = "Tempo";

/* toolbar item label for switching to the Table Triggers tab */
"Triggers" = "Trigger";

/* triggers for table label */
"Triggers for table: %@" = "Trigger per tabella: %@";

/* truncate button */
"Truncate" = "Tronca";

/* truncate tables message */
"Truncate selected tables?" = "Tronca le tabelle selezionate?";

/* truncate table menu title */
"Truncate Table..." = "Troncamento Tabella...";

/* truncate table message */
"Truncate table '%@'?" = "Tronca tabella '%@'?";

/* truncate tables menu item */
"Truncate Tables" = "Tronca Tabelle";

/* type label (Navigator) */
"Type" = "Tipo";

/* type declaration header */
"Type Declaration:" = "Dichiarazione Di Tipo:";

/* add index error message */
"Unable to add index" = "Impossibile aggiungere l'indice";

/* unable to analyze selected items message */
"Unable to analyze selected items" = "Impossibile analizzare gli elementi selezionati";

/* unable to analyze table message */
"Unable to analyze table" = "Impossibile analizzare la tabella";

/* unable to check selected items message */
"Unable to check selected items" = "Impossibile controllare gli elementi selezionati";

/* unable to check table message */
"Unable to check table" = "Impossibile controllare la tabella";

/* message of panel when connection to host failed due to access denied error */
"Unable to connect to host %@ because access was denied.\n\nDouble-check your username and password and ensure that access from your current location is permitted.\n\nMySQL said: %@" = "Impossibile connettersi all'host %1$@ perché l'accesso è stato negato.\n\nVerifica due volte il tuo nome utente e la password e assicurati che l'accesso dalla tua posizione attuale sia consentito.\n\nMySQL ha detto: %2$@";

/* message of panel when SSH port forwarding failed */
"Unable to connect to host %@ because the port connection via SSH was refused.\n\nPlease ensure that your MySQL host is set up to allow TCP/IP connections (no --skip-networking) and is configured to allow connections from the host you are tunnelling via.\n\nYou may also want to check the port is correct and that you have the necessary privileges.\n\nChecking the error detail will show the SSH debug log which may provide more details.\n\nMySQL said: %@" = "Impossibile connettersi all'host %1$@ perché la connessione alla porta tramite SSH è stata rifiutata.\n\nAssicurati che il tuo host MySQL sia configurato per consentire connessioni TCP/IP (no --skip-networking) ed è configurato per consentire connessioni dall'host che stai tunnellando via.\n\nPotresti anche voler controllare che la porta sia corretta e che tu abbia i privilegi necessari.\n\nControllare il dettaglio dell'errore mostrerà il registro di debug SSH che potrebbe fornire maggiori dettagli.\n\nMySQL ha dichiarato: %2$@";

/* message of panel when connection to host failed */
"Unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds).\n\nMySQL said: %@" = "Impossibile connettersi all'host %1$@, o la richiesta è scaduta.\n\nAssicurati che l'indirizzo sia corretto e che tu abbia i privilegi necessari, o prova ad aumentare il timeout della connessione (attualmente %2$ld secondi).\n\nMySQL ha detto: %3$@";

/* message of panel when connection to host failed */
"Unable to connect via the socket, or the request timed out.\n\nDouble-check that the socket path is correct and that you have the necessary privileges, and that the server is running.\n\nMySQL said: %@" = "Impossibile connettersi tramite il socket o la richiesta è scaduta.\n\nVerifica che il percorso del socket sia corretto e che tu abbia i privilegi necessari e che il server sia in esecuzione.\n\nMySQL ha detto: %@";

/* unable to copy database message */
"Unable to copy database" = "Impossibile copiare il database";

/* error deleting index message */
"Unable to delete index" = "Impossibile eliminare l'indice";

/* error deleting relation message */
"Unable to delete relation" = "Impossibile eliminare la relazione";

/* error deleting trigger message */
"Unable to delete trigger" = "Impossibile eliminare il trigger";

/* unable to flush selected items message */
"Unable to flush selected items" = "Impossibile risciacquare gli elementi selezionati";

/* unable to flush table message */
"Unable to flush table" = "Impossibile lavare la tabella";

/* unable to get list of users message */
"Unable to get list of users" = "Impossibile ottenere l'elenco degli utenti";

/* error killing connection message */
"Unable to kill connection" = "Impossibile terminare la connessione";

/* error killing query message */
"Unable to kill query" = "Impossibile terminare la query";

/* unable to optimze selected items message */
"Unable to optimze selected items" = "Impossibile ottimizzare gli elementi selezionati";

/* unable to optimze table message */
"Unable to optimze table" = "Impossibile ottimizzare la tabella";

/* unable to perform the checksum */
"Unable to perform the checksum" = "Impossibile eseguire il checksum";

/* error removing host message */
"Unable to remove host" = "Impossibile rimuovere l'host";

/* unable to rename database message */
"Unable to rename database" = "Impossibile rinominare il database";

/* unable to repair selected items message */
"Unable to repair selected items" = "Impossibile riparare gli elementi selezionati";

/* unable to repair table message */
"Unable to repair table" = "Impossibile riparare la tabella";

/* message of panel when connection to db failed after selecting from popupbutton */
"Unable to select database %@.\nPlease check you have the necessary privileges to view the database, and that the database still exists." = "Impossibile selezionare il database %@.\nSi prega di controllare di avere i privilegi necessari per visualizzare il database e che il database esista ancora.";

/* Unable to write row error */
"Unable to write row" = "Impossibile scrivere la riga";

/* Table Content : Remove Row : Result : n Error title */
"Unexpected number of rows removed!" = "Numero di righe rimosse inatteso!";

/* warning */
"Unknown file format" = "Formato file sconosciuto";

/* unsaved changes message */
"Unsaved changes" = "Modifiche non salvate";

/* unsaved theme message */
"Unsaved Theme" = "Tema Non Salvato";

/* Preferences : Network : Custom SSH client : warning dialog title */
"Unsupported configuration!" = "Configurazione non supportata!";

/* export : import settings : file version error title */
"Unsupported version for export settings!" = "Versione non supportata per le impostazioni di esportazione!";

/* Name for an untitled connection */
"Untitled" = "Senza Titolo";

/* Title of a new Sequel Ace Document */
"Untitled %ld" = "%ld Senza Titolo";

/* range for mediumblob type */
"Up to %@ bytes (16 MiB)" = "Fino ad %@ bytes (16 MiB)";

/* range for longblob type */
"Up to %@ bytes (4 GiB)" = "Fino ad %@ bytes (4 GiB)";

/* range for mediumtext type */
"Up to %@ characters (16 MiB)" = "Fino a %@ caratteri (16 MiB)";

/* range for enum type */
"Up to %@ distinct members (<%@ in practice)\n1-2 bytes storage" = "Fino a %1$@ membri distinti (<%2$@ in pratica)\n1-2 byte di archiviazione";

/* range for tinyblob type */
"Up to 255 bytes" = "Fino a 255 byte";

/* range for tinytext type */
"Up to 255 characters" = "Fino a 255 caratteri";

/* Open Files : Bundle : Already-Installed : Update button */
"Update" = "Aggiorna";

/* updated: %@ */
"updated: %@" = "aggiornato: %@";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field of table `%2$@` was changed." = "Aggiornamento del contenuto del campo non riuscito. Impossibile identificare l'origine del campo in modo univoco (%1$ld corrisponde). È molto probabile che durante la modifica di questo campo della tabella `%2$@` sia stato cambiato.";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field the table `%2$@` was changed by an other user." = "Aggiornamento del contenuto del campo non riuscito. Impossibile identificare l'origine del campo in modo univoco (%1$ld corrisponde). È molto probabile che durante la modifica di questo campo la tabella `%2$@` sia stata cambiata da un altro utente.";

/* updating field task description */
"Updating field data..." = "Aggiornamento dati campo...";

/* URL scheme command couldn't authenticated */
"URL scheme command couldn't authenticated" = "Comando schema URL non riuscito ad autenticare";

/* URL scheme command was terminated by user */
"URL scheme command was terminated by user" = "Il comando dello schema URL è stato terminato dall'utente";

/* URL scheme command %@ unsupported */
"URL scheme command %@ unsupported" = "Comando dello schema URL %@ non supportato";

/* Use 127.0.0.1 button */
"Use 127.0.0.1" = "Usa 127.0.0.1";

/* use standard connection button */
"Use Standard Connection" = "Usa Connessione Standard";

/* user has no hosts message */
"User has no hosts" = "L'utente non ha host";

/* user-defined value */
"User-defined value" = "Valore definito dall'utente";

/* toolbar item label for switching to the User Manager tab */
"Users" = "Utenti";

/* CSV Field Mapping : Table View : Tooltip for fields with NULL value */
"Value will be imported as MySQL NULL" = "Il valore verrà importato come MySQL NULL";

/* variable label for color table (Prefs > Editor) */
"Variable" = "Variabile";

/* version */
"version" = "versione";

/* export header version label */
"Version" = "Versione";

/* view */
"view" = "visualizza";

/* Release notes button title */
"View full release notes" = "Visualizza le note di rilascio complete";

/* header for view info pane */
"VIEW INFORMATION" = "VISUALIZZA INFORMAZIONI";

/* view html source code menu item title */
"View Source" = "Visualizza Sorgente";

/* view structure print heading */
"View Structure" = "Visualizza Struttura";

/* views */
"views" = "visualizzazioni";

/* warning */
"Warning" = "Attenzione";

/* Short important release note for why password prompts may occur */
"We've changed Sequel Ace's digital signature for GateKeeper compatibility; you'll have to allow access to your passwords again." = "Abbiamo cambiato la firma digitale di Sequel Ace per la compatibilità con GateKeeper; dovrai consentire nuovamente l'accesso alle tue password.";

/* Important release notes informational text, single change */
"We've made a few changes but we thought you should know about one particularly important one:" = "Abbiamo apportato alcune modifiche, ma abbiamo pensato che doveste sapere su uno particolarmente importante:";

/* Important release notes informational text, multiple changes */
"We've made a few changes but we thought you should know about some particularly important ones:" = "Abbiamo apportato alcune modifiche, ma abbiamo pensato che dovreste sapere su alcune particolarmente importanti:";

/* WHERE clause not valid */
"WHERE clause not valid" = "WHERE clausola non valida";

/* Title of filter preview area when the query WHERE is negated */
"WHERE NOT query" = "WHERE NOT query";

/* Title of filter preview area when the query WHERE is normal */
"WHERE query" = "Interrogazione QUANTO";

/* Generic working description */
"Working..." = "Lavorando...";

/* export label showing app is writing data */
"Writing data..." = "Scrittura dati...";

/* text showing that app is writing text file */
"Writing..." = "Scrittura...";

/* wrong data format or password */
"Wrong data format or password." = "Formato dati o password errati.";

/* wrong data format */
"Wrong data format." = "Formato dati errato.";

/* export : import settings : spf content type error title */
"Wrong SPF content type!" = "Tipo di contenuto SPF errato!";

/* export filename date token */
"Year" = "Anno";

/* message of panel when trying to copy multiple rows */
"You can only copy single rows." = "È possibile copiare solo singole righe.";

/* message of panel when trying to edit tables without index and with hidden blob/text fields */
"You can't hide blob and text fields when working with tables without index." = "Non puoi nascondere i campi di testo e blob quando lavori con tabelle senza indice.";

/* You cannot delete the last field in a table. Delete the table instead. */
"You cannot delete the last field in a table. Delete the table instead." = "Non puoi eliminare l' ultimo campo in una tabella. Elimina la tabella.";

/* SSL connection requested but not established error detail */
"You requested that the connection should be established using SSL, but MySQL made the connection without SSL.\n\nThis may be because the server does not support SSL connections, or has SSL disabled; or insufficient details were supplied to establish an SSL connection.\n\nThis connection is not encrypted." = "Hai richiesto che la connessione sia stabilita utilizzando SSL, ma MySQL ha effettuato la connessione senza SSL.\n\nQuesto potrebbe essere dovuto al fatto che il server non supporta le connessioni SSL, o ha SSL disabilitato; o non sono stati forniti dettagli sufficienti per stabilire una connessione SSL.\n\nQuesta connessione non è cifrata.";

/* Query Favorites : List : Section Heading : current connection document : tooltip (arg is the name of the spf file) */
"‘%@’ based favorites" = "‘%@’ preferiti basati";

/* table column header. Read: 'Showing all content filters for fields of type %@' (ContentFilterManager) */
"‘%@’ Fields Content Filters" = "‘%@’ Filtri Di Contenuto Campi";

/* Export file already exists message */
"%@ already exists. Do you want to replace it?" = "%@ esiste già. Vuoi sostituirlo?";

/* Bundle Editor : Outline View : Bundle item : tooltip */
"%@ Bundle" = "Pacchetto %@";

/* Export file creation error title */
"%@ could not be created" = "%@ non può essere creato";

/* %@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item. */
"%@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item." = "%@ non può essere analizzato. È possibile modificare la configurazione della colonna, ma la colonna non sarà mostrata nella vista Contenuti; per favore segnala questo problema al team Sequel Ace utilizzando la voce del menu Aiuto.";

/* connection view : ssl : client cert file picker : wrong format error title */
"%@ is not a valid client certificate file." = "%@ non è un file di certificato client valido.";

/* connection view : ssl : key file picker : wrong format error title */
"%@ is not a valid private key file." = "%@ non è un file di chiave privata valido.";

/* text shown when there are App Sandbox Issues */
"App Sandbox Issue" = "Problema Sandbox App";

/* Stale Bookmarks error title */
"Stale Bookmarks" = "Segnalibri Stali";

/* App Sandbox info link text */
"App Sandbox Info" = "App Sandbox Info";

/* error while selecting file title */
"File Selection Error" = "Errore Selezione File";

/* error while selecting file message */
"The selected file is not a valid file.\n\nPlease try again.\n\nClass: %@" = "Il file selezionato non è un file valido.\n\nRiprova. Classe\n\n: %@";

/* known hosts not writable error message */
"The selected known hosts file is not writable.\n\n%@\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "Il file host selezionato non è scrivibile.\n\n%@\n\nSi prega di ri-selezionare il file nelle preferenze di Sequel Ace e riprovare.";

/* known hosts is invalid message */
"The selected known hosts file is invalid.\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "Il file host selezionato non è valido.\n\nSelezionare nuovamente il file nelle preferenze di Sequel Ace e riprovare.";

/* known hosts contains quote message */
"The selected known hosts file contains a quote (\") in its file path which is not supported.\n\n%@\n\nPlease select a different file in Sequel Ace's Preferences or rename the file/path to remove the quote." = "Il file host selezionato contiene un preventivo (\") nel suo percorso del file che non è supportato.\n\n%@\n\nSi prega di selezionare un file diverso nelle preferenze di Sequel Ace o rinominare il file/percorso per rimuovere il preventivo.";

/* SSH Tunnel Debugging menu and window title */
"SSH Tunnel Debugging Info" = "Informazioni Di Debug Tunnel Ssh";

/* Question to user to see if they would like to re-request access */
"You have stale secure bookmarks:\n\n%@\n\nWould you like to re-request access now?" = "Hai dei segnalibri sicuri:\n\n%@\n\nVuoi richiedere nuovamente l'accesso adesso?";

/* Question to user to see if they would like to request access to missing bookmarks */
"You have missing secure bookmarks:\n\n%@\n\nWould you like to request access now?" = "Hai dei segnalibri sicuri mancanti:\n\n%@\n\nVuoi richiedere l'accesso adesso?";

/* known hosts advance menu item title */
"Use known hosts from ssh config (ADVANCED)" = "Usa host conosciuti dalla configurazione ssh (ADVANCED)";

/* The answer, yes */
"Yes" = "Sì";

/* Shown to remind users of their stale bookmarks */
"A reminder of your stale secure bookmarks:<br /><br />%@<br />" = "Un promemoria dei tuoi segnalibri sicuri:<br /><br />%@<br />";

/* Title for Stale Secure Bookmarks help window */
"Stale Secure Bookmarks" = "Segnalibri Sicuri Stali";

/* Title for Export Error alert */
"Export Error" = "Errore Di Esportazione";

/* Message for Export Error alert */
"Error while writing to the export file. Could not open file: %@" = "Errore durante la scrittura sul file di esportazione. Impossibile aprire il file: %@";

/* Message for User window error */
"Resultset from mysql.user contains neither 'Password' nor 'authentication_string' column." = "Il risultato da mysql.user non contiene né la colonna 'Password' né 'authentication_string'.";

/* Title for User window error */
"User Data Error" = "Errore Dati Utente";

/* Title for Stale Bookmark file selection dialog */
"Please re-select the file '%@' in order to restore Sequel Ace's access." = "Per favore seleziona nuovamente il file '%@' per ripristinare l'accesso di Sequel Ace.";

/* Title for File Preference file selection dialog */
"Please choose a file or folder to grant Sequel Ace access to." = "Si prega di scegliere un file o una cartella a cui concedere l'accesso a Sequel Ace.";

/* Title for Network Preference ssh config file selection dialog */
"Please choose your ssh config files(s)" = "Scegli i tuoi file di configurazione ssh";

/* Title for Network Preference ssh known hosts file selection dialog */
"Please choose your known hosts file" = "Scegli il tuo file host conosciuto";

/* Message for field editor JSON segment when JSON is invalid */
"Invalid JSON" = "JSON Non Valido";

/* Applying syntax highlighting task description */
"Applying syntax highlighting..." = "Applicazione evidenziazione sintassi...";

/* Error message when a bash task fails to start */
"Couldn't launch task.\nException reason: %@\n ENV length: %lu" = "Impossibile avviare l'attività.\nMotivo di eccezione: %@\n lunghezza ENV: %lu";

/* Error message title when we failed to create new connection view */
"New Connection Error" = "Nuovo Errore Di Connessione";

/* New Connection Error informative message */
"Failed to create new database connection window. Please restart Sequel Ace and try again." = "Impossibile creare la nuova finestra di connessione del database. Riavviare Sequel Ace e riprovare.";

/* new version is available alert title */
"A new version is available" = "È disponibile una nuova versione";

/* new version is available download button title */
"Download" = "Scarica";

/* new version is available alert informativeText */
"Version %@ is available. You are currently running %@" = "La versione %@ è disponibile. Stai eseguendo %@";

/* downloading new version window title */
"Download Progress" = "Progresso Download";

/* downloading new version time remaining placeholder text */
"Calculating time remaining..." = "Calcolo del tempo rimanente...";

/* downloading new version window message */
"Downloading Sequel Ace - %@" = "Scaricamento Asso Sequel - %@";

/* downloading new version time remaining estimate text */
"About %.1f seconds left" = "Informazioni su %.1f secondi rimasti";

/* downloading new version failure alert title */
"Download Failed" = "Download Non Riuscito";

/* Tool tip for the Show alert when update is available preference on the Notifications pane */
"Only available for GitHub downloads" = "Disponibile solo per i download di GitHub";

/* Tool tip warning for when the user sets auto-complete delay to zero */
"WARNING: Setting the auto-complete delay to 0.0 can result in strange output." = "ATTENZIONE: Impostare il ritardo di completamento automatico a 0.0 può causare uno strano output.";

/* downloading new version bytes of bytes done */
"%@ of %@" = "%@ di %@";

/* Time zone set failed informative message */
"\n\ntime_zone will be set to SYSTEM." = "\n\ntime_zone sarà impostato su SYSTEM.";

/* Menu item title for checking for updates */
"Check for Updates..." = "Controlla aggiornamenti...";

/* Warning message during connection in case the variable skip-show-database is set to ON */
"The skip-show-database variable of the database server is set to ON. Thus, you won't be able to list databases unless you have the SHOW DATABASES privilege.\n\nHowever, the databases are still accessible directly through SQL queries depending on your privileges." = "La variabile skip-show-database del server database è impostata su ON. Così, non sarai in grado di elencare i database a meno che tu non abbia il privilegio SHOW DATABASES.\n\nTuttavia, i database sono ancora accessibili direttamente tramite query SQL a seconda dei tuoi privilegi.";

/* Error alert title when the request to GitHub fails */
"GitHub Request Failed" = "Richiesta GitHub Non Riuscita";

/* Info alert title when there are Newer Releases Available */
"No Newer Release Available" = "Nessuna Rilascio Più Recente Disponibile";

/* Info alert message when there are Newer Releases Available */
"You are currently running the latest release." = "Attualmente stai eseguendo l'ultima versione.";

/* Alert button, disable the warning when skip-show-database is set to on */
"Never show this again" = "Non mostrarlo più";

/* Info alert when editing a generated column */
"The current field \"%@\" is a generated column and therefore cannot be edited." = "Il campo corrente \"%@\" è una colonna generata e quindi non può essere modificato.";

/* Release note / info modal about change in latest release about column behavior */
"The use of the \"default\" column has changed since the last version of Sequel ACE:\n\n- No default value : Leave it blank.\n- String value : Use single '' or double quotes \"\" if you want an empty string or to wrap a string\n- Expression : Use parentheses (). Except for the TIMESTAMP and DATETIME columns where you can specify the CURRENT_TIMESTAMP function without enclosing parentheses." = "L'uso della colonna \"default\" è cambiato dall'ultima versione di Sequel ACE:\n\n- Nessun valore predefinito: Lascialo vuoto.\n- Valore stringa : Usa singole virgolette o doppie \"\" se vuoi una stringa vuota o per avvolgere una stringa\n- Espressione : Usa parentesi (). Tranne che per le colonne TIMESTAMP e DATETIME dove è possibile specificare la funzione CURRENT_TIMESTAMP senza accludere parentesi.";

/* Menu option to pin a view to the top of the list of tables */
"Pin View" = "Vista PIN";

/* Menu option to pin a table to the top of the list of tables */
"Pin Table" = "Pin Table";

/* Menu option to pin a procedure to the top of the list of tables */
"Pin Procedure" = "Procedura Pin";

/* Menu option to pin a function to the top of the list of tables */
"Pin Function" = "Funzione Pin";

/* Menu option to unpin a view which is currently pinned at the top of the list of tables */
"Unpin View" = "Sblocca Vista";

/* Menu option to unpin a table which is currently pinned at the top of the list of tables */
"Unpin Table" = "Sblocca Tabella";

/* Menu option to unpin a procedure which is currently pinned at the top of the list of tables */
"Unpin Procedure" = "Sblocca Procedura";

/* Menu option to unpin a function which is currently pinned at the top of the list of tables */
"Unpin Function" = "Sblocca Funzione";

/* header for pinned table list */
"PINNED" = "PINNED";

/* Menu option to copy a table's name with database name in the list of tables */
"Copy Table Name" = "Copia Nome Tabella";

/* sequelace URL scheme error: LaunchFavorite alert title (favorite name not found) */
"LaunchFavorite URL Scheme Error" = "Errore Schema Url LaunchFavorite";

/* sequelace URL scheme error: LaunchFavorite alert description (favorite name not found) */
"The variable in the ?name= query parameter could not be matched with any of your favorites." = "La variabile nel parametro ?name= query non può essere abbinata a nessuno dei tuoi preferiti.";
