/* Table Content : Remove Row : Result : Too Few : Part 3 : Row not deleted when using primary key for DELETE statement. */
" Check the Console for possible errors inside the primary key(s) of this table!" = "¡Compruebe la consola por posibles errores en la clave(s) primaria(s) de esta tabla!";

/* Table Content : Remove Row : Result : Too Many : Part 2 : Generic text */
" Please check the Console and inform the Sequel Ace team!" = "¡Por favor, compruebe la consola y informe al equipo de Sequel Ace!";

/* Table Content : Remove Row : Result : Too Few : Part 2 : Generic help message */
" Reload the table to be sure that the contents have not changed in the meantime." = "Vuelva a cargar la tabla para asegurarse de que el contenido no ha sido modificado desde entonces.";

/* Table Content : Remove Row : Result : Too Few : Part 3 : no primary key in table generic message */
" You should also add a primary key to this table!" = "¡También debe añadir una clave primaria a esta tabla!";

/* Query snippet shell command syntax and placeholder */
"$(shell_command)" = "$(shell_command)";

/* text showing how many rows are selected */
"%@ %@ selected" = "%1$@ %2$@ seleccionada";

/* History item filtered by values label */
"%@ (Filtered by %@)" = "%1$@ (Filtrado por %2$@)";

/* History item with page number label */
"%@ (Page %lu)" = "%1$@ (Página %2$lu)";

/* Content Filter Manager : Initial name of copied filter
 Initial favourite name after duplicating a previous favourite */
"%@ Copy" = "Copia de %@";

/* text showing a single row a partially loaded result */
"%@ row in partial load" = "%@ fila en la consulta parcial";

/* text showing a single row in the result */
"%@ row in table" = "%@ fila en la tabla";

/* text showing how a single rows matched filter */
"%@ row of %@%@ matches filter" = "%1$@ fila de %2$@%3$@ cumple el filtro";

/* text showing how many rows are in a partially loaded result */
"%@ rows in partial load" = "%@ filas cargadas parcialmente";

/* text showing how many rows are in the result */
"%@ rows in table" = "%@ filas en la tabla";

/* text showing how many rows matched filter */
"%@ rows of %@%@ match filter" = "%1$@ filas de %2$@%3$@ cumplen el filtro";

/* text showing how many rows have been affected by a single query */
"%@; %ld rows affected" = "%1$@; %2$ld filas afectadas";

/* text showing how many rows have been affected by multiple queries */
"%@; %ld rows affected in total, by %ld queries taking %@" = "%1$@; %2$ld filas afectadas en total, por %3$ld consultas en %4$@";

/* text showing one row has been affected by a single query */
"%@; 1 row affected" = "%@; 1 fila afectada";

/* text showing one row has been affected by multiple queries */
"%@; 1 row affected in total, by %ld queries taking %@" = "%1$@; 1 fila afectada en total, por %2$ld consultas en %3$@";

/* text showing a query was cancelled */
"%@; Cancelled after %@" = "%1$@; Cancelada después de %2$@";

/* text showing multiple queries were cancelled */
"%@; Cancelled in query %ld, after %@" = "%1$@; Canceladas en la consulta %2$ld, después de %3$@";

/* Error display text, showing original MySQL error */
"%@\n\nMySQL said: %@" = "%1$@\n\nMySQL dijo: %2$@";

/* favorite singular label (%d == 1) */
"%lu favorite" = "%lu favorito";

/* favorites plural label (%d != 1) */
"%lu favorites" = "%lu favoritos";

/* favorite group singular label (%d == 1) */
"%lu group" = "%lu grupo";

/* favorite groups plural label (%d != 1) */
"%lu groups" = "%lu grupos";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+y (y!=1) rows instead of n selected were deleted. */
"%ld additional rows were removed!" = "¡%ld filas adicionales fueron eliminadas!";

/* Label showing the index of the selected CSV row */
"%ld of %lu record(s)" = "%1$ld de %2$lu registro(s)";

/* Label showing the index of the selected CSV row (csv partially loaded) */
"%ld of first %lu record(s)" = "%1$ld de los primeros %2$lu registro(s)";

/* Table Content : Remove Row : Result : Too Few : Part 1 : n-x (x!=1) of n selected rows were deleted. */
"%ld rows were not removed." = "%ld filas no fueron eliminadas.";

/* Export file already exists message */
"%lu files already exist. Do you want to replace them?" = "%lu archivos ya existen. ¿Desea reemplazarlos?";

/* Export files creation error title */
"%lu files could not be created" = "%lu archivos no se han podido crear";

/* Some export files already exist explanatory text */
"%lu files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "%lu archivos con los mismos nombres ya existen en la carpeta de destino. Reemplazándolos sobreescribirá sus contenidos actuales.";

/* filtered item count */
"%lu of %lu" = "%1$lu de %2$lu";

/* Export folder not writable for some files explanatory text */
"%lu of the export files could not be created because their target export folder is not writable; please select a new export location and try again." = "%lu de los archivos de exportación no se han podido crear debido a que su carpeta de destino no se puede modificar; por favor seleccione una nueva ubicación y vuelva a intentarlo.";

/* Export folder missing for some files explanatory text */
"%lu of the export files could not be created because their target export folder no longer exists; please select a new export location and try again." = "%lu de los archivos de exportación no se han podido crear debido a que su carpeta de destino ya no existe; por favor seleccione una nueva ubicación y vuelva a intentarlo.";

/* History item title with nothing selected */
"(no selection)" = "(sin selección)";

/* value shown for hidden blob and text fields */
"(not loaded)" = "(no cargado)";

/* Explanation for MySQL server has gone away error */
"(This usually indicates that the connection has been closed by the server after inactivity, but can also occur due to other conditions.  The connection has been restored; please try again if the query is safe to re-run.)" = "(Esto suele indicar que la conexión ha sido cerrada por el servidor tras inactividad, pero también puede ocurrir debido a otras condiciones. La conexión ha sido restablecida; por favor inténtelo de nuevo si es seguro volver a ejecutar la consulta.)";

/* Custom Query : text appended to the “x row(s) affected” messages. $1 is a time interval */
", first row available after %1$@" = ", primera fila disponible después de %1$@";

/* Custom Query : text appended to the “x row(s) affected” messages (for update/delete queries). $1 is a time interval */
", taking %1$@" = ", tomando %1$@";

/* warning shown in the console when no rows have been affected after writing to the db */
"/* WARNING: No rows have been affected */\n" = "/* ADVERTENCIA: Ninguna fila ha sido afectada */\n";

/* error text when multiple custom query failed */
"[ERROR in query %ld] %@\n" = "[ERROR en la consulta %1$ld] %2$@\n";

/* error text when reading of csv file gave errors */
"[ERROR in row %ld] %@\n" = "[ERROR en la fila %1$ld] %2$@\n";

/* [multiple selection] */
"[multiple selection]" = "[selección múltiple]";

/* displayed when new content filter has empty Name field (ContentFilterManager) */
"[name required]" = "[nombre requerido]";

/* [no selection] */
"[no selection]" = "[sin selección]";

/* Additional export file errors */
"\n\n(In addition, one or more errors occurred while attempting to create the export files: %lu could not be created. These files will be ignored.)" = "\n\n(Además, uno o más errores han ocurrido al intentar crear los archivos de exportación: %lu no se han podido crear. Estos archivos serán ignorados.)";

/* \n\npress shift for binary search tooltip message */
"\n\nPress ⇧ for binary search (case-sensitive)." = "\n\nPulse ⇧ para búsqueda binaria (sensible a mayúsculas).";

/* description of bit */
"A bit-field type. M specifies the number of bits. If shorter values are inserted, they will be aligned on the least significant bit. See the SET type if you want to explicitly name each bit." = "Un tipo de campo bit. M especifica el número de bits. Si se insertan valores más cortos, se alinearán en el bit menos significativo. Vea el tipo SET si desea nombrar explícitamente cada bit.";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog message */
"A Bundle ‘%@’ is already installed. Do you want to update it?" = "El paquete ‘%@’ ya está instalado. ¿Desea actualizarlo?";

/* description of binary */
"A byte array with fixed length. Shorter values will always be padded to the right with 0x00 until they fit M." = "Un arreglo de bytes con longitud fija. Los valores cortos siempre se añadirán a la derecha con 0x00 hasta que encajen en M.";

/* description of varbinary */
"A byte array with variable length. The actual number of bytes is further limited by the values of other fields in the row." = "Una matriz de bytes con longitud de variable. El número real de bytes está además limitado por los valores de otros campos en la fila.";

/* description of blob
 description of longblob
 description of mediumblob
 description of tinyblob */
"A byte array with variable length. Unlike VARBINARY this type does not count towards the maximum row length." = "Una matriz de bytes con longitud variable. A diferencia de VARBINARY, este tipo no cuenta para la longitud máxima de la fila.";

/* description of tinytext */
"A character string that can store up to 255 bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Una cadena de caracteres que puede almacenar hasta 255 bytes, pero requiere menos espacio para valores más cortos. El número real de caracteres está limitado además por la codificación utilizada. A diferencia de VARCHAR este tipo no cuenta para la longitud máxima de la fila.";

/* description of varchar */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding and the values of other fields in the row." = "Una cadena de caracteres que puede almacenar hasta bytes M, pero que requiere menos espacio para valores más cortos. El número real de caracteres está aún más limitado por la codificación utilizada y los valores de otros campos en la fila.";

/* description of text */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Una cadena de caracteres que puede almacenar hasta bytes M, pero que requiere menos espacio para valores más cortos. El número real de caracteres está limitado aún más por la codificación utilizada. A diferencia de VARCHAR este tipo no cuenta para la longitud máxima de la fila.";

/* description of char */
"A character string that will require M×w bytes per row, independent of the actual content length. w is the maximum number of bytes a single character can occupy in the given encoding." = "Una cadena de caracteres que requerirá bytes M×w por fila, independientemente de la longitud real del contenido. w es el número máximo de bytes que un solo carácter puede ocupar en la codificación dada.";

/* description of longtext
 description of mediumtext */
"A character string with variable length. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Una cadena de caracteres con longitud variable. El número real de caracteres está además limitado por la codificación utilizada. A diferencia de VARCHAR este tipo no cuenta para la longitud máxima de fila.";

/* description of json */
"A data type that validates JSON data on INSERT and internally stores it in a binary format that is both, more compact and faster to access than textual JSON.\nAvailable from MySQL 5.7.8." = "Un tipo de dato que valida datos JSON en INSERT y los almacena internamente en un formato binario que es ambos, más compacto y rápido de acceder que el JSON en formato texto.\nDisponible desde MySQL 5.7.8.";

/* Export file already exists explanatory text */
"A file with the same name already exists in the target folder. Replacing it will overwrite its current contents." = "Un archivo con el mismo nombre ya existe en la carpeta de destino. Reemplazándolos sobreescribirá el contenido actual.";

/* description of decimal */
"A fixed-point, exact decimal value. M is the maxium number of digits, of which D may be after the decimal point. When rounding, 0-4 is always rounded down, 5-9 up (“round towards nearest”)." = "Un valor decimal exacto de punto fijo. M es el número máximo de dígitos, de los cuales D puede ser después del punto decimal. Cuando se redondea, el 0-4 siempre se redondea hacia abajo, 5-9 hacia arriba (redondeado hacia el más cercano).";

/* table structure : indexes : delete index : error 1553 : title
 table structure : indexes : delete index : error 1553, no FK found : title */
"A foreign key needs this index" = "Una clave foránea necesita este índice";

/* description of set */
"A SET can define up to 64 members (as strings) of which a field can use one or more using a comma-separated list. Upon insertion the order of members is automatically normalized and duplicate members will be eliminated. Assignment of numbers is supported using the same semantics as for BIT types." = "Un SET puede definir hasta 64 miembros (como cadenas) de los cuales un campo puede usar uno o más usando una lista separada por comas. Después de la inserción el orden de los miembros es normalizado automáticamente y los miembros duplicados serán eliminados. La asignación de números es soportada usando la misma semántica que para los tipos BIT.";

/* SSH key not found message */
"A SSH key location was specified, but no file was found in the specified location.  Please re-select the key and try again." = "La ubicación de la clave SSH fue especificada, pero el archivo no fue encontrado en la ubicación especificada.  Por favor, vuelva a seleccionar la clave y inténtelo de nuevo.";

/* SSL CA certificate file not found message */
"A SSL Certificate Authority certificate location was specified, but no file was found in the specified location.  Please re-select the Certificate Authority certificate and try again." = "La ubicación del certificado SSL CA fue especificada, pero el archivo no fue encontrado en la ubicación especificada.  Por favor, vuelva a seleccionar el certificado CA y inténtelo de nuevo.";

/* SSL certificate file not found message */
"A SSL certificate location was specified, but no file was found in the specified location.  Please re-select the certificate and try again." = "La ubicación del certificado SSL fue especificada, pero el archivo no fue encontrado en la ubicación especificada.  Por favor, vuelva a seleccionar el certificado y inténtelo de nuevo.";

/* SSL key file not found message */
"A SSL key file location was specified, but no file was found in the specified location.  Please re-select the key file and try again." = "La ubicación del archivo de la clave SSL fue especificada, pero el archivo no fue encontrado en la ubicación especificada.  Por favor, vuelva a seleccionar el archivo de la clave y inténtelo de nuevo.";

/* duplicate host informative message */
"A user with the host '%@' already exists" = "El usuario con el equipo '%@' ya existe";

/* duplicate user informative message */
"A user with the name '%@' already exists" = "El usuario con el nombre '%@' ya existe";

/* table content : editing : error message description when parsing as hex string failed */
"A valid hex string may only contain the numbers 0-9 and letters A-F (a-f). It can optionally begin with „0x“ and spaces will be ignored.\nAlternatively the syntax X'val' is supported, too." = "Una cadena hexadecimal válida sólo puede contener los números 0-9 y las letras A-F (a-f). Opcionalmente puede comenzar con „0x“ y los espacios serán ignorados.\nAlternativamente, la sintaxis X'val' también está soportada.";

/* connection failed due to access denied title */
"Access denied!" = "¡Acceso denegado!";

/* range of double */
"Accurate to approx. 15 decimal places" = "Precisión de aproximadamente 15 decimales";

/* range of float */
"Accurate to approx. 7 decimal places" = "Precisión de aproximadamente 7 decimales";

/* active connection window is busy. please wait and try again. tooltip */
"Active connection window is busy. Please wait and try again." = "La conexión activa está ocupada. Por favor, espere y vuelva a intentarlo.";

/* header for activities pane */
"ACTIVITIES" = "ACTIVIDADES";

/* Add trigger button label */
"Add" = "Añadir";

/* menu item to add db */
"Add Database..." = "Añadir base de datos...";

/* Add Host */
"Add Host" = "Añadir host";

/* add global value or expression menu item */
"Add Value or Expression…" = "Añadir valor o expresión…";

/* adding index task status message */
"Adding index..." = "Añadiendo índice...";

/* Advanced options short title */
"Advanced" = "Avanzado";

/* notifications preference pane name */
"Alerts & Logs" = "Alertas & Registros";

/* notifications preference pane tooltip */
"Alerts & Logs Preferences" = "Preferencias de Alertas y Registros";

/* All databases placeholder */
"All Databases" = "Todas las bases de datos";

/* All databases (%) placeholder */
"All Databases (%)" = "Todas las bases de datos (%)";

/* All export files already exist message */
"All the export files already exist. Do you want to replace them?" = "Todos los archivos de exportación ya existen. ¿Desea reemplazarlos?";

/* An error for sequelace URL scheme command occurred. Probably no corresponding connection window found. */
"An error for sequelace URL scheme command occurred. Probably no corresponding connection window found." = "Ha ocurrido un error en el comando de esquema URL de sequelace. Probablemente no se ha encontrado ninguna ventana de conexión que corresponda.";

/* no connection available informatie message */
"An error has occurred and there doesn't seem to be a connection available." = "Ha ocurrido un error y no parece haber una conexión disponible.";

/* an error occur while executing a scheme command. if the scheme command was invoked by a bundle command, it could be that the command still runs. you can try to terminate it by pressing ⌘+. or via the activities pane. */
"An error occur while executing a scheme command. If the scheme command was invoked by a Bundle command, it could be that the command still runs. You can try to terminate it by pressing ⌘+. or via the Activities pane." = "Ha ocurrido un error al ejecutar un comando del esquema. Si el comando del esquema fue invocado, puede ser que el comando siga funcionando. Puede intentar finalizarlo, pulsando ⌘+. o a través del panel de actividades.";

/* error killing query informative message */
"An error occurred while attempting to kill connection %lld.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar finalizar la conexión %1$lld.\n\nMySQL dijo: %2$@";

/* error killing query informative message */
"An error occurred while attempting to kill the query associated with connection %lld.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar finalizar la consulta asociada con la conexión %1$lld.\n\nMySQL dijo: %2$@";

/* Error shown when unable to show create table syntax */
"An error occurred while creating table syntax.\n\n: %@" = "Se ha producido un error al crear la sintaxis de la tabla.\n\n: %@";

/* rename table error - no temporary name found */
"An error occurred while renaming '%@'. No temporary name could be found. Please try renaming to something else first." = "Ocurrió un error mientras se cambiaba el nombre '%@'. No se pudo encontrar ningún nombre temporal. Por favor, intente renombrarlo primero a algo diferente.";

/* rename table error informative message */
"An error occurred while renaming '%@'.\n\nMySQL said: %@" = "Se ha producido un error al renombrar '%1$@'.\n\nMySQL dijo: %2$@";

/* rename error - don't know what type the renamed thing is */
"An error occurred while renaming. '%@' is of an unknown type." = "Ocurrió un error mientras se cambiaba el nombre. '%@' es de un tipo desconocido.";

/* rename precedure/function error - can't delete old procedure */
"An error occurred while renaming. I couldn't delete '%@'.\n\nMySQL said: %@" = "Ocurrió un error mientras se cambiaba el nombre. No se pudo eliminar '%1$@'.\n\nMySQL dijo: %2$@";

/* rename precedure/function error - can't recreate procedure */
"An error occurred while renaming. I couldn't recreate '%@'.\n\nMySQL said: %@" = "Se ha producido un error al renombrar. No se pudo volver a crear '%1$@'.\n\nMySQL dijo: %2$@";

/* rename precedure/function error - can't retrieve syntax */
"An error occurred while renaming. I couldn't retrieve the syntax for '%@'.\n\nMySQL said: %@" = "Ocurrió un error mientras se cambiaba el nombre. No se pudo recuperar la sintaxis para '%1$@'.\n\nMySQL dijo: %2$@";

/* rename error - invalid create syntax */
"An error occurred while renaming. The CREATE syntax of '%@' could not be parsed." = "Se ha producido un error al renombrar. No se ha podido analizar la sintaxis CREATE de '%@'.";

/* message of panel when retrieving view information failed */
"An error occurred while retrieving status data.\n\nMySQL said: %@" = "Se ha producido un error al recuperar los datos de estado.\n\nMySQL dijo: %@";

/* message of panel when create syntax cannot be retrieved */
"An error occurred while retrieving the create syntax for '%@'.\nMySQL said: %@" = "Se ha producido un error al recuperar la sintaxis de creación para '%1$@'.\nMySQL dijo: %2$@";

/* add index error informative message */
"An error occurred while trying to add the index.\n\nMySQL said: %@" = "Ocurrió un error mientras se trataba de añadir el índice.\n\nMySQL dijo: %@";

/* error adding password to keychain informative message */
"An error occurred while trying to add the password to your Keychain. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Ocurrió un error mientras se trataba de agregar la contraseña a su Llavero. Reparar su Llavero podría resolver esto, pero si no lo hace, por favor repórtelo al equipo de Sequel Ace indicando el código de error %i.";

/* unable to copy database message informative message */
"An error occurred while trying to copy the database '%@' to '%@'." = "Ocurrió un error mientras se trataba de copiar la base de datos '%1$@' a '%2$@'.";

/* error deleting index informative message */
"An error occurred while trying to delete the index.\n\nMySQL said: %@" = "Ocurrió un error mientras se trataba de eliminar el índice.\n\nMySQL dijo: %@";

/* table status : row count query failed : error message */
"An error occurred while trying to determine the number of rows for %@.\nMySQL said: %@ (%lu)" = "Ocurrió un error mientras se trataba de determinar el número de filas para “%1$@”.\nMySQL dijo: %2$@ (%3$lu)";

/* unable to rename database message informative message */
"An error occurred while trying to rename the database '%@' to '%@'." = "Ocurrió un error mientras se trataba de renombrar la base de datos '%1$@' a '%2$@'.";

/* error finding keychain item to edit informative message */
"An error occurred while trying to retrieve the Keychain item you're trying to edit. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Ha ocurrido un error al intentar recuperar el ítem del llavero que está intentando editar. La reparación de su llavero puede resolver esto, pero de no ser así por favor contacte con el equipo Sequel Ace, proporcionando el código de error %i.";

/* error updating keychain item informative message */
"An error occurred while trying to update the Keychain item. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "Ha ocurrido un error al intentar actualizar el ítem del llavero. La reparación de su llavero puede resolver esto, pero de no ser así por favor contacte con el equipo de Sequel Ace, proporcionando el código de error %i.";

/* mysql error occurred message */
"An error occurred" = "Ha ocurrido un error";

/* MySQL table info retrieval error message */
"An error occurred retrieving table information.  MySQL said: %@" = "Ha ocurrido un error recuperando la información de tabla.  MySQL dijo: %@";

/* SQL encoding read error */
"An error occurred when reading the file, as it could not be read in the encoding you selected (%@).\n\nOnly %ld queries were executed." = "Se ha producido un error al leer el archivo, ya que no se pudo leer en la codificación seleccionada (%1$@).\n\nSólo se han ejecutado %2$ld consultas.";

/* CSV encoding read error */
"An error occurred when reading the file, as it could not be read using the encoding you selected (%@).\n\nOnly %ld rows were imported." = "Se ha producido un error al leer el archivo, ya que no se pudo leer usando la codificación seleccionada (%1$@).\n\nSólo se importaron %2$ld filas.";

/* SQL read error, including detail from system */
"An error occurred when reading the file.\n\nOnly %ld queries were executed.\n\n(%@)" = "Ha ocurrido un error al leer el archivo.\n\nSolo %1$ld consultas fueron ejecutadas.\n\n(%2$@)";

/* CSV read error, including detail string from system */
"An error occurred when reading the file.\n\nOnly %ld rows were imported.\n\n(%@)" = "Ha ocurrido un error al leer el archivo.\n\nSolo %1$ld filas fueron importadas.\n\n(%2$@)";

/* error adding field informative message */
"An error occurred when trying to add the field '%@' via\n\n%@\n\nMySQL said: %@" = "Ha ocurrido un error al intentar añadir el campo '%1$@' a través de\n\n%2$@\n\nMySQL dijo: %3$@";

/* error changing field informative message */
"An error occurred when trying to change the field '%@' via\n\n%@\n\nMySQL said: %@" = "Ha ocurrido un error al intentar modificar el campo '%1$@' a través de\n\n%2$@\n\nMySQL dijo: %3$@";

/* error changing table collation informative message */
"An error occurred when trying to change the table collation to '%@'.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar cambiar la intercalación de la tabla a '%1$@'.\n\nMySQL dijo: %2$@";

/* error changing table encoding informative message */
"An error occurred when trying to change the table encoding to '%@'.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar cambiar la codificación de la tabla a '%1$@'.\n\nMySQL dijo: %2$@";

/* error changing table type informative message */
"An error occurred when trying to change the table type to '%@'.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar cambiar el tipo de la tabla a '%1$@'.\n\nMySQL dijo: %2$@";

/* error changing table comment informative message */
"An error occurred when trying to change the table's comment to '%@'.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar cambiar el comentario de la tabla a '%1$@'.\n\nMySQL dijo: %2$@";

/* an error occurred while analyzing the %@.\n\nMySQL said:%@ */
"An error occurred while analyzing the %@.\n\nMySQL said:%@" = "Ha ocurrido un error al analizar %1$@.\n\nMySQL dijo:%2$@";

/* an error occurred while fetching the optimized field type.\n\nMySQL said:%@ */
"An error occurred while fetching the optimized field type.\n\nMySQL said:%@" = "Ha ocurrido un error al obtener el tipo óptimo del campo.\n\nMySQL dijo:%@";

/* an error occurred while trying to flush the %@.\n\nMySQL said:%@ */
"An error occurred while flushing the %@.\n\nMySQL said:%@" = "Ha ocurrido un error al limpiar %1$@.\n\nMySQL dijo:%2$@";

/* sql import error message */
"An error occurred while importing SQL" = "Ha ocurrido un error al importar SQL";

/* an error occurred while trying to optimze the %@.\n\nMySQL said:%@ */
"An error occurred while optimzing the %@.\n\nMySQL said:%@" = "Ha ocurrido un error al optimizar %1$@.\n\nMySQL dijo:%2$@";

/* an error occurred while performing the checksum on the %@.\n\nMySQL said:%@ */
"An error occurred while performing the checksum on %@.\n\nMySQL said:%@" = "Ha ocurrido un error al realizar el checksum de %1$@.\n\nMySQL dijo:%2$@";

/* an error occurred while trying to repair the %@.\n\nMySQL said:%@ */
"An error occurred while repairing the %@.\n\nMySQL said:%@" = "Ha ocurrido un error al reparar %1$@.\n\nMySQL dijo:%2$@";

/* message of panel when retrieving information failed */
"An error occurred while retrieving information.\nMySQL said: %@" = "Ha ocurrido un error al recuperar la información.\nMySQL dijo: %@";

/* error retrieving table information informative message */
"An error occurred while retrieving the information for table '%@'. Please try again.\n\nMySQL said: %@" = "Ha ocurrido un error al recuperar la información de la tabla '%1$@'. Por favor inténtelo de nuevo.\n\nMySQL dijo: %2$@";

/* error retrieving table information informative message */
"An error occurred while retrieving the trigger information for table '%@'. Please try again.\n\nMySQL said: %@" = "Ha ocurrido un error al recuperar la información de disparador de la tabla '%1$@'. Por favor inténtelo de nuevo.\n\nMySQL dijo: %2$@";

/* error adding new column informative message */
"An error occurred while trying to add the new column '%@' by\n\n%@.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar añadir la nueva columna '%1$@' por\n\n%2$@.\n\nMySQL dijo: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@' by\n\n%@.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar añadir la nueva tabla '%1$@' por\n\n%2$@.\n\nMySQL dijo: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@'.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar añadir la nueva tabla '%1$@'.\n\nMySQL dijo: %2$@";

/* error while trying to alter table message */
"An error occurred while trying to alter table '%@'.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar modificar la tabla '%1$@'.\n\nMySQL dijo: %2$@";

/* an error occurred while trying to check the %@.\n\nMySQL said:%@ */
"An error occurred while trying to check the %@.\n\nMySQL said:%@" = "Ha ocurrido un error al intentar comprobar %1$@.\n\nMySQL dijo:%2$@";

/* error deleting relation informative message */
"An error occurred while trying to delete the relation '%@'.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar eliminar la relación '%1$@'.\n\nMySQL dijo: %2$@";

/* unable to get list of users informative message */
"An error occurred while trying to get the list of users. Please make sure you have the necessary privileges to perform user management, including access to the mysql.user table." = "Ha ocurrido un error al intentar obtener la lista de usuarios. Por favor, asegúrese que tiene los privilegios necesarios para realizar la gestión de usuarios, incluido el acceso a la tabla mysql.user.";

/* error importing table informative message */
"An error occurred while trying to import a table via: \n%@\n\n\nMySQL said: %@" = "Ha ocurrido un error al intentar importar la tabla a través de: \n%1$@\n\n\nMySQL dijo: %2$@";

/* error moving field informative message */
"An error occurred while trying to move the field.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar mover el campo.\n\nMySQL dijo: %@";

/* error resetting auto_increment informative message */
"An error occurred while trying to reset AUTO_INCREMENT of table '%@'.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar reiniciar AUTO_INCREMENT de la tabla '%1$@'.\n\nMySQL dijo: %2$@";

/* error truncating table informative message */
"An error occurred while trying to truncate the table '%@'.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar truncar la tabla '%1$@'.\n\nMySQL dijo: %2$@";

/* mysql error occurred informative message */
"An error occurred whilst trying to perform the operation.\n\nMySQL said: %@" = "Ha ocurrido un error al intentar realizar la operación.\n\nMySQL dijo: %@";

/* mysql resource limits unsupported message */
"Resource Limits are not supported for your version of MySQL. Any Resouce Limits you specified have been discarded and not saved. MySQL said: %@" = "Los límites de recursos no son compatibles con su versión de MySQL. Cualquier límite de recursos que especificó ha sido descartado y no guardado. MySQL dijo: %@";

/* Export files creation error explanatory text */
"An unhandled error occurred when attempting to create %lu of the export files.  Please check the details and try again." = "Ha ocurrido un error incontrolado al intentar crear %lu de los archivos de exportación.  Por favor compruebe los detalles y vuelva a intentarlo.";

/* All export files creation error explanatory text */
"An unhandled error occurred when attempting to create each of the export files.  Please check the details and try again." = "Ha ocurrido un error incontrolado al intentar crear cada uno de los archivos de exportación.  Por favor compruebe los detalles y vuelva a intentarlo.";

/* Export file creation error explanatory text */
"An unhandled error occurred when attempting to create the export file.  Please check the details and try again." = "Ha ocurrido un error incontrolado al intentar crear el archivo de exportación.  Por favor compruebe los detalles y vuelva a intentarlo.";

/* ANALYZE one or more tables - result title */
"Analyze %@" = "Analizar %@";

/* analyze selected items menu item */
"Analyze Selected Items" = "Analizar ítems seleccionados";

/* analyze table menu item */
"Analyze Table" = "Analizar tabla";

/* analyze table failed message */
"Analyze table failed." = "Análisis de tabla fallido.";

/* change table type informative message */
"Are you sure you want to change this table's type to %@?\n\nPlease be aware that changing a table's type has the potential to cause the loss of some or all of its data. This action cannot be undone." = "¿Está seguro que desea cambiar el tipo de esta tabla a %@?\n\nTenga en cuenta que cambiar el tipo de la tabla tiene el potencial de causar la pérdida de parte o todos sus datos. Esta acción no se puede deshacer.";

/* clear global history list informative message */
"Are you sure you want to clear the global history list? This action cannot be undone." = "¿Está seguro que desea vaciar la lista del historial global? Esta acción no se puede deshacer.";

/* clear history list for %@ informative message */
"Are you sure you want to clear the history list for %@? This action cannot be undone." = "¿Está seguro de que quiere borrar la lista de historial de %@? Esta acción no se puede deshacer.";

/* truncate tables informative message */
"Are you sure you want to delete ALL records in the selected tables? This operation cannot be undone." = "¿Está seguro que desea eliminar TODOS los registros de las tablas seleccionadas? Esta operación no se puede deshacer.";

/* truncate table informative message */
"Are you sure you want to delete ALL records in the table '%@'? This operation cannot be undone." = "¿Está seguro que desea eliminar TODOS los registros de la tabla '%@'? Esta operación no se puede deshacer.";

/* delete all rows informative message */
"Are you sure you want to delete all the rows from this table? This action cannot be undone." = "¿Está seguro que desea eliminar todas las filas de esta tabla? Esta acción no se puede deshacer.";

/* delete table/view informative message */
"Are you sure you want to delete the %@ '%@'? This operation cannot be undone." = "¿Está seguro que desea eliminar la %1$@ '%2$@'? Esta operación no se puede deshacer.";

/* delete database informative message */
"Are you sure you want to delete the database '%@'? This operation cannot be undone." = "¿Está seguro que desea eliminar la base de datos '%@'? Esta operación no se puede deshacer.";

/* delete database informative message */
"Are you sure you want to delete the favorite '%@'? This operation cannot be undone." = "¿Está seguro que desea eliminar el favorito '%@'? Esta operación no se puede deshacer.";

/* delete field informative message */
"Are you sure you want to delete the field '%@'? This action cannot be undone." = "¿Está seguro que desea eliminar el campo '%@'? Esta acción no se puede deshacer.";

/* delete database informative message */
"Are you sure you want to delete the group '%@'? All groups and favorites within this group will also be deleted. This operation cannot be undone." = "¿Está seguro que desea eliminar el grupo '%@'? Todos los grupos y los favoritos dentro de este grupo también se eliminarán. Esta operación no se puede deshacer.";

/* delete index informative message */
"Are you sure you want to delete the index '%@'? This action cannot be undone." = "¿Está seguro que desea eliminar el índice '%@'? Esta acción no se puede deshacer.";

/* delete tables/views informative message */
"Are you sure you want to delete the selected %@? This operation cannot be undone." = "¿Está seguro que desea eliminar la %@ seleccionada? Esta operación no se puede deshacer.";

/* delete rows informative message */
"Are you sure you want to delete the selected %ld rows from this table? This action cannot be undone." = "¿Está seguro que desea eliminar las %ld filas seleccionadas de esta tabla? Esta acción no se puede deshacer.";

/* delete selected relation informative message */
"Are you sure you want to delete the selected relations? This action cannot be undone." = "¿Está seguro que desea eliminar las relaciones seleccionadas? Esta acción no se puede deshacer.";

/* delete selected row informative message */
"Are you sure you want to delete the selected row from this table? This action cannot be undone." = "¿Está seguro que desea eliminar la fila seleccionada de esta tabla? Esta acción no se puede deshacer.";

/* delete selected trigger informative message */
"Are you sure you want to delete the selected triggers? This action cannot be undone." = "¿Está seguro que desea eliminar los disparadores seleccionados? Esta acción no se puede deshacer.";

/* kill connection informative message */
"Are you sure you want to kill connection ID %lld?\n\nPlease be aware that continuing to kill this connection may result in data corruption. Please proceed with caution." = "¿Está seguro que desea finalizar la conexión %lld?\n\nTenga en cuenta que finalizar esta conexión puede dañar los datos. Por favor continúe con precaución.";

/* kill query informative message */
"Are you sure you want to kill the current query executing on connection ID %lld?\n\nPlease be aware that continuing to kill this query may result in data corruption. Please proceed with caution." = "¿Está seguro que desea finalizar la consulta actual ejecutada en la conexión %lld?\n\nTenga en cuenta que finalizar esta consulta puede dañar los datos. Por favor continúe con precaución.";

/* Bundle Editor : Remove-Bundle: remove dialog message */
"Are you sure you want to move the selected Bundle to the Trash and remove them respectively?" = "¿Está seguro que desea trasladar el paquete seleccionado a la papelera y eliminarlos respectivamente?";

/* continue to print informative message */
"Are you sure you want to print the current content view of the table '%@'?\n\nIt currently contains %@ rows, which may take a significant amount of time to print." = "¿Está seguro que desea imprimir el contenido de la vista actual de la tabla '%1$@'?\n\nActualmente contiene %2$@ filas, que puede llevar una cantidad significativa de tiempo para imprimirlas.";

/* remove all query favorites informative message */
"Are you sure you want to remove all of your saved query favorites? This action cannot be undone." = "¿Está seguro que desea eliminar todas sus consultas favoritas guardadas? Esta acción no se puede deshacer.";

/* remove all selected content filters informative message */
"Are you sure you want to remove all selected content filters? This action cannot be undone." = "¿Está seguro que desea eliminar todos los filtros de contenidos seleccionados? Esta acción no se puede deshacer.";

/* remove all selected query favorites informative message */
"Are you sure you want to remove all selected query favorites? This action cannot be undone." = "¿Está seguro que desea eliminar todas las consultas favoritas seleccionadas? Esta acción no se puede deshacer.";

/* Table Info Section : current value of auto_increment */
"auto_increment: %@" = "incremento: %@";

/* Encoding autodetect menu item */
"Autodetect" = "Detectar automáticamente";

/* background label for color table (Prefs > Editor) */
"Background" = "Fondo";

/* backtick quote label for color table (Prefs > Editor) */
"Backtick Quote" = "Comillas invertidas";

/* bash error */
"BASH Error" = "Error bash";

/* toolbar item label for switching to the Table Content tab */
"Browse & Edit Table Content" = "Explorar y editar contenido de tabla";

/* build label */
"build" = "compilar";

/* build label */
"Build" = "Compilación";

/* bundle editor menu item label */
"Bundle Editor" = "Editor de paquetes";

/* bundle error */
"Bundle Error" = "Error de paquete";

/* bundles menu item label */
"Bundles" = "Paquetes";

/* Bundle Editor : Outline View : 'BUNDLES' item */
"BUNDLES" = "PAQUETES";

/* Bundle Editor : Outline View : Menu Category item : tooltip */
"Bundles in category %@" = "Paquetes en la categoría %@";

/* bundles installation error */
"Bundles Installation Error" = "Error al instalar los paquetes";

/* bzip2 compression export summary - within a sentence */
"bzip2 compression" = "compresión bzip2";

/* description of geometry */
"Can store a single spatial value of types POINT, LINESTRING or POLYGON. Spatial support in MySQL is based on the OpenGIS Geometry Model." = "Puede almacenar un único valor espacial de los tipos POINT, LINESTRING o POLYGON.";

/* Bundle Editor : Remove-Bundle: cancel button
 cancel
 cancel button
 Open Files : Bundle : Already-Installed : Cancel button
 Preferences : Network : Custom SSH client : warning dialog : cancel button
 shutdown server : confirmation dialog : cancel button */
"Cancel" = "Cancelar";

/* sql import : charset error alert : cancel button */
"Cancel Import" = "Cancelar importación";

/* cancelling task status message */
"Cancelling..." = "Cancelando...";

/* empty query informative message */
"Cannot save an empty query." = "No puede guardar una consulta vacía.";

/* caret label for color table (Prefs > Editor) */
"Caret" = "Signo de intercalación";

/* change button */
"Change" = "Cambiar";

/* change focus to table list menu item */
"Change Focus to Table List" = "Cambiar foco al listado de tablas";

/* change table type message */
"Change table type" = "Cambiar tipo de tabla";

/* unsaved changes informative message */
"Changes have been made, which will be lost if this window is closed. Are you sure you want to continue" = "Se han realizado modificaciones, que se perderán si la ventana se cierra. ¿Está seguro que desea continuar?";

/* quitting app informal alert title */
"Close the app?" = "Close the app?";

/* quitting app informal alert body */
"Are you sure you want to quit the app?" = "Are you sure you want to quit the app?";

/* character set client: %@ */
"character set client: %@" = "conjunto de caracteres de cliente: %@";

/* CHECK one or more tables - result title */
"Check %@" = "Comprobar %@";

/* check of all selected items successfully passed message */
"Check of all selected items successfully passed." = "Comprobación de todos los ítems seleccionados superada correctamente.";

/* check option: %@ */
"check option: %@" = "marcar la opción: %@";

/* check selected items menu item */
"Check Selected Items" = "Comprobar ítems seleccionados";

/* check table menu item */
"Check Table" = "Comprobar tabla";

/* check table failed message */
"Check table failed." = "Comprobación de tabla fallida.";

/* check table successfully passed message */
"Check table successfully passed." = "Comprobación de tabla superada correctamente.";

/* check view menu item */
"Check View" = "Comprobar vista";

/* checking field data for editing task description */
"Checking field data for editing..." = "Comprobando datos de campo para editar...";

/* checksum %@ message */
"Checksum %@" = "Checksum %@";

/* checksum selected items menu item */
"Checksum Selected Items" = "Checksum de ítems seleccionados";

/* checksum table menu item */
"Checksum Table" = "Checksum de tabla";

/* Checksums of %@ message */
"Checksums of %@" = "Checksums de %@";

/* menu item for choose db */
"Choose Database..." = "Elegir base de datos...";

/* cancelling export cleaning up message */
"Cleaning up..." = "Limpiando...";

/* clear button */
"Clear" = "Vaciar";

/* toolbar item for clear console */
"Clear Console" = "Vaciar consola";

/* clear global history menu item title */
"Clear Global History" = "Vaciar historial global";

/* clear history for %@ menu title */
"Clear History for %@" = "Borrar historial de %@";

/* clear history message */
"Clear History?" = "¿Vaciar historial?";

/* tooltip for toolbar item for clear console */
"Clear the console which shows all MySQL commands performed by Sequel Ace" = "Vacía la consola que muestra todos los comandos MySQL ejecutados por Sequel Ace";

/* clear the document-based history list tooltip message */
"Clear the document-based history list" = "Borrar el historial de comandos";

/* clear the global history list tooltip message */
"Clear the global history list" = "Vacía la lista del historial global";

/* Close menu item */
"Close" = "Cerrar";

/* close tab context menu item */
"Close Tab" = "Cerrar pestaña";

/* Close Window menu item */
"Close Window" = "Cerrar ventana";

/* collation label (Navigator) */
"Collation" = "Intercalación";

/* collation connection: %@ */
"collation connection: %@" = "conexión de intercalación: %@";

/* comment label */
"Comment" = "Comentario";

/* Title of action menu item to comment line */
"Comment Line" = "Comentar línea";

/* Title of action menu item to comment selection */
"Comment Selection" = "Comentar selección";

/* connect button */
"Connect" = "Conectar";

/* Connect via socket button */
"Connect via socket" = "Conectar por socket";

/* connection established message */
"Connected" = "Conectado";

/* description for connected notification */
"Connected to %@" = "Conectado a %@";

/* message of panel when connection to db failed */
"Connected to host, but unable to connect to database %@.\n\nBe sure that the database exists and that you have the necessary privileges.\n\nMySQL said: %@" = "Conectado con el host, pero no se puede conectar con la base de datos %1$@.\n\nAsegúrese que la base de datos existe y que dispone de los privilegios necesarios.\n\nMySQL dijo: %2$@";

/* Generic connecting very short status message */
"Connecting..." = "Conectando...";

/* window title string indicating that sp is connecting */
"Connecting…" = "Conectando…";

/* error while reading connection data file */
"Connection data file couldn't be read." = "El archivo de conexión no se ha podido leer.";

/* message error while reading connection data file and suggesting to save it under a differnet name */
"Connection data file %@ couldn't be read. Please try to save the document under a different name." = "No se ha podido leer el archivo de datos de conexión %@. Por favor, intente guardar el documento con un nombre diferente.";

/* connection failed title */
"Connection failed!" = "¡Conexión fallida!";

/* Connection file is encrypted */
"Connection file is encrypted" = "El archivo de conexión está cifrado";

/* Connection success very short status message */
"Connection succeeded" = "Conexión satisfactoria";

/* Console */
"Console" = "Consola";

/* Console : Save as : Initial filename */
"ConsoleLog" = "Registros_de_consola";

/* toolbar item label for switching to the Table Content tab */
"Content" = "Contenido";

/* content filter clause is empty tooltip. */
"Content Filter clause is empty." = "La cláusula de filtro de contenido está vacía.";

/* continue button
 Continue button title */
"Continue" = "Continuar";

/* continue to print message */
"Continue to print?" = "¿Continuar imprimiendo?";

/* Copy as RTF */
"Copy as RTF" = "Copiar como RTF";

/* copy create func syntax menu item
 Table List : Context Menu : copy CREATE FUNCTION syntax */
"Copy Create Function Syntax" = "Copiar sintaxis create function";

/* copy create proc syntax menu item
 Table List : Context Menu : Copy CREATE PROCEDURE syntax
 Table List : Gear Menu : Copy CREATE PROCEDURE syntax */
"Copy Create Procedure Syntax" = "Copiar sintaxis create procedure";

/* copy create syntaxes menu item
 Table List : Context Menu : Copy CREATE syntax (multiple selection)
 Table List : Gear Menu : Copy CREATE syntax (multiple selection) */
"Copy Create Syntaxes" = "Copiar sintaxis create";

/* copy create table syntax menu item
 Table List : Context Menu : Copy CREATE syntax (single table)
 Table List : Gear Menu : Copy CREATE syntax (single table) */
"Copy Create Table Syntax" = "Copiar sintaxis create table";

/* copy create view syntax menu item
 Table List : Context Menu : Copy CREATE view statement
 Table List : Gear Menu : Copy CREATE view statement */
"Copy Create View Syntax" = "Copiar sintaxis create view";

/* copy server variable name menu item */
"Copy Variable Name" = "Copiar nombre de variable";

/* copy server variable names menu item */
"Copy Variable Names" = "Copiar nombres de variables";

/* copy server variable value menu item */
"Copy Variable Value" = "Copiar valor de variable";

/* copy server variable values menu item */
"Copy Variable Values" = "Copiar valores de variables";

/* Procedure/function export permission error */
"Could not export the %@ '%@' because of a permissions error.\n" = "No se pudo exportar el %1$@ '%2$@' debido a un error de permisos.\n";

/* Error when we can't parse/split file as CSV */
"Could not parse file as CSV" = "No se ha podido analizar el archivo como CSV";

/* message when database selection failed */
"Could not select database" = "No se ha podido seleccionar la base de datos";

/* Alter Database : Query Failed ($1 = mysql error message) */
"Couldn't alter database.\nMySQL said: %@" = "No se ha podido modificar la base de datos.\nMySQL dijo: %@";

/* Couldn't copy default themes to Application Support Theme folder!\nError: %@ */
"Couldn't copy default themes to Application Support Theme folder!\nError: %@" = "¡No se ha podido copiar los temas predeterminados en la carpeta Application Support Theme folder!\nError: %@";

/* message of panel when table cannot be created */
"Couldn't create '%@'.\nMySQL said: %@" = "No se ha podido crear '%1$@'.\nMySQL dijo: %2$@";

/* Couldn't create Application Support Bundle folder!\nError: %@ */
"Couldn't create Application Support Bundle folder!\nError: %@" = "¡No se ha podido crear la carpeta Application Support Bundle!\nError: %@";

/* Couldn't create Application Support Theme folder!\nError: %@ */
"Couldn't create Application Support Theme folder!\nError: %@" = "¡No se ha podido crear la carpeta Application Support Theme!\nError: %@";

/* message of panel when creation of db failed */
"Couldn't create database.\nMySQL said: %@" = "No se ha podido crear la base de datos.\nMySQL dijo: %@";

/* message of panel when an item cannot be deleted */
"Couldn't delete '%@'.\n\nMySQL said: %@" = "No se ha podido eliminar '%1$@'.\n\nMySQL dijo: %2$@";

/* message of panel when an item cannot be deleted including informative message about using force deletion */
"Couldn't delete '%@'.\n\nSelecting the 'Force delete' option may prevent this issue, but may leave the database in an inconsistent state.\n\nMySQL said: %@" = "No se ha podido eliminar '%1$@'.\n\nSeleccionando la opción 'Forzar borrado' puede evitar este problema, pero puede dejar la base de datos en un estado inconsistente.\n\nMySQL dijo: %2$@";

/* message of panel when field cannot be deleted */
"Couldn't delete field %@.\nMySQL said: %@" = "No se ha podido eliminar el campo %1$@.\nMySQL dijo: %2$@";

/* message when deleteing all rows failed */
"Couldn't delete rows.\n\nMySQL said: %@" = "No se ha podido eliminar las filas.\n\nMySQL dijo: %@";

/* message of panel when deleting db failed */
"Couldn't delete the database.\nMySQL said: %@" = "No se ha podido eliminar la base de datos.\nMySQL dijo: %@";

/* message of panel when an item cannot be renamed */
"Couldn't duplicate '%@'.\nMySQL said: %@" = "No se ha podido duplicar '%1$@'.\nMySQL dijo: %2$@";

/* message of panel when flushing privs failed */
"Couldn't flush privileges.\nMySQL said: %@" = "No se ha podido volver a cargar los privilegios.\nMySQL dijo: %@";

/* message of panel when table information cannot be retrieved */
"Couldn't get create syntax.\nMySQL said: %@" = "No se ha podido obtener la sintaxis create.\nMySQL dijo: %@";

/* Custom Query result editing error - could not identify a corresponding column */
"Couldn't identify field origin unambiguously. The column '%@' contains data from more than one table." = "No se ha podido identificar el origen del campo sin ambigüedades. La columna '%@' contiene datos de más de una tabla.";

/* message of panel when loading of row failed */
"Couldn't load the row. Reload the table to be sure that the row exists and use a primary key for your table." = "No se ha podido cargar la fila. Vuelva a cargar la tabla para estar seguro que la fila existe y use la clave primaria de su tabla.";

/* Couldn't read the file content of */
"Couldn't read the file content of" = "No se ha podido leer el contenido del archivo de";

/* text shown if an error occurred while sorting the result table */
"Couldn't sort column." = "No se ha podido ordenar la columna.";

/* message of panel when sorting of table failed */
"Couldn't sort table. MySQL said: %@" = "No se ha podido ordenar la tabla. MySQL dijo: %@";

/* message of panel when error while updating field to db */
"Couldn't write field.\nMySQL said: %@" = "No se ha podido escribir el campo.\nMySQL dijo: %@";

/* create syntax for table comment */
"Create syntax for" = "Crear sintaxis para";

/* Create syntax label */
"Create syntax for %@ '%@'" = "Sintaxis create de %1$@ '%2$@'";

/* Create syntaxes for selected items label */
"Create syntaxes for selected items" = "Sintaxis create de ítems seleccionados";

/* Table Info Section : table create options */
"create_options: %@" = "crear_opciones: %@";

/* created: %@
 Table Info Section : time+date table was created at */
"created: %@" = "creada: %@";

/* description of polygon */
"Creates a surface by combining one LinearRing (ie. a LineString that is closed and simple) as the outside boundary with zero or more inner LinearRings acting as \"holes\"." = "Crea una superficie combinando un LinearRing (ej. un LineString que es cerrado y simple) como el límite exterior con cero o más LinearRings internos actuando como \"agujeros\".";

/* Creating table task string */
"Creating %@..." = "Creando %@...";

/* Bundle Editor : Fallback Input source dropdown : 'current line' item */
"Current Line" = "Línea actual";

/* Bundle Editor : Fallback Input source dropdown : 'current query' item */
"Current Query" = "Consulta actual";

/* header for current selection in filtered list */
"CURRENT SELECTION" = "SELECCIÓN ACTUAL";

/* Bundle Editor : Fallback Input source dropdown : 'current word' item */
"Current Word" = "Palabra actual";

/* SSH connection : debug header with user-defined ssh binary */
"Custom SSH binary enabled. Disable in Preferences to rule out incompatibilities!" = "Binario SSH personalizado habilitado. ¡Desactive en Preferencias para evitar incompatibilidades!";

/* customize file name label */
"Customize Filename (%@)" = "Personalizar nombre de archivo (%@)";

/* data access: %@ (%@) */
"data access: %@ (%@)" = "acceso a datos: %1$@ (%2$@)";

/* Bundle Editor : Scope dropdown : 'data table' item
 data table menu item label */
"Data Table" = "Tabla de datos";

/* Bundle Editor : Outline View : 'Data Table' item : tooltip */
"Data Table Scope\ncommands will run on the Content and Query data tables" = "Ámbito tabla de datos\nlos comandos se ejecutarán en el contenido y las consultas de tablas de datos";

/* export filename database token
 export header database label
 export source
 Table Structure : Collation dropdown : 'item is the same as the collation of database' marker
 Table Structure : Encoding dropdown : 'item is database default' marker */
"Database" = "Base de datos";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'database changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'database changed' item */
"Database changed" = "Base de datos modificada";

/* message of panel when no db name is given */
"Database must have a name." = "La base de datos debe tener un nombre.";

/* databsse rename unsupported message */
"Database Rename Unsupported" = "Renombrar base de datos no soportado";

/* export filename date token */
"Date" = "Fecha";

/* export filename date token */
"Day" = "Día";

/* Add Table Sheet : Collation : Default (unknown)
 Collation Dropdown : Default (unknown)
 default label
 Table Structure : Collation dropdown : 'item is the same as the default collation of the row's charset' marker */
"Default" = "Por defecto";

/* Add Table : Collation : Default ($1 = collation name)
 Charset Dropdown : Default item ($1 = charset name)
 Collation Dropdown : Default ($1 = collation name)
 Collation Dropdown : Default collation for given charset ($1 = collation name)
 New Table Sheet : Table Engine Dropdown : Default */
"Default (%@)" = "Por defecto (%@)";

/* default bundles update */
"Default Bundles Update" = "Actualización de paquetes predeterminados";

/* import : csv field mapping : field default value */
"Default: %@" = "Por defecto: %@";

/* Query snippet default value placeholder */
"default_value" = "default_value";

/* definer label (Navigator) */
"Definer" = "Creador";

/* definer: %@ */
"definer: %@" = "creador: %@";

/* description of enum */
"Defines a list of members, of which every field can use at most one. Values are sorted by their index number (starting at 0 for the first member)." = "Define una lista de miembros, de los cuales cada campo puede utilizar como mucho uno. Los valores se ordenan por su número de índice (a partir de 0 para el primer miembro).";

/* delete button */
"Delete" = "Eliminar";

/* delete table/view message */
"Delete %@ '%@'?" = "¿Eliminar %1$@ '%2$@'?";

/* table structure : indexes : delete index : error 1553 : delete index and FK button */
"Delete Both" = "Eliminar ambos";

/* delete database message */
"Delete database '%@'?" = "¿Eliminar la base de datos '%@'?";

/* delete database message */
"Delete favorite '%@'?" = "¿Eliminar favorito '%@'?";

/* delete field message */
"Delete field '%@'?" = "¿Eliminar el campo '%@'?";

/* delete func menu title */
"Delete Function" = "Eliminar función";

/* delete functions menu title */
"Delete Functions" = "Eliminar funciones";

/* delete database message */
"Delete group '%@'?" = "¿Eliminar grupo '%@'?";

/* delete index message */
"Delete index '%@'?" = "¿Eliminar el índice '%@'?";

/* delete items menu title */
"Delete Items" = "Eliminar ítems";

/* delete proc menu title */
"Delete Procedure" = "Eliminar procedimiento";

/* delete procedures menu title */
"Delete Procedures" = "Eliminar procedimientos";

/* delete relation menu item */
"Delete Relation" = "Eliminar relación";

/* delete relation message */
"Delete relation" = "Eliminar relación";

/* delete relations menu item */
"Delete Relations" = "Eliminar relaciones";

/* delete row menu item singular */
"Delete Row" = "Eliminar fila";

/* delete rows menu item plural */
"Delete Rows" = "Eliminar filas";

/* delete rows message */
"Delete rows?" = "¿Eliminar las filas?";

/* delete tables/views message */
"Delete selected %@?" = "¿Eliminar %@ seleccionada?";

/* delete selected row message */
"Delete selected row?" = "¿Eliminar fila seleccionada?";

/* delete table menu title */
"Delete Table..." = "Eliminar tabla...";

/* delete tables menu title */
"Delete Tables" = "Eliminar tablas";

/* delete trigger menu item */
"Delete Trigger" = "Eliminar disparador";

/* delete trigger message */
"Delete trigger" = "Eliminar disparador";

/* delete triggers menu item */
"Delete Triggers" = "Eliminar disparadores";

/* delete view menu title */
"Delete View" = "Eliminar vista";

/* delete views menu title */
"Delete Views" = "Eliminar vistas";

/* Preferences : Network : SSL Chiper suites : List seperator */
"Disabled Cipher Suites" = "Juegos de cifrado desactivados";

/* force table deltion button text tooltip */
"Disables foreign key checks (FOREIGN_KEY_CHECKS) before deletion and re-enables them afterwards." = "Deshabilita las claves ajenas (FOREIGN_KEY_CHECKS) antes de eliminar y las vuelve a habilitar después.";

/* discard changes button */
"Discard changes" = "Descartar cambios";

/* description for disconnected notification */
"Disconnected from %@" = "Desconectado de %@";

/* do update operator tooltip */
"Do UPDATE where field contents match" = "Hace UPDATE donde coincide el contenido de los campos";

/* message of panel asking for confirmation for loading large text into the query editor */
"Do you really want to load a SQL file with %@ of data into the Query Editor?" = "¿Realmente desea cargar un archivo SQL con %@ datos en el Editor de consultas?";

/* message of panel asking for confirmation for inserting large text from dragging action */
"Do you really want to proceed with %@ of data?" = "¿Realmente desea continuar con %@ datos?";

/* shutdown server : confirmation dialog : title */
"Do you really want to shutdown the server?" = "¿Realmente desea apagar el servidor?";

/* dtd identifier label (Navigator) */
"DTD Identifier" = "Identificador DTD";

/* sql export dump of table label */
"Dump of table" = "Volcado de tabla";

/* sql export dump of view label */
"Dump of view" = "Volcado de vista";

/* text showing that app is writing dump */
"Dumping..." = "Volcando...";

/* duplicate object message */
"Duplicate %@ '%@' to:" = "Duplicar %1$@ '%2$@' a:";

/* duplicate func menu title */
"Duplicate Function..." = "Duplicar función...";

/* duplicate host message */
"Duplicate Host" = "Duplicar equipo";

/* duplicate proc menu title */
"Duplicate Procedure..." = "Duplicar procedimiento...";

/* duplicate table menu title */
"Duplicate Table..." = "Duplicar tabla...";

/* duplicate user message */
"Duplicate User" = "Duplicar usuario";

/* duplicate view menu title */
"Duplicate View..." = "Duplicar vista...";

/* partial copy database support informative message */
"Duplicating the database '%@' is only partially supported as it contains objects other than tables (i.e. views, procedures, functions, etc.), which will not be copied.\n\nWould you like to continue?" = "Duplicar la base de datos '%@' es solo parcialmente soportado, ya que contiene objetos distintos de las tablas (i.e. vistas, procedimientos, funciones, etc.), que no se copiarán.\n\n¿Desea continuar?";

/* edit filter */
"Edit Filters…" = "Editar filtros…";

/* Edit row button */
"Edit row" = "Editar fila";

/* toolbar item label for switching to the Table Structure tab */
"Edit Table Structure" = "Editar estructura de la tabla";

/* edit theme list label */
"Edit Theme List…" = "Editar lista de temas…";

/* edit user-defined filter */
"Edit user-defined Filters…" = "Edita los filtros definidos por el usuario…";

/* empty query message */
"Empty query" = "Consulta vacía";

/* MySQL search language code - eg in http://search.mysql.com/search?q=select&site=refman-50&lr=lang_en */
"en" = "en";

/* encoding label (Navigator) */
"Encoding" = "Codificación";

/* Table Info Section : $1 = table charset */
"encoding: %1$@" = "codificación: %1$@";

/* Table Info Section : $1 = table charset, $2 = table collation */
"encoding: %1$@ (%2$@)" = "codificando: %1$@ (%2$@)";

/* Table Info Section : Table Engine */
"engine: %@" = "motor: %@";

/* enter connection details label */
"Enter connection details below, or choose a favorite" = "Introduzca los detalles de la conexión a continuación, o elija un favorito";

/* SSH key password prompt */
"Enter your password for the SSH key\n\"%@\"" = "Introduzca su contraseña para la clave SSH\n'%@";

/* Bundle Editor : Fallback Input source dropdown : 'entire content' item
 Bundle Editor : Scope=Field : Input source dropdown: 'entire content' item */
"Entire Content" = "Contenido completo";

/* Bundle Editor : Copy-Command-Error : error dialog title
 error */
"Error" = "Error";

/* error adding field message */
"Error adding field" = "Error al añadir el campo";

/* error adding new column message */
"Error adding new column" = "Error al añadir la nueva columna";

/* error adding new table message */
"Error adding new table" = "Error al añadir la nueva tabla";

/* error adding password to keychain message */
"Error adding password to Keychain" = "Error al añadir la contraseña al llavero";

/* error changing field message */
"Error changing field" = "Error al modificar el campo";

/* error changing table collation message */
"Error changing table collation" = "Error al cambiar la intercalación de tabla";

/* error changing table comment message */
"Error changing table comment" = "Error al modificar el comentario de tabla";

/* error changing table encoding message */
"Error changing table encoding" = "Error al cambiar la codificación de tabla";

/* error changing table type message */
"Error changing table type" = "Error al cambiar el tipo de tabla";

/* error creating relation message */
"Error creating relation" = "Error al crear la relación";

/* error creating trigger message */
"Error creating trigger" = "Error al crear el disparador";

/* error for message */
"Error for" = "Error en";

/* error for bash command ($1), $2=message */
"Error for “%1$@”:\n%2$@" = "Error para “%1$@”:\n%2$@";

/* error moving field message */
"Error moving field" = "Error al trasladar el campo";

/* error occurred */
"error occurred" = "se ha producido un error";

/* error reading import file */
"Error reading import file." = "Error al leer el archivo de importación.";

/* error finding keychain item to edit message */
"Error retrieving Keychain item to edit" = "Error al recuperar ítem de llavero para editar";

/* error retrieving table information message */
"Error retrieving table information" = "Error al recuperar la información de tabla";

/* error retrieving trigger information message */
"Error retrieving trigger information" = "Error al recuperar la información de disparador";

/* error truncating table message */
"Error truncating table" = "Error al truncar la tabla";

/* error updating keychain item message */
"Error updating Keychain item" = "Error al actualizar el ítem de llavero";

/* error while analyzing selected items message */
"Error while analyzing selected items" = "Error al analizar los elementos seleccionados";

/* error while checking selected items message */
"Error while checking selected items" = "Error al comprobar los elementos seleccionados";

/* error while converting color scheme data */
"Error while converting color scheme data" = "Error al convertir los datos de tema de color";

/* error while converting connection data */
"Error while converting connection data" = "Error al convertir los datos de conexión";

/* Content filters could not be converted to plist upon export - message title (ContentFilterManager) */
"Error while converting content filter data" = "Error al convertir los datos de filtro de contenido";

/* error while converting query favorite data */
"Error while converting query favorite data" = "Error al convertir los datos de la consulta favorita";

/* error while converting session data */
"Error while converting session data" = "Error al convertir los datos de la sesión";

/* Error while deleting field */
"Error while deleting field" = "Error al eliminar el campo";

/* Bundle Editor : Copy-Command-Error : Copying failed error message */
"Error while duplicating Bundle content." = "Error al duplicar el contenido del paquete.";

/* error while executing javascript bash command */
"Error while executing JavaScript BASH command" = "Error al ejecutar el comando JavaScript BASH";

/* error while fetching the optimized field type message */
"Error while fetching the optimized field type" = "Error al obtener el tipo óptimo de campo";

/* error while flushing selected items message */
"Error while flushing selected items" = "Error al limpiar los elementos seleccionados";

/* error while importing table message */
"Error while importing table" = "Error al importar la tabla";

/* Open Files : Bundle : Install-Error : error dialog title
 Open Files : Bundle : UUID : Error dialog title */
"Error while installing Bundle" = "Error al instalar el paquete";

/* error while installing color theme file */
"Error while installing color theme file" = "Error al instalar el archivo de tema de color";

/* Bundle Editor : Trash-Bundle(s)-Error : error dialog title
 error while moving %@ to trash
 Open Files : Bundle : Already-Installed : Delete-Old-Error : Could not delete old bundle before installing new version. */
"Error while moving %@ to Trash." = "Error al mover %@ a la papelera.";

/* error while optimizing selected items message */
"Error while optimizing selected items" = "Error al optimizar los elementos seleccionados";

/* error while parsing CREATE TABLE syntax */
"Error while parsing CREATE TABLE syntax" = "Error al analizar la sintaxis CREATE TABLE";

/* error while reading connection data file */
"Error while reading connection data file" = "Error al leer el archivo de datos de conexión";

/* error while reading data file */
"Error while reading data file" = "Error al leer el archivo de datos";

/* error while repairing selected items message */
"Error while repairing selected items" = "Error al reparar los elementos seleccionados";

/* Bundle Editor : Save-Bundle-Error : error dialog title */
"Error while saving the Bundle." = "Error al guardar el paquete.";

/* Bundle Editor : Save-and-Close-Error : error dialog title */
"Error while saving %@." = "Error al guardar %@.";

/* Errors title */
"Errors" = "Errores";

/* Mysql Help Viewer : Help Topic: Example section title */
"Example:" = "Ejemplo:";

/* Bundle Editor : BLOB dropdown : 'exclude BLOB' item */
"exclude BLOB" = "excluir BLOB";

/* execution privilege label (Navigator) */
"Execution Privilege" = "Privilegio de Ejecución";

/* execution privilege: %@ */
"execution privilege: %@" = "privilegio de ejecución: %@";

/* execution stopped message */
"Execution stopped!\n" = "¡Ejecución interrumpida!\n";

/* export selected favorites menu item */
"Export Selected..." = "Exportar seleccionado...";

/* text showing that the application is importing a supplied format */
"Exporting %@" = "Exportando %@";

/* text showing that the application is exporting a Dot file */
"Exporting Dot File" = "Exportando archivo Dot";

/* text showing that the application is exporting SQL */
"Exporting SQL" = "Exportando SQL";

/* extra label (Navigator) */
"Extra" = "Extra";

/* table structure : indexes : delete index : no columns error : title */
"Failed to remove index '%@'" = "Error al eliminar el índice '%@'";

/* fatal error */
"Fatal Error" = "Error grave";

/* export filename favorite name token */
"Favorite" = "Favorito";

/* favorites label */
"Favorites" = "Favoritos";

/* favorites export error message */
"Favorites export error" = "Error al exportar favoritos";

/* favorites import error message */
"Favorites import error" = "Error al importar favoritos";

/* export label showing that the app is fetching data */
"Fetching data..." = "Recuperando datos...";

/* fetching database structure data in progress */
"fetching database structure data in progress" = "recuperando estructura de datos de la base de datos";

/* fetching database structure in progress */
"fetching database structure in progress" = "recuperando estructura de base de datos en progreso";

/* fetching table data for completion in progress message */
"fetching table data…" = "Recuperando datos de la tabla…";

/* popup menuitem for field (showing only if disabled) */
"field" = "campo";

/* Field */
"Field" = "Campo";

/* Query result editing error - could not match row being edited uniquely */
"Field is not editable. Couldn't identify field origin unambiguously (%ld matches)." = "Campo no editable. No se pudo identificar el campo de origen con precisión (%ld coincidencias).";

/* field is not editable due to no table/database */
"Field is not editable. Field has no or multiple table or database origin(s)." = "Campo no editable. El campo no tiene o tiene múltiples orígenes de tablas o bases de datos.";

/* Custom Query result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload data, check encoding, or try to add\na primary key field or more fields\nin your SELECT statement for table '%@'\nto identify field origin unambiguously." = "El campo no es editable. No se ha encontrado ningún registro coincidente.\nRecargue la tabla, compruebe la codificación, o intente añadir\nun campo clave primaria o más campos\nen el SELECT para la tabla '%@'\npara identificar de forma única el campo origen.";

/* Table Content result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload table, check the encoding, or try to add\na primary key field or more fields\nin the view declaration of '%@' to identify\nfield origin unambiguously." = "El campo no es editable. No se ha encontrado ningún registro coincidente.\nRecarga la tabla, comprueba la codificación, o intenta añadir\nun campo clave primaria o más campos\nen la declaración de vista de '%@' para identificar de forma única el origen del campo\n.";

/* error while reading data file */
"File couldn't be read." = "El archivo no se ha podido leer.";
"File couldn't be read: %@\n\nIt will be deleted." = "No se pudo leer el archivo: %@\n\nSe eliminará.";

/* File read error title (Import Dialog) */
"File read error" = "Error al leer el archivo";

/* All export files already exist explanatory text */
"Files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "Los archivos con los mismos nombres ya existen en la carpeta de destino. Reemplazándolos sobreescribirá sus contenidos actuales.";

/* filter label */
"Filter" = "Filtro";

/* apply filter label */
"Apply Filter(s)" = "Aplicar filtro(s)";

/* filter tables menu item */
"Filter Tables" = "Filtrar tablas";

/* export source */
"Filtered table content" = "Contenido de tabla filtrado";

/* filtering failed. please try again. tooltip */
"Filtering failed. Please try again." = "Filtrado fallido. Por favor vuelva a intentarlo de nuevo.";

/* Filtering table task description */
"Filtering table..." = "Filtrando tabla...";

/* description for finished exporting notification */
"Finished exporting to %@" = "Exportación de %@ finalizada";

/* description for finished importing notification */
"Finished importing %@" = "Importación %@ finalizada";

/* FLUSH one or more tables - result title */
"Flush %@" = "Limpiar %@";

/* flush selected items menu item */
"Flush Selected Items" = "Limpiar ítems seleccionados";

/* flush table menu item */
"Flush Table" = "Limpiar tabla";

/* flush table failed message */
"Flush table failed." = "Limpieza de tabla fallida.";

/* flush view menu item */
"Flush View" = "Limpiar vista";

/* title of panel when successfully flushed privs */
"Flushed Privileges" = "Privilegios vueltos a cargar";

/* For BIT fields only “1” or “0” are allowed. */
"For BIT fields only “1” or “0” are allowed." = "Para los campos BIT solo están permitidos “1” o “0”.";

/* force table deletion button text */
"Force delete (disables integrity checks)" = "Forzar borrado (deshabilita la comprobación de integridad)";

/* full text index menu item title */
"FULLTEXT" = "FULLTEXT";

/* function */
"function" = "función";

/* header for function info pane */
"FUNCTION INFORMATION" = "INFORMACIÓN DE FUNCIÓN";

/* functions */
"functions" = "funciones";

/* Bundle Editor : Scope dropdown : 'general' item
 general menu item label
 general preference pane name */
"General" = "General";

/* general preference pane tooltip */
"General Preferences" = "Preferencias generales";

/* Bundle Editor : Outline View : 'General' item : tooltip */
"General Scope\ncommands will run application-wide" = "Ámbito general\nlos comandos se ejecutarán en toda la aplicación";

/* generating print document status message */
"Generating print document..." = "Generando documento de impresión...";

/* export header generation time label */
"Generation Time" = "Tiempo de generación";

/* Content Filter Manager : Filter Entry List: 'Global' Header
 Query Favorites : List : Section Heading : global query favorites */
"Global" = "Global";

/* Query Favorites : List : Section Heading : global : tooltip */
"Globally stored favorites" = "Favoritos almacenados globalmente";

/* Preferences : Notifications : status text : notification setup */
"Notifications are delivered via the Notification Center." = "Las notificaciones se entregan a través del Centro de notificaciones.";

/* Gzip compression export summary - within a sentence */
"Gzip compression" = "Compresión gzip";

/* MySQL Help Viewer : Results list : Page title */
"Help topics for %@" = "Temas de ayuda para %@";

/* hide console */
"Hide Console" = "Ocultar consola";

/* hide navigator */
"Hide Navigator" = "Ocultar navegador";

/* hide tab bar */
"Hide Tab Bar" = "Ocultar barra de pestañas";

/* Hide Toolbar menu item */
"Hide Toolbar" = "Ocultar barra de herramientas";

/* export filename host token
 export header host label */
"Host" = "Equipo";

/* description of double */
"IEEE 754 double-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 valor de punto flotante de doble precisión, M es el número máximo de dígitos, de los cuales D puede ser después del punto decimal. Nota: Muchos números decimales solo pueden ser aproximados por valores de coma flotante. Vea DECIMAL si necesita resultados exactos.";

/* description of float */
"IEEE 754 single-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 valor de punto flotante de una sola precisión, M es el número máximo de dígitos, de los cuales D puede ser después del punto decimal. Nota: Muchos números decimales solo pueden ser aproximados por valores de coma flotante. Vea DECIMAL si necesita resultados exactos.";

/* ignore button */
"Ignore" = "Ignorar";

/* ignore errors button */
"Ignore All Errors" = "Ignorar todos los errores";

/* ignore all fields menu item */
"Ignore all Fields" = "Ignorar todos los campos";

/* ignore field label */
"Ignore field" = "Ignorar campo";

/* ignore field label */
"Ignore Field" = "Ignorar campo";

/* import button */
"Import" = "Importar";

/* import all fields menu item */
"Import all Fields" = "Importar todos los campos";

/* sql import : charset error alert : continue button */
"Import Anyway" = "Importar de todos modos";

/* import cancelled message */
"Import cancelled!\n" = "¡Importación cancelada!\n";

/* Import Error title */
"Import Error" = "Error al importar";

/* import field operator tooltip */
"Import field" = "Importa el campo";

/* import file does not exist message */
"Import file does not exist." = "El archivo de importación no existe.";

/* Export file format cannot be imported warning */
"Import of the selected data is currently not supported." = "La importación de los datos seleccionados no está soportada actualmente.";

/* SQL import progress text */
"Imported %@ of %@" = "Importado %1$@ de %2$@";

/* CSV import progress text where total size is unknown */
"Imported %@ of CSV data" = "Importado %@ datos de CSV";

/* SQL import progress text where total size is unknown */
"Imported %@ of SQL" = "Importado %@ de SQL";

/* text showing that the application is importing CSV */
"Importing CSV" = "Importando CSV";

/* text showing that the application is importing SQL */
"Importing SQL" = "Importando SQL";

/* Bundle Editor : BLOB dropdown : 'include BLOB' item */
"include BLOB" = "incluir BLOB";

/* include content table column tooltip */
"Include content" = "Incluir contenido";

/* sql import error message */
"Incompatible encoding in SQL file" = "Codificación incompatible en el archivo SQL";

/* header for blank info pane */
"INFORMATION" = "INFORMACIÓN";

/* New Table Sheet : Table Collation Dropdown : Default inherited from database
 New Table Sheet : Table Encoding Dropdown : Default inherited from database */
"Inherit from database (%@)" = "Heredar de la base de datos (%@)";

/* initializing export label */
"Initializing..." = "Inicializando...";

/* Bundle Editor : Scope dropdown : 'input field' item
 input field menu item label */
"Input Field" = "Campo de entrada";

/* input field  doesn't support insertion of snippets. */
"Input Field doesn't support insertion of snippets." = "El campo de entrada no soporta la inserción de fragmentos.";

/* input field is not editable. */
"Input Field is not editable." = "El campo de entrada no se puede editar.";

/* Bundle Editor : Outline View : 'Input Field' item : tooltip */
"Input Field Scope\ncommands will run on each text input field" = "Ámbito campo de entrada\nlos comandos se ejecutarán en cada campo de entrada de texto";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as snippet' item */
"Insert as Snippet" = "Insertar como fragmento";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as text' item */
"Insert as Text" = "Insertar como texto";

/* Bundle Editor : Outline View : 'BUNDLES' item : tooltip */
"Installed Bundles" = "Paquetes instalados";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog title */
"Installing Bundle" = "Instalando paquete";

/* insufficient details message */
"Insufficient connection details" = "Detalles de conexión insuficientes";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please enter at least the hostname." = "Ha proporcionado detalles insuficientes para establecer una conexión. Por favor, introduzca al menos el nombre de equipo.";

/* insufficient SSH tunnel details informative message */
"Insufficient details provided to establish a connection. Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Ha proporcionado detalles insuficientes para establecer una conexión. Por favor, introduzca el nombre de equipo para el túnel SSH, o deshabilite el túnel SSH.";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please provide at least a host." = "Ha proporcionado detalles insuficientes para establecer una conexión. Por favor, proporcione al menos un equipo.";

/* Interpret data as: */
"Interpret data as:" = "Interpretar datos como:";

/* Invalid database very short status message */
"Invalid database" = "Base de datos incorrecta";

/* export : import settings : file error title */
"Invalid file supplied!" = "¡Archivo suministrado no válido!";

/* table content : editing : error message title when parsing as hex string failed */
"Invalid hexadecimal value" = "Valor hexadecimal no válido";

/* is deterministic label (Navigator) */
"Is Deterministic" = "Determinista";

/* is nullable label (Navigator) */
"Is Nullable" = "Nulos";

/* is updatable: %@ */
"is updatable: %@" = "es actualizable: %@";

/* items */
"items" = "ítems";

/* javascript exception */
"JavaScript Exception" = "Excepción javascript";

/* javascript parsing error */
"JavaScript Parsing Error" = "Error analizando javascript";

/* key label (Navigator) */
"Key" = "Clave";

/* keyword label for color table (Prefs > Editor) */
"Keyword" = "Palabra reservada";

/* kill button */
"Kill" = "Finalizar";

/* kill connection message */
"Kill connection?" = "¿Finalizar conexión?";

/* kill query message */
"Kill query?" = "¿Finalizar consulta?";

/* Last Error Message */
"Last Error Message" = "Último mensaje de error";

/* Last Used entry in favorites menu */
"Last Used" = "Último usado";

/* range for json type */
"Limited to @@max_allowed_packet" = "Limitado a @@max_allowed_packet";

/* Loading table task string */
"Loading %@..." = "Cargando %@...";

/* Loading database task string */
"Loading database '%@'..." = "Cargando base de datos '%@'...";

/* Loading history entry task desc */
"Loading history entry..." = "Cargando entrada de historial...";

/* Loading table page task string */
"Loading page %lu..." = "Cargando página %lu...";

/* Loading referece task string */
"Loading reference..." = "Cargando referencia...";

/* Loading table data string */
"Loading table data..." = "Cargando datos de la tabla...";

/* Low memory export summary */
"Low memory" = "Poca memoria";

/* range of decimal */
"M (precision): Up to 65 digits\nD (scale): 0 to 30 digits" = "M (precisión): Hasta 65 dígitos\nD (escala): 0 a 30 dígitos";

/* range for blob type
 range for varbinary type */
"M: %@ to %@ bytes" = "M: %1$@ a %2$@ bytes";

/* range for text type
 range for varchar type */
"M: %@ to %@ characters" = "M: %1$@ a %2$@ caracteres";

/* range for longtext type */
"M: %@ to %@ characters (4 GiB)" = "M: %1$@ a %2$@ caracteres (4 GiB)";

/* range for binary type */
"M: 0 to 255 bytes" = "M: 0 a 255 bytes";

/* range for char type */
"M: 0 to 255 characters" = "M: 0 a 255 caracteres";

/* range for bit type */
"M: 1 (default) to 64" = "M: 1 (por defecto) a 64";

/* connection view : ssl : key file picker : wrong format error description */
"Make sure the file contains a RSA private key and is using PEM encoding." = "Asegúrese de que el archivo contiene una clave privada RSA y está usando la codificación PEM.";

/* connection view : ssl : client cert picker : wrong format error description */
"Make sure the file contains a X.509 client certificate and is using PEM encoding." = "Asegúrese de que el archivo contiene un certificado de cliente X.509 y utiliza la codificación PEM.";

/* match field menu item */
"Match Field" = "Incluir Campo";

/* Shown when user inserts too many arguments (ContentFilterManager) */
"Maximum number of arguments is 2!" = "¡El número máximo de argumentos es 2!";

/* Maximum text length is set to %ld. */
"Maximum text length is set to %ld." = "La longitud máxima del texto está establecida a %ld.";

/* Maximum text length is set to %ld. Inserted text was truncated. */
"Maximum text length is set to %ld. Inserted text was truncated." = "La longitud máxima del texto está establecida a %ld. El texto insertado fue truncado.";

/* Maximum text length is set to %llu. */
"Maximum text length is set to %llu." = "La longitud máxima del texto está establecida a %llu.";

/* Maximum text length is set to %llu. Inserted text was truncated. */
"Maximum text length is set to %llu. Inserted text was truncated." = "La longitud máxima del texto está establecida a %llu. El texto insertado fue truncado.";

/* message column title */
"Message" = "Mensaje";

/* mgtemplateengine error */
"MGTemplateEngine Error" = "Error de MGTemplateEngine";

/* export filename date token */
"Month" = "Mes";

/* multiple selection */
"multiple selection" = "selección múltiple";

/* MySQL connecting very short status message */
"MySQL connecting..." = "conectando con MySQL...";

/* mysql error message */
"MySQL Error" = "Error de MySQL";

/* mysql help */
"MySQL Help" = "Ayuda de MySQL";

/* MySQL Help for Selection */
"MySQL Help for Selection" = "Ayuda de MySQL de la selección";

/* MySQL Help for Word */
"MySQL Help for Word" = "Ayuda de MySQL del término";

/* mysql help categories */
"MySQL Help – Categories" = "Ayuda de MySQL – Categorías";

/* mysql said message */
"MySQL said:" = "MySQL dijo:";

/* shutdown server : error dialog : message */
"MySQL said:\n%@" = "MySQL dijo:\n%@";

/* message of panel when error while adding row to db */
"MySQL said:\n\n%@" = "MySQL dijo:\n\n%@";

/* Preferences : Themes : Initial filename for 'Export' */
"MyTheme" = "MiTema";

/* network preference pane name */
"Network" = "Red";

/* network preference pane tooltip */
"Network Preferences" = "Preferencias de red";

/* file preference pane name */
"Files" = "Archivos";

/* file preference pane tooltip */
"File Preferences" = "Preferencias de archivo";

/* Bundle Editor : Default name for new bundle in the list on the left */
"New Bundle" = "Nuevo paquete";

/* new column name placeholder string */
"New Column Name" = "Nuevo nombre de columna";

/* new favorite name */
"New Favorite" = "Nuevo favorito";

/* Content Filter Manager : Initial name for new filter */
"New Filter" = "Nuevo filtro";

/* new folder placeholder name */
"New Folder" = "Nueva carpeta";

/* Bundle Editor : Default name for a new bundle in the menu */
"New Name" = "Nuevo nombre";

/* new table menu item */
"New Table" = "Nueva tabla";

/* error that no color theme found */
"No color theme data found." = "No se encontraron datos de tema de color.";

/* No compression export summary - within a sentence */
"no compression" = "sin compresión";

/* no connection available message */
"No connection available" = "No hay conexión disponible";

/* no connection data found */
"No connection data found." = "No se encontraron datos de conexión.";

/* No content filters were found in file to import (ContentFilterManager) */
"No content filters found." = "No se encontraron filtros de contenido.";

/* no data found */
"No data found." = "No se encontraron datos.";

/* No errors title */
"No errors" = "No hay errores";

/* No favorites entry in favorites menu */
"No Favorties" = "No hay favoritos";

/* All export files creation error title */
"No files could be created" = "No se han podido crear los archivos";

/* no item found message */
"No item found" = "Ítem no encontrado";

/* SSH tunnel could not be created because no local port could be allocated */
"No local port could be allocated for the SSH Tunnel." = "No se ha podido asignar el puerto local al túnel SSH.";

/* header for no matches in filtered list */
"NO MATCHES" = "NO HAY RESULTADOS";

/* no optimized field type found. message */
"No optimized field type found." = "No se ha encontrado tipo óptimo de campo.";

/* error that no query favorites found */
"No query favorites found." = "No se encontraron consultas favoritas.";

/* Mysql Help Viewer : Search : No results */
"No results found." = "No se encontraron resultados.";

/* Bundle Editor : Fallback Input source dropdown : 'none' item
 Bundle Editor : Scope=? : ? dropdown: 'None' item
 Bundle Editor : Scope=Data-Table : Input source dropdown: 'none' item
 Bundle Editor : Scope=Data-Table : Output dropdown : 'none' item
 Bundle Editor : Scope=Data-Table : Trigger dropdown : 'none' item
 Bundle Editor : Scope=Field : Input source dropdown: 'None' item
 Bundle Editor : Scope=Field : Output dropdown : 'none' item
 Bundle Editor : Scope=Field : Trigger dropdown : 'none' item
 Bundle Editor : Scope=General : Input source dropdown: 'None' item
 Bundle Editor : Scope=General : Output dropdown : 'none' item
 Bundle Editor : Scope=General : Trigger dropdown : 'none' item
 compression: none */
"None" = "Ninguno";

/* not available label */
"Not available" = "No disponible";

/* Argument count (ContentFilterManager) */
"Number of arguments: %lu" = "Número de argumentos: %lu";

/* numeric label for color table (Prefs > Editor) */
"Numeric" = "Numérico";

/* Bundle Editor : Copy-Command-Error : OK button
 Bundle Editor : Save-and-Close-Error : OK button
 Bundle Editor : Save-Bundle-Error : OK button
 Bundle Editor : Trash-Bundle(s)-Error : OK button
 OK
 OK button
 Open Files : Bundle : Already-Installed : Delete-Old-Error : OK button
 Open Files : Bundle : Install-Error : OK button
 Open Files : Bundle : UUID : OK button
 Preferences : Network : Custom SSH client : warning dialog : accept button */
"OK" = "Aceptar";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+1 rows instead of n selected were deleted. */
"One additional row was removed!" = "¡Una fila adicional fue eliminada!";

/* Table Content : Remove Row : Result : Too Few : Part 1 : Only n-1 of n selected rows were deleted. */
"One row was not removed." = "Una fila no fue eliminada.";

/* Only one dragged item allowed. */
"Only one dragged item allowed." = "Solo se permite arrastrar un ítem.";

/* partial copy database support message */
"Only Partially Supported" = "Solo parcialmente soportado";

/* open function in new table title */
"Open Function in New Tab" = "Abrir función en nueva pestaña";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Function in New Window" = "Abrir función en nueva ventana";

/* open connection in new tab context menu item */
"Open in New Tab" = "Abrir en nueva pestaña";

/* menu item open in new window */
"Open in New Window" = "Abrir en nueva ventana";

/* open procedure in new table title */
"Open Procedure in New Tab" = "Abrir procedimiento en nueva pestaña";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Procedure in New Window" = "Abrir procedimiento en nueva ventana";

/* open table in new tab title
 open table in new table title */
"Open Table in New Tab" = "Abrir tabla en nueva pestaña";

/* Table List : Context Menu : Duplicate connection to new window
 Table List : Gear Menu : Duplicate connection to new window */
"Open Table in New Window" = "Abrir tabla en nueva ventana";

/* open view in new tab title
 open view in new table title */
"Open View in New Tab" = "Abrir vista en nueva pestaña";

/* Tables List : Context Menu : Duplicate connection to new window
 Tables List : Gear Menu : Duplicate connection to new window */
"Open View in New Window" = "Abrir vista en nueva ventana";

/* menu item open %@ in new window */
"Open %@ in New Window" = "Abrir %@ en una ventana nueva";

/* OPTIMIZE one or more tables - result title */
"Optimize %@" = "Optimizar %@";

/* optimize selected items menu item */
"Optimize Selected Items" = "Optimizar ítems seleccionados";

/* optimize table menu item */
"Optimize Table" = "Optimizar tabla";

/* optimize table failed message */
"Optimize table failed." = "Optimización de tabla fallida.";

/* Optimized type for field %@ */
"Optimized type for field '%@'" = "Tipo óptimo de campo '%@'";

/* optional placeholder string */
"optional" = "opcional";

/* Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed. */
"Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed." = "El parámetro que se pasa no se puede interpretar. Única cadena o una matriz (con 2 elementos) se permite.";

/* Permission Denied */
"Permission Denied" = "Permiso denegado";

/* please choose a favorite connection view label */
"Please choose a favorite" = "Por favor, elija un favorito";

/* message of panel when ssh details are incomplete */
"Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "Por favor, introduzca el equipo del túnel SSH, o deshabilite el túnel SSH.";

/* Please enter the password */
"Please enter the password for ‘%@’:" = "Por favor, introduzca la contraseña de ‘%@’:";

/* print button */
"Print" = "Imprimir";

/* print page menu item title */
"Print Page…" = "Imprimir página…";

/* privileges label (Navigator) */
"Privileges" = "Privilegios";

/* procedure */
"procedure" = "procedimiento";

/* header for procedure info pane */
"PROCEDURE INFORMATION" = "INFORMACIÓN DE PROCEDIMIENTO";

/* procedures */
"procedures" = "procedimientos";

/* proceed button */
"Proceed" = "Continuar";

/* header for procs & funcs list */
"PROCS & FUNCS" = "PROCEDIMIENTOS & FUNCIONES";

/* toolbar item label for switching to the Run Query tab */
"Query" = "Consulta";

/* query background label for color table (Prefs > Editor) */
"Query Background" = "Fondo de consulta";

/* Query cancelled error */
"Query cancelled." = "Consulta cancelada.";

/* Query cancel by resetting connection error */
"Query cancelled.  Please note that to cancel the query the connection had to be reset; transactions and connection variables were reset." = "Consulta cancelada. Tenga en cuenta que para cancelar la consulta la conexión ha sido reiniciada; las transacciones y las variable de conexión fueron restablecidas.";

/* query editor preference pane name */
"Query Editor" = "Editor de consultas";

/* query editor preference pane tooltip */
"Query Editor Preferences" = "Preferencias de editor de consultas";

/* query logging currently disabled label
 query logging disabled label */
"Query logging is currently disabled" = "El registro de consultas está actualmente deshabitado";

/* query result print heading */
"Query Result" = "Resultado de consulta";

/* export source */
"Query results" = "Resultados de la consulta";

/* Query Status */
"Query Status" = "Estado de consulta";

/* table status : row count query failed : error title */
"Querying row count failed" = "El recuento de filas falló";

/* Quick connect item label */
"Quick Connect" = "Conexión rápida";

/* quote label for color table (Prefs > Editor) */
"Quote" = "Comillas";

/* databsse rename unsupported informative message */
"Renaming the database '%@' is currently unsupported as it contains objects other than tables (i.e. views, procedures, functions, etc.).\n\nIf you would like to rename a database please use the 'Duplicate Database', move any non-table objects manually then drop the old database." = "Renombrar la base de datos '%@' no es posible porque contiene objetos distintos a tablas (p.ej. vistas, procedimientos, funciones, etc.).\n\nSi desea renombrar una base de datos, por favor utilice 'Duplicar base de datos', mueva manualmente cualquier objeto que no sea una tabla y luego elimine la base de datos antigua.";

/* range for serial type */
"Range: %@ to %@" = "Rango: %1$@ a %2$@";

/* range for time type */
"Range: -838:59:59.0 to 838:59:59.0\nF (precision): 0 (1s) to 6 (1µs)" = "Rango: -838:59:59.0 a 838:59:59.0\nF (precisión): 0 (1s) a 6 (1μs)";

/* range for year type */
"Range: 0000, 1901 to 2155" = "Rango: 0000, 1901 a 2155";

/* range for set type */
"Range: 1 to 64 members\n1, 2, 3, 4 or 8 bytes storage" = "Rango: 1 a 64 miembros\n1, 2, 3, 4 u 8 bytes de almacenamiento";

/* range for datetime type */
"Range: 1000-01-01 00:00:00.0 to 9999-12-31 23:59:59.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Rango: 1000-01-01 00:00:00.0 a 9999-12-31 23:59:59.999999\nF (precisión): 0 (1s) a 6 (1μs)";

/* range for date type */
"Range: 1000-01-01 to 9999-12-31" = "Rango: 1000-01-01 a 9999-12-31";

/* range for timestamp type */
"Range: 1970-01-01 00:00:01.0 to 2038-01-19 03:14:07.999999\nF (precision): 0 (1s) to 6 (1µs)" = "Rango: 1970-01-01 00:00:01.0 a 2038-01-19 03:14:07.999999\nF (precisión): 0 (1s) a 6 (1μs)";

/* text showing that app is reading dump */
"Reading..." = "leyendo...";

/* menu item to refresh databases */
"Refresh Databases" = "Actualizar bases de datos";

/* refresh list menu item */
"Refresh List" = "Actualizar lista";

/* toolbar item label for switching to the Table Relations tab */
"Relations" = "Relaciones";

/* Relations tab subtitle showing table name */
"Relations for table: %@" = "Relaciones de tabla: %@";

/* reload bundles menu item label */
"Reload Bundles" = "Volver a cargar paquetes";

/* Reloading data task description */
"Reloading data..." = "Volviendo a cargar datos...";

/* Reloading table task string */
"Reloading..." = "Volviendo a cargar...";

/* remote error */
"Remote Error" = "Error remoto";

/* Bundle Editor : Remove-Bundle: remove button
 remove button */
"Remove" = "Eliminar";

/* remove all button */
"Remove All" = "Eliminar todo";

/* remove all query favorites message */
"Remove all query favorites?" = "¿Eliminar todas las consultas favoritas?";

/* Bundle Editor : Remove-Bundle: remove dialog title */
"Remove selected Bundle?" = "¿Eliminar paquete seleccionado?";

/* remove selected content filters message */
"Remove selected content filters?" = "¿Eliminar filtros de contenido seleccionados?";

/* remove selected query favorites message */
"Remove selected query favorites?" = "¿Eliminar consultas favoritas seleccionadas?";

/* removing field task status message */
"Removing field..." = "Eliminando campo...";

/* removing index task status message */
"Removing index..." = "Eliminando índice...";

/* rename database message */
"Rename database '%@' to:" = "Renombrar base de datos '%@' a:";

/* rename func menu title */
"Rename Function..." = "Renombrar función...";

/* rename proc menu title */
"Rename Procedure..." = "Renombrar procedimiento...";

/* rename table menu title */
"Rename Table..." = "Renombrar tabla...";

/* rename view menu title */
"Rename View..." = "Renombrar vista...";

/* REPAIR one or more tables - result title */
"Repair %@" = "Reparar %@";

/* repair selected items menu item */
"Repair Selected Items" = "Reparar ítems seleccionados";

/* repair table menu item */
"Repair Table" = "Reparar tabla";

/* repair table failed message */
"Repair table failed." = "Reparación de tabla fallida.";

/* Replace button */
"Replace" = "Reemplazar";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace entire content' item */
"Replace Entire Content" = "Reemplazar todo el contenido";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace selection' item */
"Replace Selection" = "Reemplazar selección";

/* description of year */
"Represents a 4 digit year value, stored as 1 byte. Invalid values are converted to 0000 and two digit values 0 to 69 will be converted to years 2000 to 2069, resp. values 70 to 99 to years 1970 to 1999.\nThe YEAR(2) type was removed in MySQL 5.7.5." = "Representa un valor de año con 4 dígitos, almacenado como 1 byte. Los valores inválidos se convierten a 0000 y los valores de dos dígitos entre 0 y 69 se convierten en los años 2000 y 2069. Los valores entre 70 y 99 se convierten en los años 1970 y 1999.\nEl tipo YEAR(2) fue eliminado en MySQL 5.7.5.";

/* description of multilinestring */
"Represents a collection of LineStrings." = "Representa una colección de LineStrings.";

/* description of geometrycollection */
"Represents a collection of objects of any other single- or multi-valued spatial type. The only restriction being, that all objects must share a common coordinate system." = "Representa una colección de objetos de cualquier otro tipo espacial de un solo valor o multivalor. La única restricción es que todos los objetos deben compartir un sistema de coordenadas común.";

/* description of multipolygon */
"Represents a collection of Polygons. The Polygons making up the MultiPolygon must not intersect." = "Representa una colección de Polygons. Los Polygons que componen el MultiPolygon no deben cruzarse.";

/* description of multipoint */
"Represents a set of Points without specifying any kind of relation and/or order between them." = "Representa un conjunto de Puntos sin especificar ningún tipo de relación y/o orden entre ellos.";

/* description of point */
"Represents a single location in coordinate space using X and Y coordinates. The point is zero-dimensional." = "Representa una única ubicación en el espacio de coordenadas usando coordenadas X e Y. El punto es cero-dimensional.";

/* description of linestring */
"Represents an ordered set of coordinates where each consecutive pair of two points is connected by a straight line." = "Representa un conjunto ordenado de coordenadas donde cada par consecutivo de dos puntos está conectado por una línea recta.";

/* required placeholder string */
"required" = "requerido";

/* description of smallint */
"Requires 2 bytes storage space. M is the optional display width and does not affect the possible value range." = "Requiere 2 bytes de espacio de almacenamiento. M es el ancho opcional de la pantalla y no afecta al rango de valor posible.";

/* description of mediumint */
"Requires 3 bytes storage space. M is the optional display width and does not affect the possible value range." = "Requiere 3 bytes de espacio de almacenamiento. M es el ancho opcional de la pantalla y no afecta al rango de valor posible.";

/* description of int */
"Requires 4 bytes storage space. M is the optional display width and does not affect the possible value range. INTEGER is an alias to this type." = "Requiere 4 bytes de espacio de almacenamiento. M es el ancho de pantalla opcional y no afecta al rango de valor posible. INTEGER es un alias de este tipo.";

/* description of bigint */
"Requires 8 bytes storage space. M is the optional display width and does not affect the possible value range. Note: Arithmetic operations might fail for large numbers." = "Requiere 8 bytes de espacio de almacenamiento. M es el ancho de pantalla opcional y no afecta al rango de valor posible. Nota: Las operaciones aritméticas pueden fallar para grandes números.";

/* reset auto_increment after deletion of all rows message */
"Reset AUTO_INCREMENT after deletion\n(only for Delete ALL ROWS IN TABLE)?" = "¿Restablecer AUTO_INCREMENT después de la eliminación\n(sólo para eliminar TODAS LA FILAS DE LA TABLA)?";

/* delete selected row button */
"Delete Selected Row" = "Eliminar fila seleccionada";

/* delete selected rows button */
"Delete Selected Rows" = "Eliminar filas seleccionadas";

/* delete all rows in table button */
"Delete ALL ROWS IN TABLE" = "Borrar TODAS LAS FILAS EN TABLA";

/* Restoring session task description */
"Restoring session..." = "Restaurando sesión...";

/* return type label (Navigator) */
"Return Type" = "Tipo de retorno";

/* return type: %@ */
"return type: %@" = "devolver tipo: %@";

/* singular word for row */
"row" = "fila";

/* plural word for rows */
"rows" = "filas";

/* text showing how many rows are in the limited filter match */
"Rows %@ - %@ from filtered matches" = "Filas %1$@ - %2$@ de resultados filtrados";

/* text showing how many rows are in the limited result */
"Rows %@ - %@ of %@%@ from table" = "Filas %1$@ - %2$@ de %3$@%4$@ de la tabla";

/* Table Info Section : number of rows (exact value) */
"rows: %@" = "filas: %@";

/* Table Info Section : number of rows (estimated value) */
"rows: ~%@" = "filas: ~%@";

/* run all button */
"Run All" = "Ejecutar todo";

/* Run All menu item title */
"Run All Queries" = "Ejecutar todas las consultas";

/* Title of button to run current query in custom query view */
"Run Current" = "Ejecutar actual";

/* Title of action menu item to run current query in custom query view */
"Run Current Query" = "Ejecutar consulta actual";

/* toolbar item label for switching to the Run Query tab */
"Run Custom Query" = "Ejecutar consulta personalizada";

/* Title of button to run query just before text caret in custom query view */
"Run Previous" = "Ejecutar anterior";

/* Title of action menu item to run query just before text caret in custom query view */
"Run Previous Query" = "Ejecutar consulta anterior";

/* Title of action menu item to run selected text in custom query view */
"Run Selected Text" = "Ejecutar texto seleccionado";

/* Title of button to run selected text in custom query view */
"Run Selection" = "Ejecutar selección";

/* Running multiple queries string */
"Running query %i of %lu..." = "Ejecutando consulta %1$i de %2$lu...";

/* Running multiple queries string */
"Running query %ld of %lu..." = "Ejecutando consulta %1$ld de %2$lu...";

/* Running single query string */
"Running query..." = "Ejecutando consulta...";

/* Save trigger button label */
"Save" = "Guardar";

/* Save All to Favorites */
"Save All to Favorites" = "Guardar todo en favoritos";

/* save as button title */
"Save As..." = "Guardar como...";

/* Bundle Editor : BLOB dropdown : 'save BLOB as dat file' item */
"save BLOB as dat file" = "guardar BLOB como archivo dat";

/* Bundle Editor : BLOB dropdown : 'save BLOB as image file' item */
"save BLOB as image file" = "guardar BLOB como archivo de imagen";

/* Save Current Query to Favorites */
"Save Current Query to Favorites" = "Guardar consulta actual en favoritos";

/* save page as menu item title */
"Save Page As…" = "Guardar página como…";

/* Save Queries… */
"Save Queries…" = "Guardar consultas…";

/* Save Query… */
"Save Query…" = "Guardar consulta…";

/* Save Selection to Favorites */
"Save Selection to Favorites" = "Guardar selección en favoritos";

/* save view as button title */
"Save View As..." = "Guardar vista como...";

/* schema path header for completion tooltip */
"Schema path:" = "Ruta de esquema:";

/* Search in MySQL Documentation */
"Search in MySQL Documentation" = "Buscar en documentación de MySQL";

/* Search in MySQL Help */
"Search in MySQL Help" = "Buscar en ayuda de MySQL";

/* Select Active Query */
"Select Active Query" = "Seleccionar consulta activa";

/* toolbar item for selecting a db */
"Select Database" = "Seleccionar base de datos";

/* selected items */
"selected items" = "ítems seleccionados";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as comma-separated' item */
"Selected Rows (CSV)" = "Filas seleccionadas (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as SQL' item */
"Selected Rows (SQL)" = "Filas seleccionadas (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as tab-separated' item */
"Selected Rows (TSV)" = "Filas seleccionadas (TSV)";

/* Bundle Editor : Scope=Field : Input source dropdown: 'selected text' item */
"Selected Text" = "Texto seleccionado";

/* selection label for color table (Prefs > Editor) */
"Selection" = "Selección";

/* table structure : indexes : delete index : no columns error : description */
"Sequel Ace could not find any columns belonging to this index. Maybe it has been removed already?" = "Sequel Ace no pudo encontrar ninguna columna perteneciente a este índice. ¿Tal vez ya ha sido eliminado?";

/* Preferences : Network : Custom SSH client : warning dialog message */
"Sequel Ace only supports and is tested with the default OpenSSH client versions included with macOS. Using different clients might cause connection issues, security risks or not work at all.\n\nPlease be aware, that we cannot provide support for such configurations." = "Sequel Ace solo soporta y se prueba con las versiones predeterminadas del cliente OpenSSH incluidas con macOS. Usar clientes diferentes puede causar problemas de conexión, riesgos de seguridad o no funcionar.\n\nPor favor tenga en cuenta que no podemos proporcionar soporte para estas configuraciones.";

/* sequelace URL scheme command not supported. */
"sequelace URL scheme command not supported." = "Comando de esquema URL de sequelace no soportado.";

/* sequelace url Scheme Error */
"sequelace URL Scheme Error" = "Error de esquema URL sequelace";

/* Table Structure : Collation dropdown : 'item is the same as the collation of server' marker
 Table Structure : Encoding dropdown : 'item is server default' marker */
"Server" = "Servidor";

/* Add Database : Charset dropdown : default item ($1 = charset name)
 Add Database : Collation dropdown : default item ($1 = collation name) */
"Server Default (%@)" = "Servidor predeterminado (%@)";

/* server processes window title (var = hostname) */
"Server Processes on %@" = "Procesos de servidor en %@";

/* Initial filename for 'Save session' file */
"Session" = "Sesión";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html' item */
"Show as HTML" = "Mostrar como HTML";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html tooltip' item */
"Show as HTML Tooltip" = "Mostrar como sugerencia HTML";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as text tooltip' item */
"Show as Text Tooltip" = "Mostrar como sugerencia de texto";

/* show console */
"Show Console" = "Mostrar consola";

/* show create func syntax menu item */
"Show Create Function Syntax..." = "Mostrar sintaxis create function...";

/* show create proc syntax menu item */
"Show Create Procedure Syntax..." = "Mostrar sintaxis create procedure...";

/* show create syntaxes menu item */
"Show Create Syntaxes..." = "Mostrar sintaxis create...";

/* show create table syntax menu item */
"Show Create Table Syntax..." = "Mostrar sintaxis create table...";

/* show create view syntax menu item */
"Show Create View Syntax..." = "Mostrar sintaxis create view...";

/* Show detail button */
"Show Detail" = "Mostrar detalle";

/* MySQL Help Viewer : Results list : Link tooltip */
"Show MySQL help for %@" = "Mostrar ayuda de MySQL para %@";

/* show navigator */
"Show Navigator" = "Mostrar navegador";

/* show tab bar */
"Show Tab Bar" = "Mostrar barra de pestañas";

/* tooltip for toolbar item for show console */
"Show the console which shows all MySQL commands performed by Sequel Ace" = "Muestra la consola que muestra todos los comandos MySQL ejecutados por Sequel Ace";

/* Show Toolbar menu item */
"Show Toolbar" = "Mostrar barra de herramientas";

/* filtered item count */
"Showing %lu of %lu processes" = "Mostrando %1$lu de %2$lu procesos";

/* shutdown server : confirmation dialog : shutdown button */
"Shutdown" = "Apagar";

/* shutdown server : error dialog : title */
"Shutdown failed!" = "¡Apagado fallido!";

/* range of integer types */
"Signed: %@ to %@\nUnsigned: %@ to %@" = "Firmado: %1$@ a %2$@\nSin firmar: %3$@ a %4$@";

/* Table Info Section : table size on disk */
"size: %@" = "tamaño: %@";

/* skip existing button */
"Skip existing" = "Omitir existente";

/* skip problems button */
"Skip problems" = "Omitir problemas";

/* beta build label */
"Beta Build" = "Complilación beta";

/* socket connection failed title */
"Socket connection failed!" = "¡La conexión del socket falló!";

/* socket not found title */
"Socket not found!" = "¡Socket no encontrado!";

/* Some export folders not writable explanatory text */
"Some of the target export folders are not writable.  Please select a new export location and try again." = "Algunas de las carpetas de destino no se pueden modificar.  Por favor, seleccione una nueva ubicación y vuelva a intentarlo.";

/* Some export folders missing explanatory text */
"Some of the target export folders no longer exist.  Please select a new export location and try again." = "Algunas de las carpetas de destino ya no existen.  Por favor, seleccione una nueva ubicación y vuelva a intentarlo.";

/* Sorting table task description */
"Sorting table..." = "Ordenando tabla...";

/* spatial index menu item title */
"SPATIAL" = "SPATIAL";

/* sql data access label (Navigator) */
"SQL Data Access" = "Datos de acceso SQL";


/* Connection Secured via SSL information text */
"Connection Secured via SSL" = "Conexión segura a través de SSL";

/* SSH connected titlebar marker */
"SSH Connected" = "SSH conectado";

/* SSH connecting very short status message */
"SSH connecting..." = "conectando con SSH...";

/* SSH connecting titlebar marker */
"SSH Connecting…" = "Conectando SSH…";

/* SSH connection failed title */
"SSH connection failed!" = "¡Conexión SSH fallida!";

/* SSH disconnected titlebar marker */
"SSH Disconnected" = "SSH desconectado";

/* SSH key check error */
"SSH Key not found" = "Clave SSH no encontrada";

/* title when ssh tunnel port forwarding failed */
"SSH port forwarding failed" = "Falló el reenvío de puerto SSH";

/* SSL certificate authority file check error */
"SSL Certificate Authority File not found" = "Archivo de certificado de autorización SSL no encontrado";

/* SSL certificate file check error */
"SSL Certificate File not found" = "Archivo de certificado SSL no encontrado";

/* SSL requested but not used title */
"SSL connection not established" = "Conexión SSL no establecida";

/* SSL key file check error */
"SSL Key File not found" = "Archivo de clave SSL no encontrado";

/* Standard memory export summary */
"Standard memory" = "Memoria estándar";

/* started */
"started" = "iniciado";

/* status file for sequelace url scheme command couldn't be written error message */
"Status file for sequelace url scheme command couldn't be written!" = "¡El archivo de estado para el esquema de comando URL de sequelace no se ha podido escribir!";

/* stop button */
"Stop" = "Detener";

/* Stop queries string */
"Stop queries" = "Detener consultas";

/* Stop query string */
"Stop query" = "Detener consulta";

/* description of timestamp */
"Stores a date and time of day as seconds since the beginning of the UNIX epoch (1970-01-01 00:00:00). The values displayed/stored are affected by the connection's @@time_zone setting.\nThe representation is the same as for DATETIME. Invalid values, as well as \"second zero\", are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F. Some additional rules may apply." = "Guarda una fecha y hora del día como segundos desde el comienzo del epoch UNIX (1970-01-01 00:00:00). Los valores mostrados/almacenados se ven afectados por la configuración de @@time_zone de la conexión.\nLa representación es la misma que para DATETIME. Los valores no válidos, así como \"segundo cero\", se convierten a 0000-00-00 00:00:00:00.0. Los segundos fraccionales se añadieron en MySQL 5. .4 con una precisión de hasta microsegundos (6), especificada por F. Algunas reglas adicionales pueden aplicarse.";

/* description of datetime */
"Stores a date and time of day. The representation is YYYY-MM-DD HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Guarda una fecha y hora del día. La representación es AAA-MM-DD HH:MM:SS[. *], Siendo segundos fraccionales. El valor no se ve afectado por ningún ajuste de zona horaria. Los valores no válidos se convierten a 0000-00-00 00:00:00.0. Los segundos fraccionales se añadieron en MySQL 5.6.4 con una precisión descendente a microsegundos (6), especificados por F.";

/* description of date */
"Stores a date without time information. The representation is YYYY-MM-DD. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00." = "Guarda una fecha sin información de tiempo. La representación es AAA-MM-DD. El valor no se ve afectado por ninguna configuración de zona horaria. Los valores no válidos se convierten a 0000-00-00.";

/* description of time */
"Stores a time of day, duration or time interval. The representation is HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 00:00:00. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "Almacena una hora del día, duración o intervalo de tiempo. La representación es HH:MM:SS[. *], Siendo segundos fraccionales. El valor no se ve afectado por ningún ajuste de zona horaria. Los valores no válidos se convierten a 00:00:00. Los segundos fraccionales se añadieron en MySQL 5.6.4 con una precisión descendente a microsegundos (6), especificados por F.";

/* toolbar item label for switching to the Table Structure tab */
"Structure" = "Estructura";

/* successfully analyzed all selected items message */
"Successfully analyzed all selected items." = "Todos los ítems seleccionados analizados correctamente.";

/* analyze table successfully passed message */
"Successfully analyzed table." = "Tabla analizada correctamente.";

/* successfully flushed all selected items message */
"Successfully flushed all selected items." = "Todos los ítems seleccionados limpiados correctamente.";

/* message of panel when successfully flushed privs */
"Successfully flushed privileges." = "Privilegios vueltos a cargar correctamente.";

/* flush table successfully passed message */
"Successfully flushed table." = "Tabla limpiada correctamente.";

/* successfully optimized all selected items message */
"Successfully optimized all selected items." = "Todos los ítems seleccionados optimizados correctamente.";

/* optimize table successfully passed message */
"Successfully optimized table." = "Tabla optimizada correctamente.";

/* successfully repaired all selected items message */
"Successfully repaired all selected items." = "Todos los ítems seleccionados reparados correctamente.";

/* repair table successfully passed message */
"Successfully repaired table." = "Tabla reparada correctamente.";

/* tooltip for toolbar item for switching to the Run Query tab */
"Switch to the Run Query tab" = "Cambia a la pestaña Ejecutar consulta";

/* tooltip for toolbar item for switching to the Table Content tab */
"Switch to the Table Content tab" = "Cambia a la pestaña Contenido de tabla";

/* tooltip for toolbar item for switching to the Table Info tab */
"Switch to the Table Info tab" = "Cambia a la pestaña Información de tabla";

/* tooltip for toolbar item for switching to the Table Relations tab */
"Switch to the Table Relations tab" = "Cambia a la pestaña Relaciones de tabla";

/* tooltip for toolbar item for switching to the Table Structure tab */
"Switch to the Table Structure tab" = "Cambia a la pestaña Estructura de tabla";

/* tooltip for toolbar item for switching to the Table Triggers tab */
"Switch to the Table Triggers tab" = "Cambia a la pestaña Disparadores de tabla";

/* tooltip for toolbar item for switching to the User Manager tab */
"Switch to the User Manager tab" = "Cambia a la pestaña Administrador de usuario";

/* description for table syntax copied notification */
"Syntax for %@ table copied" = "Sintaxis de tabla %@ copiada";

/* table */
"table" = "tabla";

/* csv export table heading
 table
 Table Structure : Collation dropdown : 'item is the same as the collation of table' marker
 Table Structure : Encoding dropdown : 'item is table default' marker */
"Table" = "Tabla";

/* export label showing that the app is fetching data for a specific table */
"Table %lu of %lu (%@): Fetching data..." = "Tabla %1$lu de %2$lu (%3$@): Recuperando datos...";

/* export label showing app is fetching relations data for a specific table */
"Table %lu of %lu (%@): Fetching relations data..." = "Tabla %1$lu de %2$lu (%3$@): Recuperando datos de relaciones...";

/* export label showing app if writing data for a specific table */
"Table %lu of %lu (%@): Writing data..." = "Tabla %1$lu de %2$lu (%3$@): Escribiendo datos...";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'table changed' item */
"Table changed" = "Tabla modificada";

/* table checksum message */
"Table checksum" = "Checksum de tabla";

/* table checksum: %@ */
"Table checksum: %@" = "Checksum de tabla: %@";

/* table content print heading */
"Table Content" = "Contenido de tabla";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as comma-separated' item */
"Table Content (CSV)" = "Contenido de tabla (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as SQL' item */
"Table Content (SQL)" = "Contenido de tabla (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as tab-separated' item */
"Table Content (TSV)" = "Contenido de tabla (TSV)";

/* toolbar item for navigation history */
"Table History" = "Historial de tabla";

/* toolbar item label for switching to the Table Info tab */
"Table Info" = "Información de tabla";

/* header for table info pane */
"TABLE INFORMATION" = "INFORMACIÓN DE TABLA";

/* table information print heading */
"Table Information" = "Información de tabla";

/* message of panel when no name is given for table */
"Table must have a name." = "La tabla debe tener un nombre.";

/* general preference pane tooltip */
"Table Preferences" = "Preferencias de tabla";

/* toolbar item label for switching to the Table Relations tab */
"Table Relations" = "Relaciones de tabla";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table row changed' item */
"Table Row changed" = "Fila de tabla modificada";

/* table structure print heading */
"Table Structure" = "Estructura de tabla";

/* toolbar item label for switching to the Table Triggers tab */
"Table Triggers" = "Disparadores de tabla";

/* Add Relation sheet title, showing table name */
"Table: %@" = "Tabla: %@";

/* tables preference pane name */
"Tables" = "Tablas";

/* tables */
"tables" = "tablas";

/* header for table list */
"TABLES" = "TABLAS";

/* header for table & views list */
"TABLES & VIEWS" = "TABLAS & VISTAS";

/* Connection test very short status message */
"Testing connection..." = "Comprobando conexión...";

/* MySQL connection test very short status message */
"Testing MySQL..." = "Comprobando MySQL...";

/* SSH testing very short status message */
"Testing SSH..." = "Comprobando SSH...";

/* text label for color table (Prefs > Editor) */
"Text" = "Texto";

/* Text is too long. Maximum text length is set to %llu. */
"Text is too long. Maximum text length is set to %llu." = "El texto es demasiado largo. La longitud máxima del texto está establecida en %llu.";

/* Release notes dialog title thanking user for upgrade */
"Thanks for updating Sequel Ace!" = "¡Gracias por actualizar Sequel Ace!";

/* Open Files : Bundle : Install-Error : Destination path already exists error dialog message */
"The Bundle ‘%@’ already exists." = "El paquete ‘%@’ ya existe.";

/* Open Files : Bundle: UUID : UUID-Attribute is missing in bundle's command.plist file */
"The Bundle ‘%@’ has no UUID which is necessary to identify installed Bundles." = "El paquete ‘%@’ no tiene UUID el cual es necesario para identificar los paquetes instalados.";

"‘%@’ Bundle contains legacy components" = "El paquete ‘%@’ contiene componentes antiguos";

"In these files:\n\n%@\n\nDo you still want to install the bundle?" = "En estos archivos:\n\n%@\n\n¿Desea instalar el paquete?";

/* message while reading a spf file which matches non-supported formats. */
"The chosen file %@ contains ‘%@’ data." = "El archivo elegido “%1$@” contiene ‘%2$@’ datos.";

/* the color theme ‘%@’ already exists. */
"The color theme ‘%@’ already exists." = "El tema de color ‘%@’ ya existe.";

/* the connection is busy. please wait and try again tooltip */
"The connection is busy. Please wait and try again." = "La conexión está ocupada. Por favor espere y vuelva a intentarlo.";

/* the connection of the active connection window is not identical tooltip */
"The connection of the active connection window is not identical." = "La conexión de la ventana activa no es idéntica.";

/* Connection lost during import error message */
"The connection to the server was lost during the import.  The import is only partially complete." = "La conexión con el servidor se perdió durante la importación.  La importación solo se completó parcialmente.";

/* Create syntax permission denied detail */
"The creation syntax could not be retrieved due to a permissions error.\n\nPlease check your user permissions with an administrator." = "La sintaxis de creación no se ha podido recuperar debido a un error de permisos.\n\nPor favor, compruebe sus permisos de usuario con un administrador.";

/* CSV file open error */
"The CSV file you selected could not be found or read." = "El archivo CSV seleccionado no se ha podido encontrar o leer.";

/* Error when CSV appears to have too many columns to import, probably due to line ending mismatch */
"The CSV was read as containing more than 512 columns, more than the maximum columns permitted for speed reasons by Sequel Ace.\n\nThis usually happens due to errors reading the CSV; please double-check the CSV to be imported and the line endings and escape characters at the bottom of the CSV selection dialog." = "El CSV leído contiene más de 512 columnas, más del máximo de columnas permitidas por razones de velocidad en Sequel Ace.\n\nEsto normalmente ocurre debido a errores de lectura en el CSV; por favor, compruebe el CSV que será importado además de los finales de línea y los caracteres de escape en la parte inferior del cuadro de diálogo de selección CSV.";

/* unsaved theme informative message */
"The current color theme is unsaved. Do you want to proceed without saving it?" = "El tema de color actual no está guardado. ¿Desea continuar sin guardarlo?";

/* Short important release note for swap of custom query buttons */
"The Custom Query \"Run\" and \"Run All\" button positions and their shortcuts have been swapped." = "Las posiciones de los botones Consulta Personalizada y Ejecutar Todo y sus accesos directos han sido intercambiados.";

/* the following default bundles were updated:\n%@\nyour modifications were stored as “(user)”. */
"The following default Bundles were updated:\n%@\nYour modifications were stored as “(user)”." = "Los siguientes paquetes predeterminados fueron actualizados:\n%@\nSus modificaciones fueron almacenadas como “(user)”.";

/* favorites export error informative message */
"The following error occurred during the export process:\n\n%@" = "Ha ocurrido el siguiente error durante el proceso de exportación:\n\n%@";

/* favorites import error informative message */
"The following error occurred during the import process:\n\n%@" = "Ha ocurrido el siguiente error durante el proceso de importación:\n\n%@";

/* table structure : indexes : delete index : error 1553 : description */
"The foreign key relationship '%@' has a dependency on index '%@'. This relationship must be removed before the index can be deleted.\n\nAre you sure you want to continue to delete the relationship and the index? This action cannot be undone." = "La relación de la clave ajena '%@' tiene una dependencia en este índice. Esta relación debe ser eliminada antes de que el índice pueda ser eliminado.\n\n¿Está seguro que desea eliminar la relación y el índice? Esta acción no se puede deshacer.";

/* table list change alert message */
"The list of tables has changed" = "La lista de las tablas ha cambiado";

/* message when trying to rename a table/view/proc/etc to an already used name */
"The name '%@' is already used." = "El nombre '%@' ya está en uso.";

/* table list change alert informative message */
"The number of tables in this database has changed since the export dialog was opened. There are now %lu additional table(s), most likely added by an external application.\n\nHow would you like to proceed?" = "El número de tablas de esta base de datos ha cambiado desde que la ventana de exportación fue abierta. Actualmente hay %d tabla(s) adicional(es), probablemente añadida(s) por una aplicación externa.\n\n¿Cómo le gustaría continuar?";

/* message of panel when no rows have been affected after writing to the db */
"The row was not written to the MySQL database. You probably haven't changed anything.\nReload the table to be sure that the row exists and use a primary key for your table.\n(This error can be turned off in the preferences.)" = "La fila no fue escrita en la base de datos MySQL. Usted probablemente no ha modificado nada.\nVuelva a cargar la tabla para asegurarse que la fila existe y use la clave primaria de su tabla.\n(Este error se puede deshabilitar en las preferencias.)";

/* export : import settings : file version error description ($1 = is version, $2 = list of supported versions); note: the u00A0 is a non-breaking space, do not add more whitespace. */
"The selected export settings were stored with version %1$ld, but only settings with the following versions can be imported: %2$@.\n\nEither save the settings in a backwards compatible way or update your version of Sequel Ace." = "Las opciones de configuración seleccionadas para exportación fueron almacenadas con la versión %1$ld, pero solo las opciones con las siguientes versiones pueden ser importadas: %2$@\n\nGrabe las opciones de configuración con una versión compatible hacia atrás o actualice su versión de Sequel Ace.";

/* export : import settings : spf content type error description */
"The selected file contains data of type “%1$@”, but type “%2$@” is needed. Please choose a different file." = "El archivo seleccionado contiene datos de tipo “%1$@”, pero se necesita el tipo “%2$@”. Por favor, elija un archivo diferente.";

/* export : import settings : file error description */
"The selected file is either not a valid SPF file or severely corrupted." = "El archivo seleccionado no es un archivo SPF válido o está gravemente dañado.";

/* error deleting relation informative message */
"The selected relation couldn't be deleted.\n\nMySQL said: %@" = "La relación seleccionada no se ha podido eliminar.\n\nMySQL dijo: %@";

/* error deleting trigger informative message */
"The selected trigger couldn't be deleted.\n\nMySQL said: %@" = "El disparador seleccionado no se ha podido eliminar.\n\nMySQL dijo: %@";

/* description of tinyint */
"The smallest integer type, requires 1 byte storage space. M is the optional display width and does not affect the possible value range." = "El tipo entero más pequeño requiere un espacio de almacenamiento de 1 byte. M es el ancho de la pantalla opcional y no afecta al rango de valor posible.";

/* message of panel when connection to socket failed because optional socket could not be found */
"The socket file could not be found in any common location. Please supply the correct socket location.\n\nMySQL said: %@" = "El socket no se ha podido encontrar en ninguna ubicación. Por favor, proporcione la ubicación correcta del socket.\n\nMySQL dijo: %@";

/* error creating relation informative message */
"The specified relation could not be created.\n\nMySQL said: %@" = "La relación especificada no pudo ser creada.\n\nMySQL dijo: %@";

/* error creating trigger informative message */
"The specified trigger was unable to be created.\n\nMySQL said: %@" = "El disparador especificado no ha podido ser creado.\n\nMySQL dijo: %@";

/* sql import : charset error alert : detail message */
"The SQL file uses utf8mb4 encoding, but your MySQL version only supports the limited utf8 subset.\n\nYou can continue the import, but any non-BMP characters in the SQL file (eg. some typographic and scientific special characters, archaic CJK logograms, emojis) will be unrecoverably lost!" = "El fichero SQL usa la codificación utf8mb4, pero su versión de MySQL solo admite un set limitado de codificaciones utf8.\n\nPuede continuar con la importación, ¡pero todos los caracteres no BMP en el fichero SQL (por ejemplo, algunos caracteres tipográficos y caracteres científicos especiales, logogramas CJK arcaicos, emojis) se perderán irremediablemente!";

/* SQL file open error */
"The SQL file you selected could not be found or read." = "El archivo SQL seleccionado no se ha podido encontrar o leer.";

/* Prompt for SSH password when keychain fetch failed */
"The SSH password could not be loaded from the keychain; please enter the SSH password for %@:" = "La contraseña SSH no se ha podido cargar desde el llavero; por favor, introduzca la contraseña SSH de %@:";

/* Prompt for SSH password when direct fetch failed */
"The SSH password could not be loaded; please enter the SSH password for %@:" = "La contraseña SSH no se ha podido cargar; por favor, introduzca la contraseña SSH de %@:";

/* SSH tunnel authentication failed message */
"The SSH Tunnel could not authenticate with the remote host. Please check your password and ensure you still have access." = "El túnel SSH no se ha podido autenticar con el equipo remoto. Por favor, compruebe su contraseña y asegúrese que todavía tiene acceso.";

/* SSH tunnel unexpectedly closed */
"The SSH Tunnel has unexpectedly closed." = "El túnel SSH se ha cerrado inesperadamente.";

/* SSH tunnel was closed by remote host message */
"The SSH Tunnel was closed 'by the remote host'. This may indicate a networking issue or a network timeout." = "El túnel SSH fue cerrado 'por el equipo remoto'. Esto puede indicar un problema en la red o se ha excedido el tiempo de espera.";

/* SSH tunnel forwarding port connection refused message */
"The SSH Tunnel was established successfully, but could not forward data to the remote port as the remote port refused the connection." = "El túnel SSH fue establecido correctamente, pero no se ha podido enviar datos al puerto remoto ya que este rechazó la conexión.";

/* SSH tunnel unable to bind to local port message */
"The SSH Tunnel was unable to bind to the local port. This error may occur if you already have an SSH connection to the same server and are using a 'LocalForward' setting in your SSH configuration.\n\nWould you like to fall back to a standard connection to localhost in order to use the existing tunnel?" = "El túnel SSH no ha podido enlazar con el puerto local. Este error puede ocurrir si ya tiene una conexión SSH con el mismo servidor y usa 'LocalForward' en su configuración SSH.\n\n¿Desea conectarse al equipo local con una conexión estándar para usar el túnel existente?";

/* SSH tunnel failed or timed out message */
"The SSH Tunnel was unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds)." = "El túnel SSH no se ha podido conectar al equipo %1$@, o la solicitud expiró.\n\nAsegúrese que la dirección es correcta y que dispone de los privilegios necesarios, o intente aumentar el tiempo de espera de la conexión (actualmente %2$ld segundos).";

/* message of panel when loading of table failed and presumably due to used filter argument */
"The table data couldn't be loaded presumably due to used filter clause. \n\nMySQL said: %@" = "Los datos de la tabla no se han podido cargar probablemente debido a la cláusula del filtro usado. \n\nMySQL dijo: %@";

/* message of panel when loading of table failed */
"The table data couldn't be loaded.\n\nMySQL said: %@" = "Los datos de la tabla no se han podido cargar.\n\nMySQL dijo: %@";

/* Export folder not writable explanatory text */
"The target export folder is not writable.  Please select a new export location and try again." = "No se puede escribir en la carpeta destino de la exportación.  Por favor, seleccione una nueva ubicación para exportar y vuelva a intentarlo.";

/* Export folder not chosen by user explanatory text */
"No directory selected.  Please select a new export location and try again." = "No se ha seleccionado ningún directorio. Por favor, seleccione una nueva ubicación de exportación e inténtelo de nuevo.";
"No directory selected." = "Ningún directorio seleccionado.";
"Please select a new export location and try again." = "Por favor, seleccione una nueva ubicación para exportar e inténtelo de nuevo.";

/* Export folder missing explanatory text */
"The target export folder no longer exists.  Please select a new export location and try again." = "No existe la carpeta destino de la exportación.  Por favor, seleccione una nueva ubicación para exportar y vuelva a intentarlo.";

/* theme name label */
"Theme Name:" = "Nombre de tema:";

/* themes installation error */
"Themes Installation Error" = "Error al instalar los temas";

/* message of panel when table content cannot be copied */
"There have been errors while copying table content. Please check the new table." = "Ha habido errores al copiar el contenido de la tabla. Por favor, compruebe la nueva tabla.";

/* Cannot duplicate a table with triggers to a different database */
"Cannot duplicate a table with triggers to a different database." = "No se puede duplicar una tabla con triggers a una base de datos diferente.";

/* text shown when query was successfull */
"There were no errors." = "No hubo errores.";

/* delete field and foreign key informative message */
"This field is part of a foreign key relationship with the table '%@'. This relationship must be removed before the field can be deleted.\n\nAre you sure you want to continue to delete the relationship and the field? This action cannot be undone." = "Este campo es parte de una relación de clave ajena con la tabla '%@'. Esta relación debe ser eliminada antes de que el campo pueda ser eliminado.\n\n¿Está seguro que desea eliminar la relación y el campo? Esta acción no se puede deshacer.";

/* table structure : indexes : delete index : error 1553, no FK found : description */
"This index cannot be deleted, because it is used by an existing foreign key relationship.\n\nPlease remove the relationship, before trying to remove this index.\n\nMySQL said: %@" = "Este índice no puede ser eliminado, porque es usado por una relación de clave foránea existente.\n\nPor favor elimine la relación antes de intentar eliminar este índice.\n\nMySQL dijo: %@";

/* description of serial */
"This is an alias for BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE." = "Este es un alias para BIGINT NO SIGNED NULL AUTO_INCREMENT UNIQUE.";

/* description of dec
 description of fixed
 description of numeric */
"This is an alias for DECIMAL." = "Este es un alias para DECIMAL.";

/* description of double real */
"This is an alias for DOUBLE, unless REAL_AS_FLOAT is configured." = "Este es un alias para DOUBLE, a menos que REAL_AS_FLOAT esté configurado.";

/* description of double precision */
"This is an alias for DOUBLE." = "Este es un alias para DOUBLE.";

/* description of bool
 description of boolean */
"This is an alias for TINYINT(1)." = "Este es un alias para TINYINT(1).";

/* Table Structure : Collation dropdown : database marker tooltip */
"This is the default collation of database %@." = "Esta es la colación predeterminada de la base de datos %@.";

/* Table Structure : Collation dropdown : default marker tooltip */
"This is the default collation of encoding %@." = "Esta es la colación de codificación predeterminada %@.";

/* Table Structure : Collation dropdown : table marker tooltip */
"This is the default collation of table %@." = "Esta es la colación predeterminada de la tabla %@.";

/* Table Structure : Collation dropdown : server marker tooltip */
"This is the default collation of this server." = "Esta es la colación predeterminada de este servidor.";

/* Table Structure : Encoding dropdown : database marker tooltip */
"This is the default encoding of database %@." = "Esta es la codificación predeterminada de la base de datos %@.";

/* Table Structure : Encoding dropdown : table marker tooltip */
"This is the default encoding of table %@." = "Esta es la codificación predeterminada de la tabla %@.";

/* Table Structure : Encoding dropdown : server marker tooltip */
"This is the default encoding of this server." = "Esta es la codificación predeterminada de este servidor.";

/* This table currently does not support relations. Only tables that use the InnoDB storage engine support them. */
"This table currently does not support relations. Only tables that use the InnoDB storage engine support them." = "Esta tabla no soporta relaciones actualmente. Solo las tablas que usan el motor de almacenamiento InnoDB las soportan.";

/* user has no hosts informative message */
"This user doesn't have any hosts associated with it. It will be deleted unless one is added" = "Este usuario no tiene ningún equipo asociado. Este se eliminará a menos que sea añadido uno";

/* error removing host informative message */
"This user doesn't seem to have any associated hosts and will be removed unless a host is added." = "Este usuario no parece tener ningún equipo asociado y se eliminará a menos que un equipo sea añadido.";

/* shutdown server : confirmation dialog : message */
"This will wait for open transactions to complete and then quit the mysql daemon. Afterwards neither you nor anyone else can connect to this database!\n\nFull management access to the server's operating system is required to restart MySQL!" = "Esto esperará a que las transacciones abiertas se completen y luego cerrará el proceso de mysql. ¡Después, ni tú ni nadie más podrá conectarse a esta base de datos!\n\n¡Se requiere acceso de gestión completa al sistema operativo del servidor para reiniciar MySQL!";

/* export filename time token */
"Time" = "Hora";

/* toolbar item label for switching to the Table Triggers tab */
"Triggers" = "Disparadores";

/* triggers for table label */
"Triggers for table: %@" = "Disparadores de tabla: %@";

/* truncate button */
"Truncate" = "Truncar";

/* truncate tables message */
"Truncate selected tables?" = "¿Truncar tablas seleccionadas?";

/* truncate table menu title */
"Truncate Table..." = "Truncar tabla...";

/* truncate table message */
"Truncate table '%@'?" = "¿Truncar tabla '%@'?";

/* truncate tables menu item */
"Truncate Tables" = "Truncar tablas";

/* type label (Navigator) */
"Type" = "Tipo";

/* type declaration header */
"Type Declaration:" = "Declaración de tipo:";

/* add index error message */
"Unable to add index" = "No se puede añadir el índice";

/* unable to analyze selected items message */
"Unable to analyze selected items" = "No se puede analizar los ítems seleccionados";

/* unable to analyze table message */
"Unable to analyze table" = "No se puede analizar la tabla";

/* unable to check selected items message */
"Unable to check selected items" = "No se puede comprobar los ítems seleccionados";

/* unable to check table message */
"Unable to check table" = "No se puede comprobar la tabla";

/* message of panel when connection to host failed due to access denied error */
"Unable to connect to host %@ because access was denied.\n\nDouble-check your username and password and ensure that access from your current location is permitted.\n\nMySQL said: %@" = "No se puede conectar con el equipo %1$@ ya que el acceso fue denegado.\n\nCompruebe su nombre de usuario y contraseña y asegúrese de que el acceso desde su ubicación actual está permitido.\n\nMySQL dijo: %2$@";

/* message of panel when SSH port forwarding failed */
"Unable to connect to host %@ because the port connection via SSH was refused.\n\nPlease ensure that your MySQL host is set up to allow TCP/IP connections (no --skip-networking) and is configured to allow connections from the host you are tunnelling via.\n\nYou may also want to check the port is correct and that you have the necessary privileges.\n\nChecking the error detail will show the SSH debug log which may provide more details.\n\nMySQL said: %@" = "No se puede conectar con el equipo %1$@ ya que la conexión del puerto a través de SSH fue rechazada.\n\nPor favor asegúrese que su equipo MySQL está configurado para permitir conexiones TCP/IP (no --skip-networking) y conexiones desde el equipo a través de un túnel.\n\nTambién puede comprobar que el puerto es el correcto y que dispone de los privilegios necesarios.\n\nComprobando el detalle de error mostrará el registro de depuración SSH que puede proporcionar más detalles.\n\nMySQL dijo: %2$@";

/* message of panel when connection to host failed */
"Unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds).\n\nMySQL said: %@" = "No se puede conectar con el equipo %1$@, o la solicitud expiró.\n\nAsegúrese que la dirección es correcta y que dispone de los privilegios necesarios, o intente aumentar el tiempo de espera de conexión (actualmente %2$ld segundos).\n\nMySQL dijo: %3$@";

/* message of panel when connection to host failed */
"Unable to connect via the socket, or the request timed out.\n\nDouble-check that the socket path is correct and that you have the necessary privileges, and that the server is running.\n\nMySQL said: %@" = "No se puede conectar a través del socket, o la solicitud expiró.\n\nCompruebe que la ruta del socket es la correcta, que dispone de los privilegios necesarios y que el servidor se está ejecutando.\n\nMySQL dijo: %@";

/* unable to copy database message */
"Unable to copy database" = "No se puede copiar la base de datos";

/* error deleting index message */
"Unable to delete index" = "No se puede eliminar el índice";

/* error deleting relation message */
"Unable to delete relation" = "No se puede eliminar la relación";

/* error deleting trigger message */
"Unable to delete trigger" = "No se puede eliminar el disparador";

/* unable to flush selected items message */
"Unable to flush selected items" = "No se puede limpiar los ítems seleccionados";

/* unable to flush table message */
"Unable to flush table" = "No se puede limpiar la tabla";

/* unable to get list of users message */
"Unable to get list of users" = "No se puede obtener la lista de usuarios";

/* error killing connection message */
"Unable to kill connection" = "No se puede finalizar la conexión";

/* error killing query message */
"Unable to kill query" = "No se puede finalizar la consulta";

/* unable to optimze selected items message */
"Unable to optimze selected items" = "No se puede optimizar los ítems seleccionados";

/* unable to optimze table message */
"Unable to optimze table" = "No se puede optimizar la tabla";

/* unable to perform the checksum */
"Unable to perform the checksum" = "No se puede realizar el checksum";

/* error removing host message */
"Unable to remove host" = "No se puede eliminar el host";

/* unable to rename database message */
"Unable to rename database" = "No se puede renombrar la base de datos";

/* unable to repair selected items message */
"Unable to repair selected items" = "No se puede reparar los ítems seleccionados";

/* unable to repair table message */
"Unable to repair table" = "No se puede reparar la tabla";

/* message of panel when connection to db failed after selecting from popupbutton */
"Unable to select database %@.\nPlease check you have the necessary privileges to view the database, and that the database still exists." = "No se puede seleccionar la base de datos %@.\nPor favor, compruebe que dispone de los privilegios necesarios para ver la base de datos y que la base de datos todavía existe.";

/* Unable to write row error */
"Unable to write row" = "No se puede escribir la fila";

/* Table Content : Remove Row : Result : n Error title */
"Unexpected number of rows removed!" = "¡Número inesperado de filas eliminadas!";

/* warning */
"Unknown file format" = "Formato de archivo desconocido";

/* unsaved changes message */
"Unsaved changes" = "Cambios no guardados";

/* unsaved theme message */
"Unsaved Theme" = "Tema no guardado";

/* Preferences : Network : Custom SSH client : warning dialog title */
"Unsupported configuration!" = "¡Configuración no compatible!";

/* export : import settings : file version error title */
"Unsupported version for export settings!" = "¡Versión no soportada para la configuración de exportación!";

/* Name for an untitled connection */
"Untitled" = "Sin nombre";

/* Title of a new Sequel Ace Document */
"Untitled %ld" = "Sin nombre %ld";

/* range for mediumblob type */
"Up to %@ bytes (16 MiB)" = "Hasta %@ bytes (16 MiB)";

/* range for longblob type */
"Up to %@ bytes (4 GiB)" = "Hasta %@ bytes (4 GiB)";

/* range for mediumtext type */
"Up to %@ characters (16 MiB)" = "Hasta %@ caracteres (16 MiB)";

/* range for enum type */
"Up to %@ distinct members (<%@ in practice)\n1-2 bytes storage" = "Hasta %1$@ miembros distintos (<%2$@ en práctica)\n1-2 bytes de almacenamiento";

/* range for tinyblob type */
"Up to 255 bytes" = "Hasta 255 bytes";

/* range for tinytext type */
"Up to 255 characters" = "Hasta 255 caracteres";

/* Open Files : Bundle : Already-Installed : Update button */
"Update" = "Actualizar";

/* updated: %@ */
"updated: %@" = "actualizada: %@";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field of table `%2$@` was changed." = "Falló al actualizar el contenido del campo. No se pudo identificar el campo de origen con precisión (%1$ld coincidencias). Probablemente este campo de la tabla `%2$@` fue modificado durante la edición.";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field the table `%2$@` was changed by an other user." = "Falló al actualizar el contenido del campo. No se pudo identificar el campo de origen de forma precisa (%1$ld coincidencias). Probablemente este campo de la tabla `%2$@` fue modificado por otro usuario mientras se editaba.";

/* updating field task description */
"Updating field data..." = "Actualizando datos de campo...";

/* URL scheme command couldn't authenticated */
"URL scheme command couldn't authenticated" = "El comando de esquema de URL no pudo autenticarse";

/* URL scheme command was terminated by user */
"URL scheme command was terminated by user" = "El comando de esquema URL fue finalizado por el usuario";

/* URL scheme command %@ unsupported */
"URL scheme command %@ unsupported" = "Comando de esquema URL “%@” no soportado";

/* Use 127.0.0.1 button */
"Use 127.0.0.1" = "Usar 127.0.0.1";

/* use standard connection button */
"Use Standard Connection" = "Usar conexión estándar";

/* user has no hosts message */
"User has no hosts" = "El usuario no tiene equipos";

/* user-defined value */
"User-defined value" = "Valor definido por el usuario";

/* toolbar item label for switching to the User Manager tab */
"Users" = "Usuarios";

/* CSV Field Mapping : Table View : Tooltip for fields with NULL value */
"Value will be imported as MySQL NULL" = "El valor se importará como MySQL NULL";

/* variable label for color table (Prefs > Editor) */
"Variable" = "Variable";

/* version */
"version" = "versión";

/* export header version label */
"Version" = "Versión";

/* view */
"view" = "vista";

/* Release notes button title */
"View full release notes" = "Ver notas de versión";

/* header for view info pane */
"VIEW INFORMATION" = "INFORMACIÓN DE VISTA";

/* view html source code menu item title */
"View Source" = "Ver código fuente";

/* view structure print heading */
"View Structure" = "Estructura de vista";

/* views */
"views" = "vistas";

/* warning */
"Warning" = "Advertencia";

/* Short important release note for why password prompts may occur */
"We've changed Sequel Ace's digital signature for GateKeeper compatibility; you'll have to allow access to your passwords again." = "Hemos cambiado la firma digital de Sequel Ace para la compatibilidad con GateKeeper; tendrá que permitir de nuevo el acceso a sus contraseñas.";

/* Important release notes informational text, single change */
"We've made a few changes but we thought you should know about one particularly important one:" = "Hemos realizado algunos cambios, pero pensamos que usted debe saber acerca de uno especialmente importante:";

/* Important release notes informational text, multiple changes */
"We've made a few changes but we thought you should know about some particularly important ones:" = "Hemos realizado algunos cambios, pero pensamos que usted debe saber acerca de algunos especialmente importantes:";

/* WHERE clause not valid */
"WHERE clause not valid" = "Cláusula WHERE incorrecta";

/* Title of filter preview area when the query WHERE is negated */
"WHERE NOT query" = "Consulta WHERE NOT";

/* Title of filter preview area when the query WHERE is normal */
"WHERE query" = "Consulta WHERE";

/* Generic working description */
"Working..." = "Trabajando...";

/* export label showing app is writing data */
"Writing data..." = "Escribiendo datos...";

/* text showing that app is writing text file */
"Writing..." = "Escribiendo...";

/* wrong data format or password */
"Wrong data format or password." = "Formato de datos o contraseña incorrecta.";

/* wrong data format */
"Wrong data format." = "Formato de datos incorrecto.";

/* export : import settings : spf content type error title */
"Wrong SPF content type!" = "¡Tipo de contenido SPF incorrecto!";

/* export filename date token */
"Year" = "Año";

/* message of panel when trying to copy multiple rows */
"You can only copy single rows." = "Solo puede copiar filas individuales.";

/* message of panel when trying to edit tables without index and with hidden blob/text fields */
"You can't hide blob and text fields when working with tables without index." = "No se puede ocultar los campos blob y text al trabajar con tablas sin índices.";

/* You cannot delete the last field in a table. Delete the table instead. */
"You cannot delete the last field in a table. Delete the table instead." = "No puede eliminar el último campo de la tabla. Elimine la tabla en su lugar.";

/* SSL connection requested but not established error detail */
"You requested that the connection should be established using SSL, but MySQL made the connection without SSL.\n\nThis may be because the server does not support SSL connections, or has SSL disabled; or insufficient details were supplied to establish an SSL connection.\n\nThis connection is not encrypted." = "Ha solicitado que la conexión debe establecerse usando SSL, pero MySQL realiza la conexión sin SSL.\n\nEsto puede deberse a que el servidor no admite conexiones SSL, o SSL está deshabilitado; o fueron proporcionado detalles insuficientes para establecer una conexión SSL.\n\nEsta conexión no está cifrada.";

/* Query Favorites : List : Section Heading : current connection document : tooltip (arg is the name of the spf file) */
"‘%@’ based favorites" = "favoritos basados en ‘%@’";

/* table column header. Read: 'Showing all content filters for fields of type %@' (ContentFilterManager) */
"‘%@’ Fields Content Filters" = "‘%@’ Filtros de contenido de campos";

/* Export file already exists message */
"%@ already exists. Do you want to replace it?" = "%@ ya existe. ¿Desea reemplazarlo?";

/* Bundle Editor : Outline View : Bundle item : tooltip */
"%@ Bundle" = "Paquete de %@";

/* Export file creation error title */
"%@ could not be created" = "%@ no pudo ser creado";

/* %@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item. */
"%@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item." = "No se pudo analizar %@. Puede editar la configuración de la columna pero la columna no se mostrará en la vista de contenido; por favor reporte este problema al equipo de Sequel Ace usando el menú Ayuda.";

/* connection view : ssl : client cert file picker : wrong format error title */
"%@ is not a valid client certificate file." = "%@ no es un archivo de certificado de cliente válido.";

/* connection view : ssl : key file picker : wrong format error title */
"%@ is not a valid private key file." = "%@ no es un archivo válido de clave privada.";

/* text shown when there are App Sandbox Issues */
"App Sandbox Issue" = "Problema con App Sandbox";

/* Stale Bookmarks error title */
"Stale Bookmarks" = "Marcadores obsoletos";

/* App Sandbox info link text */
"App Sandbox Info" = "Información de App Sandbox";

/* error while selecting file title */
"File Selection Error" = "Error de selección de archivo";

/* error while selecting file message */
"The selected file is not a valid file.\n\nPlease try again.\n\nClass: %@" = "El archivo seleccionado no es un archivo válido.\n\nPor favor inténtelo de nuevo.\n\nClase: %@";

/* known hosts not writable error message */
"The selected known hosts file is not writable.\n\n%@\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "No se puede escribir en el archivo hosts seleccionado.\n\n%@\n\nPor favor, vuelva a seleccionar el archivo en las preferencias de Sequel Ace e inténtelo de nuevo.";

/* known hosts is invalid message */
"The selected known hosts file is invalid.\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "El archivo hosts seleccionado no es válido.\n\nPor favor, vuelva a seleccionar el archivo en las preferencias de Sequel Ace e inténtelo de nuevo.";

/* known hosts contains quote message */
"The selected known hosts file contains a quote (\") in its file path which is not supported.\n\n%@\n\nPlease select a different file in Sequel Ace's Preferences or rename the file/path to remove the quote." = "El archivo hosts seleccionado contiene comillas (\") en la ruta del archivo, y eso no está soportado.\n\n%@\n\nPor favor, seleccione un archivo diferente en las preferencias de Sequel Ace o renombre el archivo/ruta para eliminar las comillas.";

/* SSH Tunnel Debugging menu and window title */
"SSH Tunnel Debugging Info" = "Información de depuración del túnel SSH";

/* Question to user to see if they would like to re-request access */
"You have stale secure bookmarks:\n\n%@\n\nWould you like to re-request access now?" = "Tiene marcadores seguros antiguos:\n\n%@\n\n¿Desea volver a solicitar acceso ahora?";

/* Question to user to see if they would like to request access to missing bookmarks */
"You have missing secure bookmarks:\n\n%@\n\nWould you like to request access now?" = "Le faltan marcadores seguros:\n\n%@\n\n¿Quiere solicitar acceso ahora?";

/* known hosts advance menu item title */
"Use known hosts from ssh config (ADVANCED)" = "Usar hosts conocidos de la configuración de ssh (AVANZADO)";

/* The answer, yes */
"Yes" = "Sí";

/* Shown to remind users of their stale bookmarks */
"A reminder of your stale secure bookmarks:<br /><br />%@<br />" = "Un recordatorio de sus marcadores seguros antiguos:<br /><br />%@<br />";

/* Title for Stale Secure Bookmarks help window */
"Stale Secure Bookmarks" = "Marcadores seguros antiguos";

/* Title for Export Error alert */
"Export Error" = "Error al exportar";

/* Message for Export Error alert */
"Error while writing to the export file. Could not open file: %@" = "Error al escribir el archivo de exportación. No se pudo abrir el archivo: %@";

/* Message for User window error */
"Resultset from mysql.user contains neither 'Password' nor 'authentication_string' column." = "Los resultados no contienen ni la columna 'Password' ni la columna 'authentication_string'.";

/* Title for User window error */
"User Data Error" = "Error de datos de usuario";

/* Title for Stale Bookmark file selection dialog */
"Please re-select the file '%@' in order to restore Sequel Ace's access." = "Por favor, vuelva a seleccionar el archivo '%@' para restaurar el acceso de Sequel Ace.";

/* Title for File Preference file selection dialog */
"Please choose a file or folder to grant Sequel Ace access to." = "Por favor, elija un archivo o una carpeta a la que conceder acceso a Sequel Ace.";

/* Title for Network Preference ssh config file selection dialog */
"Please choose your ssh config files(s)" = "Por favor, seleccione su(s) archivo(s) de configuración ssh";

/* Title for Network Preference ssh known hosts file selection dialog */
"Please choose your known hosts file" = "Por favor, seleccione su archivo de hosts conocidos";

/* Message for field editor JSON segment when JSON is invalid */
"Invalid JSON" = "JSON inválido";

/* Applying syntax highlighting task description */
"Applying syntax highlighting..." = "Aplicando resaltado de sintaxis...";

/* Error message when a bash task fails to start */
"Couldn't launch task.\nException reason: %@\n ENV length: %lu" = "No se pudo iniciar la tarea.\nExcepción: %@\n Longitud ENV: %lu";

/* Error message title when we failed to create new connection view */
"New Connection Error" = "Error en la nueva conexión";

/* New Connection Error informative message */
"Failed to create new database connection window. Please restart Sequel Ace and try again." = "Error al crear una ventana nueva de conexión a la base de datos. Por favor, reinicie Sequel Ace e inténtelo de nuevo.";

/* new version is available alert title */
"A new version is available" = "Una nueva versión está disponible";

/* new version is available download button title */
"Download" = "Descargar";

/* new version is available alert informativeText */
"Version %@ is available. You are currently running %@" = "Versión %@ está disponible. Actualmente está ejecutando %@";

/* downloading new version window title */
"Download Progress" = "Progreso de descarga";

/* downloading new version time remaining placeholder text */
"Calculating time remaining..." = "Calculando el tiempo restante...";

/* downloading new version window message */
"Downloading Sequel Ace - %@" = "Descargando Sequel Ace - %@";

/* downloading new version time remaining estimate text */
"About %.1f seconds left" = "Quedan unos %.1f segundos";

/* downloading new version failure alert title */
"Download Failed" = "Descarga fallida";

/* Tool tip for the Show alert when update is available preference on the Notifications pane */
"Only available for GitHub downloads" = "Sólo disponible para descargas de GitHub";

/* Tool tip warning for when the user sets auto-complete delay to zero */
"WARNING: Setting the auto-complete delay to 0.0 can result in strange output." = "ADVERTENCIA: Establecer el retraso de autocompletado a 0.0 puede resultar en una salida extraña.";

/* downloading new version bytes of bytes done */
"%@ of %@" = "%@ de %@";

/* Time zone set failed informative message */
"\n\ntime_zone will be set to SYSTEM." = "\n\ntime_zone se establecerá en SYSTEM.";

/* Menu item title for checking for updates */
"Check for Updates..." = "Buscar actualizaciones...";

/* Warning message during connection in case the variable skip-show-database is set to ON */
"The skip-show-database variable of the database server is set to ON. Thus, you won't be able to list databases unless you have the SHOW DATABASES privilege.\n\nHowever, the databases are still accessible directly through SQL queries depending on your privileges." = "La variable skip-show-database del servidor de base de datos está establecida en ON. Por lo tanto, no podrá listar bases de datos a menos que tenga el privilegio de SHOW DATABASES .\n\nSin embargo, las bases de datos todavía son accesibles directamente a través de consultas SQL dependiendo de sus privilegios.";

/* Error alert title when the request to GitHub fails */
"GitHub Request Failed" = "Falló la solicitud de GitHub";

/* Info alert title when there are Newer Releases Available */
"No Newer Release Available" = "No hay una nueva versión disponible";

/* Info alert message when there are Newer Releases Available */
"You are currently running the latest release." = "Actualmente está ejecutando la última versión.";

/* Alert button, disable the warning when skip-show-database is set to on */
"Never show this again" = "Nunca volver a mostrar esto";

/* Info alert when editing a generated column */
"The current field \"%@\" is a generated column and therefore cannot be edited." = "El campo actual \"%@\" es una columna generada y por lo tanto no puede ser editada.";

/* Release note / info modal about change in latest release about column behavior */
"The use of the \"default\" column has changed since the last version of Sequel ACE:\n\n- No default value : Leave it blank.\n- String value : Use single '' or double quotes \"\" if you want an empty string or to wrap a string\n- Expression : Use parentheses (). Except for the TIMESTAMP and DATETIME columns where you can specify the CURRENT_TIMESTAMP function without enclosing parentheses." = "El uso de la columna \"por defecto\" ha cambiado desde la última versión de Sequel ACE:\n\n- Sin valor predeterminado: Déjalo en blanco.\n- Valor de cadena: Use comillas simples o dobles \"\" si desea una cadena vacía o envolver una cadena\n- Expresión : Use paréntesis (). Excepto para las columnas TIMESTAMP y DATETIME donde se puede especificar la función CURRENT_TIMESTAMP sin incluir paréntesis.";

/* Menu option to pin a view to the top of the list of tables */
"Pin View" = "Fijar vista";

/* Menu option to pin a table to the top of the list of tables */
"Pin Table" = "Fijar tabla";

/* Menu option to pin a procedure to the top of the list of tables */
"Pin Procedure" = "Fijar procedimiento";

/* Menu option to pin a function to the top of the list of tables */
"Pin Function" = "Fijar función";

/* Menu option to unpin a view which is currently pinned at the top of the list of tables */
"Unpin View" = "Desanclar vista";

/* Menu option to unpin a table which is currently pinned at the top of the list of tables */
"Unpin Table" = "Desanclar tabla";

/* Menu option to unpin a procedure which is currently pinned at the top of the list of tables */
"Unpin Procedure" = "Desanclar procedimiento";

/* Menu option to unpin a function which is currently pinned at the top of the list of tables */
"Unpin Function" = "Desanclar función";

/* header for pinned table list */
"PINNED" = "FIJADO";

/* Menu option to copy a table's name with database name in the list of tables */
"Copy Table Name" = "Copiar nombre de tabla";

/* sequelace URL scheme error: LaunchFavorite alert title (favorite name not found) */
"LaunchFavorite URL Scheme Error" = "Error de Esquema de URL de LaunchFavorite";

/* sequelace URL scheme error: LaunchFavorite alert description (favorite name not found) */
"The variable in the ?name= query parameter could not be matched with any of your favorites." = "La variable en el parámetro de consulta ?name= no coincide con ninguno de sus favoritos.";
