/* Table Content : Remove Row : Result : Too Few : Part 3 : Row not deleted when using primary key for DELETE statement. */
" Check the Console for possible errors inside the primary key(s) of this table!" = " このテーブルのプライマリキー内のエラーがないかコンソールで確認してください";

/* Table Content : Remove Row : Result : Too Many : Part 2 : Generic text */
" Please check the Console and inform the Sequel Ace team!" = " コンソールを確認し、Sequel Aceチームに連絡してください";

/* Table Content : Remove Row : Result : Too Few : Part 2 : Generic help message */
" Reload the table to be sure that the contents have not changed in the meantime." = " 内容が変更されていないことを確認するために、テーブルを再読み込みします。";

/* Table Content : Remove Row : Result : Too Few : Part 3 : no primary key in table generic message */
" You should also add a primary key to this table!" = " このテーブルにはプライマリキーも追加してください!";

/* Query snippet shell command syntax and placeholder */
"$(shell_command)" = "$(shell_command)";

/* text showing how many rows are selected */
"%@ %@ selected" = "%1$@ %2$@が選択されました。";

/* History item filtered by values label */
"%@ (Filtered by %@)" = "%1$@ ( %2$@ でフィルター)";

/* History item with page number label */
"%@ (Page %lu)" = "%1$@ (ページ %2$lu)";

/* Content Filter Manager : Initial name of copied filter
 Initial favourite name after duplicating a previous favourite */
"%@ Copy" = "コピー%@";

/* text showing a single row a partially loaded result */
"%@ row in partial load" = "%@ 行が部分ロードされています";

/* text showing a single row in the result */
"%@ row in table" = "テーブルの%@行";

/* text showing how a single rows matched filter */
"%@ row of %@%@ matches filter" = "%1$@ の %2$@行%3$@ フィルタに一致する";

/* text showing how many rows are in a partially loaded result */
"%@ rows in partial load" = "%@ 行が部分ロードされています";

/* text showing how many rows are in the result */
"%@ rows in table" = "テーブルの%@行";

/* text showing how many rows matched filter */
"%@ rows of %@%@ match filter" = "%1$@ 行の %2$@%3$@ 一致するフィルター";

/* text showing how many rows have been affected by a single query */
"%@; %ld rows affected" = "%1$@; %2$ld 行に影響";

/* text showing how many rows have been affected by multiple queries */
"%@; %ld rows affected in total, by %ld queries taking %@" = "%1$@; %2$ldが複数のクエリによって影響を受けた行数、%3$ldは実行されたクエリの数、%4$@は全クエリの実行に要した時間になります";

/* text showing one row has been affected by a single query */
"%@; 1 row affected" = "%@; 1 行に影響";

/* text showing one row has been affected by multiple queries */
"%@; 1 row affected in total, by %ld queries taking %@" = "%1$@; 合計 1 行に影響、%2$ld クエリで %3$@ 経過";

/* text showing a query was cancelled */
"%@; Cancelled after %@" = "%1$@; %2$@ の後にキャンセルされました";

/* text showing multiple queries were cancelled */
"%@; Cancelled in query %ld, after %@" = "%1$@; クエリ %2$ldの後に %3$@ をキャンセルしました";

/* Error display text, showing original MySQL error */
"%@\n\nMySQL said: %@" = "%1$@\n\nMySQLの応答: %2$@";

/* favorite singular label (%d == 1) */
"%lu favorite" = "%lu お気に入り";

/* favorites plural label (%d != 1) */
"%lu favorites" = "%lu お気に入り";

/* favorite group singular label (%d == 1) */
"%lu group" = "%lu グループ";

/* favorite groups plural label (%d != 1) */
"%lu groups" = "%lu グループ";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+y (y!=1) rows instead of n selected were deleted. */
"%ld additional rows were removed!" = "%ld 行追加が削除されました！";

/* Label showing the index of the selected CSV row */
"%ld of %lu record(s)" = "%1$ld / %2$lu レコード";

/* Label showing the index of the selected CSV row (csv partially loaded) */
"%ld of first %lu record(s)" = "%1$ld中の最初の%2$lu レコード ";

/* Table Content : Remove Row : Result : Too Few : Part 1 : n-x (x!=1) of n selected rows were deleted. */
"%ld rows were not removed." = "%ld 行は削除されませんでした。";

/* Export file already exists message */
"%lu files already exist. Do you want to replace them?" = "%luファイルは既に存在しています。上書きしますか？";

/* Export files creation error title */
"%lu files could not be created" = "%luファィルは作成できませんでした。";

/* Some export files already exist explanatory text */
"%lu files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "同じ名前の%lu ファイルが既にターゲットフォルダに存在します。置き換えると現在の内容が上書きされます。";

/* filtered item count */
"%lu of %lu" = "%1$lu / %2$lu";

/* Export folder not writable for some files explanatory text */
"%lu of the export files could not be created because their target export folder is not writable; please select a new export location and try again." = "ターゲットのエクスポートフォルダが書き込み可能でないため、エクスポートファイルの %lu を作成できませんでした。 新しいエクスポート場所を選択してもう一度お試しください。";

/* Export folder missing for some files explanatory text */
"%lu of the export files could not be created because their target export folder no longer exists; please select a new export location and try again." = "ターゲットのエクスポートフォルダが書き込み可能でないため、エクスポートファイルの %lu を作成できませんでした。 新しいエクスポート場所を選択してもう一度お試しください。";

/* History item title with nothing selected */
"(no selection)" = "(選択されていません)";

/* value shown for hidden blob and text fields */
"(not loaded)" = "(ロードされていません)";

/* Explanation for MySQL server has gone away error */
"(This usually indicates that the connection has been closed by the server after inactivity, but can also occur due to other conditions.  The connection has been restored; please try again if the query is safe to re-run.)" = "(これは通常、接続が非アクティブの後にサーバーによって閉じられていることを示しますが、他の条件によっても発生する可能性があります。 接続が復元されました。クエリが安全な場合は、もう一度やり直してください。)";

/* Custom Query : text appended to the “x row(s) affected” messages. $1 is a time interval */
", first row available after %1$@" = "、%1$@ 経過後に先頭行が利用可能";

/* Custom Query : text appended to the “x row(s) affected” messages (for update/delete queries). $1 is a time interval */
", taking %1$@" = "、 %1$@ 経過";

/* warning shown in the console when no rows have been affected after writing to the db */
"/* WARNING: No rows have been affected */\n" = "/* WARNING: No rows have been affected */\n";

/* error text when multiple custom query failed */
"[ERROR in query %ld] %@\n" = "[クエリ %1$ldのエラー] %2$@\n";

/* error text when reading of csv file gave errors */
"[ERROR in row %ld] %@\n" = "[クエリ %1$ldのエラー] %2$@\n";

/* [multiple selection] */
"[multiple selection]" = "[複数選択]";

/* displayed when new content filter has empty Name field (ContentFilterManager) */
"[name required]" = "[名前が必要です]";

/* [no selection] */
"[no selection]" = "[選択されていません]";

/* Additional export file errors */
"\n\n(In addition, one or more errors occurred while attempting to create the export files: %lu could not be created. These files will be ignored.)" = "\n\n(さらに、エクスポートファイルの作成中に1つ以上のエラーが発生しました: %lu を作成できませんでした。 これらのファイルは無視されます。";

/* \n\npress shift for binary search tooltip message */
"\n\nPress ⇧ for binary search (case-sensitive)." = "\n\n バイナリー検索のために⇧ を押します。";

/* description of bit */
"A bit-field type. M specifies the number of bits. If shorter values are inserted, they will be aligned on the least significant bit. See the SET type if you want to explicitly name each bit." = "ビットフィールドタイプ。M はビット数を指定します。より短い値が挿入された場合は、最も重要なビット上に位置合わせされます。 各ビットに明示的に名前を付けたい場合は、SET 型を参照してください。";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog message */
"A Bundle ‘%@’ is already installed. Do you want to update it?" = "バンドル数%@は既にインストールされています。更新しますか？";

/* description of binary */
"A byte array with fixed length. Shorter values will always be padded to the right with 0x00 until they fit M." = "長さが固定されたバイト配列。より短い値は常にMに合うまで0x00で右側にパディングされます。";

/* description of varbinary */
"A byte array with variable length. The actual number of bytes is further limited by the values of other fields in the row." = "可変長のバイト配列。実際のバイト数は、行の他のフィールドの値によってさらに制限されます。";

/* description of blob
 description of longblob
 description of mediumblob
 description of tinyblob */
"A byte array with variable length. Unlike VARBINARY this type does not count towards the maximum row length." = "可変長のバイト配列。VARBINARYとは異なり、この型は行の最大長にはカウントされません。";

/* description of tinytext */
"A character string that can store up to 255 bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "255バイトまで保存できますが、短い値の場合はスペースが少なくなります。 実際の文字数は、使用されるエンコーディングによってさらに制限されます。VARCHAR とは異なり、このタイプは行の最大長にはカウントされません。";

/* description of varchar */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding and the values of other fields in the row." = "Mバイトまで保存できますが、短い値の場合はスペースが少なくなります。 実際の文字数は、使用されるエンコードと行の他のフィールドの値によってさらに制限されます。";

/* description of text */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "Mバイトまで保存できますが、短い値の場合はスペースが少なくなります。 実際の文字数は、使用されるエンコーディングによってさらに制限されます。VARCHAR とは異なり、このタイプは行の最大長にはカウントされません。";

/* description of char */
"A character string that will require M×w bytes per row, independent of the actual content length. w is the maximum number of bytes a single character can occupy in the given encoding." = "実際のコンテンツの長さに関係なく、行ごとにM×wバイトを必要とする文字列。 wは、与えられたエンコーディングで、単一の文字が使用できる最大バイト数です。";

/* description of longtext
 description of mediumtext */
"A character string with variable length. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "可変長の文字列。実際の文字数は、使用されるエンコードによってさらに制限されます。 VARCHAR とは異なり、このタイプは行の最大長にはカウントされません。";

/* description of json */
"A data type that validates JSON data on INSERT and internally stores it in a binary format that is both, more compact and faster to access than textual JSON.\nAvailable from MySQL 5.7.8." = "INSERT 上の JSON データを検証し、内部的に両方のバイナリ形式で保存するデータ型。 テキスト形式の JSON よりもコンパクトで高速にアクセスできます。\nMySQL 5.7.8から利用できます。";

/* Export file already exists explanatory text */
"A file with the same name already exists in the target folder. Replacing it will overwrite its current contents." = "同じ名前のファイルが既にターゲットフォルダに存在します。置き換えると、現在の内容が上書きされます。";

/* description of decimal */
"A fixed-point, exact decimal value. M is the maxium number of digits, of which D may be after the decimal point. When rounding, 0-4 is always rounded down, 5-9 up (“round towards nearest”)." = "固定小数点の正確な小数値。Mは数字の最大数で、Dは小数点の後になる可能性があります。 丸めるとき、0-4は常に切り下げられ、5-9は上に切り上げられます（「最も近い方に向かってラウンド」）。";

/* table structure : indexes : delete index : error 1553 : title
 table structure : indexes : delete index : error 1553, no FK found : title */
"A foreign key needs this index" = "外部キーにはこのインデックスが必要です";

/* description of set */
"A SET can define up to 64 members (as strings) of which a field can use one or more using a comma-separated list. Upon insertion the order of members is automatically normalized and duplicate members will be eliminated. Assignment of numbers is supported using the same semantics as for BIT types." = "SETは、最大64人のメンバー(文字列として) を定義することができ、そのうちの1つまたは複数のフィールドをカンマ区切りのリストを使用することができます。 挿入すると、メンバーの順序が自動的に正規化され、重複したメンバーは削除されます。 数値の割り当ては、BITタイプと同じセマンティクスを使用してサポートされています。";

/* SSH key not found message */
"A SSH key location was specified, but no file was found in the specified location.  Please re-select the key and try again." = "SSHキーの場所が指定されましたが、指定された場所にファイルが見つかりませんでした。キーを再度選択してやり直してください。";

/* SSL CA certificate file not found message */
"A SSL Certificate Authority certificate location was specified, but no file was found in the specified location.  Please re-select the Certificate Authority certificate and try again." = "SSL 証明書の場所が指定されましたが、指定された場所にファイルが見つかりませんでした。証明書を再度選択して、もう一度やり直してください。";

/* SSL certificate file not found message */
"A SSL certificate location was specified, but no file was found in the specified location.  Please re-select the certificate and try again." = "SSL 証明書の場所が指定されましたが、指定された場所にファイルが見つかりませんでした。証明書を再度選択して、もう一度やり直してください。";

/* SSL key file not found message */
"A SSL key file location was specified, but no file was found in the specified location.  Please re-select the key file and try again." = "SSL キーファイルの場所が指定されましたが、指定された場所にファイルが見つかりませんでした。キーファイルを再度選択して、もう一度やり直してください。";

/* duplicate host informative message */
"A user with the host '%@' already exists" = "ホスト '%@' を持つユーザーは既に存在します";

/* duplicate user informative message */
"A user with the name '%@' already exists" = "名前 '%@' のユーザーは既に存在します";

/* table content : editing : error message description when parsing as hex string failed */
"A valid hex string may only contain the numbers 0-9 and letters A-F (a-f). It can optionally begin with „0x“ and spaces will be ignored.\nAlternatively the syntax X'val' is supported, too." = "有効な 16 進文字列は、0-9 と文字 A-F (a-f) のみを含めることができます。 必要に応じて、「0x」から始めることができ、スペースは無視されます。\nX'valの構文もサポートされています。";

/* connection failed due to access denied title */
"Access denied!" = "アクセスが拒否されました";

/* range of double */
"Accurate to approx. 15 decimal places" = "小数点以下15桁まで正確に";

/* range of float */
"Accurate to approx. 7 decimal places" = "小数点以下7桁まで正確に";

/* active connection window is busy. please wait and try again. tooltip */
"Active connection window is busy. Please wait and try again." = "アクティブな接続ウィンドウがビジーです。しばらくしてからもう一度お試しください。";

/* header for activities pane */
"ACTIVITIES" = "アクティビティ";

/* Add trigger button label */
"Add" = "追加";

/* menu item to add db */
"Add Database..." = "データベースを追加…";

/* Add Host */
"Add Host" = "ホストを追加";

/* add global value or expression menu item */
"Add Value or Expression…" = "値または式を追加…";

/* adding index task status message */
"Adding index..." = "インデックスを追加しています...";

/* Advanced options short title */
"Advanced" = "高度な設定";

/* notifications preference pane name */
"Alerts & Logs" = "アラート＆ログ";

/* notifications preference pane tooltip */
"Alerts & Logs Preferences" = "アラートとログ設定";

/* All databases placeholder */
"All Databases" = "すべてのデータベース";

/* All databases (%) placeholder */
"All Databases (%)" = "すべてのデータベース (%)";

/* All export files already exist message */
"All the export files already exist. Do you want to replace them?" = "すべてのエクスポートファイルは既に存在します。置き換えますか？";

/* An error for sequelace URL scheme command occurred. Probably no corresponding connection window found. */
"An error for sequelace URL scheme command occurred. Probably no corresponding connection window found." = "シーケンスURLスキームコマンドのエラーが発生しました。おそらく対応する接続ウィンドウが見つかりません。";

/* no connection available informatie message */
"An error has occurred and there doesn't seem to be a connection available." = "エラーが発生しました。接続できないようです。";

/* an error occur while executing a scheme command. if the scheme command was invoked by a bundle command, it could be that the command still runs. you can try to terminate it by pressing ⌘+. or via the activities pane. */
"An error occur while executing a scheme command. If the scheme command was invoked by a Bundle command, it could be that the command still runs. You can try to terminate it by pressing ⌘+. or via the Activities pane." = "schemeコマンドの実行中にエラーが発生します。 schemeコマンドがBundleコマンドによって呼び出された場合、そのコマンドがまだ実行される可能性があります。 ⌘+キーを押し、またはアクティビティペインを介して終了しようとすることができます。";

/* error killing query informative message */
"An error occurred while attempting to kill connection %lld.\n\nMySQL said: %@" = "接続 %1$lldのキル中にエラーが発生しました。\n\nMySQL は次のとおりです: %2$@";

/* error killing query informative message */
"An error occurred while attempting to kill the query associated with connection %lld.\n\nMySQL said: %@" = "接続 %1$lldに関連付けられたクエリのキル中にエラーが発生しました。\n\nMySQL は次のとおりです: %2$@";

/* Error shown when unable to show create table syntax */
"An error occurred while creating table syntax.\n\n: %@" = "テーブル構文の作成中にエラーが発生しました。\n\n: %@";

/* rename table error - no temporary name found */
"An error occurred while renaming '%@'. No temporary name could be found. Please try renaming to something else first." = "'%@'の名前を変更中にエラーが発生しました。一時的な名前が見つかりませんでした。最初に別の名前に変更してみてください。";

/* rename table error informative message */
"An error occurred while renaming '%@'.\n\nMySQL said: %@" = "'%1$@'の名前を変更中にエラーが発生しました。\n\nMySQLは次のとおりです: %2$@";

/* rename error - don't know what type the renamed thing is */
"An error occurred while renaming. '%@' is of an unknown type." = "名前の変更中にエラーが発生しました。'%@' は不明なタイプです。";

/* rename precedure/function error - can't delete old procedure */
"An error occurred while renaming. I couldn't delete '%@'.\n\nMySQL said: %@" = "名前の変更中にエラーが発生しました。'%1$@'を削除できませんでした。\n\nMySQLは次のとおりです: %2$@";

/* rename precedure/function error - can't recreate procedure */
"An error occurred while renaming. I couldn't recreate '%@'.\n\nMySQL said: %@" = "名前の変更中にエラーが発生しました。'%1$@'を再作成できませんでした。\n\nMySQLは次のとおりです: %2$@";

/* rename precedure/function error - can't retrieve syntax */
"An error occurred while renaming. I couldn't retrieve the syntax for '%@'.\n\nMySQL said: %@" = "名前の変更中にエラーが発生しました。'%1$@'の構文を取得できませんでした。\n\nMySQLは次のとおりです: %2$@";

/* rename error - invalid create syntax */
"An error occurred while renaming. The CREATE syntax of '%@' could not be parsed." = "名前の変更中にエラーが発生しました。'%@' の作成構文を解析できませんでした。";

/* message of panel when retrieving view information failed */
"An error occurred while retrieving status data.\n\nMySQL said: %@" = "ステータスデータの取得中にエラーが発生しました。\n\nMySQL は %@";

/* message of panel when create syntax cannot be retrieved */
"An error occurred while retrieving the create syntax for '%@'.\nMySQL said: %@" = "'%1$@'の構文の取得中にエラーが発生しました。\nMySQLは次のとおりです: %2$@";

/* add index error informative message */
"An error occurred while trying to add the index.\n\nMySQL said: %@" = "インデックスの追加中にエラーが発生しました。\n\nMySQL は %@ です。";

/* error adding password to keychain informative message */
"An error occurred while trying to add the password to your Keychain. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "キーチェーンにパスワードを追加中にエラーが発生しました。 キーチェーンを修復すると問題が解決するかもしれませんが、それが解決しない場合は、エラーコード %iを提供して、シーケンスエースチームに報告してください。";

/* unable to copy database message informative message */
"An error occurred while trying to copy the database '%@' to '%@'." = "データベース '%1$@' を '%2$@ ' にコピー中にエラーが発生しました。";

/* error deleting index informative message */
"An error occurred while trying to delete the index.\n\nMySQL said: %@" = "インデックスの削除中にエラーが発生しました。\n\nMySQL は %@ です。";

/* table status : row count query failed : error message */
"An error occurred while trying to determine the number of rows for %@.\nMySQL said: %@ (%lu)" = "\"%1$@\" の行数を決定しようとしてエラーが発生しました。\nMySQL は %2$@ (%3$lu)";

/* unable to rename database message informative message */
"An error occurred while trying to rename the database '%@' to '%@'." = "データベース '%1$@' を '%2$@ ' にリネーム中にエラーが発生しました。";

/* error finding keychain item to edit informative message */
"An error occurred while trying to retrieve the Keychain item you're trying to edit. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "編集しようとしているキーチェーンアイテムの取得中にエラーが発生しました。 キーチェーンを修復すると問題が解決するかもしれませんが、それが解決しない場合は、エラーコード %iを提供して、シーケンスエースチームに報告してください。";

/* error updating keychain item informative message */
"An error occurred while trying to update the Keychain item. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "キーチェーン項目を更新中にエラーが発生しました。 キーチェーンを修復すると問題が解決するかもしれませんが、それが解決しない場合は、エラーコード %iを提供して、シーケンスエースチームに報告してください。";

/* mysql error occurred message */
"An error occurred" = "エラーが発生しました";

/* MySQL table info retrieval error message */
"An error occurred retrieving table information.  MySQL said: %@" = "テーブル情報の取得中にエラーが発生しました。MySQLは次のとおりです: %@";

/* SQL encoding read error */
"An error occurred when reading the file, as it could not be read in the encoding you selected (%@).\n\nOnly %ld queries were executed." = "選択したエンコーディング（%1$@）でファイルを読み取ることができなかったため、ファイルの読み取り中にエラーが発生しました。\n\n%2$ldクエリのみが実行されました。";

/* CSV encoding read error */
"An error occurred when reading the file, as it could not be read using the encoding you selected (%@).\n\nOnly %ld rows were imported." = "選択したエンコーディング（%1$@）を使用してファイルを読み取ることができなかったため、ファイルの読み取り中にエラーが発生しました。\n\n%2$ld行のみがインポートされました。";

/* SQL read error, including detail from system */
"An error occurred when reading the file.\n\nOnly %ld queries were executed.\n\n(%@)" = "ファイルの読み込み中にエラーが発生しました。\n\n %1$ld クエリのみが実行されました。\n\n(%2$@)";

/* CSV read error, including detail string from system */
"An error occurred when reading the file.\n\nOnly %ld rows were imported.\n\n(%@)" = "ファイルの読み込み中にエラーが発生しました。\n\n %1$ld 行のみインポートされました。\n\n(%2$@)";

/* error adding field informative message */
"An error occurred when trying to add the field '%@' via\n\n%@\n\nMySQL said: %@" = "フィールド '%1$@'を\n\n%2$@ \n\nMySQL経由で追加しようとしたときにエラーが発生しました：%3$@";

/* error changing field informative message */
"An error occurred when trying to change the field '%@' via\n\n%@\n\nMySQL said: %@" = "フィールド '%1$@'を\n\n%2$@ \n\nMySQL経由で変更しようとしたときにエラーが発生しました：%3$@";

/* error changing table collation informative message */
"An error occurred when trying to change the table collation to '%@'.\n\nMySQL said: %@" = "テーブルの照合順序を '%1$@' に変更しようとしたときにエラーが発生しました。\n\nMySQL は次のとおりです: %2$@";

/* error changing table encoding informative message */
"An error occurred when trying to change the table encoding to '%@'.\n\nMySQL said: %@" = "テーブルエンコーディングを '%1$@' に変更しようとしたときにエラーが発生しました。\n\nMySQLは次のとおりです: %2$@";

/* error changing table type informative message */
"An error occurred when trying to change the table type to '%@'.\n\nMySQL said: %@" = "テーブル型を '%1$@' に変更しようとしたときにエラーが発生しました。\n\nMySQL は次のとおりです: %2$@";

/* error changing table comment informative message */
"An error occurred when trying to change the table's comment to '%@'.\n\nMySQL said: %@" = "テーブルのコメントを '%1$@' に変更しようとしたときにエラーが発生しました。\n\nMySQL は次のとおりです: %2$@";

/* an error occurred while analyzing the %@.\n\nMySQL said:%@ */
"An error occurred while analyzing the %@.\n\nMySQL said:%@" = "%1$@の解析中にエラーが発生しました。\n\nMySQLは次のとおりです:%2$@";

/* an error occurred while fetching the optimized field type.\n\nMySQL said:%@ */
"An error occurred while fetching the optimized field type.\n\nMySQL said:%@" = "最適化されたフィールドタイプの取得中にエラーが発生しました。\n\nMySQL は%@ です。";

/* an error occurred while trying to flush the %@.\n\nMySQL said:%@ */
"An error occurred while flushing the %@.\n\nMySQL said:%@" = "%1$@のフラッシュ中にエラーが発生しました。\n\nMySQL は%2$@";

/* sql import error message */
"An error occurred while importing SQL" = "SQLのインポート中にエラーが発生しました";

/* an error occurred while trying to optimze the %@.\n\nMySQL said:%@ */
"An error occurred while optimzing the %@.\n\nMySQL said:%@" = "%1$@の最適化中にエラーが発生しました。\n\nMySQL は次のとおりです:%2$@";

/* an error occurred while performing the checksum on the %@.\n\nMySQL said:%@ */
"An error occurred while performing the checksum on %@.\n\nMySQL said:%@" = "%1$@のチェックサム中にエラーが発生しました。\n\nMySQL は次のとおりです:%2$@";

/* an error occurred while trying to repair the %@.\n\nMySQL said:%@ */
"An error occurred while repairing the %@.\n\nMySQL said:%@" = "%1$@の修復中にエラーが発生しました。\n\nMySQL は%2$@";

/* message of panel when retrieving information failed */
"An error occurred while retrieving information.\nMySQL said: %@" = "情報の取得中にエラーが発生しました。\nMySQLは次のとおりです: %@";

/* error retrieving table information informative message */
"An error occurred while retrieving the information for table '%@'. Please try again.\n\nMySQL said: %@" = "テーブル '%1$@' のトリガー情報を取得中にエラーが発生しました。もう一度やり直してください。\n\nMySQL: %2$@";

/* error retrieving table information informative message */
"An error occurred while retrieving the trigger information for table '%@'. Please try again.\n\nMySQL said: %@" = "テーブル '%1$@' のトリガー情報を取得中にエラーが発生しました。もう一度やり直してください。\n\nMySQL: %2$@";

/* error adding new column informative message */
"An error occurred while trying to add the new column '%@' by\n\n%@.\n\nMySQL said: %@" = "新しい列「%1$@' 」を\n\n%2$@に追加しようとしてエラーが発生しました。\n\nMySQL: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@' by\n\n%@.\n\nMySQL said: %@" = "新しいテーブル '%1$@' を\n\n%2$@によって追加しようとしてエラーが発生しました。\n\nMySQL: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@'.\n\nMySQL said: %@" = "新しいテーブル「%1$@'」の追加中にエラーが発生しました。\n\nMySQL: %2$@";

/* error while trying to alter table message */
"An error occurred while trying to alter table '%@'.\n\nMySQL said: %@" = "テーブル「%1$@」の変更中にエラーが発生しました。\n\nMySQL: %2$@";

/* an error occurred while trying to check the %@.\n\nMySQL said:%@ */
"An error occurred while trying to check the %@.\n\nMySQL said:%@" = "%1$@の確認中にエラーが発生しました。\n\nMySQL は%2$@";

/* error deleting relation informative message */
"An error occurred while trying to delete the relation '%@'.\n\nMySQL said: %@" = "リレーション「%1$@'」の削除中にエラーが発生しました。\n\nMySQL: %2$@";

/* unable to get list of users informative message */
"An error occurred while trying to get the list of users. Please make sure you have the necessary privileges to perform user management, including access to the mysql.user table." = "ユーザーのリストの取得中にエラーが発生しました。 mysql.userテーブルへのアクセスを含め、ユーザー管理を実行するために必要な権限があることを確認してください。";

/* error importing table informative message */
"An error occurred while trying to import a table via: \n%@\n\n\nMySQL said: %@" = "テーブルのインポート中にエラーが発生しました: \n%1$@\n\n\nMySQL: %2$@";

/* error moving field informative message */
"An error occurred while trying to move the field.\n\nMySQL said: %@" = "フィールドの移動中にエラーが発生しました。\n\nMySQL: %@";

/* error resetting auto_increment informative message */
"An error occurred while trying to reset AUTO_INCREMENT of table '%@'.\n\nMySQL said: %@" = "テーブル'%1$@'のAUTO_INCREMENTをリセット中にエラーが発生しました。\n\nMySQL: %2$@";

/* error truncating table informative message */
"An error occurred while trying to truncate the table '%@'.\n\nMySQL said: %@" = "テーブル「%1$@'」を切り捨てようとしてエラーが発生しました。\n\nMySQL: %2$@";

/* mysql error occurred informative message */
"An error occurred whilst trying to perform the operation.\n\nMySQL said: %@" = "操作の実行中にエラーが発生しました。\n\nMySQL : %@ ";

/* mysql resource limits unsupported message */
"Resource Limits are not supported for your version of MySQL. Any Resouce Limits you specified have been discarded and not saved. MySQL said: %@" = "お使いのバージョンのMySQLではリソース制限はサポートされていません。指定したリソース制限は破棄され保存されていません。MySQLは次のとおりです: %@";

/* Export files creation error explanatory text */
"An unhandled error occurred when attempting to create %lu of the export files.  Please check the details and try again." = "エクスポートファイルの %lu を作成しようとしたときに処理されないエラーが発生しました。詳細を確認して、もう一度やり直してください。";

/* All export files creation error explanatory text */
"An unhandled error occurred when attempting to create each of the export files.  Please check the details and try again." = "各エクスポートファイルの作成時に処理されていないエラーが発生しました。詳細を確認して、もう一度やり直してください。";

/* Export file creation error explanatory text */
"An unhandled error occurred when attempting to create the export file.  Please check the details and try again." = "エクスポートファイルの作成時に処理されないエラーが発生しました。詳細を確認して、もう一度やり直してください。";

/* ANALYZE one or more tables - result title */
"Analyze %@" = "%@ を分析";

/* analyze selected items menu item */
"Analyze Selected Items" = "選択した項目を分析する";

/* analyze table menu item */
"Analyze Table" = "テーブルを分析する";

/* analyze table failed message */
"Analyze table failed." = "テーブルの解析に失敗しました。";

/* change table type informative message */
"Are you sure you want to change this table's type to %@?\n\nPlease be aware that changing a table's type has the potential to cause the loss of some or all of its data. This action cannot be undone." = "このテーブルのタイプを%@に変更してもよろしいですか？\n\nテーブルのタイプを変更すると、データの一部またはすべてが失われる可能性があることに注意してください。このアクションは元に戻せません。";

/* clear global history list informative message */
"Are you sure you want to clear the global history list? This action cannot be undone." = "グローバル履歴リストをクリアしてもよろしいですか？この操作は元に戻せません。";

/* clear history list for %@ informative message */
"Are you sure you want to clear the history list for %@? This action cannot be undone." = "%@の履歴リストをクリアしてもよろしいですか？この操作は元に戻せません。";

/* truncate tables informative message */
"Are you sure you want to delete ALL records in the selected tables? This operation cannot be undone." = "選択したテーブル内のすべてのレコードを削除してもよろしいですか？この操作は元に戻せません。";

/* truncate table informative message */
"Are you sure you want to delete ALL records in the table '%@'? This operation cannot be undone." = "テーブル '%@' 内のすべてのレコードを削除してもよろしいですか？この操作は元に戻せません。";

/* delete all rows informative message */
"Are you sure you want to delete all the rows from this table? This action cannot be undone." = "このテーブルからすべての行を削除してもよろしいですか？この操作は元に戻せません。";

/* delete table/view informative message */
"Are you sure you want to delete the %@ '%@'? This operation cannot be undone." = "%1$@ '%2$@を削除してもよろしいですか？この操作は元に戻せません。";

/* delete database informative message */
"Are you sure you want to delete the database '%@'? This operation cannot be undone." = "データベース '%@' を削除してもよろしいですか？この操作は元に戻せません。";

/* delete database informative message */
"Are you sure you want to delete the favorite '%@'? This operation cannot be undone." = "お気に入りの '%@' を削除してもよろしいですか？この操作は元に戻せません。";

/* delete field informative message */
"Are you sure you want to delete the field '%@'? This action cannot be undone." = "フィールド '%@' を削除してもよろしいですか? この操作は元に戻せません。";

/* delete database informative message */
"Are you sure you want to delete the group '%@'? All groups and favorites within this group will also be deleted. This operation cannot be undone." = "グループ '%@' を削除してもよろしいですか？このグループ内のすべてのグループとお気に入りも削除されます。 この操作は元に戻せません。";

/* delete index informative message */
"Are you sure you want to delete the index '%@'? This action cannot be undone." = "インデックス '%@'を削除してもよろしいですか? この操作は元に戻せません。";

/* delete tables/views informative message */
"Are you sure you want to delete the selected %@? This operation cannot be undone." = "選択した %@を削除してもよろしいですか？この操作は元に戻せません。";

/* delete rows informative message */
"Are you sure you want to delete the selected %ld rows from this table? This action cannot be undone." = "選択した %ld 行をこのテーブルから削除してもよろしいですか？この操作は元に戻せません。";

/* delete selected relation informative message */
"Are you sure you want to delete the selected relations? This action cannot be undone." = "選択したリレーションを削除してもよろしいですか？この操作は元に戻せません。";

/* delete selected row informative message */
"Are you sure you want to delete the selected row from this table? This action cannot be undone." = "このテーブルから選択した行を削除してもよろしいですか？この操作は元に戻せません。";

/* delete selected trigger informative message */
"Are you sure you want to delete the selected triggers? This action cannot be undone." = "選択したトリガーを削除してもよろしいですか？この操作は元に戻せません。";

/* kill connection informative message */
"Are you sure you want to kill connection ID %lld?\n\nPlease be aware that continuing to kill this connection may result in data corruption. Please proceed with caution." = "本当に接続 ID %lldを終了しますか？ format@@0\n\nこの接続を停止し続けるとデータが破損する可能性があることにご注意ください。 慎重に進んでください。";

/* kill query informative message */
"Are you sure you want to kill the current query executing on connection ID %lld?\n\nPlease be aware that continuing to kill this query may result in data corruption. Please proceed with caution." = "接続 ID %lldで実行中の現在のクエリを本当に終了しますか？ format@@0\n\nこの問い合わせを続けるとデータが破損する可能性があることに注意してください。 慎重に進んでください。";

/* Bundle Editor : Remove-Bundle: remove dialog message */
"Are you sure you want to move the selected Bundle to the Trash and remove them respectively?" = "選択したバンドルをゴミ箱に移動し、それぞれ削除してもよろしいですか？";

/* continue to print informative message */
"Are you sure you want to print the current content view of the table '%@'?\n\nIt currently contains %@ rows, which may take a significant amount of time to print." = "テーブル '%1$@' の現在のコンテンツビューを印刷してもよろしいですか？ format@@0\n\n現在、 %2$@ 行を含んでおり、印刷にはかなりの時間がかかる場合があります。";

/* remove all query favorites informative message */
"Are you sure you want to remove all of your saved query favorites? This action cannot be undone." = "保存したクエリのお気に入りをすべて削除してもよろしいですか？この操作は元に戻せません。";

/* remove all selected content filters informative message */
"Are you sure you want to remove all selected content filters? This action cannot be undone." = "選択したすべてのコンテンツフィルタを削除してもよろしいですか？この操作は元に戻せません。";

/* remove all selected query favorites informative message */
"Are you sure you want to remove all selected query favorites? This action cannot be undone." = "選択したクエリのお気に入りをすべて削除してもよろしいですか？この操作は元に戻せません。";

/* Table Info Section : current value of auto_increment */
"auto_increment: %@" = "auto_increment: %@";

/* Encoding autodetect menu item */
"Autodetect" = "自動検出";

/* background label for color table (Prefs > Editor) */
"Background" = "バックグラウンド";

/* backtick quote label for color table (Prefs > Editor) */
"Backtick Quote" = "バックティックの引用";

/* bash error */
"BASH Error" = "BASH エラー";

/* toolbar item label for switching to the Table Content tab */
"Browse & Edit Table Content" = "表の内容を参照して編集する";

/* build label */
"build" = "ビルド";

/* build label */
"Build" = "ビルド";

/* bundle editor menu item label */
"Bundle Editor" = "バンドルエディター";

/* bundle error */
"Bundle Error" = "バンドルエラー";

/* bundles menu item label */
"Bundles" = "バンドル";

/* Bundle Editor : Outline View : 'BUNDLES' item */
"BUNDLES" = "バンドル";

/* Bundle Editor : Outline View : Menu Category item : tooltip */
"Bundles in category %@" = "カテゴリ %@ のバンドル";

/* bundles installation error */
"Bundles Installation Error" = "バンドルインストールエラー";

/* bzip2 compression export summary - within a sentence */
"bzip2 compression" = "bzip2 圧縮";

/* description of geometry */
"Can store a single spatial value of types POINT, LINESTRING or POLYGON. Spatial support in MySQL is based on the OpenGIS Geometry Model." = "POINT、LINETRING、POLYGONの単一の空間値を格納できます。MySQLにおける空間サポートはOpenGIS幾何モデルに基づいています。";

/* Bundle Editor : Remove-Bundle: cancel button
 cancel
 cancel button
 Open Files : Bundle : Already-Installed : Cancel button
 Preferences : Network : Custom SSH client : warning dialog : cancel button
 shutdown server : confirmation dialog : cancel button */
"Cancel" = "取消";

/* sql import : charset error alert : cancel button */
"Cancel Import" = "インポートのキャンセル";

/* cancelling task status message */
"Cancelling..." = "キャンセル中...";

/* empty query informative message */
"Cannot save an empty query." = "空のクエリを保存できません。";

/* caret label for color table (Prefs > Editor) */
"Caret" = "キャレット";

/* change button */
"Change" = "変更";

/* change focus to table list menu item */
"Change Focus to Table List" = "テーブルリストにフォーカスを変更";

/* change table type message */
"Change table type" = "テーブルの種類を変更";

/* unsaved changes informative message */
"Changes have been made, which will be lost if this window is closed. Are you sure you want to continue" = "変更が行われました。このウィンドウを閉じると失われます。続行してもよろしいですか？";

/* quitting app informal alert title */
"Close the app?" = "Close the app?";

/* quitting app informal alert body */
"Are you sure you want to quit the app?" = "アプリを終了してもよろしいですか？";

/* character set client: %@ */
"character set client: %@" = "文字セットクライアント: %@";

/* CHECK one or more tables - result title */
"Check %@" = "%@ をチェック";

/* check of all selected items successfully passed message */
"Check of all selected items successfully passed." = "選択したすべての項目を正常に完了しました。";

/* check option: %@ */
"check option: %@" = "チェックオプション: %@";

/* check selected items menu item */
"Check Selected Items" = "選択したアイテムをチェック";

/* check table menu item */
"Check Table" = "テーブルをチェック";

/* check table failed message */
"Check table failed." = "テーブルのチェックに失敗しました。";

/* check table successfully passed message */
"Check table successfully passed." = "チェックテーブルが正常に渡されました。";

/* check view menu item */
"Check View" = "ビューをチェック";

/* checking field data for editing task description */
"Checking field data for editing..." = "編集するフィールドデータを確認しています...";

/* checksum %@ message */
"Checksum %@" = "チェックサム %@";

/* checksum selected items menu item */
"Checksum Selected Items" = "選択したアイテムをチェック";

/* checksum table menu item */
"Checksum Table" = "テーブルのチェックサム";

/* Checksums of %@ message */
"Checksums of %@" = "チェックサム %@";

/* menu item for choose db */
"Choose Database..." = "データベースを選択...";

/* cancelling export cleaning up message */
"Cleaning up..." = "クリーンアップ中...";

/* clear button */
"Clear" = "クリア";

/* toolbar item for clear console */
"Clear Console" = "コンソールを消去";

/* clear global history menu item title */
"Clear Global History" = "グローバル履歴をクリア";

/* clear history for %@ menu title */
"Clear History for %@" = "%@の履歴をクリア";

/* clear history message */
"Clear History?" = "履歴を消去しますか？";

/* tooltip for toolbar item for clear console */
"Clear the console which shows all MySQL commands performed by Sequel Ace" = "Sequel Aceによって実行されるすべてのMySQLコマンドを表示するコンソールをクリアします";

/* clear the document-based history list tooltip message */
"Clear the document-based history list" = "ドキュメントベースの履歴リストをクリア";

/* clear the global history list tooltip message */
"Clear the global history list" = "グローバル履歴リストをクリア";

/* Close menu item */
"Close" = "閉じる";

/* close tab context menu item */
"Close Tab" = "タブを閉じる";

/* Close Window menu item */
"Close Window" = "ウィンドウを閉じる";

/* collation label (Navigator) */
"Collation" = "照合順序";

/* collation connection: %@ */
"collation connection: %@" = "照合順序接続: %@";

/* comment label */
"Comment" = "コメント";

/* Title of action menu item to comment line */
"Comment Line" = "コメント行";

/* Title of action menu item to comment selection */
"Comment Selection" = "コメントの選択";

/* connect button */
"Connect" = "接続";

/* Connect via socket button */
"Connect via socket" = "ソケット経由で接続";

/* connection established message */
"Connected" = "接続済み";

/* description for connected notification */
"Connected to %@" = "%@ に接続しました";

/* message of panel when connection to db failed */
"Connected to host, but unable to connect to database %@.\n\nBe sure that the database exists and that you have the necessary privileges.\n\nMySQL said: %@" = "ホストに接続しましたが、データベース %1$@に接続できません。 format@@0\n\nデータベースが存在し、必要な権限があることを確認してください。\n\nMySQL は %2$@";

/* Generic connecting very short status message */
"Connecting..." = "接続中...";

/* window title string indicating that sp is connecting */
"Connecting…" = "接続中...";

/* error while reading connection data file */
"Connection data file couldn't be read." = "接続データファイルを読み取れませんでした。";

/* message error while reading connection data file and suggesting to save it under a differnet name */
"Connection data file %@ couldn't be read. Please try to save the document under a different name." = "接続データファイル %@ を読み取ることができませんでした。別の名前でドキュメントを保存してみてください。";

/* connection failed title */
"Connection failed!" = "接続に失敗しました ！";

/* Connection file is encrypted */
"Connection file is encrypted" = "接続ファイルが暗号化されています";

/* Connection success very short status message */
"Connection succeeded" = "接続が成功しました";

/* Console */
"Console" = "コンソール";

/* Console : Save as : Initial filename */
"ConsoleLog" = "ConsoleLog";

/* toolbar item label for switching to the Table Content tab */
"Content" = "内容";

/* content filter clause is empty tooltip. */
"Content Filter clause is empty." = "コンテンツフィルタ句は空です。";

/* continue button
 Continue button title */
"Continue" = "続行";

/* continue to print message */
"Continue to print?" = "印刷を続けますか？";

/* Copy as RTF */
"Copy as RTF" = "RTFとしてコピー";

/* copy create func syntax menu item
 Table List : Context Menu : copy CREATE FUNCTION syntax */
"Copy Create Function Syntax" = "関数の構文をコピー";

/* copy create proc syntax menu item
 Table List : Context Menu : Copy CREATE PROCEDURE syntax
 Table List : Gear Menu : Copy CREATE PROCEDURE syntax */
"Copy Create Procedure Syntax" = "プロシージャの構文をコピー";

/* copy create syntaxes menu item
 Table List : Context Menu : Copy CREATE syntax (multiple selection)
 Table List : Gear Menu : Copy CREATE syntax (multiple selection) */
"Copy Create Syntaxes" = "構文をコピー";

/* copy create table syntax menu item
 Table List : Context Menu : Copy CREATE syntax (single table)
 Table List : Gear Menu : Copy CREATE syntax (single table) */
"Copy Create Table Syntax" = "テーブルの構文をコピー";

/* copy create view syntax menu item
 Table List : Context Menu : Copy CREATE view statement
 Table List : Gear Menu : Copy CREATE view statement */
"Copy Create View Syntax" = "ビューの構文をコピー";

/* copy server variable name menu item */
"Copy Variable Name" = "変数名をコピー";

/* copy server variable names menu item */
"Copy Variable Names" = "変数名をコピー";

/* copy server variable value menu item */
"Copy Variable Value" = "変数の値をコピー";

/* copy server variable values menu item */
"Copy Variable Values" = "変数名をコピー";

/* Procedure/function export permission error */
"Could not export the %@ '%@' because of a permissions error.\n" = "パーミッションエラーのため %1$@ '%2$@' をエクスポートできませんでした。\n";

/* Error when we can't parse/split file as CSV */
"Could not parse file as CSV" = "CSVファイルをパースできませんでした";

/* message when database selection failed */
"Could not select database" = "データベースを選択できませんでした";

/* Alter Database : Query Failed ($1 = mysql error message) */
"Couldn't alter database.\nMySQL said: %@" = "データベースを変更できませんでした。\nMySQL: %@";

/* Couldn't copy default themes to Application Support Theme folder!\nError: %@ */
"Couldn't copy default themes to Application Support Theme folder!\nError: %@" = "Application Support Theme フォルダにデフォルトのテーマをコピーできませんでした!\nエラー: %@";

/* message of panel when table cannot be created */
"Couldn't create '%@'.\nMySQL said: %@" = "'%1$@' を作成できませんでした。\nMySQL は %2$@";

/* Couldn't create Application Support Bundle folder!\nError: %@ */
"Couldn't create Application Support Bundle folder!\nError: %@" = "Application Support Bundle フォルダを作成できませんでした!\nエラー: %@";

/* Couldn't create Application Support Theme folder!\nError: %@ */
"Couldn't create Application Support Theme folder!\nError: %@" = "アプリケーションのサポートテーマフォルダを作成できませんでした!\nエラー: %@";

/* message of panel when creation of db failed */
"Couldn't create database.\nMySQL said: %@" = "データベースを作成できませんでした。\nMySQL は: %@";

/* message of panel when an item cannot be deleted */
"Couldn't delete '%@'.\n\nMySQL said: %@" = "'%1$@'を削除できませんでした。\n\nMySQL: %2$@";

/* message of panel when an item cannot be deleted including informative message about using force deletion */
"Couldn't delete '%@'.\n\nSelecting the 'Force delete' option may prevent this issue, but may leave the database in an inconsistent state.\n\nMySQL said: %@" = "'%1$@'を削除できませんでした。\n\n '強制削除'オプションを選択すると、この問題を防ぐことができますが、データベースに一貫性のない状態が残る可能性があります。\n\nMySQLによると：%2$@";

/* message of panel when field cannot be deleted */
"Couldn't delete field %@.\nMySQL said: %@" = "'%1$@'を削除できませんでした。\nMySQL: %2$@";

/* message when deleteing all rows failed */
"Couldn't delete rows.\n\nMySQL said: %@" = "行を削除できませんでした。\n\nMySQL は %@";

/* message of panel when deleting db failed */
"Couldn't delete the database.\nMySQL said: %@" = "データベースを削除できませんでした。\nMySQL は: %@";

/* message of panel when an item cannot be renamed */
"Couldn't duplicate '%@'.\nMySQL said: %@" = "'%1$@'を複製できませんでした。\nMySQLは次のとおりです: %2$@";

/* message of panel when flushing privs failed */
"Couldn't flush privileges.\nMySQL said: %@" = "権限をフラッシュできませんでした。\nMySQL は %@";

/* message of panel when table information cannot be retrieved */
"Couldn't get create syntax.\nMySQL said: %@" = "構文を取得できませんでした。\nMySQL: %@";

/* Custom Query result editing error - could not identify a corresponding column */
"Couldn't identify field origin unambiguously. The column '%@' contains data from more than one table." = "フィールドの原点を明確に識別できませんでした。列 '%@' には複数のテーブルのデータが含まれています。";

/* message of panel when loading of row failed */
"Couldn't load the row. Reload the table to be sure that the row exists and use a primary key for your table." = "行をロードできませんでした。行が存在することを確認するためにテーブルをリロードし、テーブルにプライマリキーを使用してください。";

/* Couldn't read the file content of */
"Couldn't read the file content of" = "次のファイルの内容を読み取ることができませんでした";

/* text shown if an error occurred while sorting the result table */
"Couldn't sort column." = "列をソートできませんでした。";

/* message of panel when sorting of table failed */
"Couldn't sort table. MySQL said: %@" = "テーブルをソートできませんでした。MySQL: %@";

/* message of panel when error while updating field to db */
"Couldn't write field.\nMySQL said: %@" = "フィールドを書き込めませんでした。\nMySQL: %@";

/* create syntax for table comment */
"Create syntax for" = "構文";

/* Create syntax label */
"Create syntax for %@ '%@'" = "%1$@ '%2$@ ' の構文";

/* Create syntaxes for selected items label */
"Create syntaxes for selected items" = "選択アイテムの構文";

/* Table Info Section : table create options */
"create_options: %@" = "作成オプション: %@";

/* created: %@
 Table Info Section : time+date table was created at */
"created: %@" = "作成: %@";

/* description of polygon */
"Creates a surface by combining one LinearRing (ie. a LineString that is closed and simple) as the outside boundary with zero or more inner LinearRings acting as \"holes\"." = "外側の境界として1つのLinearRing（つまり、閉じた単純なLineString）と、「穴」として機能する0個以上の内側のLinearRingを組み合わせてサーフェスを作成します。";

/* Creating table task string */
"Creating %@..." = "%@ を作成しています...";

/* Bundle Editor : Fallback Input source dropdown : 'current line' item */
"Current Line" = "現在の行";

/* Bundle Editor : Fallback Input source dropdown : 'current query' item */
"Current Query" = "現在のクエリ";

/* header for current selection in filtered list */
"CURRENT SELECTION" = "現在の選択";

/* Bundle Editor : Fallback Input source dropdown : 'current word' item */
"Current Word" = "現在の単語";

/* SSH connection : debug header with user-defined ssh binary */
"Custom SSH binary enabled. Disable in Preferences to rule out incompatibilities!" = "カスタムSSHバイナリを有効にしました。環境設定で無効にすると互換性がありません。";

/* customize file name label */
"Customize Filename (%@)" = "ファイル名をカスタマイズ (%@)";

/* data access: %@ (%@) */
"data access: %@ (%@)" = "データアクセス: %1$@ (%2$@)";

/* Bundle Editor : Scope dropdown : 'data table' item
 data table menu item label */
"Data Table" = "データテーブル";

/* Bundle Editor : Outline View : 'Data Table' item : tooltip */
"Data Table Scope\ncommands will run on the Content and Query data tables" = "データテーブルスコープ\nコマンドは、データテーブル上で実行されます。";

/* export filename database token
 export header database label
 export source
 Table Structure : Collation dropdown : 'item is the same as the collation of database' marker
 Table Structure : Encoding dropdown : 'item is database default' marker */
"Database" = "データベース";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'database changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'database changed' item */
"Database changed" = "データベースが変更されました";

/* message of panel when no db name is given */
"Database must have a name." = "データベースには名前が必要です。";

/* databsse rename unsupported message */
"Database Rename Unsupported" = "データベース名はサポートされていません";

/* export filename date token */
"Date" = "日付";

/* export filename date token */
"Day" = "日";

/* Add Table Sheet : Collation : Default (unknown)
 Collation Dropdown : Default (unknown)
 default label
 Table Structure : Collation dropdown : 'item is the same as the default collation of the row's charset' marker */
"Default" = "デフォルト";

/* Add Table : Collation : Default ($1 = collation name)
 Charset Dropdown : Default item ($1 = charset name)
 Collation Dropdown : Default ($1 = collation name)
 Collation Dropdown : Default collation for given charset ($1 = collation name)
 New Table Sheet : Table Engine Dropdown : Default */
"Default (%@)" = "デフォルト (%@)";

/* default bundles update */
"Default Bundles Update" = "デフォルトのバンドルの更新";

/* import : csv field mapping : field default value */
"Default: %@" = "デフォルト: %@";

/* Query snippet default value placeholder */
"default_value" = "default_value";

/* definer label (Navigator) */
"Definer" = "定義者";

/* definer: %@ */
"definer: %@" = "定義者: %@";

/* description of enum */
"Defines a list of members, of which every field can use at most one. Values are sorted by their index number (starting at 0 for the first member)." = "すべてのフィールドが最大1つで使用できるメンバーのリストを定義します。 値はインデックス番号(最初のメンバーの場合は0から)でソートされます。";

/* delete button */
"Delete" = "削除";

/* delete table/view message */
"Delete %@ '%@'?" = "%1$@ '%2$@ を削除しますか？";

/* table structure : indexes : delete index : error 1553 : delete index and FK button */
"Delete Both" = "両方を削除";

/* delete database message */
"Delete database '%@'?" = "データベース'%@ 'を削除しますか?";

/* delete database message */
"Delete favorite '%@'?" = "お気に入りの '%@' を削除しますか？";

/* delete field message */
"Delete field '%@'?" = "フィールド「%@」を削除しますか？";

/* delete func menu title */
"Delete Function" = "関数の削除";

/* delete functions menu title */
"Delete Functions" = "関数の削除";

/* delete database message */
"Delete group '%@'?" = "グループ「%@」を削除しますか？";

/* delete index message */
"Delete index '%@'?" = "インデックス「%@」を削除しますか？";

/* delete items menu title */
"Delete Items" = "項目を削除";

/* delete proc menu title */
"Delete Procedure" = "プロシージャの削除";

/* delete procedures menu title */
"Delete Procedures" = "プロシージャの削除";

/* delete relation menu item */
"Delete Relation" = "関連の削除";

/* delete relation message */
"Delete relation" = "関係の削除";

/* delete relations menu item */
"Delete Relations" = "関連の削除";

/* delete row menu item singular */
"Delete Row" = "行の削除";

/* delete rows menu item plural */
"Delete Rows" = "行の削除";

/* delete rows message */
"Delete rows?" = "行を削除しますか？";

/* delete tables/views message */
"Delete selected %@?" = "選択した %@ を削除しますか？";

/* delete selected row message */
"Delete selected row?" = "選択した行を削除しますか？";

/* delete table menu title */
"Delete Table..." = "テーブルを削除";

/* delete tables menu title */
"Delete Tables" = "テーブルを削除";

/* delete trigger menu item */
"Delete Trigger" = "トリガーを削除";

/* delete trigger message */
"Delete trigger" = "トリガーを削除";

/* delete triggers menu item */
"Delete Triggers" = "トリガーを削除";

/* delete view menu title */
"Delete View" = "ビューを削除";

/* delete views menu title */
"Delete Views" = "ビューを削除";

/* Preferences : Network : SSL Chiper suites : List seperator */
"Disabled Cipher Suites" = "無効化された暗号スイート";

/* force table deltion button text tooltip */
"Disables foreign key checks (FOREIGN_KEY_CHECKS) before deletion and re-enables them afterwards." = "削除前に外部キーチェック(FOREIGN_KEY_CHECKS)を無効にし、後で再び有効にします。";

/* discard changes button */
"Discard changes" = "変更を破棄";

/* description for disconnected notification */
"Disconnected from %@" = "%@ から切断されました";

/* do update operator tooltip */
"Do UPDATE where field contents match" = "フィールドの内容が一致するところでUPDATE を行います。";

/* message of panel asking for confirmation for loading large text into the query editor */
"Do you really want to load a SQL file with %@ of data into the Query Editor?" = "クエリエディタに %@ のデータを含むSQLファイルを読み込みますか？";

/* message of panel asking for confirmation for inserting large text from dragging action */
"Do you really want to proceed with %@ of data?" = "本当に %@ のデータを続行しますか？";

/* shutdown server : confirmation dialog : title */
"Do you really want to shutdown the server?" = "本当にサーバーをシャットダウンしますか？";

/* dtd identifier label (Navigator) */
"DTD Identifier" = "DTD 識別子";

/* sql export dump of table label */
"Dump of table" = "テーブルのダンプ";

/* sql export dump of view label */
"Dump of view" = "ダンプ表示";

/* text showing that app is writing dump */
"Dumping..." = "ダンプ中...";

/* duplicate object message */
"Duplicate %@ '%@' to:" = "%1$@ '%2$@' を複製:";

/* duplicate func menu title */
"Duplicate Function..." = "ファンクションの複製...";

/* duplicate host message */
"Duplicate Host" = "ホストを複製";

/* duplicate proc menu title */
"Duplicate Procedure..." = "プロシージャの複製...";

/* duplicate table menu title */
"Duplicate Table..." = "テーブルを複製...";

/* duplicate user message */
"Duplicate User" = "ユーザーを複製";

/* duplicate view menu title */
"Duplicate View..." = "ビューを複製...";

/* partial copy database support informative message */
"Duplicating the database '%@' is only partially supported as it contains objects other than tables (i.e. views, procedures, functions, etc.), which will not be copied.\n\nWould you like to continue?" = "データベース '%@' を複製することは、テーブル以外のオブジェクトが含まれているため、部分的にのみサポートされています ビュー、プロシージャー、関数など)、コピーされません。Aformat@@0\n\n続行しますか？";

/* edit filter */
"Edit Filters…" = "フィルターを編集…";

/* Edit row button */
"Edit row" = "行の編集";

/* toolbar item label for switching to the Table Structure tab */
"Edit Table Structure" = "テーブル構造の編集";

/* edit theme list label */
"Edit Theme List…" = "テーマリストを編集…";

/* edit user-defined filter */
"Edit user-defined Filters…" = "ユーザー定義フィルターを編集…";

/* empty query message */
"Empty query" = "空のクエリ";

/* MySQL search language code - eg in http://search.mysql.com/search?q=select&site=refman-50&lr=lang_en */
"en" = "en";

/* encoding label (Navigator) */
"Encoding" = "エンコーディング";

/* Table Info Section : $1 = table charset */
"encoding: %1$@" = "エンコーディング: %1$@";

/* Table Info Section : $1 = table charset, $2 = table collation */
"encoding: %1$@ (%2$@)" = "エンコーディング: %1$@ (%2$@)";

/* Table Info Section : Table Engine */
"engine: %@" = "エンジン: %@";

/* enter connection details label */
"Enter connection details below, or choose a favorite" = "以下に接続の詳細を入力するか、お気に入りを選択してください";

/* SSH key password prompt */
"Enter your password for the SSH key\n\"%@\"" = "SSHキー\n'%@ のパスワードを入力してください";

/* Bundle Editor : Fallback Input source dropdown : 'entire content' item
 Bundle Editor : Scope=Field : Input source dropdown: 'entire content' item */
"Entire Content" = "コンテンツ全体";

/* Bundle Editor : Copy-Command-Error : error dialog title
 error */
"Error" = "エラー";

/* error adding field message */
"Error adding field" = "フィールドにエラーがあります。";

/* error adding new column message */
"Error adding new column" = "新しい列の追加に失敗しました";

/* error adding new table message */
"Error adding new table" = "新しいテーブルの追加エラー";

/* error adding password to keychain message */
"Error adding password to Keychain" = "キーチェーンにパスワードを追加する際にエラーが発生しました";

/* error changing field message */
"Error changing field" = "フィールド設定時にエラー発生";

/* error changing table collation message */
"Error changing table collation" = "テーブルの照合順序の変更に失敗しました";

/* error changing table comment message */
"Error changing table comment" = "テーブルのコメントの変更に失敗しました";

/* error changing table encoding message */
"Error changing table encoding" = "テーブルエンコーディングの変更エラー";

/* error changing table type message */
"Error changing table type" = "テーブルの種類の変更に失敗しました";

/* error creating relation message */
"Error creating relation" = "リレーションの作成に失敗しました";

/* error creating trigger message */
"Error creating trigger" = "トリガーの作成中にエラー";

/* error for message */
"Error for" = "エラー：";

/* error for bash command ($1), $2=message */
"Error for “%1$@”:\n%2$@" = "“%1$@”のエラー：\n%2$@";

/* error moving field message */
"Error moving field" = "フィールド移動中にエラーが発生しました";

/* error occurred */
"error occurred" = "エラーが発生しました";

/* error reading import file */
"Error reading import file." = "インポートファイルの読み込み中にエラーが発生しました。";

/* error finding keychain item to edit message */
"Error retrieving Keychain item to edit" = "編集するキーチェーンアイテムの取得に失敗しました";

/* error retrieving table information message */
"Error retrieving table information" = "テーブル情報の取得に失敗しました";

/* error retrieving trigger information message */
"Error retrieving trigger information" = "トリガー情報取得エラー";

/* error truncating table message */
"Error truncating table" = "テーブルの切り詰めに失敗しました";

/* error updating keychain item message */
"Error updating Keychain item" = "キーチェーンアイテムの更新中にエラーが発生しました";

/* error while analyzing selected items message */
"Error while analyzing selected items" = "選択した項目を分析中にエラーが発生しました";

/* error while checking selected items message */
"Error while checking selected items" = "選択した項目を確認中にエラーが発生しました";

/* error while converting color scheme data */
"Error while converting color scheme data" = "カラースキームデータの変換中にエラーが発生しました";

/* error while converting connection data */
"Error while converting connection data" = "接続データの変換中にエラー";

/* Content filters could not be converted to plist upon export - message title (ContentFilterManager) */
"Error while converting content filter data" = "コンテンツフィルタデータの変換中にエラーが発生しました";

/* error while converting query favorite data */
"Error while converting query favorite data" = "クエリお気に入りデータの変換中にエラーが発生しました";

/* error while converting session data */
"Error while converting session data" = "セッションデータの変換中にエラー";

/* Error while deleting field */
"Error while deleting field" = "フィールド削除中にエラーが発生しました";

/* Bundle Editor : Copy-Command-Error : Copying failed error message */
"Error while duplicating Bundle content." = "バンドルコンテンツの複製中にエラーが発生しました。";

/* error while executing javascript bash command */
"Error while executing JavaScript BASH command" = "JavaScript BASHコマンド実行中にエラーが発生しました";

/* error while fetching the optimized field type message */
"Error while fetching the optimized field type" = "最適化されたフィールドタイプの取得中にエラーが発生しました";

/* error while flushing selected items message */
"Error while flushing selected items" = "選択したアイテムをフラッシュ中にエラーが発生しました";

/* error while importing table message */
"Error while importing table" = "テーブルのインポート中にエラーが発生しました";

/* Open Files : Bundle : Install-Error : error dialog title
 Open Files : Bundle : UUID : Error dialog title */
"Error while installing Bundle" = "バンドルのインストール中にエラーが発生しました";

/* error while installing color theme file */
"Error while installing color theme file" = "カラーテーマファイルのインストール中にエラーが発生しました";

/* Bundle Editor : Trash-Bundle(s)-Error : error dialog title
 error while moving %@ to trash
 Open Files : Bundle : Already-Installed : Delete-Old-Error : Could not delete old bundle before installing new version. */
"Error while moving %@ to Trash." = "%@ をゴミ箱に移動中にエラーが発生しました。";

/* error while optimizing selected items message */
"Error while optimizing selected items" = "選択した項目を最適化中にエラーが発生しました";

/* error while parsing CREATE TABLE syntax */
"Error while parsing CREATE TABLE syntax" = "CREATE TABLE 構文の解析中にエラーが発生しました";

/* error while reading connection data file */
"Error while reading connection data file" = "接続データファイルの読み込み中にエラーが発生しました";

/* error while reading data file */
"Error while reading data file" = "データファイルの読み込み中にエラーが発生しました";

/* error while repairing selected items message */
"Error while repairing selected items" = "選択した項目を修復中にエラーが発生しました";

/* Bundle Editor : Save-Bundle-Error : error dialog title */
"Error while saving the Bundle." = "バンドルの保存中にエラーが発生しました。";

/* Bundle Editor : Save-and-Close-Error : error dialog title */
"Error while saving %@." = "%@ の保存中にエラーが発生しました。";

/* Errors title */
"Errors" = "エラー";

/* Mysql Help Viewer : Help Topic: Example section title */
"Example:" = "例:";

/* Bundle Editor : BLOB dropdown : 'exclude BLOB' item */
"exclude BLOB" = "BLOBを除外する";

/* execution privilege label (Navigator) */
"Execution Privilege" = "実行権限";

/* execution privilege: %@ */
"execution privilege: %@" = "実行権限: %@";

/* execution stopped message */
"Execution stopped!\n" = "処理が停止しました！\n";

/* export selected favorites menu item */
"Export Selected..." = "選択したものをエクスポート...";

/* text showing that the application is importing a supplied format */
"Exporting %@" = "%@ をエクスポート中";

/* text showing that the application is exporting a Dot file */
"Exporting Dot File" = "ドットファイルのエクスポート";

/* text showing that the application is exporting SQL */
"Exporting SQL" = "SQLのエクスポート";

/* extra label (Navigator) */
"Extra" = "エクストラ";

/* table structure : indexes : delete index : no columns error : title */
"Failed to remove index '%@'" = "インデックスの削除に失敗しました '%@'";

/* fatal error */
"Fatal Error" = "致命的エラー";

/* export filename favorite name token */
"Favorite" = "お気に入り";

/* favorites label */
"Favorites" = "お気に入り";

/* favorites export error message */
"Favorites export error" = "お気に入りのエクスポートエラー";

/* favorites import error message */
"Favorites import error" = "お気に入りのエクスポートエラー";

/* export label showing that the app is fetching data */
"Fetching data..." = "データを取得しています...";

/* fetching database structure data in progress */
"fetching database structure data in progress" = "進行中のデータベース構造データの取得中";

/* fetching database structure in progress */
"fetching database structure in progress" = "進行中のデータベース構造の取得中";

/* fetching table data for completion in progress message */
"fetching table data…" = "テーブルデータの取得中…";

/* popup menuitem for field (showing only if disabled) */
"field" = "フィールド";

/* Field */
"Field" = "フィールド";

/* Query result editing error - could not match row being edited uniquely */
"Field is not editable. Couldn't identify field origin unambiguously (%ld matches)." = "フィールドは編集できません。フィールドの原点を明確に識別できませんでした（%ld 件の一致）。";

/* field is not editable due to no table/database */
"Field is not editable. Field has no or multiple table or database origin(s)." = "フィールドは編集できません。フィールドにはテーブルまたはデータベースの原点がありません。";

/* Custom Query result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload data, check encoding, or try to add\na primary key field or more fields\nin your SELECT statement for table '%@'\nto identify field origin unambiguously." = "フィールドは編集できません。一致するレコードが見つかりませんでした。\nデータの再読み込み, エンコードの確認 または、テーブル'%@' '\nの SELECT ステートメントにプライマリキーフィールド\n以上のフィールド\nを追加して、フィールドの原点を明確に識別しようとします。";

/* Table Content result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload table, check the encoding, or try to add\na primary key field or more fields\nin the view declaration of '%@' to identify\nfield origin unambiguously." = "フィールドは編集できません。一致するレコードが見つかりません。\nテーブルをリロードするか、エンコーディングを確認するか、追加してみてください\n
主キーフィールドまたは複数のフィールド\n'%@'のビュー宣言で、\nフィールドの起源を明確に識別します。";

/* error while reading data file */
"File couldn't be read." = "ファイルを読み込めませんでした。";
"File couldn't be read: %@\n\nIt will be deleted." = "ファイルを読み込めませんでした: %@\n\n削除されます。";

/* File read error title (Import Dialog) */
"File read error" = "ファイル読み込みエラー";

/* All export files already exist explanatory text */
"Files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "同じ名前のファイルが既にターゲットフォルダに存在します。置き換えると、現在の内容が上書きされます。";

/* filter label */
"Filter" = "フィルタ";

/* apply filter label */
"Apply Filter(s)" = "フィルターを適用";

/* filter tables menu item */
"Filter Tables" = "テーブルのフィルタリング";

/* export source */
"Filtered table content" = "フィルタされたテーブルの内容";

/* filtering failed. please try again. tooltip */
"Filtering failed. Please try again." = "フィルタに失敗しました。再試行してください。";

/* Filtering table task description */
"Filtering table..." = "テーブルのフィルタリング中...";

/* description for finished exporting notification */
"Finished exporting to %@" = "%@ へのエクスポートが完了しました";

/* description for finished importing notification */
"Finished importing %@" = "%@ のインポートが終了しました";

/* FLUSH one or more tables - result title */
"Flush %@" = "%@ をフラッシュする";

/* flush selected items menu item */
"Flush Selected Items" = "選択したアイテムをフラッシュする";

/* flush table menu item */
"Flush Table" = "テーブルをフラッシュする";

/* flush table failed message */
"Flush table failed." = "テーブルのフラッシュに失敗しました。";

/* flush view menu item */
"Flush View" = "フラッシュビュー";

/* title of panel when successfully flushed privs */
"Flushed Privileges" = "フラッシュ特権";

/* For BIT fields only “1” or “0” are allowed. */
"For BIT fields only “1” or “0” are allowed." = "BITフィールドの場合、「1」または「0」のみが許可されます。";

/* force table deletion button text */
"Force delete (disables integrity checks)" = "強制削除 (整合性チェックを無効にします)";

/* full text index menu item title */
"FULLTEXT" = "FULLTEXT";

/* function */
"function" = "function";

/* header for function info pane */
"FUNCTION INFORMATION" = "ファンクション情報";

/* functions */
"functions" = "関数";

/* Bundle Editor : Scope dropdown : 'general' item
 general menu item label
 general preference pane name */
"General" = "一般";

/* general preference pane tooltip */
"General Preferences" = "一般設定";

/* Bundle Editor : Outline View : 'General' item : tooltip */
"General Scope\ncommands will run application-wide" = "一般的なスコープ\nコマンドは、アプリケーション全体を実行します。";

/* generating print document status message */
"Generating print document..." = "印刷ドキュメントを生成しています...";

/* export header generation time label */
"Generation Time" = "生成時間";

/* Content Filter Manager : Filter Entry List: 'Global' Header
 Query Favorites : List : Section Heading : global query favorites */
"Global" = "グローバル";

/* Query Favorites : List : Section Heading : global : tooltip */
"Globally stored favorites" = "グローバルに保存されたお気に入り";

/* Preferences : Notifications : status text : notification setup */
"Notifications are delivered via the Notification Center." = "通知は通知センター経由で配信されます。";

/* Gzip compression export summary - within a sentence */
"Gzip compression" = "Gzip 圧縮";

/* MySQL Help Viewer : Results list : Page title */
"Help topics for %@" = "%@のヘルプトピック";

/* hide console */
"Hide Console" = "コンソールを隠す";

/* hide navigator */
"Hide Navigator" = "ナビゲーションを隠す";

/* hide tab bar */
"Hide Tab Bar" = "タブバーを隠す";

/* Hide Toolbar menu item */
"Hide Toolbar" = "ツールバーを非表示";

/* export filename host token
 export header host label */
"Host" = "ホスト";

/* description of double */
"IEEE 754 double-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 倍精度浮動小数点値。M は最大桁数で、D は小数点以降である可能性があります。 注意: 多くの小数は浮動小数点値でのみ近似できます。正確な結果が必要な場合はDECIMALを参照してください。";

/* description of float */
"IEEE 754 single-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 単精度浮動小数点値。M は最大桁数で、D は小数点以降である可能性があります。 注意: 多くの小数は浮動小数点値でのみ近似できます。正確な結果が必要な場合はDECIMALを参照してください。";

/* ignore button */
"Ignore" = "無視";

/* ignore errors button */
"Ignore All Errors" = "すべてのエラーを無視";

/* ignore all fields menu item */
"Ignore all Fields" = "このフィールドを無視します";

/* ignore field label */
"Ignore field" = "フィールドを無視";

/* ignore field label */
"Ignore Field" = "フィールドを無視";

/* import button */
"Import" = "インポート";

/* import all fields menu item */
"Import all Fields" = "すべてのフィールドをインポート";

/* sql import : charset error alert : continue button */
"Import Anyway" = "とにかくインポート";

/* import cancelled message */
"Import cancelled!\n" = "インポートがキャンセルされました!\n";

/* Import Error title */
"Import Error" = "インポートエラー";

/* import field operator tooltip */
"Import field" = "インポートされたフィールド";

/* import file does not exist message */
"Import file does not exist." = "ファイルが存在しません。";

/* Export file format cannot be imported warning */
"Import of the selected data is currently not supported." = "選択したデータのインポートは現在サポートされていません。";

/* SQL import progress text */
"Imported %@ of %@" = "%1$@ / %2$@ をインポートしました";

/* CSV import progress text where total size is unknown */
"Imported %@ of CSV data" = "CSVデータの %@ をインポートしました";

/* SQL import progress text where total size is unknown */
"Imported %@ of SQL" = "SQLの %@ をインポートしました";

/* text showing that the application is importing CSV */
"Importing CSV" = "CSVをインポート中";

/* text showing that the application is importing SQL */
"Importing SQL" = "SQLをインポート中";

/* Bundle Editor : BLOB dropdown : 'include BLOB' item */
"include BLOB" = "BLOBを含む";

/* include content table column tooltip */
"Include content" = "コンテンツを含める";

/* sql import error message */
"Incompatible encoding in SQL file" = "SQLファイル内の符号化方式に互換性がありません";

/* header for blank info pane */
"INFORMATION" = "情報";

/* New Table Sheet : Table Collation Dropdown : Default inherited from database
 New Table Sheet : Table Encoding Dropdown : Default inherited from database */
"Inherit from database (%@)" = "データベースから継承する (%@)";

/* initializing export label */
"Initializing..." = "初期化中...";

/* Bundle Editor : Scope dropdown : 'input field' item
 input field menu item label */
"Input Field" = "入力フィールド";

/* input field  doesn't support insertion of snippets. */
"Input Field doesn't support insertion of snippets." = "入力フィールドはスニペットの挿入をサポートしていません。";

/* input field is not editable. */
"Input Field is not editable." = "入力フィールドは編集できません。";

/* Bundle Editor : Outline View : 'Input Field' item : tooltip */
"Input Field Scope\ncommands will run on each text input field" = "入力フィールドスコープ\nコマンドは、各テキスト入力フィールド上で実行されます。";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as snippet' item */
"Insert as Snippet" = "スニペットとして挿入";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as text' item */
"Insert as Text" = "テキストの挿入";

/* Bundle Editor : Outline View : 'BUNDLES' item : tooltip */
"Installed Bundles" = "インストール済みバンドル";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog title */
"Installing Bundle" = "インストール済みバンドル";

/* insufficient details message */
"Insufficient connection details" = "接続の詳細が不足しています";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please enter at least the hostname." = "接続を確立するための詳細が不足しています。ホスト名を入力してください。";

/* insufficient SSH tunnel details informative message */
"Insufficient details provided to establish a connection. Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "接続を確立するための詳細が不足しています。SSH Tunnel のホスト名を入力するか、SSH Tunnel を無効にしてください。";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please provide at least a host." = "接続を確立するための詳細が不足しています。少なくともホストを提供してください。";

/* Interpret data as: */
"Interpret data as:" = "データの解釈:";

/* Invalid database very short status message */
"Invalid database" = "無効なデータベース";

/* export : import settings : file error title */
"Invalid file supplied!" = "無効なファイルタイプです！";

/* table content : editing : error message title when parsing as hex string failed */
"Invalid hexadecimal value" = "16進数の値が無効です";

/* is deterministic label (Navigator) */
"Is Deterministic" = "確定的";

/* is nullable label (Navigator) */
"Is Nullable" = "Null を許す";

/* is updatable: %@ */
"is updatable: %@" = "更新可否: %@";

/* items */
"items" = "項目";

/* javascript exception */
"JavaScript Exception" = "JavaScript 例外";

/* javascript parsing error */
"JavaScript Parsing Error" = "JavaScript 解析エラー";

/* key label (Navigator) */
"Key" = "キー";

/* keyword label for color table (Prefs > Editor) */
"Keyword" = "キーワード";

/* kill button */
"Kill" = "強制終了";

/* kill connection message */
"Kill connection?" = "接続を終了しますか?";

/* kill query message */
"Kill query?" = "クエリを終了しますか?";

/* Last Error Message */
"Last Error Message" = "最終エラー メッセージ";

/* Last Used entry in favorites menu */
"Last Used" = "最後の使用";

/* range for json type */
"Limited to @@max_allowed_packet" = "@@max_allowed_packetに限定";

/* Loading table task string */
"Loading %@..." = "%@ を読み込み中...";

/* Loading database task string */
"Loading database '%@'..." = "データベースを読み込み中 '%@'...";

/* Loading history entry task desc */
"Loading history entry..." = "履歴を読み込み中...";

/* Loading table page task string */
"Loading page %lu..." = "ページ %lu を読み込み中...";

/* Loading referece task string */
"Loading reference..." = "参照を読み込んでいます...";

/* Loading table data string */
"Loading table data..." = "テーブルデータを読み込み中...";

/* Low memory export summary */
"Low memory" = "メモリ不足";

/* range of decimal */
"M (precision): Up to 65 digits\nD (scale): 0 to 30 digits" = "M (精度)：最大 65桁\nD (スケール): 0 から 30桁まで";

/* range for blob type
 range for varbinary type */
"M: %@ to %@ bytes" = "M: %1$@ から %2$@ バイト";

/* range for text type
 range for varchar type */
"M: %@ to %@ characters" = "M: %1$@ から %2$@ 文字";

/* range for longtext type */
"M: %@ to %@ characters (4 GiB)" = "M: %1$@ から %2$@ 文字 (4 GiB)";

/* range for binary type */
"M: 0 to 255 bytes" = "M: 0 から 255 バイト";

/* range for char type */
"M: 0 to 255 characters" = "M: 0 から 255 文字";

/* range for bit type */
"M: 1 (default) to 64" = "M: 1 (デフォルト) から 64";

/* connection view : ssl : key file picker : wrong format error description */
"Make sure the file contains a RSA private key and is using PEM encoding." = "ファイルに RSA 秘密鍵が含まれており、PEM エンコードを使用していることを確認します。";

/* connection view : ssl : client cert picker : wrong format error description */
"Make sure the file contains a X.509 client certificate and is using PEM encoding." = "ファイルに X.509 クライアント証明書が含まれており、PEM エンコーディングを使用していることを確認します。";

/* match field menu item */
"Match Field" = "フィールドの一致";

/* Shown when user inserts too many arguments (ContentFilterManager) */
"Maximum number of arguments is 2!" = "引数の最大数は 2 です!";

/* Maximum text length is set to %ld. */
"Maximum text length is set to %ld." = "テキストの最大長は %ld に設定されています。";

/* Maximum text length is set to %ld. Inserted text was truncated. */
"Maximum text length is set to %ld. Inserted text was truncated." = "テキストの最大長は %ldに設定されています。挿入されたテキストは切り捨てられました。";

/* Maximum text length is set to %llu. */
"Maximum text length is set to %llu." = "テキストの最大長は %llu に設定されています。";

/* Maximum text length is set to %llu. Inserted text was truncated. */
"Maximum text length is set to %llu. Inserted text was truncated." = "テキストの最大長は %lluに設定されています。挿入されたテキストは切り捨てられました。";

/* message column title */
"Message" = "メッセージ";

/* mgtemplateengine error */
"MGTemplateEngine Error" = "MGTemplateEngine エラー";

/* export filename date token */
"Month" = "月";

/* multiple selection */
"multiple selection" = "複数選択";

/* MySQL connecting very short status message */
"MySQL connecting..." = "MySQL 接続中...";

/* mysql error message */
"MySQL Error" = "MySQL Error";

/* mysql help */
"MySQL Help" = "MySQLヘルプ";

/* MySQL Help for Selection */
"MySQL Help for Selection" = "MySQLヘルプ 選択";

/* MySQL Help for Word */
"MySQL Help for Word" = "MySQLヘルプ 用語";

/* mysql help categories */
"MySQL Help – Categories" = "MySQL ヘルプ – カテゴリ";

/* mysql said message */
"MySQL said:" = "MySQL :";

/* shutdown server : error dialog : message */
"MySQL said:\n%@" = "MySQL :\n%@";

/* message of panel when error while adding row to db */
"MySQL said:\n\n%@" = "MySQL :\n\n%@";

/* Preferences : Themes : Initial filename for 'Export' */
"MyTheme" = "MyTheme";

/* network preference pane name */
"Network" = "ネットワーク";

/* network preference pane tooltip */
"Network Preferences" = "ネットワーク設定";

/* file preference pane name */
"Files" = "ファイル";

/* file preference pane tooltip */
"File Preferences" = "ファイル設定";

/* Bundle Editor : Default name for new bundle in the list on the left */
"New Bundle" = "新しいバンドル";

/* new column name placeholder string */
"New Column Name" = "新しい列名";

/* new favorite name */
"New Favorite" = "新着お気に入り";

/* Content Filter Manager : Initial name for new filter */
"New Filter" = "新しいフィルタ";

/* new folder placeholder name */
"New Folder" = "新規フォルダ";

/* Bundle Editor : Default name for a new bundle in the menu */
"New Name" = "新しい名前";

/* new table menu item */
"New Table" = "新規テーブル";

/* error that no color theme found */
"No color theme data found." = "カラーテーマデータが見つかりません。";

/* No compression export summary - within a sentence */
"no compression" = "圧縮なし";

/* no connection available message */
"No connection available" = "ネットワーク接続不可";

/* no connection data found */
"No connection data found." = "接続データが見つかりません。";

/* No content filters were found in file to import (ContentFilterManager) */
"No content filters found." = "コンテンツフィルタが見つかりません。";

/* no data found */
"No data found." = "データがありません。";

/* No errors title */
"No errors" = "エラーなし";

/* No favorites entry in favorites menu */
"No Favorties" = "お気に入りがありません";

/* All export files creation error title */
"No files could be created" = "ファィルは作成できませんでした。";

/* no item found message */
"No item found" = "アイテムが見つかりません";

/* SSH tunnel could not be created because no local port could be allocated */
"No local port could be allocated for the SSH Tunnel." = "SSHトンネルに割り当てられるローカルポートはありません。";

/* header for no matches in filtered list */
"NO MATCHES" = "マッチなし";

/* no optimized field type found. message */
"No optimized field type found." = "最適化されたフィールドタイプが見つかりません。";

/* error that no query favorites found */
"No query favorites found." = "クエリのお気に入りが見つかりませんでした。";

/* Mysql Help Viewer : Search : No results */
"No results found." = "結果はありません";

/* Bundle Editor : Fallback Input source dropdown : 'none' item
 Bundle Editor : Scope=? : ? dropdown: 'None' item
 Bundle Editor : Scope=Data-Table : Input source dropdown: 'none' item
 Bundle Editor : Scope=Data-Table : Output dropdown : 'none' item
 Bundle Editor : Scope=Data-Table : Trigger dropdown : 'none' item
 Bundle Editor : Scope=Field : Input source dropdown: 'None' item
 Bundle Editor : Scope=Field : Output dropdown : 'none' item
 Bundle Editor : Scope=Field : Trigger dropdown : 'none' item
 Bundle Editor : Scope=General : Input source dropdown: 'None' item
 Bundle Editor : Scope=General : Output dropdown : 'none' item
 Bundle Editor : Scope=General : Trigger dropdown : 'none' item
 compression: none */
"None" = "なし";

/* not available label */
"Not available" = "利用できません";

/* Argument count (ContentFilterManager) */
"Number of arguments: %lu" = "引数の数: %lu";

/* numeric label for color table (Prefs > Editor) */
"Numeric" = "数値";

/* Bundle Editor : Copy-Command-Error : OK button
 Bundle Editor : Save-and-Close-Error : OK button
 Bundle Editor : Save-Bundle-Error : OK button
 Bundle Editor : Trash-Bundle(s)-Error : OK button
 OK
 OK button
 Open Files : Bundle : Already-Installed : Delete-Old-Error : OK button
 Open Files : Bundle : Install-Error : OK button
 Open Files : Bundle : UUID : OK button
 Preferences : Network : Custom SSH client : warning dialog : accept button */
"OK" = "OK";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+1 rows instead of n selected were deleted. */
"One additional row was removed!" = "1つの追加行が削除されました！";

/* Table Content : Remove Row : Result : Too Few : Part 1 : Only n-1 of n selected rows were deleted. */
"One row was not removed." = "1行は削除されませんでした。";

/* Only one dragged item allowed. */
"Only one dragged item allowed." = "ドラッグされたアイテムは1つだけ許可されています。";

/* partial copy database support message */
"Only Partially Supported" = "一部サポートのみ";

/* open function in new table title */
"Open Function in New Tab" = "新しいタブで関数を開く";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Function in New Window" = "新しいウィンドウで関数を開く";

/* open connection in new tab context menu item */
"Open in New Tab" = "新しいタブを開く";

/* menu item open in new window */
"Open in New Window" = "新しいウィンドウで開く";

/* open procedure in new table title */
"Open Procedure in New Tab" = "新しいタブでプロシージャを開く";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Procedure in New Window" = "新しいウインドウでプロシージャを開く";

/* open table in new tab title
 open table in new table title */
"Open Table in New Tab" = "新しいタブでテーブルを開く";

/* Table List : Context Menu : Duplicate connection to new window
 Table List : Gear Menu : Duplicate connection to new window */
"Open Table in New Window" = "新しいウインドウでテーブルを開く";

/* open view in new tab title
 open view in new table title */
"Open View in New Tab" = "新しいタブでビューを開く";

/* Tables List : Context Menu : Duplicate connection to new window
 Tables List : Gear Menu : Duplicate connection to new window */
"Open View in New Window" = "新しいウインドウでビューを開く";

/* menu item open %@ in new window */
"Open %@ in New Window" = "新しいウィンドウで %@ を開く";

/* OPTIMIZE one or more tables - result title */
"Optimize %@" = "%@を最適化";

/* optimize selected items menu item */
"Optimize Selected Items" = "選択したアイテムを最適化";

/* optimize table menu item */
"Optimize Table" = "テーブルの最適化";

/* optimize table failed message */
"Optimize table failed." = "テーブルの最適化に失敗しました。";

/* Optimized type for field %@ */
"Optimized type for field '%@'" = "フィールド '%@ ' に最適化されたタイプ";

/* optional placeholder string */
"optional" = "省略可能";

/* Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed. */
"Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed." = "渡されたパラメータを解釈できませんでした。文字列または配列（2つの要素を含む）のみが許可されます。";

/* Permission Denied */
"Permission Denied" = "権限がありません";

/* please choose a favorite connection view label */
"Please choose a favorite" = "お気に入りを選択してください";

/* message of panel when ssh details are incomplete */
"Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "SSH Tunnel のホスト名を入力するか、SSH Tunnel を無効にしてください。";

/* Please enter the password */
"Please enter the password for ‘%@’:" = "\"%@\" のパスワードを入力してください:";

/* print button */
"Print" = "印刷";

/* print page menu item title */
"Print Page…" = "ページを印刷中..";

/* privileges label (Navigator) */
"Privileges" = "権限";

/* procedure */
"procedure" = "プロシージャ";

/* header for procedure info pane */
"PROCEDURE INFORMATION" = "プロシージャ情報";

/* procedures */
"procedures" = "プロシージャ";

/* proceed button */
"Proceed" = "続行";

/* header for procs & funcs list */
"PROCS & FUNCS" = "PROCS & FUNCS";

/* toolbar item label for switching to the Run Query tab */
"Query" = "クエリ";

/* query background label for color table (Prefs > Editor) */
"Query Background" = "クエリのバックグランド実行";

/* Query cancelled error */
"Query cancelled." = "クエリがキャンセルされました。";

/* Query cancel by resetting connection error */
"Query cancelled.  Please note that to cancel the query the connection had to be reset; transactions and connection variables were reset." = "クエリはキャンセルされました。クエリをキャンセルするには、接続をリセットする必要がありました。トランザクションと接続変数がリセットされました。";

/* query editor preference pane name */
"Query Editor" = "クエリエディター";

/* query editor preference pane tooltip */
"Query Editor Preferences" = "クエリエディタの設定";

/* query logging currently disabled label
 query logging disabled label */
"Query logging is currently disabled" = "クエリのログは現在無効です";

/* query result print heading */
"Query Result" = "クエリの結果";

/* export source */
"Query results" = "クエリの結果";

/* Query Status */
"Query Status" = "クエリの状態";

/* table status : row count query failed : error title */
"Querying row count failed" = "行数の問い合わせに失敗しました";

/* Quick connect item label */
"Quick Connect" = "クイック接続";

/* quote label for color table (Prefs > Editor) */
"Quote" = "引用符";

/* databsse rename unsupported informative message */
"Renaming the database '%@' is currently unsupported as it contains objects other than tables (i.e. views, procedures, functions, etc.).\n\nIf you would like to rename a database please use the 'Duplicate Database', move any non-table objects manually then drop the old database." = "データベース '%@' の名前の変更は、テーブル以外のオブジェクト (すなわちビュー、プロシージャ、関数など) が含まれているため、現在サポートされていません。 format@@0\n\nデータベースの名前を変更したい場合は、「データベースの複製」を使用してください。 テーブル以外のオブジェクトを手動で移動し、古いデータベースをドロップします。";

/* range for serial type */
"Range: %@ to %@" = "範囲: %1$@ から %2$@";

/* range for time type */
"Range: -838:59:59.0 to 838:59:59.0\nF (precision): 0 (1s) to 6 (1µs)" = "範囲:-838:59:59.0~838:59:59:59.0\nF(精度):0(1)~6(1μs)";

/* range for year type */
"Range: 0000, 1901 to 2155" = "範囲:0000、1901年から2155年まで";

/* range for set type */
"Range: 1 to 64 members\n1, 2, 3, 4 or 8 bytes storage" = "範囲: 1 ~ 64 メンバー\n1、2、3、4 または 8 バイトストレージ";

/* range for datetime type */
"Range: 1000-01-01 00:00:00.0 to 9999-12-31 23:59:59.999999\nF (precision): 0 (1s) to 6 (1µs)" = "範囲: 1000-01-01 00:00.0〜9999-12-31 23:59:59.999999\nF (精度: 0(1)〜6(1μs)";

/* range for date type */
"Range: 1000-01-01 to 9999-12-31" = "範囲: 1000-01-01-9999-12-31";

/* range for timestamp type */
"Range: 1970-01-01 00:00:01.0 to 2038-01-19 03:14:07.999999\nF (precision): 0 (1s) to 6 (1µs)" = "範囲: 1970-01-01 00:00.0〜2038-01-19 03:14:07.999999\nF (精度: 0(1)〜6(1μs)";

/* text showing that app is reading dump */
"Reading..." = "読み込み中...";

/* menu item to refresh databases */
"Refresh Databases" = "データベースの再表示";

/* refresh list menu item */
"Refresh List" = "リストを更新";

/* toolbar item label for switching to the Table Relations tab */
"Relations" = "関連";

/* Relations tab subtitle showing table name */
"Relations for table: %@" = "テーブルのリレーション: %@";

/* reload bundles menu item label */
"Reload Bundles" = "バンドルを再読み込み";

/* Reloading data task description */
"Reloading data..." = "データを再読み込み中...";

/* Reloading table task string */
"Reloading..." = "リロード中...";

/* remote error */
"Remote Error" = "リモートエラー";

/* Bundle Editor : Remove-Bundle: remove button
 remove button */
"Remove" = "削除";

/* remove all button */
"Remove All" = "全て削除";

/* remove all query favorites message */
"Remove all query favorites?" = "すべてのクエリのお気に入りを削除しますか？";

/* Bundle Editor : Remove-Bundle: remove dialog title */
"Remove selected Bundle?" = "選択したバンドルを削除しますか?";

/* remove selected content filters message */
"Remove selected content filters?" = "選択したコンテンツフィルタを削除しますか？";

/* remove selected query favorites message */
"Remove selected query favorites?" = "選択したクエリのお気に入りを削除しますか？";

/* removing field task status message */
"Removing field..." = "フィールドを削除しています...";

/* removing index task status message */
"Removing index..." = "インデックスを削除しています...";

/* rename database message */
"Rename database '%@' to:" = "データベース '%@' の名前を変更:";

/* rename func menu title */
"Rename Function..." = "ファンクションの名前を変更...";

/* rename proc menu title */
"Rename Procedure..." = "プロシージャの名前を変更...";

/* rename table menu title */
"Rename Table..." = "テーブルの名前を変更...";

/* rename view menu title */
"Rename View..." = "ビューの名前を変更...";

/* REPAIR one or more tables - result title */
"Repair %@" = "%@を修復";

/* repair selected items menu item */
"Repair Selected Items" = "選択したアイテムを修復";

/* repair table menu item */
"Repair Table" = "テーブルの修復";

/* repair table failed message */
"Repair table failed." = "テーブルの修復に失敗しました。";

/* Replace button */
"Replace" = "置換";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace entire content' item */
"Replace Entire Content" = "内容全体を置換";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace selection' item */
"Replace Selection" = "選択の置換";

/* description of year */
"Represents a 4 digit year value, stored as 1 byte. Invalid values are converted to 0000 and two digit values 0 to 69 will be converted to years 2000 to 2069, resp. values 70 to 99 to years 1970 to 1999.\nThe YEAR(2) type was removed in MySQL 5.7.5." = "1 バイトとして格納された 4 桁の年値を表します。 無効な値は0000に変換され、2桁の値は0から69に変換されます。2000年から2069年に変換されます。 values 70 to 99 to Year 1970 to 1999 1970 to 1999\nYEAR(2) type was removed in MySQL 5.7.5.";

/* description of multilinestring */
"Represents a collection of LineStrings." = "LineStringsのコレクションを表します。";

/* description of geometrycollection */
"Represents a collection of objects of any other single- or multi-valued spatial type. The only restriction being, that all objects must share a common coordinate system." = "他の単一または多重値の空間タイプのオブジェクトのコレクションを表します。 唯一の制限は、すべてのオブジェクトが共通の座標系を共有する必要があることです。";

/* description of multipolygon */
"Represents a collection of Polygons. The Polygons making up the MultiPolygon must not intersect." = "多角形のコレクションを表します。多角形を構成する多角形は交差してはいけません。";

/* description of multipoint */
"Represents a set of Points without specifying any kind of relation and/or order between them." = "ポイントのセットを表し、それらの間のリレーションおよび/または順序を指定しません。";

/* description of point */
"Represents a single location in coordinate space using X and Y coordinates. The point is zero-dimensional." = "X座標とY座標を使用して座標空間内の単一の位置を表します。点は0次元です。";

/* description of linestring */
"Represents an ordered set of coordinates where each consecutive pair of two points is connected by a straight line." = "2 つの点の連続したペアが直線で接続されている座標セットを表します。";

/* required placeholder string */
"required" = "必須";

/* description of smallint */
"Requires 2 bytes storage space. M is the optional display width and does not affect the possible value range." = "2バイトのストレージスペースが必要です。Mはオプションの表示幅であり、可能な値の範囲には影響しません。";

/* description of mediumint */
"Requires 3 bytes storage space. M is the optional display width and does not affect the possible value range." = "3バイトのストレージスペースが必要です。Mはオプションの表示幅であり、可能な値の範囲には影響しません。";

/* description of int */
"Requires 4 bytes storage space. M is the optional display width and does not affect the possible value range. INTEGER is an alias to this type." = "4バイトのストレージスペースが必要です。Mはオプションの表示幅であり、可能な値の範囲には影響しません。 INTEGERはこの型へのエイリアスです。";

/* description of bigint */
"Requires 8 bytes storage space. M is the optional display width and does not affect the possible value range. Note: Arithmetic operations might fail for large numbers." = "8バイトのストレージスペースが必要です。Mはオプションの表示幅であり、可能な値の範囲には影響しません。 注: 算術演算は多数の場合失敗する可能性があります。";

/* reset auto_increment after deletion of all rows message */
"Reset AUTO_INCREMENT after deletion\n(only for Delete ALL ROWS IN TABLE)?" = "削除後にAUTO_INCREMENTをリセット\n(テーブル内のすべてのROWSを削除する場合のみ)?";

/* delete selected row button */
"Delete Selected Row" = "選択した行を削除";

/* delete selected rows button */
"Delete Selected Rows" = "選択した行を削除";

/* delete all rows in table button */
"Delete ALL ROWS IN TABLE" = "テーブル内のすべてのROWを削除";

/* Restoring session task description */
"Restoring session..." = "セッションを復元しています...";

/* return type label (Navigator) */
"Return Type" = "戻り値";

/* return type: %@ */
"return type: %@" = "戻り値の型: %@";

/* singular word for row */
"row" = "行";

/* plural word for rows */
"rows" = "行";

/* text showing how many rows are in the limited filter match */
"Rows %@ - %@ from filtered matches" = "行 %1$@ - %2$@ にフィルタリングされた";

/* text showing how many rows are in the limited result */
"Rows %@ - %@ of %@%@ from table" = "テーブルから %1$@ - %2$@ 行目 %3$@%4$@";

/* Table Info Section : number of rows (exact value) */
"rows: %@" = "行数: %@";

/* Table Info Section : number of rows (estimated value) */
"rows: ~%@" = "行数: ~%@";

/* run all button */
"Run All" = "すべて実行";

/* Run All menu item title */
"Run All Queries" = "すべてのクエリを実行";

/* Title of button to run current query in custom query view */
"Run Current" = "現在の実行";

/* Title of action menu item to run current query in custom query view */
"Run Current Query" = "現在のクエリを実行";

/* toolbar item label for switching to the Run Query tab */
"Run Custom Query" = "カスタムクエリの実行";

/* Title of button to run query just before text caret in custom query view */
"Run Previous" = "前回の実行";

/* Title of action menu item to run query just before text caret in custom query view */
"Run Previous Query" = "前のクエリを実行";

/* Title of action menu item to run selected text in custom query view */
"Run Selected Text" = "選択したテキストを実行";

/* Title of button to run selected text in custom query view */
"Run Selection" = "選択を実行";

/* Running multiple queries string */
"Running query %i of %lu..." = "クエリ %1$i / %2$lu を実行中...";

/* Running multiple queries string */
"Running query %ld of %lu..." = "クエリ %1$ld / %2$lu を実行中...";

/* Running single query string */
"Running query..." = "クエリを実行中...";

/* Save trigger button label */
"Save" = "保存";

/* Save All to Favorites */
"Save All to Favorites" = "すべてをお気に入りに保存";

/* save as button title */
"Save As..." = "別名で保存...";

/* Bundle Editor : BLOB dropdown : 'save BLOB as dat file' item */
"save BLOB as dat file" = "BLOB を dat ファイルとして保存";

/* Bundle Editor : BLOB dropdown : 'save BLOB as image file' item */
"save BLOB as image file" = "BLOBを画像ファイルとして保存";

/* Save Current Query to Favorites */
"Save Current Query to Favorites" = "現在のクエリをお気に入りに保存";

/* save page as menu item title */
"Save Page As…" = "ページを別名で保存…";

/* Save Queries… */
"Save Queries…" = "クエリを保存…";

/* Save Query… */
"Save Query…" = "クエリを保存…";

/* Save Selection to Favorites */
"Save Selection to Favorites" = "選択範囲をお気に入りに保存";

/* save view as button title */
"Save View As..." = "ビューを保存...";

/* schema path header for completion tooltip */
"Schema path:" = "スキーマパス:";

/* Search in MySQL Documentation */
"Search in MySQL Documentation" = "MySQL ドキュメント内で検索";

/* Search in MySQL Help */
"Search in MySQL Help" = "MySQLのヘルプで検索";

/* Select Active Query */
"Select Active Query" = "アクティブなクエリを選択";

/* toolbar item for selecting a db */
"Select Database" = "データベースを選択";

/* selected items */
"selected items" = "選択した項目";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as comma-separated' item */
"Selected Rows (CSV)" = "選択行（CSV）";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as SQL' item */
"Selected Rows (SQL)" = "選択行（SQL）";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as tab-separated' item */
"Selected Rows (TSV)" = "選択行（TSV）";

/* Bundle Editor : Scope=Field : Input source dropdown: 'selected text' item */
"Selected Text" = "テキストを選択";

/* selection label for color table (Prefs > Editor) */
"Selection" = "選択";

/* table structure : indexes : delete index : no columns error : description */
"Sequel Ace could not find any columns belonging to this index. Maybe it has been removed already?" = "Sequel Aceはこのインデックスに属する列を見つけることができませんでした。多分それは既に削除されていますか？";

/* Preferences : Network : Custom SSH client : warning dialog message */
"Sequel Ace only supports and is tested with the default OpenSSH client versions included with macOS. Using different clients might cause connection issues, security risks or not work at all.\n\nPlease be aware, that we cannot provide support for such configurations." = "Sequel Aceは、macOSに含まれているデフォルトのOpenSSHクライアントバージョンのみをサポートし、テストされています。異なるクライアントを使用すると、接続の問題やセキュリティリスクが発生したり、まったく機能しなくなったりする可能性があります。\n\nこのような構成のサポートは提供できないことに注意してください。";

/* sequelace URL scheme command not supported. */
"sequelace URL scheme command not supported." = "Sequel Ace URL scheme コマンドはサポートされていません。";

/* sequelace url Scheme Error */
"sequelace URL Scheme Error" = "シーケンスURLスキームエラー";

/* Table Structure : Collation dropdown : 'item is the same as the collation of server' marker
 Table Structure : Encoding dropdown : 'item is server default' marker */
"Server" = "サーバ";

/* Add Database : Charset dropdown : default item ($1 = charset name)
 Add Database : Collation dropdown : default item ($1 = collation name) */
"Server Default (%@)" = "サーバーのデフォルト (%@)";

/* server processes window title (var = hostname) */
"Server Processes on %@" = "%@ のサーバープロセス";

/* Initial filename for 'Save session' file */
"Session" = "セッション";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html' item */
"Show as HTML" = "HTML 表示";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html tooltip' item */
"Show as HTML Tooltip" = "HTML ツールチップとして表示";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as text tooltip' item */
"Show as Text Tooltip" = "テキストツールチップとして表示";

/* show console */
"Show Console" = "コンソールを表示";

/* show create func syntax menu item */
"Show Create Function Syntax..." = "関数の構文を表示...";

/* show create proc syntax menu item */
"Show Create Procedure Syntax..." = "プロシージャの構文を表示...";

/* show create syntaxes menu item */
"Show Create Syntaxes..." = "構文を表示...";

/* show create table syntax menu item */
"Show Create Table Syntax..." = "テーブルの構文を表示...";

/* show create view syntax menu item */
"Show Create View Syntax..." = "ビューの構文を表示...";

/* Show detail button */
"Show Detail" = "詳細を表示";

/* MySQL Help Viewer : Results list : Link tooltip */
"Show MySQL help for %@" = "%@ の MySQL ヘルプを表示";

/* show navigator */
"Show Navigator" = "ナビゲーションを表示する";

/* show tab bar */
"Show Tab Bar" = "タブバーを表示";

/* tooltip for toolbar item for show console */
"Show the console which shows all MySQL commands performed by Sequel Ace" = "Sequel Aceによって実行されるすべてのMySQLコマンドを表示するコンソールを表示します";

/* Show Toolbar menu item */
"Show Toolbar" = "ツールバーを表示";

/* filtered item count */
"Showing %lu of %lu processes" = "%1$lu 個中の %2$lu 個のプロセスを表示中";

/* shutdown server : confirmation dialog : shutdown button */
"Shutdown" = "シャット ダウン";

/* shutdown server : error dialog : title */
"Shutdown failed!" = "シャットダウン失敗！";

/* range of integer types */
"Signed: %@ to %@\nUnsigned: %@ to %@" = "署名済み: %1$@ to %2$@\n未署名: %3$@ to %4$@";

/* Table Info Section : table size on disk */
"size: %@" = "サイズ：%@";

/* skip existing button */
"Skip existing" = "既存をスキップ";

/* skip problems button */
"Skip problems" = "問題をスキップ";

/* beta build label */
"Beta Build" = "ベータビルド";

/* socket connection failed title */
"Socket connection failed!" = "ソケット接続に失敗しました！";

/* socket not found title */
"Socket not found!" = "ソケットが見つかりません！";

/* Some export folders not writable explanatory text */
"Some of the target export folders are not writable.  Please select a new export location and try again." = "いくつかのターゲットエクスポートフォルダが書き込み可能ではありません。新しいエクスポート場所を選択して、もう一度お試しください。";

/* Some export folders missing explanatory text */
"Some of the target export folders no longer exist.  Please select a new export location and try again." = "ターゲットのエクスポートフォルダが存在しません。新しいエクスポート場所を選択して、もう一度お試しください。";

/* Sorting table task description */
"Sorting table..." = "テーブルをソートしています...";

/* spatial index menu item title */
"SPATIAL" = "SPATIAL";

/* sql data access label (Navigator) */
"SQL Data Access" = "SQL データアクセス";


/* Connection Secured via SSL information text */
"Connection Secured via SSL" = "SSL経由で接続が保護されました";

/* SSH connected titlebar marker */
"SSH Connected" = "SSH 接続済み";

/* SSH connecting very short status message */
"SSH connecting..." = "SSH接続中...";

/* SSH connecting titlebar marker */
"SSH Connecting…" = "SSH接続中…";

/* SSH connection failed title */
"SSH connection failed!" = "SSH接続に失敗しました";

/* SSH disconnected titlebar marker */
"SSH Disconnected" = "SSH接続が切断されました";

/* SSH key check error */
"SSH Key not found" = "SSH鍵が見つかりません";

/* title when ssh tunnel port forwarding failed */
"SSH port forwarding failed" = "SSHポートの転送に失敗しました";

/* SSL certificate authority file check error */
"SSL Certificate Authority File not found" = "SSL認証局ファイルが見つかりません";

/* SSL certificate file check error */
"SSL Certificate File not found" = "SSL 証明書ファイルが見つかりません";

/* SSL requested but not used title */
"SSL connection not established" = "SSL 接続が確立されていません";

/* SSL key file check error */
"SSL Key File not found" = "SSH鍵が見つかりません";

/* Standard memory export summary */
"Standard memory" = "標準メモリ";

/* started */
"started" = "開始しました";

/* status file for sequelace url scheme command couldn't be written error message */
"Status file for sequelace url scheme command couldn't be written!" = "シーケンスURLスキームコマンドの状態ファイルを書き込めませんでした！";

/* stop button */
"Stop" = "停止";

/* Stop queries string */
"Stop queries" = "クエリを停止";

/* Stop query string */
"Stop query" = "クエリを停止";

/* description of timestamp */
"Stores a date and time of day as seconds since the beginning of the UNIX epoch (1970-01-01 00:00:00). The values displayed/stored are affected by the connection's @@time_zone setting.\nThe representation is the same as for DATETIME. Invalid values, as well as \"second zero\", are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F. Some additional rules may apply." = "日付と時刻をUNIX時代(1970-01-01 00:00:00)の始まりから秒単位で保存します。 表示/保存される値は、接続の @time_zone 設定の影響を受けます。\n表現は DATEME と同じです。 「second zero」と同様に無効な値は、0000-00-00 00:00.0に変換されます。MySQL 5では分数秒が追加されました。 .4 マイクロ秒 (6) までの精度を持つ。Fによって指定された追加の規則が適用される場合があります。";

/* description of datetime */
"Stores a date and time of day. The representation is YYYY-MM-DD HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "日付と時刻を保存します。表現は YYYY-MM-DD HH:MM:SS[] です。 *]、分数秒です。値はタイムゾーン設定の影響を受けません。 無効な値は0000-00-00 00:00:00.0に変換されます。MySQL 5.6.4では、Fで指定された精度のマイクロ秒 (6) までの分割秒が追加されました。";

/* description of date */
"Stores a date without time information. The representation is YYYY-MM-DD. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00." = "時刻情報のない日付を保存します。表現は YYYY-MM-DD です。 この値はタイムゾーン設定の影響を受けません。無効な値は0000-00-00に変換されます。";

/* description of time */
"Stores a time of day, duration or time interval. The representation is HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 00:00:00. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "時刻、期間、または時間間隔を保存します。表現は HH:MM:SS[] です。 *]、分数秒です。値はタイムゾーン設定の影響を受けません。 無効な値は00:00:00に変換されます。MySQL 5.6.4ではFで指定された精度のマイクロ秒(6)に分数秒が追加されました。";

/* toolbar item label for switching to the Table Structure tab */
"Structure" = "構造";

/* successfully analyzed all selected items message */
"Successfully analyzed all selected items." = "選択したすべての項目を正常に分析しました。";

/* analyze table successfully passed message */
"Successfully analyzed table." = "テーブルの分析に成功しました。";

/* successfully flushed all selected items message */
"Successfully flushed all selected items." = "選択したすべてのアイテムを正常にフラッシュしました。";

/* message of panel when successfully flushed privs */
"Successfully flushed privileges." = "権限をフラッシュしました。";

/* flush table successfully passed message */
"Successfully flushed table." = "テーブルを正常にフラッシュしました。";

/* successfully optimized all selected items message */
"Successfully optimized all selected items." = "選択したすべての項目を正常に最適化しました。";

/* optimize table successfully passed message */
"Successfully optimized table." = "テーブルの最適化に成功しました。";

/* successfully repaired all selected items message */
"Successfully repaired all selected items." = "選択したすべてのアイテムを正常に修復しました。";

/* repair table successfully passed message */
"Successfully repaired table." = "テーブルの修復に成功しました。";

/* tooltip for toolbar item for switching to the Run Query tab */
"Switch to the Run Query tab" = "実行クエリタブに切り替える";

/* tooltip for toolbar item for switching to the Table Content tab */
"Switch to the Table Content tab" = "テーブルコンテンツタブに切り替える";

/* tooltip for toolbar item for switching to the Table Info tab */
"Switch to the Table Info tab" = "テーブル情報タブに切り替える";

/* tooltip for toolbar item for switching to the Table Relations tab */
"Switch to the Table Relations tab" = "テーブルリレーションタブに切り替える";

/* tooltip for toolbar item for switching to the Table Structure tab */
"Switch to the Table Structure tab" = "テーブル構造タブに切り替える";

/* tooltip for toolbar item for switching to the Table Triggers tab */
"Switch to the Table Triggers tab" = "テーブルトリガータブに切り替える";

/* tooltip for toolbar item for switching to the User Manager tab */
"Switch to the User Manager tab" = "ユーザーマネージャータブに切り替える";

/* description for table syntax copied notification */
"Syntax for %@ table copied" = "%@ テーブルの構文がコピーされました";

/* table */
"table" = "テーブル";

/* csv export table heading
 table
 Table Structure : Collation dropdown : 'item is the same as the collation of table' marker
 Table Structure : Encoding dropdown : 'item is table default' marker */
"Table" = "テーブル";

/* export label showing that the app is fetching data for a specific table */
"Table %lu of %lu (%@): Fetching data..." = "テーブル %1$lu of %2$lu (%3$@): データを取得しています...";

/* export label showing app is fetching relations data for a specific table */
"Table %lu of %lu (%@): Fetching relations data..." = "テーブル %1$lu of %2$lu (%3$@): リレーションデータを取得しています...";

/* export label showing app if writing data for a specific table */
"Table %lu of %lu (%@): Writing data..." = "テーブル %1$lu of %2$lu (%3$@): データの書き込み...";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'table changed' item */
"Table changed" = "テーブルが変更されました";

/* table checksum message */
"Table checksum" = "テーブルチェックサム";

/* table checksum: %@ */
"Table checksum: %@" = "テーブルチェックサム: %@";

/* table content print heading */
"Table Content" = "テーブルの内容";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as comma-separated' item */
"Table Content (CSV)" = "テーブルの内容（CSV）";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as SQL' item */
"Table Content (SQL)" = "テーブルの内容（CSV）";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as tab-separated' item */
"Table Content (TSV)" = "テーブルの内容（TSV）";

/* toolbar item for navigation history */
"Table History" = "テーブルの履歴";

/* toolbar item label for switching to the Table Info tab */
"Table Info" = "テーブル情報";

/* header for table info pane */
"TABLE INFORMATION" = "テーブル情報";

/* table information print heading */
"Table Information" = "テーブル情報";

/* message of panel when no name is given for table */
"Table must have a name." = "テーブルには名前が必要です。";

/* general preference pane tooltip */
"Table Preferences" = "テーブルの設定";

/* toolbar item label for switching to the Table Relations tab */
"Table Relations" = "テーブルの関係";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table row changed' item */
"Table Row changed" = "テーブル行が変更されました";

/* table structure print heading */
"Table Structure" = "テーブル構造";

/* toolbar item label for switching to the Table Triggers tab */
"Table Triggers" = "テーブルのトリガー";

/* Add Relation sheet title, showing table name */
"Table: %@" = "テーブル:%@";

/* tables preference pane name */
"Tables" = "テーブル";

/* tables */
"tables" = "テーブル";

/* header for table list */
"TABLES" = "テーブル";

/* header for table & views list */
"TABLES & VIEWS" = "テーブルとビュー";

/* Connection test very short status message */
"Testing connection..." = "接続テスト中...";

/* MySQL connection test very short status message */
"Testing MySQL..." = "Testing MySQL...";

/* SSH testing very short status message */
"Testing SSH..." = "SSH をテストしています...";

/* text label for color table (Prefs > Editor) */
"Text" = "テキスト";

/* Text is too long. Maximum text length is set to %llu. */
"Text is too long. Maximum text length is set to %llu." = "テキストが長すぎます。最大文字数は %llu に設定されています。";

/* Release notes dialog title thanking user for upgrade */
"Thanks for updating Sequel Ace!" = "Sequel Ace!を更新していただきありがとうございます。";

/* Open Files : Bundle : Install-Error : Destination path already exists error dialog message */
"The Bundle ‘%@’ already exists." = "バンドル「%@」は既に存在します。";

/* Open Files : Bundle: UUID : UUID-Attribute is missing in bundle's command.plist file */
"The Bundle ‘%@’ has no UUID which is necessary to identify installed Bundles." = "バンドル数%@はインストールされたバンドルを識別するのに必要な UUID を持っていません。";

"‘%@’ Bundle contains legacy components" = "%@のバンドルにレガシーコンポーネントが含まれています";

"In these files:\n\n%@\n\nDo you still want to install the bundle?" = "これらのファイルで:\n\n%@\n\nバンドルをインストールしますか？";

/* message while reading a spf file which matches non-supported formats. */
"The chosen file %@ contains ‘%@’ data." = "選択されたファイル「%1$@」には、%2$@のデータが含まれています。";

/* the color theme ‘%@’ already exists. */
"The color theme ‘%@’ already exists." = "カラー '%@' は既に存在します。";

/* the connection is busy. please wait and try again tooltip */
"The connection is busy. Please wait and try again." = "接続中です。しばらくしてからもう一度お試しください。";

/* the connection of the active connection window is not identical tooltip */
"The connection of the active connection window is not identical." = "アクティブな接続ウィンドウの接続は同じではありません。";

/* Connection lost during import error message */
"The connection to the server was lost during the import.  The import is only partially complete." = "インポート中にサーバーへの接続が失われました。インポートは部分的にのみ完了します。";

/* Create syntax permission denied detail */
"The creation syntax could not be retrieved due to a permissions error.\n\nPlease check your user permissions with an administrator." = "権限エラーのため構文を取得できませんでした。\n\n管理者にてユーザー権限を確認してください。";

/* CSV file open error */
"The CSV file you selected could not be found or read." = "選択した CSV ファイルが見つかりませんでした。";

/* Error when CSV appears to have too many columns to import, probably due to line ending mismatch */
"The CSV was read as containing more than 512 columns, more than the maximum columns permitted for speed reasons by Sequel Ace.\n\nThis usually happens due to errors reading the CSV; please double-check the CSV to be imported and the line endings and escape characters at the bottom of the CSV selection dialog." = "CSVは、512以上の列を含むとして読み込まれ、Sequel Aceによって速度の理由で許容される最大列数を超えました。\n\nこれは通常、CSV の読み取りエラーによって発生します。 インポートするCSVと、CSV選択ダイアログ下部の行末とエスケープ文字を再確認してください。";

/* unsaved theme informative message */
"The current color theme is unsaved. Do you want to proceed without saving it?" = "現在の色のテーマは保存されていません。保存せずに続行しますか？";

/* Short important release note for swap of custom query buttons */
"The Custom Query \"Run\" and \"Run All\" button positions and their shortcuts have been swapped." = "カスタムクエリの実行と実行 すべてのボタンの位置とそれらのショートカットが交換されました。";

/* the following default bundles were updated:\n%@\nyour modifications were stored as “(user)”. */
"The following default Bundles were updated:\n%@\nYour modifications were stored as “(user)”." = "次のデフォルトのバンドルが更新されました:\n%@\nあなたの変更は\"(ユーザー)\"として保存されました。";

/* favorites export error informative message */
"The following error occurred during the export process:\n\n%@" = "エクスポート処理中に次のエラーが発生しました:\n\n%@";

/* favorites import error informative message */
"The following error occurred during the import process:\n\n%@" = "インポート中に次のエラーが発生しました:\n\n%@";

/* table structure : indexes : delete index : error 1553 : description */
"The foreign key relationship '%@' has a dependency on index '%@'. This relationship must be removed before the index can be deleted.\n\nAre you sure you want to continue to delete the relationship and the index? This action cannot be undone." = "外部キー関係 '%1$@'は、インデックス '%2$@'に依存しています。インデックスを削除する前に、この関係を削除する必要があります。\n\n関係とインデックスを引き続き削除してもよろしいですか？このアクションは元に戻せません。";

/* table list change alert message */
"The list of tables has changed" = "テーブルのリストが変更されました";

/* message when trying to rename a table/view/proc/etc to an already used name */
"The name '%@' is already used." = "%@ という名前は既に使用されています。";

/* table list change alert informative message */
"The number of tables in this database has changed since the export dialog was opened. There are now %lu additional table(s), most likely added by an external application.\n\nHow would you like to proceed?" = "エクスポート ダイアログが開かれてから、このデータベースのテーブル数が変更されました。 %lu テーブルが追加されました。おそらく外部アプリケーションによって追加されます。\n\n続行しますか？";

/* message of panel when no rows have been affected after writing to the db */
"The row was not written to the MySQL database. You probably haven't changed anything.\nReload the table to be sure that the row exists and use a primary key for your table.\n(This error can be turned off in the preferences.)" = "行はMySQLデータベースに書き込まれませんでした。おそらく何も変更していません。\nテーブルをリロードして、行が存在することを確認し、テーブルの主キーを使用してください。\n（このエラーは設定でオフにできます。）";

/* export : import settings : file version error description ($1 = is version, $2 = list of supported versions); note: the u00A0 is a non-breaking space, do not add more whitespace. */
"The selected export settings were stored with version %1$ld, but only settings with the following versions can be imported: %2$@.\n\nEither save the settings in a backwards compatible way or update your version of Sequel Ace." = "選択したエクスポート設定はバージョン%1$ldで保存されましたが、インポートできるのは次のバージョンの設定のみです：%2$@。\n\n下位互換性のある方法で設定を保存するか、SequelAceのバージョンを更新します。";

/* export : import settings : spf content type error description */
"The selected file contains data of type “%1$@”, but type “%2$@” is needed. Please choose a different file." = "選択されたファイルには「%1$@」タイプのデータが含まれていますが、「%2$@」タイプが必要です。別のファイルを選択してください。";

/* export : import settings : file error description */
"The selected file is either not a valid SPF file or severely corrupted." = "選択したファイルは有効なSPFファイルではないか、ひどく破損しています。";

/* error deleting relation informative message */
"The selected relation couldn't be deleted.\n\nMySQL said: %@" = "選択したリレーションを削除できませんでした。\n\nMySQL: %@";

/* error deleting trigger informative message */
"The selected trigger couldn't be deleted.\n\nMySQL said: %@" = "選択したトリガーを削除できませんでした。\n\nMySQL は %@ です。";

/* description of tinyint */
"The smallest integer type, requires 1 byte storage space. M is the optional display width and does not affect the possible value range." = "最小の整数型、1バイトのストレージスペースが必要です。Mはオプションの表示幅であり、可能な値の範囲には影響しません。";

/* message of panel when connection to socket failed because optional socket could not be found */
"The socket file could not be found in any common location. Please supply the correct socket location.\n\nMySQL said: %@" = "ソケットファイルが見つかりませんでした。正しいソケットの場所を指定してください。\n\nMySQL は %@ です。";

/* error creating relation informative message */
"The specified relation could not be created.\n\nMySQL said: %@" = "指定されたリレーションを作成できませんでした。\n\nMySQL は %@";

/* error creating trigger informative message */
"The specified trigger was unable to be created.\n\nMySQL said: %@" = "指定されたトリガーは作成できませんでした。\n\nMySQL は %@ です。";

/* sql import : charset error alert : detail message */
"The SQL file uses utf8mb4 encoding, but your MySQL version only supports the limited utf8 subset.\n\nYou can continue the import, but any non-BMP characters in the SQL file (eg. some typographic and scientific special characters, archaic CJK logograms, emojis) will be unrecoverably lost!" = "SQLファイルはutf8mb4エンコーディングを使用しますが、MySQLバージョンは制限付きのutf8サブセットのみをサポートしています。\n\nインポートは続けることができますが、SQL ファイル内の任意の BMP 以外の文字(例えば。 いくつかのタイポグラフィと科学的な特殊文字は、古風なCJKロゴグラム、絵文字が回復不能に失われます!";

/* SQL file open error */
"The SQL file you selected could not be found or read." = "選択した SQL ファイルを見つけることができませんでした。";

/* Prompt for SSH password when keychain fetch failed */
"The SSH password could not be loaded from the keychain; please enter the SSH password for %@:" = "SSHパスワードをキーチェーンから読み込めませんでした。 %@のSSHパスワードを入力してください:";

/* Prompt for SSH password when direct fetch failed */
"The SSH password could not be loaded; please enter the SSH password for %@:" = "SSHパスワードを読み込めませんでした。 %@のSSHパスワードを入力してください:";

/* SSH tunnel authentication failed message */
"The SSH Tunnel could not authenticate with the remote host. Please check your password and ensure you still have access." = "SSHトンネルはリモートホストで認証できませんでした。パスワードを確認し、まだアクセス可能であることを確認してください。";

/* SSH tunnel unexpectedly closed */
"The SSH Tunnel has unexpectedly closed." = "SSHトンネルは予期せず閉鎖されました。";

/* SSH tunnel was closed by remote host message */
"The SSH Tunnel was closed 'by the remote host'. This may indicate a networking issue or a network timeout." = "SSHトンネルは「リモートホストによって」クローズされました。これはネットワークの問題やネットワークタイムアウトを示している可能性があります。";

/* SSH tunnel forwarding port connection refused message */
"The SSH Tunnel was established successfully, but could not forward data to the remote port as the remote port refused the connection." = "SSHトンネルは正常に設置されましたが、リモートポートが接続を拒否したため、データをリモートポートに転送できませんでした。";

/* SSH tunnel unable to bind to local port message */
"The SSH Tunnel was unable to bind to the local port. This error may occur if you already have an SSH connection to the same server and are using a 'LocalForward' setting in your SSH configuration.\n\nWould you like to fall back to a standard connection to localhost in order to use the existing tunnel?" = "SSHトンネルはローカルポートにバインドできませんでした。 このエラーは、既に同じサーバーにSSH接続があり、SSH設定で「LocalForward」設定を使用している場合に発生する可能性があります。\n\n既存のトンネルを使用するために、localhost への標準接続に戻りますか?";

/* SSH tunnel failed or timed out message */
"The SSH Tunnel was unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds)." = "SSHトンネルがホスト%1$@に接続できなかったか、リクエストがタイムアウトしました。\n\nアドレスが正しく、必要な権限があることを確認するか、接続タイムアウト（現在は%2$ld）を増やしてみてください秒）。";

/* message of panel when loading of table failed and presumably due to used filter argument */
"The table data couldn't be loaded presumably due to used filter clause. \n\nMySQL said: %@" = "おそらく使用されたフィルタ句のためテーブルデータを読み込むことができませんでした。 \n\nMySQL は %@ と言いました。";

/* message of panel when loading of table failed */
"The table data couldn't be loaded.\n\nMySQL said: %@" = "テーブルデータを読み込めませんでした。\n\nMySQL は %@ です。";

/* Export folder not writable explanatory text */
"The target export folder is not writable.  Please select a new export location and try again." = "ターゲットのエクスポートフォルダは書き込み可能ではありません。新しいエクスポート場所を選択して、もう一度お試しください。";

/* Export folder not chosen by user explanatory text */
"No directory selected.  Please select a new export location and try again." = "ディレクトリが選択されていません。新しいエクスポート先を選択して再度お試し下さい。";
"No directory selected." = "ディレクトリが選択されていません。";
"Please select a new export location and try again." = "新しいエクスポート先を選択して、もう一度お試しください。";

/* Export folder missing explanatory text */
"The target export folder no longer exists.  Please select a new export location and try again." = "ターゲットのエクスポートフォルダが存在しません。新しいエクスポート場所を選択して再度お試し下さい。";

/* theme name label */
"Theme Name:" = "テーマ名:";

/* themes installation error */
"Themes Installation Error" = "テーマのインストールエラー";

/* message of panel when table content cannot be copied */
"There have been errors while copying table content. Please check the new table." = "テーブルコンテンツのコピー中にエラーが発生しました。新しいテーブルを確認してください。";

/* Cannot duplicate a table with triggers to a different database */
"Cannot duplicate a table with triggers to a different database." = "別のデータベースにトリガーを持つテーブルは複製できません。";

/* text shown when query was successfull */
"There were no errors." = "エラーはありませんでした。";

/* delete field and foreign key informative message */
"This field is part of a foreign key relationship with the table '%@'. This relationship must be removed before the field can be deleted.\n\nAre you sure you want to continue to delete the relationship and the field? This action cannot be undone." = "このフィールドはテーブル'%@'との外部キー関係の一部です。 フィールドを削除する前に、このリレーションシップを削除する必要があります。 format@@0\n\nリレーションシップとフィールドを削除してもよろしいですか？この操作は元に戻せません。";

/* table structure : indexes : delete index : error 1553, no FK found : description */
"This index cannot be deleted, because it is used by an existing foreign key relationship.\n\nPlease remove the relationship, before trying to remove this index.\n\nMySQL said: %@" = "既存の外部鍵関係で使用されているため、このインデックスは削除できません。\n\nこのインデックスを削除しようとする前に、リレーションシップを削除してください。\n\nMySQL は %@";

/* description of serial */
"This is an alias for BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE." = "これはBIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUEのエイリアスです。";

/* description of dec
 description of fixed
 description of numeric */
"This is an alias for DECIMAL." = "これはDECIMALのエイリアスです。";

/* description of double real */
"This is an alias for DOUBLE, unless REAL_AS_FLOAT is configured." = "REAL_AS_FLOATが設定されていない限り、これはDOUBLEのエイリアスです。";

/* description of double precision */
"This is an alias for DOUBLE." = "これはDECIMALのエイリアスです。";

/* description of bool
 description of boolean */
"This is an alias for TINYINT(1)." = "これはTINYINT(1)の別名です。";

/* Table Structure : Collation dropdown : database marker tooltip */
"This is the default collation of database %@." = "これはデータベース %@ のデフォルトの照合順序です。";

/* Table Structure : Collation dropdown : default marker tooltip */
"This is the default collation of encoding %@." = "エンコーディング %@のデフォルトの照合順序です。";

/* Table Structure : Collation dropdown : table marker tooltip */
"This is the default collation of table %@." = "これはテーブル %@ のデフォルトの照合順序です。";

/* Table Structure : Collation dropdown : server marker tooltip */
"This is the default collation of this server." = "これはこのサーバーのデフォルトの照合順序です。";

/* Table Structure : Encoding dropdown : database marker tooltip */
"This is the default encoding of database %@." = "これはデータベース %@ のデフォルトエンコーディングです。";

/* Table Structure : Encoding dropdown : table marker tooltip */
"This is the default encoding of table %@." = "これはテーブル %@ のデフォルトエンコーディングです。";

/* Table Structure : Encoding dropdown : server marker tooltip */
"This is the default encoding of this server." = "これはこのサーバーのデフォルトのエンコーディングです。";

/* This table currently does not support relations. Only tables that use the InnoDB storage engine support them. */
"This table currently does not support relations. Only tables that use the InnoDB storage engine support them." = "このテーブルは現在リレーションをサポートしていません。InnoDB ストレージ エンジンを使用するテーブルのみがサポートしています。";

/* user has no hosts informative message */
"This user doesn't have any hosts associated with it. It will be deleted unless one is added" = "このユーザーは関連付けられたホストを持っていません。追加されない限り削除されます";

/* error removing host informative message */
"This user doesn't seem to have any associated hosts and will be removed unless a host is added." = "このユーザーは関連付けられたホストを持っていないため、ホストが追加されない限り削除されます。";

/* shutdown server : confirmation dialog : message */
"This will wait for open transactions to complete and then quit the mysql daemon. Afterwards neither you nor anyone else can connect to this database!\n\nFull management access to the server's operating system is required to restart MySQL!" = "これにより、オープンなトランザクションが完了し、mysql デーモンを終了するのを待機します。 その後、あなたも他の誰もこのデータベースに接続できません！\n\nMySQLを再起動するには、サーバーのオペレーティングシステムへの完全な管理アクセスが必要です。";

/* export filename time token */
"Time" = "時刻";

/* toolbar item label for switching to the Table Triggers tab */
"Triggers" = "トリガー";

/* triggers for table label */
"Triggers for table: %@" = "テーブルのトリガー: %@";

/* truncate button */
"Truncate" = "切り捨て";

/* truncate tables message */
"Truncate selected tables?" = "選択したテーブルを切り捨てますか？";

/* truncate table menu title */
"Truncate Table..." = "テーブルを切り詰める...";

/* truncate table message */
"Truncate table '%@'?" = "テーブル'%@'を切り詰める'?";

/* truncate tables menu item */
"Truncate Tables" = "テーブルを切り詰める";

/* type label (Navigator) */
"Type" = "タイプ";

/* type declaration header */
"Type Declaration:" = "型宣言:";

/* add index error message */
"Unable to add index" = "インデックスを追加できません";

/* unable to analyze selected items message */
"Unable to analyze selected items" = "選択した項目を分析できません";

/* unable to analyze table message */
"Unable to analyze table" = "テーブルを分析できません";

/* unable to check selected items message */
"Unable to check selected items" = "選択したアイテムを確認できません";

/* unable to check table message */
"Unable to check table" = "テーブルを確認できません";

/* message of panel when connection to host failed due to access denied error */
"Unable to connect to host %@ because access was denied.\n\nDouble-check your username and password and ensure that access from your current location is permitted.\n\nMySQL said: %@" = "アクセスが拒否されたため、ホスト%1$@に接続できません。\n\nユーザー名とパスワードを再確認し、現在の場所からのアクセスが許可されていることを確認してください。\n\nMySQLによると：%2$@";

/* message of panel when SSH port forwarding failed */
"Unable to connect to host %@ because the port connection via SSH was refused.\n\nPlease ensure that your MySQL host is set up to allow TCP/IP connections (no --skip-networking) and is configured to allow connections from the host you are tunnelling via.\n\nYou may also want to check the port is correct and that you have the necessary privileges.\n\nChecking the error detail will show the SSH debug log which may provide more details.\n\nMySQL said: %@" = "SSH経由のポート接続が拒否されたため、ホスト%1$@に接続できません。\n\nMySQLホストがTCP / IP接続を許可するように設定され（no --skip-networking）、トンネリングを介してトンネリングしているホストからの接続を許可するように構成されていることを確認してください。\n\nポートが正しいことと、必要な権限があることを確認することもできます。\n\nエラーの詳細を確認すると、SSHデバッグログが表示され、詳細が提供される場合があります。\n\nMySQL said: %2$@";

/* message of panel when connection to host failed */
"Unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds).\n\nMySQL said: %@" = "ホスト%1$@に接続できないか、リクエストがタイムアウトしました。\n\nアドレスが正しく、必要な権限があることを確認するか、接続タイムアウト（現在は%2$ld秒）を増やしてみてください。\n\nMySQLによると：%3$@";

/* message of panel when connection to host failed */
"Unable to connect via the socket, or the request timed out.\n\nDouble-check that the socket path is correct and that you have the necessary privileges, and that the server is running.\n\nMySQL said: %@" = "ソケット経由で接続できないか、リクエストがタイムアウトしました。\n\nソケットパスが正しいこと、必要な権限があること、サーバーが実行されていることを再確認してください。\n\nMySQLによると：%@";

/* unable to copy database message */
"Unable to copy database" = "データベースをコピーできません";

/* error deleting index message */
"Unable to delete index" = "インデックスを削除できません";

/* error deleting relation message */
"Unable to delete relation" = "リレーションを削除できません";

/* error deleting trigger message */
"Unable to delete trigger" = "トリガーを削除できません";

/* unable to flush selected items message */
"Unable to flush selected items" = "選択したアイテムをフラッシュできません";

/* unable to flush table message */
"Unable to flush table" = "テーブルをフラッシュできません";

/* unable to get list of users message */
"Unable to get list of users" = "ユーザーのリストを取得できません";

/* error killing connection message */
"Unable to kill connection" = "接続を終了できません";

/* error killing query message */
"Unable to kill query" = "クエリを終了できません";

/* unable to optimze selected items message */
"Unable to optimze selected items" = "選択したアイテムを最適化できません";

/* unable to optimze table message */
"Unable to optimze table" = "テーブルを最適化できません";

/* unable to perform the checksum */
"Unable to perform the checksum" = "チェックサムを実行できません";

/* error removing host message */
"Unable to remove host" = "ホストを削除できません";

/* unable to rename database message */
"Unable to rename database" = "データベースの名前が変更できませんでした";

/* unable to repair selected items message */
"Unable to repair selected items" = "選択したアイテムを修理できません";

/* unable to repair table message */
"Unable to repair table" = "テーブルを修復できません";

/* message of panel when connection to db failed after selecting from popupbutton */
"Unable to select database %@.\nPlease check you have the necessary privileges to view the database, and that the database still exists." = "データベース %@を選択できません。\nデータベースを表示するために必要な権限があり、データベースが存在することを確認してください。";

/* Unable to write row error */
"Unable to write row" = "行を書き込むことができません";

/* Table Content : Remove Row : Result : n Error title */
"Unexpected number of rows removed!" = "想定外の行数が削除されました！";

/* warning */
"Unknown file format" = "不明なファイル形式";

/* unsaved changes message */
"Unsaved changes" = "保存されていない変更";

/* unsaved theme message */
"Unsaved Theme" = "未保存のテーマ";

/* Preferences : Network : Custom SSH client : warning dialog title */
"Unsupported configuration!" = "サポートされていない設定です！";

/* export : import settings : file version error title */
"Unsupported version for export settings!" = "エクスポート設定でサポートされていないバージョンです！";

/* Name for an untitled connection */
"Untitled" = "無題";

/* Title of a new Sequel Ace Document */
"Untitled %ld" = "無題の %ld";

/* range for mediumblob type */
"Up to %@ bytes (16 MiB)" = "最大 %@ バイト (16 MiB)";

/* range for longblob type */
"Up to %@ bytes (4 GiB)" = "最大 %@ バイト (4 GiB)";

/* range for mediumtext type */
"Up to %@ characters (16 MiB)" = "最大 %@ 文字 (16 MiB)";

/* range for enum type */
"Up to %@ distinct members (<%@ in practice)\n1-2 bytes storage" = "最大 %1$@ 個の異なるメンバー (<%2$@ 実際)\n1-2 バイトのストレージ";

/* range for tinyblob type */
"Up to 255 bytes" = "最大 255 バイト";

/* range for tinytext type */
"Up to 255 characters" = "最大 255 文字";

/* Open Files : Bundle : Already-Installed : Update button */
"Update" = "更新";

/* updated: %@ */
"updated: %@" = "更新: %@";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field of table `%2$@` was changed." = "フィールドコンテンツの更新に失敗しました。フィールドの原点を明確に識別できませんでした（%1$ldが一致します）。テーブル `%2$@`のこのフィールドの編集中に変更された可能性が非常に高いです。";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field the table `%2$@` was changed by an other user." = "フィールドコンテンツの更新に失敗しました。フィールドの原点を明確に識別できませんでした（%1$ldが一致します）。このフィールドの編集中に、テーブル `%2$@`が他のユーザーによって変更された可能性があります。";

/* updating field task description */
"Updating field data..." = "フィールドデータを更新しています...";

/* URL scheme command couldn't authenticated */
"URL scheme command couldn't authenticated" = "URLスキームコマンドは認証できませんでした";

/* URL scheme command was terminated by user */
"URL scheme command was terminated by user" = "URLスキームコマンドはユーザーによって終了されました";

/* URL scheme command %@ unsupported */
"URL scheme command %@ unsupported" = "URL スキームコマンド %@ はサポートされていません";

/* Use 127.0.0.1 button */
"Use 127.0.0.1" = "127.0.0.1を使用";

/* use standard connection button */
"Use Standard Connection" = "標準接続を使用";

/* user has no hosts message */
"User has no hosts" = "ユーザーにホストがありません";

/* user-defined value */
"User-defined value" = "ユーザー定義値";

/* toolbar item label for switching to the User Manager tab */
"Users" = "ユーザ";

/* CSV Field Mapping : Table View : Tooltip for fields with NULL value */
"Value will be imported as MySQL NULL" = "値はMySQL NULLとしてインポートされます";

/* variable label for color table (Prefs > Editor) */
"Variable" = "変数";

/* version */
"version" = "バージョン";

/* export header version label */
"Version" = "バージョン";

/* view */
"view" = "表示";

/* Release notes button title */
"View full release notes" = "全てのリリースノートを表示";

/* header for view info pane */
"VIEW INFORMATION" = "ビュー情報";

/* view html source code menu item title */
"View Source" = "ソースを表示";

/* view structure print heading */
"View Structure" = "ストラクチャーを表示";

/* views */
"views" = "表示";

/* warning */
"Warning" = "警告";

/* Short important release note for why password prompts may occur */
"We've changed Sequel Ace's digital signature for GateKeeper compatibility; you'll have to allow access to your passwords again." = "GateKeeperの互換性のためにSequel Aceのデジタル署名を変更しました。パスワードへのアクセスを再度許可する必要があります。";

/* Important release notes informational text, single change */
"We've made a few changes but we thought you should know about one particularly important one:" = "いくつか変更を加えましたが、特に重要なことを知っておくべきだと思いました。";

/* Important release notes informational text, multiple changes */
"We've made a few changes but we thought you should know about some particularly important ones:" = "いくつかの変更を加えましたが、特に重要なものについて知っておくべきだと思いました。";

/* WHERE clause not valid */
"WHERE clause not valid" = "WHERE句が無効です";

/* Title of filter preview area when the query WHERE is negated */
"WHERE NOT query" = "WHERE NOTクェリー";

/* Title of filter preview area when the query WHERE is normal */
"WHERE query" = "WHEREクエリ";

/* Generic working description */
"Working..." = "作業中...";

/* export label showing app is writing data */
"Writing data..." = "データを書き込み中...";

/* text showing that app is writing text file */
"Writing..." = "書き込み中..";

/* wrong data format or password */
"Wrong data format or password." = "間違ったデータ形式またはパスワード。";

/* wrong data format */
"Wrong data format." = "データ形式が間違っています。";

/* export : import settings : spf content type error title */
"Wrong SPF content type!" = "SPFコンテンツタイプが間違っています！";

/* export filename date token */
"Year" = "年";

/* message of panel when trying to copy multiple rows */
"You can only copy single rows." = "コピーできるのは1行のみです。";

/* message of panel when trying to edit tables without index and with hidden blob/text fields */
"You can't hide blob and text fields when working with tables without index." = "インデックスのないテーブルで作業する場合、Blob フィールドとテキストフィールドは非表示にできません。";

/* You cannot delete the last field in a table. Delete the table instead. */
"You cannot delete the last field in a table. Delete the table instead." = "テーブルの最後のフィールドを削除することはできません。代わりにテーブルを削除してください。";

/* SSL connection requested but not established error detail */
"You requested that the connection should be established using SSL, but MySQL made the connection without SSL.\n\nThis may be because the server does not support SSL connections, or has SSL disabled; or insufficient details were supplied to establish an SSL connection.\n\nThis connection is not encrypted." = "SSLを使用して接続を確立するように要求しましたが、MySQLはSSLなしで接続しました。\n\nこれは、サーバーがSSL接続をサポートしていないか、SSLが無効になっていることが原因である可能性があります。または、SSL接続を確立するために十分な詳細が提供されていません。\n\nこの接続は暗号化されていません。";

/* Query Favorites : List : Section Heading : current connection document : tooltip (arg is the name of the spf file) */
"‘%@’ based favorites" = "%@をベースにしたお気に入り";

/* table column header. Read: 'Showing all content filters for fields of type %@' (ContentFilterManager) */
"‘%@’ Fields Content Filters" = "%@のフィールドコンテンツフィルター";

/* Export file already exists message */
"%@ already exists. Do you want to replace it?" = "%@ は既に存在します。置き換えますか？";

/* Bundle Editor : Outline View : Bundle item : tooltip */
"%@ Bundle" = "%@ バンドル";

/* Export file creation error title */
"%@ could not be created" = "%@ を作成できませんでした";

/* %@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item. */
"%@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item." = "%@ は解析できませんでした。 カラムの設定は編集できますが、カラムはコンテンツビューに表示されません。 この問題は、ヘルプメニュー項目を使用して、Sequel Aceチームに報告してください。";

/* connection view : ssl : client cert file picker : wrong format error title */
"%@ is not a valid client certificate file." = "%@ は有効なクライアント証明書ファイルではありません。";

/* connection view : ssl : key file picker : wrong format error title */
"%@ is not a valid private key file." = "%@ は有効な秘密鍵ファイルではありません。";

/* text shown when there are App Sandbox Issues */
"App Sandbox Issue" = "App Sandbo の問題";

/* Stale Bookmarks error title */
"Stale Bookmarks" = "古いブックマーク";

/* App Sandbox info link text */
"App Sandbox Info" = "アプリのサンドボックス情報";

/* error while selecting file title */
"File Selection Error" = "ファイル選択エラー";

/* error while selecting file message */
"The selected file is not a valid file.\n\nPlease try again.\n\nClass: %@" = "選択したファイルは有効なファイルではありません。\n\nもう一度やり直してください。\n\nクラス: %@";

/* known hosts not writable error message */
"The selected known hosts file is not writable.\n\n%@\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "選択された既知のホストファイルは書き込み可能ではありません。\n\n%@\n\nSequel Aceの環境設定でファイルを再度選択して、もう一度やり直してください。";

/* known hosts is invalid message */
"The selected known hosts file is invalid.\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "選択された既知のホストファイルは無効です。\n\nSequel Aceの環境設定でファイルを再度選択し、もう一度やり直してください。";

/* known hosts contains quote message */
"The selected known hosts file contains a quote (\") in its file path which is not supported.\n\n%@\n\nPlease select a different file in Sequel Ace's Preferences or rename the file/path to remove the quote." = "選択された既知のホストファイルのファイルパスに、サポートされていない引用符 (\") が含まれています。\n\n%@\n\nSequel Aceの環境設定で別のファイルを選択するか、ファイル名あるいはパス名を変更して引用符を取り除いてください。";

/* SSH Tunnel Debugging menu and window title */
"SSH Tunnel Debugging Info" = "SSHトンネルのデバッグ情報";

/* Question to user to see if they would like to re-request access */
"You have stale secure bookmarks:\n\n%@\n\nWould you like to re-request access now?" = "古い安全なブックマークがあります:\n\n%@\n\n今すぐアクセスをリクエストしますか？";

/* Question to user to see if they would like to request access to missing bookmarks */
"You have missing secure bookmarks:\n\n%@\n\nWould you like to request access now?" = "安全なブックマークがありません:\n\n%@\n\n今すぐアクセスをリクエストしますか？";

/* known hosts advance menu item title */
"Use known hosts from ssh config (ADVANCED)" = "ssh config (ADVANCED) から既知のホストを使用する";

/* The answer, yes */
"Yes" = "はい";

/* Shown to remind users of their stale bookmarks */
"A reminder of your stale secure bookmarks:<br /><br />%@<br />" = "古い安全なブックマークのリマインダー:<br /><br />%@<br />";

/* Title for Stale Secure Bookmarks help window */
"Stale Secure Bookmarks" = "古いセキュアブックマーク";

/* Title for Export Error alert */
"Export Error" = "エクスポートエラー";

/* Message for Export Error alert */
"Error while writing to the export file. Could not open file: %@" = "エクスポートファイルに書き込み中にエラーが発生しました: %@";

/* Message for User window error */
"Resultset from mysql.user contains neither 'Password' nor 'authentication_string' column." = "mysql.user からの結果セットには、'Password' と 'authentication_string' 列は含まれていません。";

/* Title for User window error */
"User Data Error" = "ユーザデータのエラー";

/* Title for Stale Bookmark file selection dialog */
"Please re-select the file '%@' in order to restore Sequel Ace's access." = "Sequel Aceのアクセスを復元するには、ファイル '%@' を再度選択してください。";

/* Title for File Preference file selection dialog */
"Please choose a file or folder to grant Sequel Ace access to." = "Sequel Aceへのアクセスを許可するファイルまたはフォルダを選択してください。";

/* Title for Network Preference ssh config file selection dialog */
"Please choose your ssh config files(s)" = "ssh 設定ファイルを選択してください";

/* Title for Network Preference ssh known hosts file selection dialog */
"Please choose your known hosts file" = "既知のホストファイルを選択してください";

/* Message for field editor JSON segment when JSON is invalid */
"Invalid JSON" = "不正なJSON";

/* Applying syntax highlighting task description */
"Applying syntax highlighting..." = "シンタックスハイライトを適用しています...";

/* Error message when a bash task fails to start */
"Couldn't launch task.\nException reason: %@\n ENV length: %lu" = "タスクを起動できませんでした。\n例外理由: %@\n ENV 長さ: %lu";

/* Error message title when we failed to create new connection view */
"New Connection Error" = "新しい接続エラー";

/* New Connection Error informative message */
"Failed to create new database connection window. Please restart Sequel Ace and try again." = "新しいデータベース接続ウィンドウの作成に失敗しました。シーケンスエースを再起動してもう一度お試しください。";

/* new version is available alert title */
"A new version is available" = "新しいバージョンが利用可能です";

/* new version is available download button title */
"Download" = "ダウンロード";

/* new version is available alert informativeText */
"Version %@ is available. You are currently running %@" = "バージョン %@ は利用可能です。現在 %@ を実行しています。";

/* downloading new version window title */
"Download Progress" = "ダウンロード進行中";

/* downloading new version time remaining placeholder text */
"Calculating time remaining..." = "残り時間を計算しています...";

/* downloading new version window message */
"Downloading Sequel Ace - %@" = "Sequel Aceをダウンロード中 - %@";

/* downloading new version time remaining estimate text */
"About %.1f seconds left" = "約 %.1f 秒後";

/* downloading new version failure alert title */
"Download Failed" = "ダウンロード失敗";

/* Tool tip for the Show alert when update is available preference on the Notifications pane */
"Only available for GitHub downloads" = "GitHubダウンロードでのみ利用可能";

/* Tool tip warning for when the user sets auto-complete delay to zero */
"WARNING: Setting the auto-complete delay to 0.0 can result in strange output." = "警告: 自動補完の遅延を 0.0 に設定すると、奇妙な出力になります。";

/* downloading new version bytes of bytes done */
"%@ of %@" = "%@ / %@";

/* Time zone set failed informative message */
"\n\ntime_zone will be set to SYSTEM." = "\n\ntime_zone は SYSTEM.";

/* Menu item title for checking for updates */
"Check for Updates..." = "アップデートの確認...";

/* Warning message during connection in case the variable skip-show-database is set to ON */
"The skip-show-database variable of the database server is set to ON. Thus, you won't be able to list databases unless you have the SHOW DATABASES privilege.\n\nHowever, the databases are still accessible directly through SQL queries depending on your privileges." = "データベースサーバーのskip-show-database変数はONに設定されています。 したがって、データの表示権限を持たない限り、データベースを一覧表示できません。\n\nただし、特権によってはSQLクエリを介してデータベースに直接アクセスできます。";

/* Error alert title when the request to GitHub fails */
"GitHub Request Failed" = "GitHub リクエストに失敗しました";

/* Info alert title when there are Newer Releases Available */
"No Newer Release Available" = "新しいリリースはありません";

/* Info alert message when there are Newer Releases Available */
"You are currently running the latest release." = "現在最新のリリースを実行しています。";

/* Alert button, disable the warning when skip-show-database is set to on */
"Never show this again" = "次回から表示しない";

/* Info alert when editing a generated column */
"The current field \"%@\" is a generated column and therefore cannot be edited." = "現在のフィールド \"%@\" は生成された列であるため、編集できません。";

/* Release note / info modal about change in latest release about column behavior */
"The use of the \"default\" column has changed since the last version of Sequel ACE:\n\n- No default value : Leave it blank.\n- String value : Use single '' or double quotes \"\" if you want an empty string or to wrap a string\n- Expression : Use parentheses (). Except for the TIMESTAMP and DATETIME columns where you can specify the CURRENT_TIMESTAMP function without enclosing parentheses." = "列のデフォルト値の用法がSequel Aceの最終バージョンから変更されています:\n\n- デフォルト値なし : 無指定のままとする。\n- 文字列値 : 空文字列の使用や文字列の折り返しをする場合、引用符 '' か二重引用符 \"\" を使う。\n- 式 : 括弧 () を使う。ただし、括弧なしでCURRENT_TIMESTAMPを指定可能なTIMESTAMP列とDATETIME列を除く。";

/* Menu option to pin a view to the top of the list of tables */
"Pin View" = "ビューをピンで固定";

/* Menu option to pin a table to the top of the list of tables */
"Pin Table" = "テーブルをピンで固定";

/* Menu option to pin a procedure to the top of the list of tables */
"Pin Procedure" = "プロシージャをピンで固定";

/* Menu option to pin a function to the top of the list of tables */
"Pin Function" = "関数をピンで固定";

/* Menu option to unpin a view which is currently pinned at the top of the list of tables */
"Unpin View" = "ビューのピン固定を解除";

/* Menu option to unpin a table which is currently pinned at the top of the list of tables */
"Unpin Table" = "テーブルのピン固定を解除";

/* Menu option to unpin a procedure which is currently pinned at the top of the list of tables */
"Unpin Procedure" = "プロシージャのピン固定を解除";

/* Menu option to unpin a function which is currently pinned at the top of the list of tables */
"Unpin Function" = "関数のピン固定を解除";

/* header for pinned table list */
"PINNED" = "ピンで固定";

/* Menu option to copy a table's name with database name in the list of tables */
"Copy Table Name" = "テーブル名をコピー";

/* sequelace URL scheme error: LaunchFavorite alert title (favorite name not found) */
"LaunchFavorite URL Scheme Error" = "LaunchFavorite URL Scheme Error";

/* sequelace URL scheme error: LaunchFavorite alert description (favorite name not found) */
"The variable in the ?name= query parameter could not be matched with any of your favorites." = "?name= クエリパラメータの変数は、お気に入りのどれとも一致しませんでした。";
