/* Table Content : Remove Row : Result : Too Few : Part 3 : Row not deleted when using primary key for DELETE statement. */
" Check the Console for possible errors inside the primary key(s) of this table!" = " تحقق من وحدة التحكم لاحتمال حدوث أخطاء داخل المفتاح الأساسي لهذا الجدول!";

/* Table Content : Remove Row : Result : Too Many : Part 2 : Generic text */
" Please check the Console and inform the Sequel Ace team!" = " الرجاء التحقق من وحدة التحكم وإبلاغ فريق Ace team!";

/* Table Content : Remove Row : Result : Too Few : Part 2 : Generic help message */
" Reload the table to be sure that the contents have not changed in the meantime." = " قم بإعادة تحميل الجدول للتأكد من أن المحتويات لم تتغير.";

/* Table Content : Remove Row : Result : Too Few : Part 3 : no primary key in table generic message */
" You should also add a primary key to this table!" = " يجب عليك أيضًا إضافة مفتاح أساسي إلى هذا الجدول!";

/* Query snippet shell command syntax and placeholder */
"$(shell_command)" = "$(shell_command)";

/* text showing how many rows are selected */
"%@ %@ selected" = "تم تحديد %1$@ %2$@";

/* History item filtered by values label */
"%@ (Filtered by %@)" = "%1$@ (تصفية بواسطة %2$@)";

/* History item with page number label */
"%@ (Page %lu)" = "%1$@ (الصفحة %2$lu)";

/* Content Filter Manager : Initial name of copied filter
 Initial favourite name after duplicating a previous favourite */
"%@ Copy" = "نسخ %@";

/* text showing a single row a partially loaded result */
"%@ row in partial load" = "%@ سطر في تحميل جزئي";

/* text showing a single row in the result */
"%@ row in table" = "%@ سطر في الجدول";

/* text showing how a single rows matched filter */
"%@ row of %@%@ matches filter" = "%1$@ سطر من %2$@%3$@ تطابق عامل التصفية";

/* text showing how many rows are in a partially loaded result */
"%@ rows in partial load" = "%@ سطر في تحميل جزئي";

/* text showing how many rows are in the result */
"%@ rows in table" = "%@ سطر في الجدول";

/* text showing how many rows matched filter */
"%@ rows of %@%@ match filter" = "%1$@ سطر من %2$@%3$@ تطابق عامل التصفية";

/* text showing how many rows have been affected by a single query */
"%@; %ld rows affected" = "%1$@; %2$ld سطر تأثر";

/* text showing how many rows have been affected by multiple queries */
"%@; %ld rows affected in total, by %ld queries taking %@" = "%1$@; %2$ld سطر متأثر إجمالاً، بواسطة %3$ld تم التنفيذ خلال %4$@";

/* text showing one row has been affected by a single query */
"%@; 1 row affected" = "%@; سطر تأثر";

/* text showing one row has been affected by multiple queries */
"%@; 1 row affected in total, by %ld queries taking %@" = "%1$@؛ صف 1 متأثرة إجمالاً، بواسطة %2$ld استفسارات ب %3$@";

/* text showing a query was cancelled */
"%@; Cancelled after %@" = "%1$@; ألغي بعد %2$@";

/* text showing multiple queries were cancelled */
"%@; Cancelled in query %ld, after %@" = "%1$@; ملغاة في الاستعلام %2$ldبعد %3$@";

/* Error display text, showing original MySQL error */
"%@\n\nMySQL said: %@" = "%1$@\n\nMySQL قال: %2$@";

/* favorite singular label (%d == 1) */
"%lu favorite" = "%lu مفضلة";

/* favorites plural label (%d != 1) */
"%lu favorites" = "%lu مفضلة";

/* favorite group singular label (%d == 1) */
"%lu group" = "%lu group";

/* favorite groups plural label (%d != 1) */
"%lu groups" = "مجموعات %lu";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+y (y!=1) rows instead of n selected were deleted. */
"%ld additional rows were removed!" = "%ld additional rows were removed!";

/* Label showing the index of the selected CSV row */
"%ld of %lu record(s)" = "%1$ld من سجل %2$lu";

/* Label showing the index of the selected CSV row (csv partially loaded) */
"%ld of first %lu record(s)" = "%1$ld من أول سجل %2$lu";

/* Table Content : Remove Row : Result : Too Few : Part 1 : n-x (x!=1) of n selected rows were deleted. */
"%ld rows were not removed." = "لم تتم إزالة الصفوف %ld.";

/* Export file already exists message */
"%lu files already exist. Do you want to replace them?" = "ملفات %lu موجودة بالفعل. هل تريد استبدالها؟";

/* Export files creation error title */
"%lu files could not be created" = "تعذر إنشاء ملفات %lu";

/* Some export files already exist explanatory text */
"%lu files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "ملفات %lu بنفس الأسماء موجودة بالفعل في المجلد المستهدف. سيتم استبدالها بمحتوياتها الحالية.";

/* filtered item count */
"%lu of %lu" = "%1$lu من %2$lu";

/* Export folder not writable for some files explanatory text */
"%lu of the export files could not be created because their target export folder is not writable; please select a new export location and try again." = "تعذر إنشاء %lu من ملفات التصدير لأن مجلد التصدير المستهدف غير قابل للكتابة؛ الرجاء تحديد موقع تصدير جديد وحاول مرة أخرى.";

/* Export folder missing for some files explanatory text */
"%lu of the export files could not be created because their target export folder no longer exists; please select a new export location and try again." = "تعذر إنشاء %lu من ملفات التصدير لأن مجلد التصدير المستهدف لم يعد موجوداً؛ الرجاء تحديد موقع تصدير جديد وحاول مرة أخرى.";

/* History item title with nothing selected */
"(no selection)" = "(لا يوجد تحديد)";

/* value shown for hidden blob and text fields */
"(not loaded)" = "(غير محمل)";

/* Explanation for MySQL server has gone away error */
"(This usually indicates that the connection has been closed by the server after inactivity, but can also occur due to other conditions.  The connection has been restored; please try again if the query is safe to re-run.)" = "(وهذا يشير عادة إلى أن الاتصال قد أغلق من قبل الخادم بعد عدم النشاط، ولكن يمكن أن يحدث أيضا بسبب ظروف أخرى. تم استعادة الاتصال؛ الرجاء المحاولة مرة أخرى إذا كان الاستعلام آمناً لإعادة التشغيل.)";

/* Custom Query : text appended to the “x row(s) affected” messages. $1 is a time interval */
", first row available after %1$@" = "، الصف الأول متاح بعد %1$@";

/* Custom Query : text appended to the “x row(s) affected” messages (for update/delete queries). $1 is a time interval */
", taking %1$@" = "، أخذ %1$@";

/* warning shown in the console when no rows have been affected after writing to the db */
"/* WARNING: No rows have been affected */\n" = "/* تحذير: لم يتأثر أي صف */\n";

/* error text when multiple custom query failed */
"[ERROR in query %ld] %@\n" = "[خطأ في الاستعلام %1$ld] %2$@\n";

/* error text when reading of csv file gave errors */
"[ERROR in row %ld] %@\n" = "[خطأ في الصف %1$ld] %2$@\n";

/* [multiple selection] */
"[multiple selection]" = "[اختيار متعدد]";

/* displayed when new content filter has empty Name field (ContentFilterManager) */
"[name required]" = "[الاسم مطلوب]";

/* [no selection] */
"[no selection]" = "[لا إختيار]";

/* Additional export file errors */
"\n\n(In addition, one or more errors occurred while attempting to create the export files: %lu could not be created. These files will be ignored.)" = "\n\n(بالإضافة إلى ذلك، حدث خطأ واحد أو أكثر أثناء محاولة إنشاء ملفات التصدير: %lu لا يمكن إنشاؤه. سيتم تجاهل هذه الملفات.";

/* \n\npress shift for binary search tooltip message */
"\n\nPress ⇧ for binary search (case-sensitive)." = "\n\nاضغط <unk> للبحث الثنائي (حساس لحالة الأحرف).";

/* description of bit */
"A bit-field type. M specifies the number of bits. If shorter values are inserted, they will be aligned on the least significant bit. See the SET type if you want to explicitly name each bit." = "نوع بت - الحقل. M يحدد عدد البيت. إذا تم إدخال قيم أقصر، سيتم مواءمتها على الأقل أهمية. انظر إلى نوع مجموعة الـ SET إذا كنت ترغب في تسمية كل جزء صراحة.";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog message */
"A Bundle ‘%@’ is already installed. Do you want to update it?" = "تم بالفعل تثبيت حزمة ''%@. هل تريد تحديثها؟";

/* description of binary */
"A byte array with fixed length. Shorter values will always be padded to the right with 0x00 until they fit M." = "مصفوفة بايت ذات طول ثابت. القيم الاقصر ستضاف دائما إلى اليمين مع 0x00 حتى تناسب M.";

/* description of varbinary */
"A byte array with variable length. The actual number of bytes is further limited by the values of other fields in the row." = "مصفوفة بايت بطول متغير. العدد الفعلي من البايت محدود كذلك بقيم الحقول الأخرى في الصف.";

/* description of blob
 description of longblob
 description of mediumblob
 description of tinyblob */
"A byte array with variable length. Unlike VARBINARY this type does not count towards the maximum row length." = "صفيفة بايت بطول متغير. على عكس فاربيناري، هذا النوع لا يحسب في طول الصف الأقصى.";

/* description of tinytext */
"A character string that can store up to 255 bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "سلسلة حروف يمكن تخزين ما يصل إلى 255 بايت، ولكنها تتطلب مساحة أقل لقيم أقصر. العدد الفعلي للأحرف محدود كذلك بواسطة الترميز المستخدم. على عكس VARCHAR هذا النوع لا يحسب في الطول الأقصى للصف.";

/* description of varchar */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding and the values of other fields in the row." = "سلسلة حروف يمكن تخزينها حتى M بايت، ولكنها تتطلب مساحة أقل لقيم أقصر. العدد الفعلي للأحرف محدود كذلك بواسطة الترميز المستخدم وقيم الحقول الأخرى في الصف.";

/* description of text */
"A character string that can store up to M bytes, but requires less space for shorter values. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "سلسلة حروف يمكن تخزينها حتى M بايت، ولكنها تتطلب مساحة أقل لقيم أقصر. العدد الفعلي للأحرف محدود كذلك بواسطة الترميز المستخدم. على عكس VARCHAR هذا النوع لا يحسب في الطول الأقصى للصف.";

/* description of char */
"A character string that will require M×w bytes per row, independent of the actual content length. w is the maximum number of bytes a single character can occupy in the given encoding." = "سلسلة حرف تتطلب M×w بايت في الصف الواحد، بمعزل عن طول المحتوى الفعلي. w هو الحد الأقصى لعدد البايت الذي يمكن أن يشغله حرف واحد في الترميز المحدد.";

/* description of longtext
 description of mediumtext */
"A character string with variable length. The actual number of characters is further limited by the used encoding. Unlike VARCHAR this type does not count towards the maximum row length." = "سلسلة حروف بطول متغير. العدد الفعلي للأحرف محدود أكثر من خلال الترميز المستخدم. على عكس VARCHAR هذا النوع لا يحسب في الطول الأقصى للصف.";

/* description of json */
"A data type that validates JSON data on INSERT and internally stores it in a binary format that is both, more compact and faster to access than textual JSON.\nAvailable from MySQL 5.7.8." = "نوع البيانات الذي يصدق على بيانات JSON عن INSERT ويخزنها داخليا في شكل ثنائي هو كليهما، أكثر تواضعا وأسرع للوصول من النص JSON.\nمتاح من MySQL 5.7.8.";

/* Export file already exists explanatory text */
"A file with the same name already exists in the target folder. Replacing it will overwrite its current contents." = "يوجد بالفعل ملف بنفس الاسم في المجلد المستهدف. سيتم استبداله بمحتوياته الحالية.";

/* description of decimal */
"A fixed-point, exact decimal value. M is the maxium number of digits, of which D may be after the decimal point. When rounding, 0-4 is always rounded down, 5-9 up (“round towards nearest”)." = "نقطة ثابتة، القيمة العشرية الدقيقة. M هو العدد الأقصى للأرقام، قد يكون D منها بعد النقطة العشرية. وعند التقريب يتم دوما تقريب 0-4، 5-9 لأعلى (\"جولة نحو الراسخة\").";

/* table structure : indexes : delete index : error 1553 : title
 table structure : indexes : delete index : error 1553, no FK found : title */
"A foreign key needs this index" = "المفتاح الأجنبي يحتاج إلى هذا المؤشر";

/* description of set */
"A SET can define up to 64 members (as strings) of which a field can use one or more using a comma-separated list. Upon insertion the order of members is automatically normalized and duplicate members will be eliminated. Assignment of numbers is supported using the same semantics as for BIT types." = "يمكنك تعيين ما يصل إلى 64 عضوا (كسل) يمكن للحقل استخدام واحد أو أكثر منهم باستخدام قائمة مفصولة بفاصلة. وعند الإدراج يتم تلقائياً تطبيع ترتيب الأعضاء وإلغاء الأعضاء المتكررين. يتم دعم تعيين الأرقام باستخدام نفس الألفاظ المستخدمة لأنواع معاهدات الاستثمار الثنائية.";

/* SSH key not found message */
"A SSH key location was specified, but no file was found in the specified location.  Please re-select the key and try again." = "تم تحديد موقع مفتاح SSH، ولكن لم يتم العثور على ملف في الموقع المحدد. الرجاء إعادة تحديد المفتاح وحاول مرة أخرى.";

/* SSL CA certificate file not found message */
"A SSL Certificate Authority certificate location was specified, but no file was found in the specified location.  Please re-select the Certificate Authority certificate and try again." = "تم تحديد موقع شهادة سلطة شهادات SSL ، ولكن لم يتم العثور على ملف في الموقع المحدد. الرجاء إعادة تحديد شهادة سلطة الشهادة وحاول مرة أخرى.";

/* SSL certificate file not found message */
"A SSL certificate location was specified, but no file was found in the specified location.  Please re-select the certificate and try again." = "تم تحديد موقع شهادة SSL، ولكن لم يتم العثور على ملف في الموقع المحدد. الرجاء إعادة تحديد الشهادة وحاول مرة أخرى.";

/* SSL key file not found message */
"A SSL key file location was specified, but no file was found in the specified location.  Please re-select the key file and try again." = "تم تحديد موقع ملف مفتاح SSL، ولكن لم يتم العثور على ملف في الموقع المحدد. الرجاء إعادة تحديد ملف المفتاح وحاول مرة أخرى.";

/* duplicate host informative message */
"A user with the host '%@' already exists" = "مستخدم مع المضيف '%@' موجود بالفعل";

/* duplicate user informative message */
"A user with the name '%@' already exists" = "مستخدم باسم '%@' موجود بالفعل";

/* table content : editing : error message description when parsing as hex string failed */
"A valid hex string may only contain the numbers 0-9 and letters A-F (a-f). It can optionally begin with „0x“ and spaces will be ignored.\nAlternatively the syntax X'val' is supported, too." = "A valid hex string may only contain the numbers 0-9 and letters A-F (a-f). It can optionally begin with „0x“ and spaces will be ignored.\nAlternatively the syntax X'val' is supported, too.";

/* connection failed due to access denied title */
"Access denied!" = "تم رفض الوصول!";

/* range of double */
"Accurate to approx. 15 decimal places" = "دقيقة إلى حوالي 15 كسر عشري";

/* range of float */
"Accurate to approx. 7 decimal places" = "دقيقة إلى حوالي 7 أماكن عشرية";

/* active connection window is busy. please wait and try again. tooltip */
"Active connection window is busy. Please wait and try again." = "نافذة الاتصال النشطة مشغولة. الرجاء الانتظار والمحاولة مرة أخرى.";

/* header for activities pane */
"ACTIVITIES" = "الأنشطة";

/* Add trigger button label */
"Add" = "إضافة";

/* menu item to add db */
"Add Database..." = "إضافة قاعدة بيانات...";

/* Add Host */
"Add Host" = "إضافة مضيف";

/* add global value or expression menu item */
"Add Value or Expression…" = "إضافة قيمة أو تعبير…";

/* adding index task status message */
"Adding index..." = "إضافة فهرس...";

/* Advanced options short title */
"Advanced" = "متقدم";

/* notifications preference pane name */
"Alerts & Logs" = "التنبيهات والسجلات";

/* notifications preference pane tooltip */
"Alerts & Logs Preferences" = "تفضيلات التنبيهات والسجلات";

/* All databases placeholder */
"All Databases" = "جميع قواعد البيانات";

/* All databases (%) placeholder */
"All Databases (%)" = "جميع قواعد البيانات (%)";

/* All export files already exist message */
"All the export files already exist. Do you want to replace them?" = "جميع ملفات التصدير موجودة بالفعل. هل تريد استبدالها؟";

/* An error for sequelace URL scheme command occurred. Probably no corresponding connection window found. */
"An error for sequelace URL scheme command occurred. Probably no corresponding connection window found." = "حدث خطأ في أمر مخطط URL التتابع. ربما لم يتم العثور على نافذة اتصال مقابلة.";

/* no connection available informatie message */
"An error has occurred and there doesn't seem to be a connection available." = "لقد حدث خطأ ولا يبدو أن هناك اتصال متاح.";

/* an error occur while executing a scheme command. if the scheme command was invoked by a bundle command, it could be that the command still runs. you can try to terminate it by pressing ⌘+. or via the activities pane. */
"An error occur while executing a scheme command. If the scheme command was invoked by a Bundle command, it could be that the command still runs. You can try to terminate it by pressing ⌘+. or via the Activities pane." = "حدث خطأ أثناء تنفيذ أمر مخطط. وإذا ما احتج بأمر النظام من جانب أمر بِنْدِل، فإنه يمكن أن يكون الأمر لا يزال قائما. يمكنك محاولة إنهائها بالضغط على <unk> +، أو عبر لوحة الأنشطة.";

/* error killing query informative message */
"An error occurred while attempting to kill connection %lld.\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة قتل الاتصال %1$lld.\n\nMySQL قال: %2$@";

/* error killing query informative message */
"An error occurred while attempting to kill the query associated with connection %lld.\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة قتل الاستعلام المرتبط بالاتصال %1$lld.\n\nMySQL قال: %2$@";

/* Error shown when unable to show create table syntax */
"An error occurred while creating table syntax.\n\n: %@" = "حدث خطأ أثناء إنشاء بناء الجدول.\n\n: %@";

/* rename table error - no temporary name found */
"An error occurred while renaming '%@'. No temporary name could be found. Please try renaming to something else first." = "حدث خطأ أثناء إعادة تسمية '%@'. لم يتم العثور على اسم مؤقت. الرجاء محاولة إعادة التسمية إلى شيء آخر أولا.";

/* rename table error informative message */
"An error occurred while renaming '%@'.\n\nMySQL said: %@" = "حدث خطأ أثناء إعادة تسمية '%1$@'.\n\nMySQL قال: %2$@";

/* rename error - don't know what type the renamed thing is */
"An error occurred while renaming. '%@' is of an unknown type." = "حدث خطأ أثناء إعادة التسمية. '%@' من نوع غير معروف.";

/* rename precedure/function error - can't delete old procedure */
"An error occurred while renaming. I couldn't delete '%@'.\n\nMySQL said: %@" = "حدث خطأ أثناء إعادة التسمية. لم أستطع حذف '%1$@'.\n\nMySQL قال: %2$@";

/* rename precedure/function error - can't recreate procedure */
"An error occurred while renaming. I couldn't recreate '%@'.\n\nMySQL said: %@" = "حدث خطأ أثناء إعادة التسمية. لم أستطع إعادة إنشاء '%1$@'.\n\nMySQL قال: %2$@";

/* rename precedure/function error - can't retrieve syntax */
"An error occurred while renaming. I couldn't retrieve the syntax for '%@'.\n\nMySQL said: %@" = "حدث خطأ أثناء إعادة التسمية. لم أستطع استرداد الجملة لـ '%1$@'.\n\nMySQL قال: %2$@";

/* rename error - invalid create syntax */
"An error occurred while renaming. The CREATE syntax of '%@' could not be parsed." = "حدث خطأ أثناء إعادة التسمية. لا يمكن تحليل بناء الجملة المنشئة لـ '%@'.";

/* message of panel when retrieving view information failed */
"An error occurred while retrieving status data.\n\nMySQL said: %@" = "حدث خطأ أثناء استرداد بيانات الحالة.\n\nMySQL قال: %@";

/* message of panel when create syntax cannot be retrieved */
"An error occurred while retrieving the create syntax for '%@'.\nMySQL said: %@" = "حدث خطأ أثناء استرداد بناء الجملة لـ '%1$@'.\nMySQL قال: %2$@";

/* add index error informative message */
"An error occurred while trying to add the index.\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة إضافة الفهرس.\n\nMySQL قال: %@";

/* error adding password to keychain informative message */
"An error occurred while trying to add the password to your Keychain. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "حدث خطأ أثناء محاولة إضافة كلمة المرور إلى سلسلة المفاتيح الخاصة بك. إصلاح سلسلة المفاتيح الخاصة بك قد يحل هذا الأمر، ولكن إذا لم يتم الإبلاغ عنه إلى فريق Sequel Ace ، فيقدم رمز الخطأ %i.";

/* unable to copy database message informative message */
"An error occurred while trying to copy the database '%@' to '%@'." = "حدث خطأ أثناء محاولة نسخ قاعدة البيانات '%1$@' إلى '%2$@'.";

/* error deleting index informative message */
"An error occurred while trying to delete the index.\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة حذف الفهرس.\n\nMySQL قال: %@";

/* table status : row count query failed : error message */
"An error occurred while trying to determine the number of rows for %@.\nMySQL said: %@ (%lu)" = "An error occurred while trying to determine the number of rows for “%1$@”.\nMySQL said: %2$@ (%3$lu)";

/* unable to rename database message informative message */
"An error occurred while trying to rename the database '%@' to '%@'." = "حدث خطأ أثناء محاولة إعادة تسمية قاعدة البيانات '%1$@' إلى '%2$@'.";

/* error finding keychain item to edit informative message */
"An error occurred while trying to retrieve the Keychain item you're trying to edit. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "حدث خطأ أثناء محاولة استرداد عنصر سلسلة المفاتيح الذي تحاول تعديله. إصلاح سلسلة المفاتيح الخاصة بك قد يحل هذا الأمر، ولكن إذا لم يتم الإبلاغ عنه إلى فريق Sequel Ace ، فيقدم رمز الخطأ %i.";

/* error updating keychain item informative message */
"An error occurred while trying to update the Keychain item. Repairing your Keychain might resolve this, but if it doesn't please report it to the Sequel Ace team, supplying the error code %i." = "حدث خطأ أثناء محاولة تحديث عنصر سلسلة المفاتيح. إصلاح سلسلة المفاتيح الخاصة بك قد يحل هذا الأمر، ولكن إذا لم يتم الإبلاغ عنه إلى فريق Sequel Ace ، فيقدم رمز الخطأ %i.";

/* mysql error occurred message */
"An error occurred" = "حدث خطأ";

/* MySQL table info retrieval error message */
"An error occurred retrieving table information.  MySQL said: %@" = "حدث خطأ أثناء استرداد معلومات الجدول. قال MySQL: %@";

/* SQL encoding read error */
"An error occurred when reading the file, as it could not be read in the encoding you selected (%@).\n\nOnly %ld queries were executed." = "حدث خطأ عند قراءة الملف، لأنه لا يمكن قراءته في الترميز الذي اخترته (%1$@).\n\nتم تنفيذ الاستفسارات %2$ld فقط.";

/* CSV encoding read error */
"An error occurred when reading the file, as it could not be read using the encoding you selected (%@).\n\nOnly %ld rows were imported." = "حدث خطأ عند قراءة الملف، لأنه لا يمكن قراءته باستخدام الترميز الذي اخترته (%1$@).\n\nتم استيراد صفوف %2$ld فقط.";

/* SQL read error, including detail from system */
"An error occurred when reading the file.\n\nOnly %ld queries were executed.\n\n(%@)" = "حدث خطأ عند قراءة الملف.\n\nتم تنفيذ الاستعلامات %1$ld فقط.\n\n(%2$@)";

/* CSV read error, including detail string from system */
"An error occurred when reading the file.\n\nOnly %ld rows were imported.\n\n(%@)" = "حدث خطأ عند قراءة الملف.\n\nتم استيراد صفوف %1$ld فقط.\n\n(%2$@)";

/* error adding field informative message */
"An error occurred when trying to add the field '%@' via\n\n%@\n\nMySQL said: %@" = "حدث خطأ عند محاولة إضافة الحقل '%1$@' عبر\n\n%2$@\n\nMySQL قال: %3$@";

/* error changing field informative message */
"An error occurred when trying to change the field '%@' via\n\n%@\n\nMySQL said: %@" = "حدث خطأ عند محاولة تغيير الحقل '%1$@' من خلال\n\n%2$@\n\nMySQL قال: %3$@";

/* error changing table collation informative message */
"An error occurred when trying to change the table collation to '%@'.\n\nMySQL said: %@" = "حدث خطأ عند محاولة تغيير مقارنة الجدول إلى '%1$@'.\n\nMySQL قال: %2$@";

/* error changing table encoding informative message */
"An error occurred when trying to change the table encoding to '%@'.\n\nMySQL said: %@" = "حدث خطأ عند محاولة تغيير ترميز الجدول إلى '%1$@'.\n\nMySQL قال: %2$@";

/* error changing table type informative message */
"An error occurred when trying to change the table type to '%@'.\n\nMySQL said: %@" = "حدث خطأ عند محاولة تغيير نوع الجدول إلى '%1$@'.\n\nMySQL قال: %2$@";

/* error changing table comment informative message */
"An error occurred when trying to change the table's comment to '%@'.\n\nMySQL said: %@" = "حدث خطأ عند محاولة تغيير تعليق الجدول إلى '%1$@'.\n\nMySQL قال: %2$@";

/* an error occurred while analyzing the %@.\n\nMySQL said:%@ */
"An error occurred while analyzing the %@.\n\nMySQL said:%@" = "حدث خطأ أثناء تحليل %1$@.\n\nMySQL قال:%2$@";

/* an error occurred while fetching the optimized field type.\n\nMySQL said:%@ */
"An error occurred while fetching the optimized field type.\n\nMySQL said:%@" = "An error occurred while fetching the optimized field type.\n\nMySQL said:%@";

/* an error occurred while trying to flush the %@.\n\nMySQL said:%@ */
"An error occurred while flushing the %@.\n\nMySQL said:%@" = "حدث خطأ أثناء مسح %1$@.\n\nMySQL قال:%2$@";

/* sql import error message */
"An error occurred while importing SQL" = "حدث خطأ أثناء استيراد SQL";

/* an error occurred while trying to optimze the %@.\n\nMySQL said:%@ */
"An error occurred while optimzing the %@.\n\nMySQL said:%@" = "حدث خطأ أثناء تحسين %1$@.\n\nMySQL قال:%2$@";

/* an error occurred while performing the checksum on the %@.\n\nMySQL said:%@ */
"An error occurred while performing the checksum on %@.\n\nMySQL said:%@" = "حدث خطأ أثناء أداء المجموع الاختباري على %1$@.\n\nMySQL قال:%2$@";

/* an error occurred while trying to repair the %@.\n\nMySQL said:%@ */
"An error occurred while repairing the %@.\n\nMySQL said:%@" = "حدث خطأ أثناء إصلاح %1$@.\n\nMySQL قال:%2$@";

/* message of panel when retrieving information failed */
"An error occurred while retrieving information.\nMySQL said: %@" = "حدث خطأ أثناء استرداد المعلومات. قال:\nMySQL: %@";

/* error retrieving table information informative message */
"An error occurred while retrieving the information for table '%@'. Please try again.\n\nMySQL said: %@" = "حدث خطأ أثناء استرداد معلومات الجدول '%1$@'. الرجاء المحاولة مرة أخرى.\n\nMySQL قال: %2$@";

/* error retrieving table information informative message */
"An error occurred while retrieving the trigger information for table '%@'. Please try again.\n\nMySQL said: %@" = "حدث خطأ أثناء استرداد معلومات المشغل للجدول '%1$@'. الرجاء المحاولة مرة أخرى.\n\nMySQL قال: %2$@";

/* error adding new column informative message */
"An error occurred while trying to add the new column '%@' by\n\n%@.\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة إضافة العمود ''%1$@من قبل\n\n%2$@.\n\nMySQL قال: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@' by\n\n%@.\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة إضافة الجدول الجديد '%1$@من قبل\n\n%2$@.\n\nMySQL قال: %3$@";

/* error adding new table informative message */
"An error occurred while trying to add the new table '%@'.\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة إضافة الجدول الجديد '%1$@'.\n\nMySQL قال: %2$@";

/* error while trying to alter table message */
"An error occurred while trying to alter table '%@'.\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة تغيير الجدول '%1$@'.\n\nMySQL قال: %2$@";

/* an error occurred while trying to check the %@.\n\nMySQL said:%@ */
"An error occurred while trying to check the %@.\n\nMySQL said:%@" = "حدث خطأ أثناء محاولة التحقق من %1$@.\n\nMySQL قال:%2$@";

/* error deleting relation informative message */
"An error occurred while trying to delete the relation '%@'.\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة حذف العلاقة '%1$@'.\n\nMySQL قال: %2$@";

/* unable to get list of users informative message */
"An error occurred while trying to get the list of users. Please make sure you have the necessary privileges to perform user management, including access to the mysql.user table." = "حدث خطأ أثناء محاولة الحصول على قائمة المستخدمين. الرجاء التأكد من أن لديك الامتيازات اللازمة لأداء إدارة المستخدم، بما في ذلك الوصول إلى جدول mysql.user.";

/* error importing table informative message */
"An error occurred while trying to import a table via: \n%@\n\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة استيراد الجدول من خلال: \n%1$@\n\n\nMySQL قال: %2$@";

/* error moving field informative message */
"An error occurred while trying to move the field.\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة نقل الحقل.\n\nMySQL قال: %@";

/* error resetting auto_increment informative message */
"An error occurred while trying to reset AUTO_INCREMENT of table '%@'.\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة إعادة تعيين AUTO_INCREMENT من الجدول '%1$@'.\n\nMySQL قال: %2$@";

/* error truncating table informative message */
"An error occurred while trying to truncate the table '%@'.\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة اقتطاع الجدول '%1$@'.\n\nMySQL قال: %2$@";

/* mysql error occurred informative message */
"An error occurred whilst trying to perform the operation.\n\nMySQL said: %@" = "حدث خطأ أثناء محاولة تنفيذ العملية.\n\nMySQL قال: %@";

/* mysql resource limits unsupported message */
"Resource Limits are not supported for your version of MySQL. Any Resouce Limits you specified have been discarded and not saved. MySQL said: %@" = "حدود الموارد غير مدعومة لإصدار MySQL. تم تجاهل أي حدود ريسوس قمت بتحديدها ولم يتم حفظها. قالت MySQL: %@";

/* Export files creation error explanatory text */
"An unhandled error occurred when attempting to create %lu of the export files.  Please check the details and try again." = "حدث خطأ غير معالج عند محاولة إنشاء %lu من ملفات التصدير. الرجاء التحقق من التفاصيل وحاول مرة أخرى.";

/* All export files creation error explanatory text */
"An unhandled error occurred when attempting to create each of the export files.  Please check the details and try again." = "حدث خطأ غير معالج عند محاولة إنشاء كل ملف من ملفات التصدير. الرجاء التحقق من التفاصيل وحاول مرة أخرى.";

/* Export file creation error explanatory text */
"An unhandled error occurred when attempting to create the export file.  Please check the details and try again." = "حدث خطأ غير معالج عند محاولة إنشاء ملف التصدير. الرجاء التحقق من التفاصيل وحاول مرة أخرى.";

/* ANALYZE one or more tables - result title */
"Analyze %@" = "تحليل %@";

/* analyze selected items menu item */
"Analyze Selected Items" = "تحليل العناصر المحددة";

/* analyze table menu item */
"Analyze Table" = "جدول التحليل";

/* analyze table failed message */
"Analyze table failed." = "فشل تحليل الجدول.";

/* change table type informative message */
"Are you sure you want to change this table's type to %@?\n\nPlease be aware that changing a table's type has the potential to cause the loss of some or all of its data. This action cannot be undone." = "هل أنت متأكد من أنك تريد تغيير نوع هذا الجدول إلى %@؟\n\nيرجى أن تكون على علم بأن تغيير نوع الجدول يمكن أن يسبب فقدان بعض أو كل بياناته. لا يمكن التراجع عن هذا الإجراء.";

/* clear global history list informative message */
"Are you sure you want to clear the global history list? This action cannot be undone." = "هل أنت متأكد من أنك تريد مسح قائمة التاريخ العالمي؟ لا يمكن التراجع عن هذا الإجراء.";

/* clear history list for %@ informative message */
"Are you sure you want to clear the history list for %@? This action cannot be undone." = "هل أنت متأكد من أنك تريد مسح قائمة المحفوظات ل %@؟ لا يمكن التراجع عن هذا الإجراء.";

/* truncate tables informative message */
"Are you sure you want to delete ALL records in the selected tables? This operation cannot be undone." = "هل أنت متأكد من أنك تريد حذف جميع السجلات في الجداول المحددة؟ لا يمكن التراجع عن هذه العملية.";

/* truncate table informative message */
"Are you sure you want to delete ALL records in the table '%@'? This operation cannot be undone." = "هل أنت متأكد من أنك تريد حذف جميع السجلات في الجدول ''%@'؟ لا يمكن التراجع عن هذه العملية.";

/* delete all rows informative message */
"Are you sure you want to delete all the rows from this table? This action cannot be undone." = "هل أنت متأكد من أنك تريد حذف جميع الصفوف من هذا الجدول؟ لا يمكن التراجع عن هذا الإجراء.";

/* delete table/view informative message */
"Are you sure you want to delete the %@ '%@'? This operation cannot be undone." = "هل أنت متأكد من أنك تريد حذف %1$@ '%2$@'؟ لا يمكن التراجع عن هذه العملية.";

/* delete database informative message */
"Are you sure you want to delete the database '%@'? This operation cannot be undone." = "هل أنت متأكد من أنك تريد حذف قاعدة البيانات '%@'؟ لا يمكن التراجع عن هذه العملية.";

/* delete database informative message */
"Are you sure you want to delete the favorite '%@'? This operation cannot be undone." = "هل أنت متأكد من أنك تريد حذف المفضل '%@'؟ لا يمكن التراجع عن هذه العملية.";

/* delete field informative message */
"Are you sure you want to delete the field '%@'? This action cannot be undone." = "هل أنت متأكد من أنك تريد حذف الحقل '%@'؟ لا يمكن التراجع عن هذا الإجراء.";

/* delete database informative message */
"Are you sure you want to delete the group '%@'? All groups and favorites within this group will also be deleted. This operation cannot be undone." = "هل أنت متأكد من أنك تريد حذف المجموعة '%@'؟ سيتم حذف جميع المجموعات والمفضلة داخل هذه المجموعة أيضًا. لا يمكن التراجع عن هذه العملية.";

/* delete index informative message */
"Are you sure you want to delete the index '%@'? This action cannot be undone." = "هل أنت متأكد من أنك تريد حذف الفهرس '%@'؟ لا يمكن التراجع عن هذا الإجراء.";

/* delete tables/views informative message */
"Are you sure you want to delete the selected %@? This operation cannot be undone." = "هل أنت متأكد من أنك تريد حذف %@المحددة؟ لا يمكن التراجع عن هذه العملية.";

/* delete rows informative message */
"Are you sure you want to delete the selected %ld rows from this table? This action cannot be undone." = "هل أنت متأكد من أنك تريد حذف صفوف %ld المحددة من هذا الجدول؟ لا يمكن التراجع عن هذا الإجراء.";

/* delete selected relation informative message */
"Are you sure you want to delete the selected relations? This action cannot be undone." = "هل أنت متأكد من أنك تريد حذف العلاقات المحددة؟ لا يمكن التراجع عن هذا الإجراء.";

/* delete selected row informative message */
"Are you sure you want to delete the selected row from this table? This action cannot be undone." = "هل أنت متأكد من أنك تريد حذف الصف المحدد من هذا الجدول؟ لا يمكن التراجع عن هذا الإجراء.";

/* delete selected trigger informative message */
"Are you sure you want to delete the selected triggers? This action cannot be undone." = "هل أنت متأكد من أنك تريد حذف المشغلات المحددة؟ لا يمكن التراجع عن هذا الإجراء.";

/* kill connection informative message */
"Are you sure you want to kill connection ID %lld?\n\nPlease be aware that continuing to kill this connection may result in data corruption. Please proceed with caution." = "هل أنت متأكد من أنك تريد قتل معرف الاتصال %lld؟\n\nيرجى العلم بأن الاستمرار في قتل هذا الاتصال قد يؤدي إلى فساد البيانات. يرجى المضي قدما بحذر.";

/* kill query informative message */
"Are you sure you want to kill the current query executing on connection ID %lld?\n\nPlease be aware that continuing to kill this query may result in data corruption. Please proceed with caution." = "هل أنت متأكد من أنك تريد قتل الاستعلام الحالي الذي ينفذ على معرف الاتصال %lld؟\n\nيرجى العلم بأن الاستمرار في قتل هذا الاستعلام قد يؤدي إلى فساد البيانات. يرجى المضي قدما بحذر.";

/* Bundle Editor : Remove-Bundle: remove dialog message */
"Are you sure you want to move the selected Bundle to the Trash and remove them respectively?" = "هل أنت متأكد من أنك تريد نقل الحزمة المحددة إلى سلة المهملات وإزالتها على التوالي؟";

/* continue to print informative message */
"Are you sure you want to print the current content view of the table '%@'?\n\nIt currently contains %@ rows, which may take a significant amount of time to print." = "هل أنت متأكد من أنك تريد طباعة عرض المحتوى الحالي من الجدول '%1$@'؟\n\nيحتوي حاليا على صفوف %2$@ ، مما قد يستغرق وقتا طويلا للطباعة.";

/* remove all query favorites informative message */
"Are you sure you want to remove all of your saved query favorites? This action cannot be undone." = "هل أنت متأكد من أنك تريد إزالة جميع المفضلات الخاصة بك في الاستعلام المحفوظة؟ لا يمكن التراجع عن هذا الإجراء.";

/* remove all selected content filters informative message */
"Are you sure you want to remove all selected content filters? This action cannot be undone." = "هل أنت متأكد من أنك تريد إزالة جميع فلاتر المحتوى المحددة؟ لا يمكن التراجع عن هذا الإجراء.";

/* remove all selected query favorites informative message */
"Are you sure you want to remove all selected query favorites? This action cannot be undone." = "هل أنت متأكد من أنك تريد إزالة جميع المفضلات الاستعلام المحددة؟ لا يمكن التراجع عن هذا الإجراء.";

/* Table Info Section : current value of auto_increment */
"auto_increment: %@" = "تلقائي_زيادة: %@";

/* Encoding autodetect menu item */
"Autodetect" = "الكشف التلقائي";

/* background label for color table (Prefs > Editor) */
"Background" = "الخلفية";

/* backtick quote label for color table (Prefs > Editor) */
"Backtick Quote" = "اقتباس الخلف";

/* bash error */
"BASH Error" = "خطأ BASH";

/* toolbar item label for switching to the Table Content tab */
"Browse & Edit Table Content" = "تصفح وتحرير محتوى الجدول";

/* build label */
"build" = "بناء";

/* build label */
"Build" = "بناء";

/* bundle editor menu item label */
"Bundle Editor" = "محرر الحزمة";

/* bundle error */
"Bundle Error" = "خطأ في الحزمة";

/* bundles menu item label */
"Bundles" = "Bundles";

/* Bundle Editor : Outline View : 'BUNDLES' item */
"BUNDLES" = "BUNDLES";

/* Bundle Editor : Outline View : Menu Category item : tooltip */
"Bundles in category %@" = "الحزم في الفئة %@";

/* bundles installation error */
"Bundles Installation Error" = "خطأ في تثبيت الحزم";

/* bzip2 compression export summary - within a sentence */
"bzip2 compression" = "ضغط bzip2";

/* description of geometry */
"Can store a single spatial value of types POINT, LINESTRING or POLYGON. Spatial support in MySQL is based on the OpenGIS Geometry Model." = "يمكن تخزين قيمة مكانية واحدة للأنواع POINT أو LINESTRING أو POLYGON. يعتمد الدعم المكاني في MySQL على نموذج OpenGIS الهندسي.";

/* Bundle Editor : Remove-Bundle: cancel button
 cancel
 cancel button
 Open Files : Bundle : Already-Installed : Cancel button
 Preferences : Network : Custom SSH client : warning dialog : cancel button
 shutdown server : confirmation dialog : cancel button */
"Cancel" = "إلغاء";

/* sql import : charset error alert : cancel button */
"Cancel Import" = "إلغاء الاستيراد";

/* cancelling task status message */
"Cancelling..." = "إلغاء...";

/* empty query informative message */
"Cannot save an empty query." = "لا يمكن حفظ استعلام فارغ.";

/* caret label for color table (Prefs > Editor) */
"Caret" = "الكهنة";

/* change button */
"Change" = "تغيير";

/* change focus to table list menu item */
"Change Focus to Table List" = "تغيير التركيز إلى قائمة الجدول";

/* change table type message */
"Change table type" = "تغيير نوع الجدول";

/* unsaved changes informative message */
"Changes have been made, which will be lost if this window is closed. Are you sure you want to continue" = "تم إجراء تغييرات، والتي سيتم فقدها إذا تم إغلاق هذه النافذة. هل أنت متأكد من أنك تريد المتابعة";

/* quitting app informal alert title */
"Close the app?" = "Close the app?";

/* quitting app informal alert body */
"Are you sure you want to quit the app?" = "Are you sure you want to quit the app?";

/* character set client: %@ */
"character set client: %@" = "عميل تعيين الحرف: %@";

/* CHECK one or more tables - result title */
"Check %@" = "تحقق من %@";

/* check of all selected items successfully passed message */
"Check of all selected items successfully passed." = "تحقق من جميع العناصر المحددة تم اجتيازها بنجاح.";

/* check option: %@ */
"check option: %@" = "خيار التحقق: %@";

/* check selected items menu item */
"Check Selected Items" = "تحقق من العناصر المحددة";

/* check table menu item */
"Check Table" = "التحقق من الجدول";

/* check table failed message */
"Check table failed." = "فشل التحقق من الجدول.";

/* check table successfully passed message */
"Check table successfully passed." = "تمرير الجدول بنجاح.";

/* check view menu item */
"Check View" = "تحقق من العرض";

/* checking field data for editing task description */
"Checking field data for editing..." = "التحقق من بيانات الحقل للتحرير...";

/* checksum %@ message */
"Checksum %@" = "خروج %@";

/* checksum selected items menu item */
"Checksum Selected Items" = "إختيار العناصر المختارة";

/* checksum table menu item */
"Checksum Table" = "جدول المجموع الاختباري";

/* Checksums of %@ message */
"Checksums of %@" = "مجاميع الشيكات %@";

/* menu item for choose db */
"Choose Database..." = "اختر قاعدة البيانات...";

/* cancelling export cleaning up message */
"Cleaning up..." = "تنظيف...";

/* clear button */
"Clear" = "مسح";

/* toolbar item for clear console */
"Clear Console" = "مسح وحدة التحكم";

/* clear global history menu item title */
"Clear Global History" = "مسح التاريخ العالمي";

/* clear history for %@ menu title */
"Clear History for %@" = "مسح المحفوظات ل %@";

/* clear history message */
"Clear History?" = "مسح التاريخ؟";

/* tooltip for toolbar item for clear console */
"Clear the console which shows all MySQL commands performed by Sequel Ace" = "مسح وحدة التحكم التي تظهر جميع أوامر MySQL التي تقوم بها Sequel Ace";

/* clear the document-based history list tooltip message */
"Clear the document-based history list" = "مسح قائمة المحفوظات المستندة إلى الوثائق";

/* clear the global history list tooltip message */
"Clear the global history list" = "مسح قائمة التاريخ العالمي";

/* Close menu item */
"Close" = "أغلق";

/* close tab context menu item */
"Close Tab" = "إغلاق علامة التبويب";

/* Close Window menu item */
"Close Window" = "إغلاق النافذة";

/* collation label (Navigator) */
"Collation" = "التجميع";

/* collation connection: %@ */
"collation connection: %@" = "اتصال التجميع: %@";

/* comment label */
"Comment" = "تعليق";

/* Title of action menu item to comment line */
"Comment Line" = "سطر التعليق";

/* Title of action menu item to comment selection */
"Comment Selection" = "اختيار التعليق";

/* connect button */
"Connect" = "الاتصال";

/* Connect via socket button */
"Connect via socket" = "الاتصال عبر المقبس";

/* connection established message */
"Connected" = "متصل";

/* description for connected notification */
"Connected to %@" = "متصل بـ %@";

/* message of panel when connection to db failed */
"Connected to host, but unable to connect to database %@.\n\nBe sure that the database exists and that you have the necessary privileges.\n\nMySQL said: %@" = "Connected to host, but unable to connect to database %1$@.\n\nBe sure that the database exists and that you have the necessary privileges.\n\nMySQL said: %2$@";

/* Generic connecting very short status message */
"Connecting..." = "جاري الاتصال...";

/* window title string indicating that sp is connecting */
"Connecting…" = "جارٍ توصيل…";

/* error while reading connection data file */
"Connection data file couldn't be read." = "تعذر قراءة ملف بيانات الاتصال.";

/* message error while reading connection data file and suggesting to save it under a differnet name */
"Connection data file %@ couldn't be read. Please try to save the document under a different name." = "تعذر قراءة ملف بيانات الاتصال %@ . الرجاء محاولة حفظ المستند تحت اسم مختلف.";

/* connection failed title */
"Connection failed!" = "فشل الاتصال!";

/* Connection file is encrypted */
"Connection file is encrypted" = "تم تشفير ملف الاتصال";

/* Connection success very short status message */
"Connection succeeded" = "تم الاتصال بنجاح";

/* Console */
"Console" = "وحدة";

/* Console : Save as : Initial filename */
"ConsoleLog" = "سجل غير رسمي";

/* toolbar item label for switching to the Table Content tab */
"Content" = "محتوى";

/* content filter clause is empty tooltip. */
"Content Filter clause is empty." = "شرط تصفية المحتوى فارغ.";

/* continue button
 Continue button title */
"Continue" = "متابعة";

/* continue to print message */
"Continue to print?" = "هل تريد الاستمرار في الطباعة؟";

/* Copy as RTF */
"Copy as RTF" = "نسخ كـ RTF";

/* copy create func syntax menu item
 Table List : Context Menu : copy CREATE FUNCTION syntax */
"Copy Create Function Syntax" = "نسخ إنشاء صيغة دالة";

/* copy create proc syntax menu item
 Table List : Context Menu : Copy CREATE PROCEDURE syntax
 Table List : Gear Menu : Copy CREATE PROCEDURE syntax */
"Copy Create Procedure Syntax" = "نسخ إنشاء صيغة إجرائية";

/* copy create syntaxes menu item
 Table List : Context Menu : Copy CREATE syntax (multiple selection)
 Table List : Gear Menu : Copy CREATE syntax (multiple selection) */
"Copy Create Syntaxes" = "نسخ إنشاء توليفة";

/* copy create table syntax menu item
 Table List : Context Menu : Copy CREATE syntax (single table)
 Table List : Gear Menu : Copy CREATE syntax (single table) */
"Copy Create Table Syntax" = "نسخ إنشاء صيغة الجدول";

/* copy create view syntax menu item
 Table List : Context Menu : Copy CREATE view statement
 Table List : Gear Menu : Copy CREATE view statement */
"Copy Create View Syntax" = "نسخ إنشاء صيغة العرض";

/* copy server variable name menu item */
"Copy Variable Name" = "نسخ اسم المتغير";

/* copy server variable names menu item */
"Copy Variable Names" = "نسخ أسماء المتغيرات";

/* copy server variable value menu item */
"Copy Variable Value" = "نسخ قيمة المتغير";

/* copy server variable values menu item */
"Copy Variable Values" = "نسخ القيم المتغيرة";

/* Procedure/function export permission error */
"Could not export the %@ '%@' because of a permissions error.\n" = "تعذر تصدير %1$@ '%2$@بسبب خطأ في الأذونات.\n";

/* Error when we can't parse/split file as CSV */
"Could not parse file as CSV" = "تعذر تحليل الملف كـ CSV";

/* message when database selection failed */
"Could not select database" = "تعذر تحديد قاعدة البيانات";

/* Alter Database : Query Failed ($1 = mysql error message) */
"Couldn't alter database.\nMySQL said: %@" = "Couldn't alter database.\nMySQL said: %@";

/* Couldn't copy default themes to Application Support Theme folder!\nError: %@ */
"Couldn't copy default themes to Application Support Theme folder!\nError: %@" = "Couldn't copy default themes to Application Support Theme folder!\nError: %@";

/* message of panel when table cannot be created */
"Couldn't create '%@'.\nMySQL said: %@" = "تعذر إنشاء '%1$@'.\nMySQL قال: %2$@";

/* Couldn't create Application Support Bundle folder!\nError: %@ */
"Couldn't create Application Support Bundle folder!\nError: %@" = "Couldn't create Application Support Bundle folder!\nError: %@";

/* Couldn't create Application Support Theme folder!\nError: %@ */
"Couldn't create Application Support Theme folder!\nError: %@" = "Couldn't create Application Support Theme folder!\nError: %@";

/* message of panel when creation of db failed */
"Couldn't create database.\nMySQL said: %@" = "Couldn't create database.\nMySQL said: %@";

/* message of panel when an item cannot be deleted */
"Couldn't delete '%@'.\n\nMySQL said: %@" = "تعذر حذف '%1$@'.\n\nMySQL قال: %2$@";

/* message of panel when an item cannot be deleted including informative message about using force deletion */
"Couldn't delete '%@'.\n\nSelecting the 'Force delete' option may prevent this issue, but may leave the database in an inconsistent state.\n\nMySQL said: %@" = "تعذر حذف '%1$@'.\n\nاختيار خيار \"فرض الحذف\" قد يمنع هذه المشكلة، ولكن قد يترك قاعدة البيانات في حالة غير متسقة.\n\nMySQL قال: %2$@";

/* message of panel when field cannot be deleted */
"Couldn't delete field %@.\nMySQL said: %@" = "تعذر حذف الحقل %1$@.\nMySQL قال: %2$@";

/* message when deleteing all rows failed */
"Couldn't delete rows.\n\nMySQL said: %@" = "تعذر حذف الصفوف\n\nMySQL قال: %@";

/* message of panel when deleting db failed */
"Couldn't delete the database.\nMySQL said: %@" = "Couldn't delete the database.\nMySQL said: %@";

/* message of panel when an item cannot be renamed */
"Couldn't duplicate '%@'.\nMySQL said: %@" = "تعذر تكرار '%1$@'.\nMySQL قال: %2$@";

/* message of panel when flushing privs failed */
"Couldn't flush privileges.\nMySQL said: %@" = "Couldn't flush privileges.\nMySQL said: %@";

/* message of panel when table information cannot be retrieved */
"Couldn't get create syntax.\nMySQL said: %@" = "Couldn't get create syntax.\nMySQL said: %@";

/* Custom Query result editing error - could not identify a corresponding column */
"Couldn't identify field origin unambiguously. The column '%@' contains data from more than one table." = "تعذر تحديد مصدر الحقل بشكل لا لبس فيه. العمود '%@' يحتوي على بيانات من أكثر من جدول.";

/* message of panel when loading of row failed */
"Couldn't load the row. Reload the table to be sure that the row exists and use a primary key for your table." = "تعذر تحميل الصف. أعد تحميل الجدول للتأكد من وجود الصف واستخدام مفتاح أساسي لجدولك.";

/* Couldn't read the file content of */
"Couldn't read the file content of" = "تعذر قراءة محتوى الملف من";

/* text shown if an error occurred while sorting the result table */
"Couldn't sort column." = "تعذر فرز العمود.";

/* message of panel when sorting of table failed */
"Couldn't sort table. MySQL said: %@" = "تعذر فرز الجدول. قال MySQL: %@";

/* message of panel when error while updating field to db */
"Couldn't write field.\nMySQL said: %@" = "Couldn't write field.\nMySQL said: %@";

/* create syntax for table comment */
"Create syntax for" = "إنشاء بناء الجملة لـ";

/* Create syntax label */
"Create syntax for %@ '%@'" = "إنشاء بناء الجملة ل %1$@ '%2$@'";

/* Create syntaxes for selected items label */
"Create syntaxes for selected items" = "إنشاء مجموعة من العناصر المحددة";

/* Table Info Section : table create options */
"create_options: %@" = "إنشاء_خيارات: %@";

/* created: %@
 Table Info Section : time+date table was created at */
"created: %@" = "تم إنشاؤه: %@";

/* description of polygon */
"Creates a surface by combining one LinearRing (ie. a LineString that is closed and simple) as the outside boundary with zero or more inner LinearRings acting as \"holes\"." = "ينشئ سطحا بالجمع بين رابط واحد (أي . (أ) سلسلة اللينسترين المغلقة والبسيطة) بوصفها الحدود الخارجية التي تعمل فيها الخطوط الداخلية صفر أو أكثر بوصفها ”ثقوب“.";

/* Creating table task string */
"Creating %@..." = "إنشاء %@...";

/* Bundle Editor : Fallback Input source dropdown : 'current line' item */
"Current Line" = "الخط الحالي";

/* Bundle Editor : Fallback Input source dropdown : 'current query' item */
"Current Query" = "الاستعلام الحالي";

/* header for current selection in filtered list */
"CURRENT SELECTION" = "تحديد الموقع";

/* Bundle Editor : Fallback Input source dropdown : 'current word' item */
"Current Word" = "الكلمة الحالية";

/* SSH connection : debug header with user-defined ssh binary */
"Custom SSH binary enabled. Disable in Preferences to rule out incompatibilities!" = "تم تمكين SSH الثنائية. تعطيل في التفضيلات لاستبعاد التعارض!";

/* customize file name label */
"Customize Filename (%@)" = "تخصيص اسم الملف (%@)";

/* data access: %@ (%@) */
"data access: %@ (%@)" = "الوصول إلى البيانات: %1$@ (%2$@)";

/* Bundle Editor : Scope dropdown : 'data table' item
 data table menu item label */
"Data Table" = "جدول البيانات";

/* Bundle Editor : Outline View : 'Data Table' item : tooltip */
"Data Table Scope\ncommands will run on the Content and Query data tables" = "نطاق جدول البيانات\nسيتم تشغيل الأوامر على جداول بيانات المحتوى والاستعلام";

/* export filename database token
 export header database label
 export source
 Table Structure : Collation dropdown : 'item is the same as the collation of database' marker
 Table Structure : Encoding dropdown : 'item is database default' marker */
"Database" = "قاعدة البيانات";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'database changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'database changed' item */
"Database changed" = "تم تغيير قاعدة البيانات";

/* message of panel when no db name is given */
"Database must have a name." = "قاعدة البيانات يجب أن يكون لها اسم.";

/* databsse rename unsupported message */
"Database Rename Unsupported" = "إعادة تسمية قاعدة البيانات غير مدعومة";

/* export filename date token */
"Date" = "التاريخ";

/* export filename date token */
"Day" = "يوم";

/* Add Table Sheet : Collation : Default (unknown)
 Collation Dropdown : Default (unknown)
 default label
 Table Structure : Collation dropdown : 'item is the same as the default collation of the row's charset' marker */
"Default" = "الافتراضي";

/* Add Table : Collation : Default ($1 = collation name)
 Charset Dropdown : Default item ($1 = charset name)
 Collation Dropdown : Default ($1 = collation name)
 Collation Dropdown : Default collation for given charset ($1 = collation name)
 New Table Sheet : Table Engine Dropdown : Default */
"Default (%@)" = "الافتراضي (%@)";

/* default bundles update */
"Default Bundles Update" = "تحديث الحزم الافتراضية";

/* import : csv field mapping : field default value */
"Default: %@" = "الافتراضي: %@";

/* Query snippet default value placeholder */
"default_value" = "القيمة الافتراضية_";

/* definer label (Navigator) */
"Definer" = "المعرف";

/* definer: %@ */
"definer: %@" = "معرف: %@";

/* description of enum */
"Defines a list of members, of which every field can use at most one. Values are sorted by their index number (starting at 0 for the first member)." = "يحدد قائمة الأعضاء التي يمكن لكل حقل منها أن يستخدم واحداً على الأكثر. يتم فرز القيم حسب رقم فهرستها (بدءاً من 0 للعضو الأول).";

/* delete button */
"Delete" = "حذف";

/* delete table/view message */
"Delete %@ '%@'?" = "حذف %1$@ '%2$@'؟";

/* table structure : indexes : delete index : error 1553 : delete index and FK button */
"Delete Both" = "حذف كليهما";

/* delete database message */
"Delete database '%@'?" = "حذف قاعدة البيانات '%@'؟";

/* delete database message */
"Delete favorite '%@'?" = "حذف مفضلة '%@'؟";

/* delete field message */
"Delete field '%@'?" = "حذف الحقل '%@'؟";

/* delete func menu title */
"Delete Function" = "حذف الدالة";

/* delete functions menu title */
"Delete Functions" = "حذف الدوال";

/* delete database message */
"Delete group '%@'?" = "حذف المجموعة '%@'؟";

/* delete index message */
"Delete index '%@'?" = "حذف الفهرس '%@'؟";

/* delete items menu title */
"Delete Items" = "حذف العناصر";

/* delete proc menu title */
"Delete Procedure" = "حذف الإجراء";

/* delete procedures menu title */
"Delete Procedures" = "تحذف الإجراءات";

/* delete relation menu item */
"Delete Relation" = "حذف العلاقة";

/* delete relation message */
"Delete relation" = "حذف العلاقة";

/* delete relations menu item */
"Delete Relations" = "حذف العلاقات";

/* delete row menu item singular */
"Delete Row" = "حذف الصف";

/* delete rows menu item plural */
"Delete Rows" = "حذف الصفوف";

/* delete rows message */
"Delete rows?" = "حذف الصفوف؟";

/* delete tables/views message */
"Delete selected %@?" = "حذف %@ المحدد ؟";

/* delete selected row message */
"Delete selected row?" = "حذف الصف المحدد؟";

/* delete table menu title */
"Delete Table..." = "حذف الجدول...";

/* delete tables menu title */
"Delete Tables" = "حذف الجداول";

/* delete trigger menu item */
"Delete Trigger" = "حذف المشغل";

/* delete trigger message */
"Delete trigger" = "حذف المشغل";

/* delete triggers menu item */
"Delete Triggers" = "حذف المحطات";

/* delete view menu title */
"Delete View" = "حذف طريقة العرض";

/* delete views menu title */
"Delete Views" = "حذف المشاهدات";

/* Preferences : Network : SSL Chiper suites : List seperator */
"Disabled Cipher Suites" = "تم تعطيل سيفر السويتيه";

/* force table deltion button text tooltip */
"Disables foreign key checks (FOREIGN_KEY_CHECKS) before deletion and re-enables them afterwards." = "تعطيل عمليات فحص المفاتيح الأجنبية (FOREIGN_KEY_CHECKS) قبل الحذف وإعادة تمكينها بعد ذلك.";

/* discard changes button */
"Discard changes" = "تجاهل التغييرات";

/* description for disconnected notification */
"Disconnected from %@" = "قطع الاتصال بـ %@";

/* do update operator tooltip */
"Do UPDATE where field contents match" = "قم بالترقية حيث يتطابق محتوى الحقل";

/* message of panel asking for confirmation for loading large text into the query editor */
"Do you really want to load a SQL file with %@ of data into the Query Editor?" = "هل تريد حقاً تحميل ملف SQL مع %@ من البيانات في محرر الاستبيانات؟";

/* message of panel asking for confirmation for inserting large text from dragging action */
"Do you really want to proceed with %@ of data?" = "هل تريد حقاً المضي قدماً مع %@ من البيانات؟";

/* shutdown server : confirmation dialog : title */
"Do you really want to shutdown the server?" = "هل تريد حقاً إغلاق الخادم؟";

/* dtd identifier label (Navigator) */
"DTD Identifier" = "معرف DTD";

/* sql export dump of table label */
"Dump of table" = "تفريغ الجدول";

/* sql export dump of view label */
"Dump of view" = "تفريغ العرض";

/* text showing that app is writing dump */
"Dumping..." = "إغراق...";

/* duplicate object message */
"Duplicate %@ '%@' to:" = "تكرار %1$@ '%2$@' إلى:";

/* duplicate func menu title */
"Duplicate Function..." = "تكرار الدالة...";

/* duplicate host message */
"Duplicate Host" = "تكرار المضيف";

/* duplicate proc menu title */
"Duplicate Procedure..." = "تكرار الإجراء...";

/* duplicate table menu title */
"Duplicate Table..." = "تكرار الجدول...";

/* duplicate user message */
"Duplicate User" = "مستخدم مكرر";

/* duplicate view menu title */
"Duplicate View..." = "تكرار العرض...";

/* partial copy database support informative message */
"Duplicating the database '%@' is only partially supported as it contains objects other than tables (i.e. views, procedures, functions, etc.), which will not be copied.\n\nWould you like to continue?" = "Duplicating the database '%@' is only partially supported as it contains objects other than tables (i.e. views, procedures, functions, etc.), which will not be copied.\n\nWould you like to continue?";

/* edit filter */
"Edit Filters…" = "تحرير الفلاتر…";

/* Edit row button */
"Edit row" = "تعديل الصف";

/* toolbar item label for switching to the Table Structure tab */
"Edit Table Structure" = "تعديل بنية الجدول";

/* edit theme list label */
"Edit Theme List…" = "تحرير قائمة السمات…";

/* edit user-defined filter */
"Edit user-defined Filters…" = "تحرير عوامل التصفية المعرّفة للمستخدم…";

/* empty query message */
"Empty query" = "استعلام فارغ";

/* MySQL search language code - eg in http://search.mysql.com/search?q=select&site=refman-50&lr=lang_en */
"en" = "ش";

/* encoding label (Navigator) */
"Encoding" = "الترميز";

/* Table Info Section : $1 = table charset */
"encoding: %1$@" = "الترميز: %1$@";

/* Table Info Section : $1 = table charset, $2 = table collation */
"encoding: %1$@ (%2$@)" = "الترميز: %1$@ (%2$@)";

/* Table Info Section : Table Engine */
"engine: %@" = "محركة: %@";

/* enter connection details label */
"Enter connection details below, or choose a favorite" = "أدخل تفاصيل الاتصال أدناه، أو اختر المفضلة";

/* SSH key password prompt */
"Enter your password for the SSH key\n\"%@\"" = "أدخل كلمة المرور الخاصة بك لمفتاح SSH\n'%@";

/* Bundle Editor : Fallback Input source dropdown : 'entire content' item
 Bundle Editor : Scope=Field : Input source dropdown: 'entire content' item */
"Entire Content" = "محتوى كامل";

/* Bundle Editor : Copy-Command-Error : error dialog title
 error */
"Error" = "خطأ";

/* error adding field message */
"Error adding field" = "خطأ في إضافة حقل";

/* error adding new column message */
"Error adding new column" = "خطأ في إضافة عمود جديد";

/* error adding new table message */
"Error adding new table" = "خطأ في إضافة جدول جديد";

/* error adding password to keychain message */
"Error adding password to Keychain" = "خطأ في إضافة كلمة المرور إلى سلسلة المفاتيح";

/* error changing field message */
"Error changing field" = "خطأ في تغيير الحقل";

/* error changing table collation message */
"Error changing table collation" = "خطأ في تغيير تجميع الجدول";

/* error changing table comment message */
"Error changing table comment" = "خطأ في تغيير تعليق الجدول";

/* error changing table encoding message */
"Error changing table encoding" = "خطأ في تغيير ترميز الجدول";

/* error changing table type message */
"Error changing table type" = "خطأ في تغيير نوع الجدول";

/* error creating relation message */
"Error creating relation" = "خطأ في إنشاء العلاقة";

/* error creating trigger message */
"Error creating trigger" = "خطأ في إنشاء المشغل";

/* error for message */
"Error for" = "خطأ لـ";

/* error for bash command ($1), $2=message */
"Error for “%1$@”:\n%2$@" = "خطأ ل \"%1$@\":\n%2$@";

/* error moving field message */
"Error moving field" = "خطأ في نقل الحقل";

/* error occurred */
"error occurred" = "حدث خطأ";

/* error reading import file */
"Error reading import file." = "خطأ في قراءة ملف الاستيراد.";

/* error finding keychain item to edit message */
"Error retrieving Keychain item to edit" = "خطأ في استرداد عنصر سلسلة المفاتيح لتعديل";

/* error retrieving table information message */
"Error retrieving table information" = "خطأ في استرداد معلومات الجدول";

/* error retrieving trigger information message */
"Error retrieving trigger information" = "خطأ في استرداد معلومات المشغل";

/* error truncating table message */
"Error truncating table" = "خطأ في تقسيم الجدول";

/* error updating keychain item message */
"Error updating Keychain item" = "خطأ في تحديث عنصر سلسلة المفاتيح";

/* error while analyzing selected items message */
"Error while analyzing selected items" = "حدث خطأ أثناء تحليل العناصر المحددة";

/* error while checking selected items message */
"Error while checking selected items" = "حدث خطأ أثناء التحقق من العناصر المحددة";

/* error while converting color scheme data */
"Error while converting color scheme data" = "حدث خطأ أثناء تحويل بيانات مخطط الألوان";

/* error while converting connection data */
"Error while converting connection data" = "حدث خطأ أثناء تحويل بيانات الاتصال";

/* Content filters could not be converted to plist upon export - message title (ContentFilterManager) */
"Error while converting content filter data" = "حدث خطأ أثناء تحويل بيانات فلتر المحتوى";

/* error while converting query favorite data */
"Error while converting query favorite data" = "حدث خطأ أثناء تحويل بيانات الاستعلام المفضلة";

/* error while converting session data */
"Error while converting session data" = "حدث خطأ أثناء تحويل بيانات الجلسة";

/* Error while deleting field */
"Error while deleting field" = "حدث خطأ أثناء حذف الحقل";

/* Bundle Editor : Copy-Command-Error : Copying failed error message */
"Error while duplicating Bundle content." = "حدث خطأ أثناء تكرار محتوى الحزمة.";

/* error while executing javascript bash command */
"Error while executing JavaScript BASH command" = "حدث خطأ أثناء تنفيذ أمر JavaScript BASH";

/* error while fetching the optimized field type message */
"Error while fetching the optimized field type" = "حدث خطأ أثناء جلب نوع الحقل المحسن";

/* error while flushing selected items message */
"Error while flushing selected items" = "حدث خطأ أثناء مسح العناصر المحددة";

/* error while importing table message */
"Error while importing table" = "حدث خطأ أثناء استيراد الجدول";

/* Open Files : Bundle : Install-Error : error dialog title
 Open Files : Bundle : UUID : Error dialog title */
"Error while installing Bundle" = "حدث خطأ أثناء تثبيت الحزمة";

/* error while installing color theme file */
"Error while installing color theme file" = "حدث خطأ أثناء تثبيت ملف السمة الملونة";

/* Bundle Editor : Trash-Bundle(s)-Error : error dialog title
 error while moving %@ to trash
 Open Files : Bundle : Already-Installed : Delete-Old-Error : Could not delete old bundle before installing new version. */
"Error while moving %@ to Trash." = "حدث خطأ أثناء نقل %@ إلى سلة المهملات.";

/* error while optimizing selected items message */
"Error while optimizing selected items" = "حدث خطأ أثناء تحسين العناصر المحددة";

/* error while parsing CREATE TABLE syntax */
"Error while parsing CREATE TABLE syntax" = "حدث خطأ أثناء تحليل صيغة الإنشاء";

/* error while reading connection data file */
"Error while reading connection data file" = "حدث خطأ أثناء قراءة ملف بيانات الاتصال";

/* error while reading data file */
"Error while reading data file" = "حدث خطأ أثناء قراءة ملف البيانات";

/* error while repairing selected items message */
"Error while repairing selected items" = "حدث خطأ أثناء إصلاح العناصر المحددة";

/* Bundle Editor : Save-Bundle-Error : error dialog title */
"Error while saving the Bundle." = "حدث خطأ أثناء حفظ الحزمة.";

/* Bundle Editor : Save-and-Close-Error : error dialog title */
"Error while saving %@." = "حدث خطأ أثناء حفظ %@.";

/* Errors title */
"Errors" = "أخطاء";

/* Mysql Help Viewer : Help Topic: Example section title */
"Example:" = "مثال:";

/* Bundle Editor : BLOB dropdown : 'exclude BLOB' item */
"exclude BLOB" = "استبعاد BLOB";

/* execution privilege label (Navigator) */
"Execution Privilege" = "امتياز التنفيذ";

/* execution privilege: %@ */
"execution privilege: %@" = "امتياز التنفيذ: %@";

/* execution stopped message */
"Execution stopped!\n" = "توقف التنفيذ!\n";

/* export selected favorites menu item */
"Export Selected..." = "تصدير المحدد...";

/* text showing that the application is importing a supplied format */
"Exporting %@" = "تصدير %@";

/* text showing that the application is exporting a Dot file */
"Exporting Dot File" = "تصدير ملف Dot";

/* text showing that the application is exporting SQL */
"Exporting SQL" = "تصدير SQL";

/* extra label (Navigator) */
"Extra" = "اضافية";

/* table structure : indexes : delete index : no columns error : title */
"Failed to remove index '%@'" = "فشل في إزالة الفهرس '%@'";

/* fatal error */
"Fatal Error" = "خطأ مميت";

/* export filename favorite name token */
"Favorite" = "المفضلة";

/* favorites label */
"Favorites" = "المفضلة";

/* favorites export error message */
"Favorites export error" = "خطأ تصدير المفضلة";

/* favorites import error message */
"Favorites import error" = "خطأ استيراد المفضلة";

/* export label showing that the app is fetching data */
"Fetching data..." = "جلب البيانات...";

/* fetching database structure data in progress */
"fetching database structure data in progress" = "جلب بيانات بنية قاعدة البيانات قيد التقدم";

/* fetching database structure in progress */
"fetching database structure in progress" = "جلب بنية قاعدة البيانات في تقدم";

/* fetching table data for completion in progress message */
"fetching table data…" = "جلب بيانات الجدول…";

/* popup menuitem for field (showing only if disabled) */
"field" = "حقل";

/* Field */
"Field" = "الحقل";

/* Query result editing error - could not match row being edited uniquely */
"Field is not editable. Couldn't identify field origin unambiguously (%ld matches)." = "الحقل غير قابل للتحرير. تعذر تحديد مصدر الحقل بشكل لا لبس فيه ( مطابقة%ld).";

/* field is not editable due to no table/database */
"Field is not editable. Field has no or multiple table or database origin(s)." = "الحقل غير قابل للتحرير. الحقل لا يحتوي على جدول أو جداول متعددة أو منشأ قاعدة البيانات.";

/* Custom Query result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload data, check encoding, or try to add\na primary key field or more fields\nin your SELECT statement for table '%@'\nto identify field origin unambiguously." = "Field is not editable. No matching record found.\nReload data, check encoding, or try to add\na primary key field or more fields\nin your SELECT statement for table '%@'\nto identify field origin unambiguously.";

/* Table Content result editing error - could not identify original row */
"Field is not editable. No matching record found.\nReload table, check the encoding, or try to add\na primary key field or more fields\nin the view declaration of '%@' to identify\nfield origin unambiguously." = "Field is not editable. No matching record found.\nReload table, check the encoding, or try to add\na primary key field or more fields\nin the view declaration of '%@' to identify\nfield origin unambiguously.";

/* error while reading data file */
"File couldn't be read." = "تعذر قراءة الملف.";
"File couldn't be read: %@\n\nIt will be deleted." = "تعذر قراءة الملف: %@\n\nسيتم حذفه.";

/* File read error title (Import Dialog) */
"File read error" = "خطأ في قراءة الملف";

/* All export files already exist explanatory text */
"Files with the same names already exist in the target folder. Replacing them will overwrite their current contents." = "الملفات بنفس الأسماء موجودة بالفعل في المجلد المستهدف. سيتم استبدالها بمحتوياتها الحالية.";

/* filter label */
"Filter" = "تصفية";

/* apply filter label */
"Apply Filter(s)" = "تطبيق عامل التصفية";

/* filter tables menu item */
"Filter Tables" = "تصفية الجداول";

/* export source */
"Filtered table content" = "تصفية محتوى الجدول";

/* filtering failed. please try again. tooltip */
"Filtering failed. Please try again." = "فشلت عملية التصفية. الرجاء المحاولة مرة أخرى.";

/* Filtering table task description */
"Filtering table..." = "تصفية الجدول...";

/* description for finished exporting notification */
"Finished exporting to %@" = "انتهى التصدير إلى %@";

/* description for finished importing notification */
"Finished importing %@" = "انتهى استيراد %@";

/* FLUSH one or more tables - result title */
"Flush %@" = "دفع %@";

/* flush selected items menu item */
"Flush Selected Items" = "مسح العناصر المحددة";

/* flush table menu item */
"Flush Table" = "جدول الشحن";

/* flush table failed message */
"Flush table failed." = "فشل دفع الجدول.";

/* flush view menu item */
"Flush View" = "عرض الفلاش";

/* title of panel when successfully flushed privs */
"Flushed Privileges" = "الامتيازات المسحوبة";

/* For BIT fields only “1” or “0” are allowed. */
"For BIT fields only “1” or “0” are allowed." = "وفيما يتعلق بميادين تكنولوجيا المعلومات الثنائية لا يسمح إلا ب \"1\" أو \"صفر\".";

/* force table deletion button text */
"Force delete (disables integrity checks)" = "إجبار الحذف (تعطيل عمليات التحقق من السلامة)";

/* full text index menu item title */
"FULLTEXT" = "FULLTEXT";

/* function */
"function" = "دالة";

/* header for function info pane */
"FUNCTION INFORMATION" = "معلومات الفراغ";

/* functions */
"functions" = "الدوال";

/* Bundle Editor : Scope dropdown : 'general' item
 general menu item label
 general preference pane name */
"General" = "عام";

/* general preference pane tooltip */
"General Preferences" = "التفضيلات العامة";

/* Bundle Editor : Outline View : 'General' item : tooltip */
"General Scope\ncommands will run application-wide" = "سيتم تشغيل أوامر النطاق العام\nعلى نطاق التطبيق";

/* generating print document status message */
"Generating print document..." = "توليد وثيقة مطبوعة...";

/* export header generation time label */
"Generation Time" = "وقت الجيل";

/* Content Filter Manager : Filter Entry List: 'Global' Header
 Query Favorites : List : Section Heading : global query favorites */
"Global" = "عام";

/* Query Favorites : List : Section Heading : global : tooltip */
"Globally stored favorites" = "المفضلة المخزنة عالميا";

/* Preferences : Notifications : status text : notification setup */
"Notifications are delivered via the Notification Center." = "يتم إيصال الإشعارات عن طريق مركز الإشعار.";

/* Gzip compression export summary - within a sentence */
"Gzip compression" = "ضغط Gzip";

/* MySQL Help Viewer : Results list : Page title */
"Help topics for %@" = "مواضيع المساعدة لـ %@";

/* hide console */
"Hide Console" = "إخفاء وحدة التحكم";

/* hide navigator */
"Hide Navigator" = "Hide Navigator";

/* hide tab bar */
"Hide Tab Bar" = "إخفاء شريط التبويب";

/* Hide Toolbar menu item */
"Hide Toolbar" = "إخفاء شريط الأدوات";

/* export filename host token
 export header host label */
"Host" = "المضيف";

/* description of double */
"IEEE 754 double-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 قيمة نقطة تعويم ذات نقطتين دقيقتين. M هو العدد الأقصى للأرقام، وقد يكون D منها بعد النقطة العشرية. ملاحظة: العديد من الأرقام العشرية لا يمكن تقريبها إلا عن طريق قيم النقاط العائمة. انظر DECIMAL إذا كنت تحتاج إلى نتائج دقيقة.";

/* description of float */
"IEEE 754 single-precision floating-point value. M is the maxium number of digits, of which D may be after the decimal point. Note: Many decimal numbers can only be approximated by floating-point values. See DECIMAL if you require exact results." = "IEEE 754 قيمة النقطة العائمة الأحادية الدقة. M هو العدد الأقصى للأرقام، وقد يكون D منها بعد النقطة العشرية. ملاحظة: العديد من الأرقام العشرية لا يمكن تقريبها إلا عن طريق قيم النقاط العائمة. انظر DECIMAL إذا كنت تحتاج إلى نتائج دقيقة.";

/* ignore button */
"Ignore" = "تجاهل";

/* ignore errors button */
"Ignore All Errors" = "تجاهل جميع الأخطاء";

/* ignore all fields menu item */
"Ignore all Fields" = "تجاهل جميع الحقول";

/* ignore field label */
"Ignore field" = "تجاهل الحقل";

/* ignore field label */
"Ignore Field" = "تجاهل الحقل";

/* import button */
"Import" = "استيراد";

/* import all fields menu item */
"Import all Fields" = "استيراد جميع الحقول";

/* sql import : charset error alert : continue button */
"Import Anyway" = "استيراد على أي حال";

/* import cancelled message */
"Import cancelled!\n" = "تم إلغاء الاستيراد!\n";

/* Import Error title */
"Import Error" = "خطأ في الاستيراد";

/* import field operator tooltip */
"Import field" = "استيراد الحقل";

/* import file does not exist message */
"Import file does not exist." = "ملف الاستيراد غير موجود.";

/* Export file format cannot be imported warning */
"Import of the selected data is currently not supported." = "استيراد البيانات المحددة غير مدعوم حاليا.";

/* SQL import progress text */
"Imported %@ of %@" = "مستورد %1$@ من %2$@";

/* CSV import progress text where total size is unknown */
"Imported %@ of CSV data" = "تم استيراد %@ من بيانات CSV";

/* SQL import progress text where total size is unknown */
"Imported %@ of SQL" = "مستورد %@ من SQL";

/* text showing that the application is importing CSV */
"Importing CSV" = "استيراد CSV";

/* text showing that the application is importing SQL */
"Importing SQL" = "استيراد SQL";

/* Bundle Editor : BLOB dropdown : 'include BLOB' item */
"include BLOB" = "تضمين BLOB";

/* include content table column tooltip */
"Include content" = "تضمين المحتوى";

/* sql import error message */
"Incompatible encoding in SQL file" = "ترميز غير متوافق في ملف SQL";

/* header for blank info pane */
"INFORMATION" = "معلومات";

/* New Table Sheet : Table Collation Dropdown : Default inherited from database
 New Table Sheet : Table Encoding Dropdown : Default inherited from database */
"Inherit from database (%@)" = "ورث من قاعدة البيانات (%@)";

/* initializing export label */
"Initializing..." = "تهيئة...";

/* Bundle Editor : Scope dropdown : 'input field' item
 input field menu item label */
"Input Field" = "حقل الإدخال";

/* input field  doesn't support insertion of snippets. */
"Input Field doesn't support insertion of snippets." = "حقل الإدخال لا يدعم إدخال كتل الكود.";

/* input field is not editable. */
"Input Field is not editable." = "حقل الإدخال غير قابل للتحرير.";

/* Bundle Editor : Outline View : 'Input Field' item : tooltip */
"Input Field Scope\ncommands will run on each text input field" = "نطاق حقل الإدخال\nسيتم تشغيل الأوامر في كل حقل إدخال نصي";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as snippet' item */
"Insert as Snippet" = "إدراج كتلة كتلة";

/* Bundle Editor : Scope=Field : Output dropdown : 'insert as text' item */
"Insert as Text" = "إدراج كنص";

/* Bundle Editor : Outline View : 'BUNDLES' item : tooltip */
"Installed Bundles" = "الحزم المثبتة";

/* Open Files : Bundle : Already-Installed : 'Update Bundle' question dialog title */
"Installing Bundle" = "تثبيت الحزمة";

/* insufficient details message */
"Insufficient connection details" = "تفاصيل الاتصال غير كافية";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please enter at least the hostname." = "التفاصيل المقدمة غير كافية لإنشاء اتصال. الرجاء إدخال اسم المضيف على الأقل.";

/* insufficient SSH tunnel details informative message */
"Insufficient details provided to establish a connection. Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "التفاصيل المقدمة غير كافية لإنشاء اتصال. الرجاء إدخال اسم المضيف لتنزيل SSH، أو تعطيل نفق SSH.";

/* insufficient details informative message */
"Insufficient details provided to establish a connection. Please provide at least a host." = "التفاصيل المقدمة غير كافية لإنشاء اتصال. يرجى تقديم مضيف على الأقل.";

/* Interpret data as: */
"Interpret data as:" = "تفسير البيانات بأنها:";

/* Invalid database very short status message */
"Invalid database" = "قاعدة بيانات غير صالحة";

/* export : import settings : file error title */
"Invalid file supplied!" = "تم توفير ملف غير صالح!";

/* table content : editing : error message title when parsing as hex string failed */
"Invalid hexadecimal value" = "قيمة سداسية المستوى غير صحيحة";

/* is deterministic label (Navigator) */
"Is Deterministic" = "هو ديكوزاري";

/* is nullable label (Navigator) */
"Is Nullable" = "غير قابل للإلغاء";

/* is updatable: %@ */
"is updatable: %@" = "هو قاعدة بيانات: %@";

/* items */
"items" = "العناصر";

/* javascript exception */
"JavaScript Exception" = "استثناء جافا سكريبت";

/* javascript parsing error */
"JavaScript Parsing Error" = "خطأ في تحليل جافا سكريبت";

/* key label (Navigator) */
"Key" = "المفتاح";

/* keyword label for color table (Prefs > Editor) */
"Keyword" = "Keyword";

/* kill button */
"Kill" = "اقتل";

/* kill connection message */
"Kill connection?" = "اقتل الاتصال؟";

/* kill query message */
"Kill query?" = "هل تريد الاستعلام؟";

/* Last Error Message */
"Last Error Message" = "آخر رسالة خطأ";

/* Last Used entry in favorites menu */
"Last Used" = "آخر استخدام";

/* range for json type */
"Limited to @@max_allowed_packet" = "محدود بـ @@max_allowed_packet";

/* Loading table task string */
"Loading %@..." = "جاري تحميل %@...";

/* Loading database task string */
"Loading database '%@'..." = "تحميل قاعدة البيانات '%@'...";

/* Loading history entry task desc */
"Loading history entry..." = "جارٍ تحميل إدخال...";

/* Loading table page task string */
"Loading page %lu..." = "تحميل الصفحة %lu...";

/* Loading referece task string */
"Loading reference..." = "تحميل المرجع...";

/* Loading table data string */
"Loading table data..." = "تحميل بيانات الجدول...";

/* Low memory export summary */
"Low memory" = "الذاكرة المنخفضة";

/* range of decimal */
"M (precision): Up to 65 digits\nD (scale): 0 to 30 digits" = "M (دقيقة): حتى 65 رقما\nD (المقياس): 0 إلى 30 رقما";

/* range for blob type
 range for varbinary type */
"M: %@ to %@ bytes" = "م: %1$@ إلى %2$@ بايت";

/* range for text type
 range for varchar type */
"M: %@ to %@ characters" = "M: %1$@ إلى %2$@ أحرف";

/* range for longtext type */
"M: %@ to %@ characters (4 GiB)" = "م: %1$@ إلى %2$@ أحرف (4 GiB)";

/* range for binary type */
"M: 0 to 255 bytes" = "م: 0 إلى 255 بايت";

/* range for char type */
"M: 0 to 255 characters" = "ما: 0 إلى 255 حرفاً";

/* range for bit type */
"M: 1 (default) to 64" = "م: 1 (افتراضي) إلى 64";

/* connection view : ssl : key file picker : wrong format error description */
"Make sure the file contains a RSA private key and is using PEM encoding." = "تأكد من أن الملف يحتوي على مفتاح RSA خاص ويستخدم ترميز PEM.";

/* connection view : ssl : client cert picker : wrong format error description */
"Make sure the file contains a X.509 client certificate and is using PEM encoding." = "تأكد من أن الملف يحتوي على شهادة عميل X.509 ويستخدم ترميز PEM.";

/* match field menu item */
"Match Field" = "حقل المطابقة";

/* Shown when user inserts too many arguments (ContentFilterManager) */
"Maximum number of arguments is 2!" = "الحد الأقصى لعدد الحجج هو 2!";

/* Maximum text length is set to %ld. */
"Maximum text length is set to %ld." = "تم تعيين الحد الأقصى لطول النص إلى %ld.";

/* Maximum text length is set to %ld. Inserted text was truncated. */
"Maximum text length is set to %ld. Inserted text was truncated." = "تم تعيين الحد الأقصى لطول النص إلى %ld. تم اقتطاع النص المُدرج.";

/* Maximum text length is set to %llu. */
"Maximum text length is set to %llu." = "تم تعيين الحد الأقصى لطول النص إلى %llu.";

/* Maximum text length is set to %llu. Inserted text was truncated. */
"Maximum text length is set to %llu. Inserted text was truncated." = "تم تعيين الحد الأقصى لطول النص إلى %llu. تم اقتطاع النص المُدرج.";

/* message column title */
"Message" = "رسالة";

/* mgtemplateengine error */
"MGTemplateEngine Error" = "خطأ MGTemplateEngine";

/* export filename date token */
"Month" = "شهر";

/* multiple selection */
"multiple selection" = "اختيار متعدد";

/* MySQL connecting very short status message */
"MySQL connecting..." = "اتصال MySQL...";

/* mysql error message */
"MySQL Error" = "MySQL Error";

/* mysql help */
"MySQL Help" = "MySQL Help";

/* MySQL Help for Selection */
"MySQL Help for Selection" = "مساعدة MySQL للاختيار";

/* MySQL Help for Word */
"MySQL Help for Word" = "مساعدة MySQL للكلمات";

/* mysql help categories */
"MySQL Help – Categories" = "MySQL تعليمات - الفئات";

/* mysql said message */
"MySQL said:" = "قال MySQL:";

/* shutdown server : error dialog : message */
"MySQL said:\n%@" = "قالت MySQL:\n%@";

/* message of panel when error while adding row to db */
"MySQL said:\n\n%@" = "قال MySQL:\n\n%@";

/* Preferences : Themes : Initial filename for 'Export' */
"MyTheme" = "ثيمة";

/* network preference pane name */
"Network" = "الشبكة";

/* network preference pane tooltip */
"Network Preferences" = "تفضيلات الشبكة";

/* file preference pane name */
"Files" = "الملفات";

/* file preference pane tooltip */
"File Preferences" = "تفضيلات الملف";

/* Bundle Editor : Default name for new bundle in the list on the left */
"New Bundle" = "حزمة جديدة";

/* new column name placeholder string */
"New Column Name" = "اسم العمود الجديد";

/* new favorite name */
"New Favorite" = "مفضلة جديدة";

/* Content Filter Manager : Initial name for new filter */
"New Filter" = "فلتر جديد";

/* new folder placeholder name */
"New Folder" = "مجلد جديد";

/* Bundle Editor : Default name for a new bundle in the menu */
"New Name" = "اسم جديد";

/* new table menu item */
"New Table" = "جدول جديد";

/* error that no color theme found */
"No color theme data found." = "لم يتم العثور على بيانات سمة الألوان.";

/* No compression export summary - within a sentence */
"no compression" = "بدون ضغط";

/* no connection available message */
"No connection available" = "لا يوجد اتصال متاح";

/* no connection data found */
"No connection data found." = "لم يتم العثور على بيانات اتصال.";

/* No content filters were found in file to import (ContentFilterManager) */
"No content filters found." = "لم يتم العثور على فلاتر محتوى.";

/* no data found */
"No data found." = "لا توجد بيانات.";

/* No errors title */
"No errors" = "لا توجد أخطاء";

/* No favorites entry in favorites menu */
"No Favorties" = "لا توجد مفضلة";

/* All export files creation error title */
"No files could be created" = "لا يمكن إنشاء ملفات";

/* no item found message */
"No item found" = "لم يتم العثور على عنصر";

/* SSH tunnel could not be created because no local port could be allocated */
"No local port could be allocated for the SSH Tunnel." = "لا يمكن تخصيص أي منفذ محلي لنفق SSH.";

/* header for no matches in filtered list */
"NO MATCHES" = "لا توجد علامات";

/* no optimized field type found. message */
"No optimized field type found." = "لم يتم العثور على نوع حقل مُحسَّن.";

/* error that no query favorites found */
"No query favorites found." = "لم يتم العثور على أي استعلام مفضل.";

/* Mysql Help Viewer : Search : No results */
"No results found." = "لا توجد نتائج.";

/* Bundle Editor : Fallback Input source dropdown : 'none' item
 Bundle Editor : Scope=? : ? dropdown: 'None' item
 Bundle Editor : Scope=Data-Table : Input source dropdown: 'none' item
 Bundle Editor : Scope=Data-Table : Output dropdown : 'none' item
 Bundle Editor : Scope=Data-Table : Trigger dropdown : 'none' item
 Bundle Editor : Scope=Field : Input source dropdown: 'None' item
 Bundle Editor : Scope=Field : Output dropdown : 'none' item
 Bundle Editor : Scope=Field : Trigger dropdown : 'none' item
 Bundle Editor : Scope=General : Input source dropdown: 'None' item
 Bundle Editor : Scope=General : Output dropdown : 'none' item
 Bundle Editor : Scope=General : Trigger dropdown : 'none' item
 compression: none */
"None" = "لا";

/* not available label */
"Not available" = "غير متوفر";

/* Argument count (ContentFilterManager) */
"Number of arguments: %lu" = "عدد الحجوزات: %lu";

/* numeric label for color table (Prefs > Editor) */
"Numeric" = "Numeric";

/* Bundle Editor : Copy-Command-Error : OK button
 Bundle Editor : Save-and-Close-Error : OK button
 Bundle Editor : Save-Bundle-Error : OK button
 Bundle Editor : Trash-Bundle(s)-Error : OK button
 OK
 OK button
 Open Files : Bundle : Already-Installed : Delete-Old-Error : OK button
 Open Files : Bundle : Install-Error : OK button
 Open Files : Bundle : UUID : OK button
 Preferences : Network : Custom SSH client : warning dialog : accept button */
"OK" = "حسناً";

/* Table Content : Remove Row : Result : Too Many : Part 1 : n+1 rows instead of n selected were deleted. */
"One additional row was removed!" = "تمت إزالة صف إضافي واحد!";

/* Table Content : Remove Row : Result : Too Few : Part 1 : Only n-1 of n selected rows were deleted. */
"One row was not removed." = "لم تتم إزالة صف واحد.";

/* Only one dragged item allowed. */
"Only one dragged item allowed." = "سُحب عنصر واحد فقط مسموح به.";

/* partial copy database support message */
"Only Partially Supported" = "دعم جزئي فقط";

/* open function in new table title */
"Open Function in New Tab" = "فتح الدالة في علامة تبويب جديدة";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Function in New Window" = "فتح الدالة في نافذة جديدة";

/* open connection in new tab context menu item */
"Open in New Tab" = "فتح في علامة تبويب جديدة";

/* menu item open in new window */
"Open in New Window" = "فتح في نافذة جديدة";

/* open procedure in new table title */
"Open Procedure in New Tab" = "فتح الإجراء في علامة تبويب جديدة";

/* Table List : Context Menu : duplicate connection to new window
 Table List : Gear Menu : duplicate connection to new window */
"Open Procedure in New Window" = "إجراءات مفتوحة في نافذة جديدة";

/* open table in new tab title
 open table in new table title */
"Open Table in New Tab" = "فتح الجدول في علامة تبويب جديدة";

/* Table List : Context Menu : Duplicate connection to new window
 Table List : Gear Menu : Duplicate connection to new window */
"Open Table in New Window" = "فتح الجدول في نافذة جديدة";

/* open view in new tab title
 open view in new table title */
"Open View in New Tab" = "فتح العرض في علامة تبويب جديدة";

/* Tables List : Context Menu : Duplicate connection to new window
 Tables List : Gear Menu : Duplicate connection to new window */
"Open View in New Window" = "فتح العرض في نافذة جديدة";

/* menu item open %@ in new window */
"Open %@ in New Window" = "فتح %@ في نافذة جديدة";

/* OPTIMIZE one or more tables - result title */
"Optimize %@" = "تحسين %@";

/* optimize selected items menu item */
"Optimize Selected Items" = "تحسين العناصر المحددة";

/* optimize table menu item */
"Optimize Table" = "تحسين الجدول";

/* optimize table failed message */
"Optimize table failed." = "فشل تحسين الجدول.";

/* Optimized type for field %@ */
"Optimized type for field '%@'" = "نوع محسن للحقل '%@'";

/* optional placeholder string */
"optional" = "اختياري";

/* Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed. */
"Passed parameter couldn't be interpreted. Only string or array (with 2 elements) are allowed." = "المعامل الذي تم اجتيازه لا يمكن تفسيره. يسمح فقط بسلسلة أو مصفوفة (مع عنصرين).";

/* Permission Denied */
"Permission Denied" = "تم رفض الإذن";

/* please choose a favorite connection view label */
"Please choose a favorite" = "الرجاء اختيار المفضلة";

/* message of panel when ssh details are incomplete */
"Please enter the hostname for the SSH Tunnel, or disable the SSH Tunnel." = "الرجاء إدخال اسم المضيف لتنزيل SSH، أو تعطيل نفق SSH.";

/* Please enter the password */
"Please enter the password for ‘%@’:" = "الرجاء إدخال كلمة المرور لـ ''%@':";

/* print button */
"Print" = "طباعة";

/* print page menu item title */
"Print Page…" = "طباعة الصفحة…";

/* privileges label (Navigator) */
"Privileges" = "الامتيازات";

/* procedure */
"procedure" = "الإجراء";

/* header for procedure info pane */
"PROCEDURE INFORMATION" = "معلومات العملية";

/* procedures */
"procedures" = "الإجراءات";

/* proceed button */
"Proceed" = "المتابعة";

/* header for procs & funcs list */
"PROCS & FUNCS" = "PROCS & FUNCS";

/* toolbar item label for switching to the Run Query tab */
"Query" = "الاستعلام";

/* query background label for color table (Prefs > Editor) */
"Query Background" = "خلفية الاستعلام";

/* Query cancelled error */
"Query cancelled." = "تم إلغاء الاستبيان.";

/* Query cancel by resetting connection error */
"Query cancelled.  Please note that to cancel the query the connection had to be reset; transactions and connection variables were reset." = "تم إلغاء الاستبيان. يرجى ملاحظة أنه لإلغاء الاستعلام يجب إعادة تعيين الاتصال؛ تمت إعادة تعيين المعاملات ومتغيرات الاتصال.";

/* query editor preference pane name */
"Query Editor" = "محرر الاستعلام";

/* query editor preference pane tooltip */
"Query Editor Preferences" = "تفضيلات محرر الاستعلام";

/* query logging currently disabled label
 query logging disabled label */
"Query logging is currently disabled" = "تسجيل الاستعلام معطل حاليا";

/* query result print heading */
"Query Result" = "نتيجة الاستعلام";

/* export source */
"Query results" = "نتائج الاستعلام";

/* Query Status */
"Query Status" = "حالة الاستعلام";

/* table status : row count query failed : error title */
"Querying row count failed" = "فشل الاستعلام عن عدد الصف";

/* Quick connect item label */
"Quick Connect" = "الاتصال السريع";

/* quote label for color table (Prefs > Editor) */
"Quote" = "اقتباس";

/* databsse rename unsupported informative message */
"Renaming the database '%@' is currently unsupported as it contains objects other than tables (i.e. views, procedures, functions, etc.).\n\nIf you would like to rename a database please use the 'Duplicate Database', move any non-table objects manually then drop the old database." = "إعادة تسمية قاعدة البيانات '%@' غير مدعومة حاليا لأنها تحتوي على عناصر أخرى غير الجداول (أي المشاهدات والإجراءات والوظائف إلخ).\n\nإذا كنت ترغب في إعادة تسمية قاعدة البيانات الرجاء استخدام \"قاعدة بيانات مكررة\"، نقل أي كائنات غير جدول يدوياً ثم أسقط قاعدة البيانات القديمة.";

/* range for serial type */
"Range: %@ to %@" = "النطاق: %1$@ إلى %2$@";

/* range for time type */
"Range: -838:59:59.0 to 838:59:59.0\nF (precision): 0 (1s) to 6 (1µs)" = "النطاق: -838:59:59.0 إلى 838:59:59:59.0\nF (الدقيقة): 0 (1s) إلى 6 (1 ميكروغرام)";

/* range for year type */
"Range: 0000, 1901 to 2155" = "النطاق: 0000, 1901 إلى 2155";

/* range for set type */
"Range: 1 to 64 members\n1, 2, 3, 4 or 8 bytes storage" = "النطاق: 1 إلى 64 عضوا\n1 أو 2 أو 3 أو 4 أو 8 بايت";

/* range for datetime type */
"Range: 1000-01-01 00:00:00.0 to 9999-12-31 23:59:59.999999\nF (precision): 0 (1s) to 6 (1µs)" = "النطاق: 1000-01-01 00:00.0 إلى 9999-12-31 23:59:59.999999\nF (الدقيقة): 0 (1s) إلى 6 (ميكروغرام)";

/* range for date type */
"Range: 1000-01-01 to 9999-12-31" = "النطاق: 1000-01-01 إلى 9999-12-31";

/* range for timestamp type */
"Range: 1970-01-01 00:00:01.0 to 2038-01-19 03:14:07.999999\nF (precision): 0 (1s) to 6 (1µs)" = "النطاق: 1970-01-01 00:00:00:01.0 إلى 2038-01-19 03:14:07.999999\nF (الدقيقة): 0 (1s) إلى 6 (1 ميكروغرام)";

/* text showing that app is reading dump */
"Reading..." = "القراءة...";

/* menu item to refresh databases */
"Refresh Databases" = "تحديث قواعد البيانات";

/* refresh list menu item */
"Refresh List" = "تحديث القائمة";

/* toolbar item label for switching to the Table Relations tab */
"Relations" = "العلاقات";

/* Relations tab subtitle showing table name */
"Relations for table: %@" = "العلاقات مع الجدول: %@";

/* reload bundles menu item label */
"Reload Bundles" = "إعادة تحميل الحزم";

/* Reloading data task description */
"Reloading data..." = "إعادة تحميل البيانات...";

/* Reloading table task string */
"Reloading..." = "Reloading...";

/* remote error */
"Remote Error" = "خطأ بعيد";

/* Bundle Editor : Remove-Bundle: remove button
 remove button */
"Remove" = "إزالة";

/* remove all button */
"Remove All" = "إزالة الكل";

/* remove all query favorites message */
"Remove all query favorites?" = "إزالة جميع مفضلات الاستعلام؟";

/* Bundle Editor : Remove-Bundle: remove dialog title */
"Remove selected Bundle?" = "إزالة الحزمة المحددة؟";

/* remove selected content filters message */
"Remove selected content filters?" = "إزالة فلاتر المحتوى المحددة؟";

/* remove selected query favorites message */
"Remove selected query favorites?" = "إزالة مفضلة الاستعلام المحددة؟";

/* removing field task status message */
"Removing field..." = "إزالة الحقل...";

/* removing index task status message */
"Removing index..." = "إزالة الفهرس...";

/* rename database message */
"Rename database '%@' to:" = "إعادة تسمية قاعدة البيانات '%@' إلى:";

/* rename func menu title */
"Rename Function..." = "إعادة تسمية الدالة...";

/* rename proc menu title */
"Rename Procedure..." = "إعادة تسمية الإجراء...";

/* rename table menu title */
"Rename Table..." = "إعادة تسمية الجدول...";

/* rename view menu title */
"Rename View..." = "إعادة تسمية العرض...";

/* REPAIR one or more tables - result title */
"Repair %@" = "إصلاح %@";

/* repair selected items menu item */
"Repair Selected Items" = "إصلاح العناصر المحددة";

/* repair table menu item */
"Repair Table" = "جدول الإصلاح";

/* repair table failed message */
"Repair table failed." = "فشل إصلاح الجدول.";

/* Replace button */
"Replace" = "استبدل";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace entire content' item */
"Replace Entire Content" = "استبدال المحتوى بأكمله";

/* Bundle Editor : Scope=Field : Output dropdown : 'replace selection' item */
"Replace Selection" = "استبدال التحديد";

/* description of year */
"Represents a 4 digit year value, stored as 1 byte. Invalid values are converted to 0000 and two digit values 0 to 69 will be converted to years 2000 to 2069, resp. values 70 to 99 to years 1970 to 1999.\nThe YEAR(2) type was removed in MySQL 5.7.5." = "يمثل قيمة سنة مكونة من 4 أرقام، مخزنة كـ 1 بايت. يتم تحويل القيم غير الصحيحة إلى 0000 وسيتم تحويل القيم من 0 إلى 69 إلى سنة 2000 إلى سنة 2069.\nتم إزالة نوع YEAR(2) في MySQL 5.7.5.";

/* description of multilinestring */
"Represents a collection of LineStrings." = "يمثل مجموعة من الخطوط الخطية.";

/* description of geometrycollection */
"Represents a collection of objects of any other single- or multi-valued spatial type. The only restriction being, that all objects must share a common coordinate system." = "يمثل مجموعة من الأجسام من أي نوع مكاني آخر أحادي أو متعدد القيمة. والقيد الوحيد هو أن جميع الأشياء يجب أن تشترك في نظام تنسيقي مشترك.";

/* description of multipolygon */
"Represents a collection of Polygons. The Polygons making up the MultiPolygon must not intersect." = "يمثل مجموعة من مضلعات البوليغون. المضلعات التي تتكون من عدة مضلعات يجب ألا تتقاطع";

/* description of multipoint */
"Represents a set of Points without specifying any kind of relation and/or order between them." = "يمثل مجموعة من النقاط دون تحديد أي نوع من العلاقة و/أو الترتيب بينهم.";

/* description of point */
"Represents a single location in coordinate space using X and Y coordinates. The point is zero-dimensional." = "يمثل موقع واحد في مساحة الإحداثيات باستخدام إحداثيات X و Y . النقطة ذات بعد صفر.";

/* description of linestring */
"Represents an ordered set of coordinates where each consecutive pair of two points is connected by a straight line." = "يمثل مجموعة مطلوبة من الإحداثيات حيث كل زوج متتابع من نقطتين متصلتين بخط مستقيم.";

/* required placeholder string */
"required" = "مطلوب";

/* description of smallint */
"Requires 2 bytes storage space. M is the optional display width and does not affect the possible value range." = "يتطلب مساحة تخزين 2 بايت. M هو عرض العرض الاختياري ولا يؤثر على نطاق القيمة المحتملة.";

/* description of mediumint */
"Requires 3 bytes storage space. M is the optional display width and does not affect the possible value range." = "يتطلب مساحة تخزين 3 بايت. M هو عرض العرض الاختياري ولا يؤثر على نطاق القيمة المحتملة.";

/* description of int */
"Requires 4 bytes storage space. M is the optional display width and does not affect the possible value range. INTEGER is an alias to this type." = "يتطلب مساحة تخزين 4 بايت. M هو عرض العرض الاختياري ولا يؤثر على نطاق القيمة المحتملة. INTEGER هو اسم مستعار لهذا النوع.";

/* description of bigint */
"Requires 8 bytes storage space. M is the optional display width and does not affect the possible value range. Note: Arithmetic operations might fail for large numbers." = "يتطلب مساحة تخزين 8 بايت. M هو عرض العرض الاختياري ولا يؤثر على نطاق القيمة المحتملة. ملاحظة: قد تفشل العمليات الخاصة بالأعداد الكبيرة.";

/* reset auto_increment after deletion of all rows message */
"Reset AUTO_INCREMENT after deletion\n(only for Delete ALL ROWS IN TABLE)?" = "إعادة تعيين AUTO_INCREMENT بعد الحذف\n(فقط لحذف جميع ROWS في TBLE)؟";

/* delete selected row button */
"Delete Selected Row" = "حذف الصف المحدد";

/* delete selected rows button */
"Delete Selected Rows" = "حذف الصفوف المحددة";

/* delete all rows in table button */
"Delete ALL ROWS IN TABLE" = "حذف جميع الأذونات في اللعبة";

/* Restoring session task description */
"Restoring session..." = "استعادة الجلسة...";

/* return type label (Navigator) */
"Return Type" = "نوع الإرجاع";

/* return type: %@ */
"return type: %@" = "نوع الإرجاع: %@";

/* singular word for row */
"row" = "صف";

/* plural word for rows */
"rows" = "صفوف";

/* text showing how many rows are in the limited filter match */
"Rows %@ - %@ from filtered matches" = "الصفوف %1$@ - %2$@ من المطابقات المصفاة";

/* text showing how many rows are in the limited result */
"Rows %@ - %@ of %@%@ from table" = "الصفوف %1$@ - %2$@ من %3$@%4$@ من الجدول";

/* Table Info Section : number of rows (exact value) */
"rows: %@" = "صفوف: %@";

/* Table Info Section : number of rows (estimated value) */
"rows: ~%@" = "صفوف: ~%@";

/* run all button */
"Run All" = "تشغيل الكل";

/* Run All menu item title */
"Run All Queries" = "تشغيل جميع الاستفسارات";

/* Title of button to run current query in custom query view */
"Run Current" = "تشغيل الحالي";

/* Title of action menu item to run current query in custom query view */
"Run Current Query" = "تشغيل الاستعلام الحالي";

/* toolbar item label for switching to the Run Query tab */
"Run Custom Query" = "تشغيل استعلام مخصص";

/* Title of button to run query just before text caret in custom query view */
"Run Previous" = "تشغيل السابق";

/* Title of action menu item to run query just before text caret in custom query view */
"Run Previous Query" = "تشغيل الاستعلام السابق";

/* Title of action menu item to run selected text in custom query view */
"Run Selected Text" = "تشغيل النص المحدد";

/* Title of button to run selected text in custom query view */
"Run Selection" = "تشغيل التحديد";

/* Running multiple queries string */
"Running query %i of %lu..." = "جار تشغيل الاستعلام %1$i من %2$lu...";

/* Running multiple queries string */
"Running query %ld of %lu..." = "جار تشغيل الاستعلام %1$ld من %2$lu...";

/* Running single query string */
"Running query..." = "تشغيل الاستعلام...";

/* Save trigger button label */
"Save" = "حفظ";

/* Save All to Favorites */
"Save All to Favorites" = "حفظ الكل في المفضلة";

/* save as button title */
"Save As..." = "حفظ باسم...";

/* Bundle Editor : BLOB dropdown : 'save BLOB as dat file' item */
"save BLOB as dat file" = "حفظ BLOB كملف dat";

/* Bundle Editor : BLOB dropdown : 'save BLOB as image file' item */
"save BLOB as image file" = "حفظ BLOB كملف صورة";

/* Save Current Query to Favorites */
"Save Current Query to Favorites" = "حفظ الاستعلام الحالي إلى المفضلة";

/* save page as menu item title */
"Save Page As…" = "حفظ الصفحة ك…";

/* Save Queries… */
"Save Queries…" = "حفظ الاستفسارات…";

/* Save Query… */
"Save Query…" = "حفظ الاستعلام…";

/* Save Selection to Favorites */
"Save Selection to Favorites" = "حفظ التحديد إلى المفضلة";

/* save view as button title */
"Save View As..." = "حفظ العرض باسم...";

/* schema path header for completion tooltip */
"Schema path:" = "مسار المخطط:";

/* Search in MySQL Documentation */
"Search in MySQL Documentation" = "البحث في وثائق MySQL";

/* Search in MySQL Help */
"Search in MySQL Help" = "البحث في مساعدة MySQL";

/* Select Active Query */
"Select Active Query" = "حدد الاستعلام النشط";

/* toolbar item for selecting a db */
"Select Database" = "حدد قاعدة البيانات";

/* selected items */
"selected items" = "العناصر المحددة";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as comma-separated' item */
"Selected Rows (CSV)" = "الصفوف المحددة (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as SQL' item */
"Selected Rows (SQL)" = "الصفوف المحددة (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'selected rows as tab-separated' item */
"Selected Rows (TSV)" = "صفوف مختارة (TSV)";

/* Bundle Editor : Scope=Field : Input source dropdown: 'selected text' item */
"Selected Text" = "النص المحدد";

/* selection label for color table (Prefs > Editor) */
"Selection" = "التحديد";

/* table structure : indexes : delete index : no columns error : description */
"Sequel Ace could not find any columns belonging to this index. Maybe it has been removed already?" = "لم يتمكن Sequel Ace من العثور على أي أعمدة تابعة لهذا الفهرس. ربما تم إزالتها بالفعل؟";

/* Preferences : Network : Custom SSH client : warning dialog message */
"Sequel Ace only supports and is tested with the default OpenSSH client versions included with macOS. Using different clients might cause connection issues, security risks or not work at all.\n\nPlease be aware, that we cannot provide support for such configurations." = "Sequel Ace يدعم فقط ويتم اختباره مع إصدارات عملاء OpenSSH الافتراضية مع macOS. استخدام عملاء مختلفين قد يسبب مشاكل اتصال أو مخاطر أمنية أو لا يعمل على الإطلاق.\n\nمن فضلك كن على علم بأننا لا نستطيع تقديم الدعم لمثل هذه الإعدادات.";

/* sequelace URL scheme command not supported. */
"sequelace URL scheme command not supported." = "أمر مخطط URL المتتالي غير مدعوم.";

/* sequelace url Scheme Error */
"sequelace URL Scheme Error" = "خطأ في مخطط URL التتابع";

/* Table Structure : Collation dropdown : 'item is the same as the collation of server' marker
 Table Structure : Encoding dropdown : 'item is server default' marker */
"Server" = "خادم";

/* Add Database : Charset dropdown : default item ($1 = charset name)
 Add Database : Collation dropdown : default item ($1 = collation name) */
"Server Default (%@)" = "افتراضي الخادم (%@)";

/* server processes window title (var = hostname) */
"Server Processes on %@" = "عمليات الخادم على %@";

/* Initial filename for 'Save session' file */
"Session" = "الجلسة";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html' item */
"Show as HTML" = "إظهار كـ HTML";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as html tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as html tooltip' item */
"Show as HTML Tooltip" = "إظهار كنصيحة لأداة HTML";

/* Bundle Editor : Scope=Data-Table : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=Field : Output dropdown : 'show as text tooltip' item
 Bundle Editor : Scope=General : Output dropdown : 'show as text tooltip' item */
"Show as Text Tooltip" = "إظهار كنصيحة نصية";

/* show console */
"Show Console" = "إظهار وحدة التحكم";

/* show create func syntax menu item */
"Show Create Function Syntax..." = "إظهار إنشاء بناء الدالة...";

/* show create proc syntax menu item */
"Show Create Procedure Syntax..." = "إظهار إنشاء صيغة إجرائية...";

/* show create syntaxes menu item */
"Show Create Syntaxes..." = "إظهار إنشاء بنايات...";

/* show create table syntax menu item */
"Show Create Table Syntax..." = "إظهار إنشاء صيغة الجدول...";

/* show create view syntax menu item */
"Show Create View Syntax..." = "إظهار إنشاء أسلوب العرض...";

/* Show detail button */
"Show Detail" = "إظهار التفاصيل";

/* MySQL Help Viewer : Results list : Link tooltip */
"Show MySQL help for %@" = "إظهار مساعدة MySQL ل %@";

/* show navigator */
"Show Navigator" = "Show Navigator";

/* show tab bar */
"Show Tab Bar" = "إظهار شريط التبويب";

/* tooltip for toolbar item for show console */
"Show the console which shows all MySQL commands performed by Sequel Ace" = "إظهار وحدة التحكم التي تظهر جميع أوامر MySQL التي تقوم بها Sequel Ace";

/* Show Toolbar menu item */
"Show Toolbar" = "إظهار شريط الأدوات";

/* filtered item count */
"Showing %lu of %lu processes" = "عرض %1$lu من عمليات %2$lu";

/* shutdown server : confirmation dialog : shutdown button */
"Shutdown" = "إيقاف";

/* shutdown server : error dialog : title */
"Shutdown failed!" = "فشل الإغلاق!";

/* range of integer types */
"Signed: %@ to %@\nUnsigned: %@ to %@" = "توقيع: %1$@ إلى %2$@\nغير موقع: %3$@ إلى %4$@";

/* Table Info Section : table size on disk */
"size: %@" = "الحجم: %@";

/* skip existing button */
"Skip existing" = "تخطي موجود";

/* skip problems button */
"Skip problems" = "تخطي المشاكل";

/* beta build label */
"Beta Build" = "بناء بيتا";

/* socket connection failed title */
"Socket connection failed!" = "فشل اتصال المقطع!";

/* socket not found title */
"Socket not found!" = "المقطع غير موجود!";

/* Some export folders not writable explanatory text */
"Some of the target export folders are not writable.  Please select a new export location and try again." = "بعض مجلدات التصدير المستهدفة غير قابلة للكتابة. الرجاء تحديد موقع تصدير جديد وحاول مرة أخرى.";

/* Some export folders missing explanatory text */
"Some of the target export folders no longer exist.  Please select a new export location and try again." = "بعض مجلدات التصدير المستهدفة لم تعد موجودة. الرجاء تحديد موقع تصدير جديد وحاول مرة أخرى.";

/* Sorting table task description */
"Sorting table..." = "فرز الجدول...";

/* spatial index menu item title */
"SPATIAL" = "SPATIAL";

/* sql data access label (Navigator) */
"SQL Data Access" = "الوصول إلى بيانات SQL";


/* Connection Secured via SSL information text */
"Connection Secured via SSL" = "اتصال مؤمن عبر SSL";

/* SSH connected titlebar marker */
"SSH Connected" = "SSH متصل";

/* SSH connecting very short status message */
"SSH connecting..." = "إتصال SSH...";

/* SSH connecting titlebar marker */
"SSH Connecting…" = "إتصال SSH…";

/* SSH connection failed title */
"SSH connection failed!" = "فشل الاتصال بSSH!";

/* SSH disconnected titlebar marker */
"SSH Disconnected" = "SSH غير متصل";

/* SSH key check error */
"SSH Key not found" = "لم يتم العثور على مفتاح SSH";

/* title when ssh tunnel port forwarding failed */
"SSH port forwarding failed" = "فشل إعادة توجيه منفذ SSH";

/* SSL certificate authority file check error */
"SSL Certificate Authority File not found" = "لم يتم العثور على ملف سلطة شهادة SSL";

/* SSL certificate file check error */
"SSL Certificate File not found" = "لم يتم العثور على ملف شهادة SSL";

/* SSL requested but not used title */
"SSL connection not established" = "لم يتم إنشاء اتصال SSL";

/* SSL key file check error */
"SSL Key File not found" = "لم يتم العثور على ملف مفتاح SSL";

/* Standard memory export summary */
"Standard memory" = "الذاكرة القياسية";

/* started */
"started" = "بدأ";

/* status file for sequelace url scheme command couldn't be written error message */
"Status file for sequelace url scheme command couldn't be written!" = "تعذر كتابة ملف الحالة لأمر نظام url sequelace !";

/* stop button */
"Stop" = "توقف";

/* Stop queries string */
"Stop queries" = "إيقاف الاستعلامات";

/* Stop query string */
"Stop query" = "إيقاف الاستعلام";

/* description of timestamp */
"Stores a date and time of day as seconds since the beginning of the UNIX epoch (1970-01-01 00:00:00). The values displayed/stored are affected by the connection's @@time_zone setting.\nThe representation is the same as for DATETIME. Invalid values, as well as \"second zero\", are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F. Some additional rules may apply." = "Stores a date and time of day as seconds since the beginning of the UNIX epoch (1970-01-01 00:00:00). The values displayed/stored are affected by the connection's @@time_zone setting.\nThe representation is the same as for DATETIME. Invalid values, as well as \"second zero\", are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F. Some additional rules may apply.";

/* description of datetime */
"Stores a date and time of day. The representation is YYYY-MM-DD HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00 00:00:00.0. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "يخزن تاريخًا ووقتًا من اليوم. التمثيل هو YYY-MM-DD HH:MM:SS[. *]، أنا ثواني كسرية. القيمة لا تتأثر بأي تحديد للمنطقة الزمنية. تم تحويل القيم غير الصحيحة إلى 0000-00-00:00:00.0. تمت إضافة ثوان جزئية في MySQL 5.6.4 بدقة إلى ميكروثواني (6)، محددة من قبل F.";

/* description of date */
"Stores a date without time information. The representation is YYYY-MM-DD. The value is not affected by any time zone setting. Invalid values are converted to 0000-00-00." = "يخزن تاريخ بدون معلومات عن الوقت. التمثيل هو YYYY-MM-DD. القيمة لا تتأثر بأي تعيين منطقة زمنية. القيم غير صالحة يتم تحويلها إلى 00-00-00.";

/* description of time */
"Stores a time of day, duration or time interval. The representation is HH:MM:SS[.I*], I being fractional seconds. The value is not affected by any time zone setting. Invalid values are converted to 00:00:00. Fractional seconds were added in MySQL 5.6.4 with a precision down to microseconds (6), specified by F." = "يخزن في وقت من اليوم أو المدة أو الوقت. التمثيل هو HH:MM:SS[. *]، أنا ثواني كسرية. القيمة لا تتأثر بأي تحديد للمنطقة الزمنية. تم تحويل القيم غير الصحيحة إلى 00:00:00. تمت إضافة ثوان جزئية في MySQL 5.6.4 مع الدقة لأسفل إلى ميكروثواني (6)، محددة من F.";

/* toolbar item label for switching to the Table Structure tab */
"Structure" = "الهيكل";

/* successfully analyzed all selected items message */
"Successfully analyzed all selected items." = "تم تحليل جميع العناصر المحددة بنجاح.";

/* analyze table successfully passed message */
"Successfully analyzed table." = "تم تحليل الجدول بنجاح.";

/* successfully flushed all selected items message */
"Successfully flushed all selected items." = "تم مسح جميع العناصر المحددة بنجاح.";

/* message of panel when successfully flushed privs */
"Successfully flushed privileges." = "سحق الامتيازات بنجاح.";

/* flush table successfully passed message */
"Successfully flushed table." = "منضدة مسحوبة بنجاح.";

/* successfully optimized all selected items message */
"Successfully optimized all selected items." = "تم تحسين جميع العناصر المحددة بنجاح.";

/* optimize table successfully passed message */
"Successfully optimized table." = "تم تحسين الجدول بنجاح.";

/* successfully repaired all selected items message */
"Successfully repaired all selected items." = "تم إصلاح جميع العناصر المحددة بنجاح.";

/* repair table successfully passed message */
"Successfully repaired table." = "تم إصلاح الجدول بنجاح.";

/* tooltip for toolbar item for switching to the Run Query tab */
"Switch to the Run Query tab" = "التبديل إلى علامة تبويب تشغيل الاستعلام";

/* tooltip for toolbar item for switching to the Table Content tab */
"Switch to the Table Content tab" = "التبديل إلى علامة تبويب محتوى الجدول";

/* tooltip for toolbar item for switching to the Table Info tab */
"Switch to the Table Info tab" = "التبديل إلى علامة تبويب معلومات الجدول";

/* tooltip for toolbar item for switching to the Table Relations tab */
"Switch to the Table Relations tab" = "التبديل إلى علامة تبويب علاقات الجدول";

/* tooltip for toolbar item for switching to the Table Structure tab */
"Switch to the Table Structure tab" = "التبديل إلى تبويب بنية الجدول";

/* tooltip for toolbar item for switching to the Table Triggers tab */
"Switch to the Table Triggers tab" = "التبديل إلى علامة تبويب مشغلات الجدول";

/* tooltip for toolbar item for switching to the User Manager tab */
"Switch to the User Manager tab" = "التبديل إلى علامة تبويب مدير المستخدم";

/* description for table syntax copied notification */
"Syntax for %@ table copied" = "تم نسخ بناء الجملة لجدول %@";

/* table */
"table" = "الجدول";

/* csv export table heading
 table
 Table Structure : Collation dropdown : 'item is the same as the collation of table' marker
 Table Structure : Encoding dropdown : 'item is table default' marker */
"Table" = "الجدول";

/* export label showing that the app is fetching data for a specific table */
"Table %lu of %lu (%@): Fetching data..." = "الجدول %1$lu من %2$lu (%3$@): جلب البيانات...";

/* export label showing app is fetching relations data for a specific table */
"Table %lu of %lu (%@): Fetching relations data..." = "Table %1$lu of %2$lu (%3$@): Fetching relations data...";

/* export label showing app if writing data for a specific table */
"Table %lu of %lu (%@): Writing data..." = "الجدول %1$lu من %2$lu (%3$@): كتابة البيانات...";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table changed' item
 Bundle Editor : Scope=General : Trigger dropdown : 'table changed' item */
"Table changed" = "تم تغيير الجدول";

/* table checksum message */
"Table checksum" = "جدول المجموع الاختباري";

/* table checksum: %@ */
"Table checksum: %@" = "الجدول الاختباري: %@";

/* table content print heading */
"Table Content" = "محتوى الجدول";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as comma-separated' item */
"Table Content (CSV)" = "محتوى الجدول (CSV)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as SQL' item */
"Table Content (SQL)" = "محتوى الجدول (SQL)";

/* Bundle Editor : Scope=Data-Table : Input source dropdown: 'table content as tab-separated' item */
"Table Content (TSV)" = "محتوى الجدول (TSV)";

/* toolbar item for navigation history */
"Table History" = "تاريخ الجدول";

/* toolbar item label for switching to the Table Info tab */
"Table Info" = "معلومات الجدول";

/* header for table info pane */
"TABLE INFORMATION" = "معلومات اللعبة";

/* table information print heading */
"Table Information" = "معلومات الجدول";

/* message of panel when no name is given for table */
"Table must have a name." = "يجب أن يكون للجدول اسم.";

/* general preference pane tooltip */
"Table Preferences" = "تفضيلات الجدول";

/* toolbar item label for switching to the Table Relations tab */
"Table Relations" = "علاقات الجدول";

/* Bundle Editor : Scope=Data-Table : Trigger dropdown : 'table row changed' item */
"Table Row changed" = "تم تغيير صف الجدول";

/* table structure print heading */
"Table Structure" = "هيكل الجدول";

/* toolbar item label for switching to the Table Triggers tab */
"Table Triggers" = "محفزات الجدول";

/* Add Relation sheet title, showing table name */
"Table: %@" = "الجدول: %@";

/* tables preference pane name */
"Tables" = "الجداول";

/* tables */
"tables" = "الجداول";

/* header for table list */
"TABLES" = "تابلس";

/* header for table & views list */
"TABLES & VIEWS" = "الفصول و العروض";

/* Connection test very short status message */
"Testing connection..." = "اختبار الاتصال...";

/* MySQL connection test very short status message */
"Testing MySQL..." = "Testing MySQL...";

/* SSH testing very short status message */
"Testing SSH..." = "اختبار SSH...";

/* text label for color table (Prefs > Editor) */
"Text" = "نص";

/* Text is too long. Maximum text length is set to %llu. */
"Text is too long. Maximum text length is set to %llu." = "النص طويل جداً. تم تعيين الحد الأقصى لطول النص إلى %llu.";

/* Release notes dialog title thanking user for upgrade */
"Thanks for updating Sequel Ace!" = "شكرا لتحديث Sequel Ace!";

/* Open Files : Bundle : Install-Error : Destination path already exists error dialog message */
"The Bundle ‘%@’ already exists." = "الحزمة '%@' موجودة بالفعل.";

/* Open Files : Bundle: UUID : UUID-Attribute is missing in bundle's command.plist file */
"The Bundle ‘%@’ has no UUID which is necessary to identify installed Bundles." = "الحزمة '%@ليس لديها UUID اللازمة لتحديد الحزم المثبتة.";

"‘%@’ Bundle contains legacy components" = "احزمة%@تحتوي على مكونات قديمة";

"In these files:\n\n%@\n\nDo you still want to install the bundle?" = "في هذه الملفات:\n\n%@\n\nهل مازلت تريد تثبيت الحزمة؟";

/* message while reading a spf file which matches non-supported formats. */
"The chosen file %@ contains ‘%@’ data." = "يحتوي الملف المختار \"%1$@\" على بيانات%2$@.";

/* the color theme ‘%@’ already exists. */
"The color theme ‘%@’ already exists." = "السمة اللونية '%@' موجودة بالفعل.";

/* the connection is busy. please wait and try again tooltip */
"The connection is busy. Please wait and try again." = "الاتصال مشغول. الرجاء الانتظار والمحاولة مرة أخرى.";

/* the connection of the active connection window is not identical tooltip */
"The connection of the active connection window is not identical." = "اتصال نافذة الإتصال النشط غير متطابق.";

/* Connection lost during import error message */
"The connection to the server was lost during the import.  The import is only partially complete." = "تم فقدان الاتصال بالخادم أثناء الاستيراد. الاستيراد اكتمل جزئيا فقط.";

/* Create syntax permission denied detail */
"The creation syntax could not be retrieved due to a permissions error.\n\nPlease check your user permissions with an administrator." = "The creation syntax could not be retrieved due to a permissions error.\n\nPlease check your user permissions with an administrator.";

/* CSV file open error */
"The CSV file you selected could not be found or read." = "تعذر العثور على ملف CSV الذي اخترته أو قراءته.";

/* Error when CSV appears to have too many columns to import, probably due to line ending mismatch */
"The CSV was read as containing more than 512 columns, more than the maximum columns permitted for speed reasons by Sequel Ace.\n\nThis usually happens due to errors reading the CSV; please double-check the CSV to be imported and the line endings and escape characters at the bottom of the CSV selection dialog." = "وقد قُرئت CSV على أنها تحتوي على أكثر من 512 عمودا، أي أكثر من الحد الأقصى المسموح به لأسباب تتعلق بالسرعة بواسطة Sequel Ace .\n\nيحدث هذا عادة بسبب أخطاء في قراءة CSV؛ الرجاء التحقق من CSV المراد استيراده و نهاية الخط و حروف الهروب في أسفل مربع حوار اختيار CSV.";

/* unsaved theme informative message */
"The current color theme is unsaved. Do you want to proceed without saving it?" = "سمة اللون الحالية غير محفوظة. هل تريد المتابعة دون حفظها؟";

/* Short important release note for swap of custom query buttons */
"The Custom Query \"Run\" and \"Run All\" button positions and their shortcuts have been swapped." = "تم تبديل الاستعلام المخصص وتشغيل جميع مواقع الزر واختصارها.";

/* the following default bundles were updated:\n%@\nyour modifications were stored as “(user)”. */
"The following default Bundles were updated:\n%@\nYour modifications were stored as “(user)”." = "تم تحديث الحزم الافتراضية التالية:\n%@\nتم تخزين تعديلاتك كـ \"(مستخدم)\".";

/* favorites export error informative message */
"The following error occurred during the export process:\n\n%@" = "حدث الخطأ التالي أثناء عملية التصدير:\n\n%@";

/* favorites import error informative message */
"The following error occurred during the import process:\n\n%@" = "حدث الخطأ التالي أثناء عملية الاستيراد:\n\n%@";

/* table structure : indexes : delete index : error 1553 : description */
"The foreign key relationship '%@' has a dependency on index '%@'. This relationship must be removed before the index can be deleted.\n\nAre you sure you want to continue to delete the relationship and the index? This action cannot be undone." = "علاقة المفتاح الأجنبي '%1$@' تعتمد على المؤشر '%2$@'. يجب إزالة هذه العلاقة قبل حذف الفهرس.\n\nهل أنت متأكد من أنك تريد الاستمرار في حذف العلاقة والفهرس؟ لا يمكن التراجع عن هذا الإجراء.";

/* table list change alert message */
"The list of tables has changed" = "تم تغيير قائمة الجداول";

/* message when trying to rename a table/view/proc/etc to an already used name */
"The name '%@' is already used." = "الاسم '%@' مستخدم مسبقا.";

/* table list change alert informative message */
"The number of tables in this database has changed since the export dialog was opened. There are now %lu additional table(s), most likely added by an external application.\n\nHow would you like to proceed?" = "وقد تغير عدد الجداول في قاعدة البيانات هذه منذ فتح مربع حوار التصدير. يوجد الآن %lu جدول إضافي، على الأرجح إضافة بواسطة تطبيق خارجي.\n\nكيف ترغب في المتابعة؟";

/* message of panel when no rows have been affected after writing to the db */
"The row was not written to the MySQL database. You probably haven't changed anything.\nReload the table to be sure that the row exists and use a primary key for your table.\n(This error can be turned off in the preferences.)" = "The row was not written to the MySQL database. You probably haven't changed anything.\nReload the table to be sure that the row exists and use a primary key for your table.\n(This error can be turned off in the preferences.)";

/* export : import settings : file version error description ($1 = is version, $2 = list of supported versions); note: the u00A0 is a non-breaking space, do not add more whitespace. */
"The selected export settings were stored with version %1$ld, but only settings with the following versions can be imported: %2$@.\n\nEither save the settings in a backwards compatible way or update your version of Sequel Ace." = "تم تخزين إعدادات التصدير المحددة مع الإصدار%1$ld، ولكن يمكن استيراد الإعدادات مع الإصدارات التالية فقط: %2$@.\n\nإما حفظ الإعدادات بطريقة متوافقة إلى الوراء أو تحديث إصدارك من ثلج سيكل.";

/* export : import settings : spf content type error description */
"The selected file contains data of type “%1$@”, but type “%2$@” is needed. Please choose a different file." = "يحتوي الملف المختار على بيانات من النوع \"%1$@\"، ولكن نوع \"%2$@\" مطلوب. الرجاء اختيار ملف مختلف.";

/* export : import settings : file error description */
"The selected file is either not a valid SPF file or severely corrupted." = "الملف المحدد إما أنه ليس ملف SPF صالح أو أنه معطوب بشكل خطير.";

/* error deleting relation informative message */
"The selected relation couldn't be deleted.\n\nMySQL said: %@" = "لا يمكن حذف العلاقة المحددة.\n\nMySQL قال: %@";

/* error deleting trigger informative message */
"The selected trigger couldn't be deleted.\n\nMySQL said: %@" = "The selected trigger couldn't be deleted.\n\nMySQL said: %@";

/* description of tinyint */
"The smallest integer type, requires 1 byte storage space. M is the optional display width and does not affect the possible value range." = "أصغر نوع صحيح، يتطلب مساحة تخزين 1 بايت. M هو عرض العرض الاختياري ولا يؤثر على نطاق القيمة المحتملة.";

/* message of panel when connection to socket failed because optional socket could not be found */
"The socket file could not be found in any common location. Please supply the correct socket location.\n\nMySQL said: %@" = "The socket file could not be found in any common location. Please supply the correct socket location.\n\nMySQL said: %@";

/* error creating relation informative message */
"The specified relation could not be created.\n\nMySQL said: %@" = "لا يمكن إنشاء العلاقة المحددة.\n\nMySQL قال: %@";

/* error creating trigger informative message */
"The specified trigger was unable to be created.\n\nMySQL said: %@" = "The specified trigger was unable to be created.\n\nMySQL said: %@";

/* sql import : charset error alert : detail message */
"The SQL file uses utf8mb4 encoding, but your MySQL version only supports the limited utf8 subset.\n\nYou can continue the import, but any non-BMP characters in the SQL file (eg. some typographic and scientific special characters, archaic CJK logograms, emojis) will be unrecoverably lost!" = "ملف SQL يستخدم ترميز utf8mb4، ولكن إصدار MySQL الخاص بك يدعم فقط المجموعة الفرعية المحدودة utf8.\n\nيمكنك متابعة الاستيراد، ولكن أي أحرف غير BMP في ملف SQL (على سبيل المثال). بعض الشخصيات الطباعية والعلمية الخاصة، شعارات CJK العتيقة، الرموز التعبيرية) ستفقد بشكل لا يمكن استردادها!";

/* SQL file open error */
"The SQL file you selected could not be found or read." = "تعذر العثور على ملف SQL الذي اخترته أو قراءته.";

/* Prompt for SSH password when keychain fetch failed */
"The SSH password could not be loaded from the keychain; please enter the SSH password for %@:" = "لا يمكن تحميل كلمة مرور SSH من سلسلة المفاتيح؛ الرجاء إدخال كلمة مرور SSH ل %@:";

/* Prompt for SSH password when direct fetch failed */
"The SSH password could not be loaded; please enter the SSH password for %@:" = "لا يمكن تحميل كلمة مرور SSH؛ الرجاء إدخال كلمة مرور SSH ل %@:";

/* SSH tunnel authentication failed message */
"The SSH Tunnel could not authenticate with the remote host. Please check your password and ensure you still have access." = "تعذر مصادقة نفق SSH مع المضيف البعيد. الرجاء التحقق من كلمة المرور الخاصة بك والتأكد من أنه لا يزال لديك حق الوصول.";

/* SSH tunnel unexpectedly closed */
"The SSH Tunnel has unexpectedly closed." = "لقد أغلق نفق SSH بشكل غير متوقع.";

/* SSH tunnel was closed by remote host message */
"The SSH Tunnel was closed 'by the remote host'. This may indicate a networking issue or a network timeout." = "تم إغلاق نفق SSH \"من قبل المضيف البعيد\". قد يشير هذا إلى مشكلة في الربط الشبكي أو مهلة الشبكة.";

/* SSH tunnel forwarding port connection refused message */
"The SSH Tunnel was established successfully, but could not forward data to the remote port as the remote port refused the connection." = "وقد تم إنشاء نفق SSH بنجاح، ولكنه لم يتمكن من إرسال البيانات إلى الميناء النائي لأن الميناء النائي رفض الاتصال.";

/* SSH tunnel unable to bind to local port message */
"The SSH Tunnel was unable to bind to the local port. This error may occur if you already have an SSH connection to the same server and are using a 'LocalForward' setting in your SSH configuration.\n\nWould you like to fall back to a standard connection to localhost in order to use the existing tunnel?" = "لم يتمكن نفق SSH من الربط مع الميناء المحلي. قد يحدث هذا الخطأ إذا كان لديك بالفعل اتصال SSH لنفس الخادم وتستخدم إعداد 'LocalForward' في تكوين SSH الخاص بك.\n\nهل ترغب في العودة إلى اتصال عادي مع localhost من أجل استخدام النفق الحالي؟";

/* SSH tunnel failed or timed out message */
"The SSH Tunnel was unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds)." = "لم يتمكن نفق SSH من الاتصال باستضافة %1$@، أو انتهت مهلة الطلب.\n\nتأكد من أن العنوان صحيح وأن لديك الامتيازات اللازمة، أو حاول زيادة مهلة الاتصال (حاليا %2$ld ثوان).";

/* message of panel when loading of table failed and presumably due to used filter argument */
"The table data couldn't be loaded presumably due to used filter clause. \n\nMySQL said: %@" = "The table data couldn't be loaded presumably due to used filter clause. \n\nMySQL said: %@";

/* message of panel when loading of table failed */
"The table data couldn't be loaded.\n\nMySQL said: %@" = "تعذر تحميل بيانات الجدول.\n\nMySQL: %@";

/* Export folder not writable explanatory text */
"The target export folder is not writable.  Please select a new export location and try again." = "مجلد التصدير المستهدف غير قابل للكتابة. الرجاء تحديد موقع تصدير جديد وحاول مرة أخرى.";

/* Export folder not chosen by user explanatory text */
"No directory selected.  Please select a new export location and try again." = "لم يتم تحديد الدليل. الرجاء تحديد موقع تصدير جديد وحاول مرة أخرى.";
"No directory selected." = "لا يوجد دليل محدد.";
"Please select a new export location and try again." = "الرجاء تحديد موقع تصدير جديد وحاول مرة أخرى.";

/* Export folder missing explanatory text */
"The target export folder no longer exists.  Please select a new export location and try again." = "مجلد التصدير المستهدف لم يعد موجودا. الرجاء تحديد موقع تصدير جديد وحاول مرة أخرى.";

/* theme name label */
"Theme Name:" = "اسم السمة:";

/* themes installation error */
"Themes Installation Error" = "خطأ في تثبيت السمات";

/* message of panel when table content cannot be copied */
"There have been errors while copying table content. Please check the new table." = "حدثت أخطاء أثناء نسخ محتوى الجدول. الرجاء التحقق من الجدول الجديد.";

/* Cannot duplicate a table with triggers to a different database */
"Cannot duplicate a table with triggers to a different database." = "لا يمكن تكرار جدول مع مشغلات إلى قاعدة بيانات مختلفة.";

/* text shown when query was successfull */
"There were no errors." = "لم تكن هناك أخطاء.";

/* delete field and foreign key informative message */
"This field is part of a foreign key relationship with the table '%@'. This relationship must be removed before the field can be deleted.\n\nAre you sure you want to continue to delete the relationship and the field? This action cannot be undone." = "هذا الحقل هو جزء من علاقة مفتاح أجنبية مع الجدول '%@'. يجب إزالة هذه العلاقة قبل حذف الحقل.\n\nهل أنت متأكد من أنك تريد الاستمرار في حذف العلاقة والحقل؟ لا يمكن التراجع عن هذا الإجراء.";

/* table structure : indexes : delete index : error 1553, no FK found : description */
"This index cannot be deleted, because it is used by an existing foreign key relationship.\n\nPlease remove the relationship, before trying to remove this index.\n\nMySQL said: %@" = "لا يمكن حذف هذا الفهرس لأنه يستخدم من قبل علاقة مفتاح أجنبية قائمة.\n\nالرجاء إزالة العلاقة، قبل محاولة إزالة هذا الفهرس.\n\nMySQL قال: %@";

/* description of serial */
"This is an alias for BIGINT UNSIGNED NOT NULL AUTO_INCREMENT UNIQUE." = "هذا اسم مستعار لـ BIGINT UNSIGNED ليس AUTO_INCREMENT UNIQUE.";

/* description of dec
 description of fixed
 description of numeric */
"This is an alias for DECIMAL." = "هذا اسم مستعار لـ DECIMAL.";

/* description of double real */
"This is an alias for DOUBLE, unless REAL_AS_FLOAT is configured." = "هذا اسم مستعار ل DOUBLE، ما لم يتم تكوين REAL_AS_FLOAT.";

/* description of double precision */
"This is an alias for DOUBLE." = "هذا اسم مستعار ل DOUBLE.";

/* description of bool
 description of boolean */
"This is an alias for TINYINT(1)." = "وهذا اسم مستعار لـ TINYINT(1).";

/* Table Structure : Collation dropdown : database marker tooltip */
"This is the default collation of database %@." = "هذا هو التجميع الافتراضي لقاعدة البيانات %@.";

/* Table Structure : Collation dropdown : default marker tooltip */
"This is the default collation of encoding %@." = "هذا هو التجميع الافتراضي للترميز %@.";

/* Table Structure : Collation dropdown : table marker tooltip */
"This is the default collation of table %@." = "هذا هو التجميع الافتراضي للجدول %@.";

/* Table Structure : Collation dropdown : server marker tooltip */
"This is the default collation of this server." = "هذا هو التجميع الافتراضي لهذا الخادم.";

/* Table Structure : Encoding dropdown : database marker tooltip */
"This is the default encoding of database %@." = "هذا هو الترميز الافتراضي لقاعدة البيانات %@.";

/* Table Structure : Encoding dropdown : table marker tooltip */
"This is the default encoding of table %@." = "هذا هو الترميز الافتراضي للجدول %@.";

/* Table Structure : Encoding dropdown : server marker tooltip */
"This is the default encoding of this server." = "هذا هو الترميز الافتراضي لهذا الخادم.";

/* This table currently does not support relations. Only tables that use the InnoDB storage engine support them. */
"This table currently does not support relations. Only tables that use the InnoDB storage engine support them." = "هذا الجدول حاليا لا يدعم العلاقات. فقط الجداول التي تستخدم محرك تخزين InnoDB تدعمهم.";

/* user has no hosts informative message */
"This user doesn't have any hosts associated with it. It will be deleted unless one is added" = "هذا المستخدم ليس لديه أي مضيف مرتبط به. سيتم حذفه ما لم يتم إضافة واحد";

/* error removing host informative message */
"This user doesn't seem to have any associated hosts and will be removed unless a host is added." = "لا يبدو أن لهذا المستخدم أي مضيف مرتبط وسيتم إزالته ما لم يتم إضافة مضيف.";

/* shutdown server : confirmation dialog : message */
"This will wait for open transactions to complete and then quit the mysql daemon. Afterwards neither you nor anyone else can connect to this database!\n\nFull management access to the server's operating system is required to restart MySQL!" = "هذا سينتظر حتى تكتمل المعاملات المفتوحة ومن ثم يترك صدى Mysql. بعد ذلك لا يمكنك أو أي شخص آخر الاتصال بقاعدة البيانات هذه!\n\nمطلوب وصول الإدارة الكاملة إلى نظام تشغيل الخادم لإعادة تشغيل MySQL!";

/* export filename time token */
"Time" = "الوقت";

/* toolbar item label for switching to the Table Triggers tab */
"Triggers" = "المحفزات";

/* triggers for table label */
"Triggers for table: %@" = "مشغلات على الجدول: %@";

/* truncate button */
"Truncate" = "اقتطاع";

/* truncate tables message */
"Truncate selected tables?" = "اقتطاع الجداول المحددة؟";

/* truncate table menu title */
"Truncate Table..." = "اقتطاع الجدول...";

/* truncate table message */
"Truncate table '%@'?" = "حذف الجدول '%@'؟";

/* truncate tables menu item */
"Truncate Tables" = "اقتطاع الجداول";

/* type label (Navigator) */
"Type" = "نوع";

/* type declaration header */
"Type Declaration:" = "نوع الإعلان:";

/* add index error message */
"Unable to add index" = "غير قادر على إضافة فهرس";

/* unable to analyze selected items message */
"Unable to analyze selected items" = "غير قادر على تحليل العناصر المحددة";

/* unable to analyze table message */
"Unable to analyze table" = "غير قادر على تحليل الجدول";

/* unable to check selected items message */
"Unable to check selected items" = "غير قادر على التحقق من العناصر المحددة";

/* unable to check table message */
"Unable to check table" = "غير قادر على التحقق من الجدول";

/* message of panel when connection to host failed due to access denied error */
"Unable to connect to host %@ because access was denied.\n\nDouble-check your username and password and ensure that access from your current location is permitted.\n\nMySQL said: %@" = "غير قادر على الاتصال بالمضيف %1$@ بسبب رفض الوصول.\n\nتحقق مزدوج من اسم المستخدم وكلمة المرور الخاصة بك والتأكد من السماح بالوصول من موقعك الحالي.\n\nMySQL قال: %2$@";

/* message of panel when SSH port forwarding failed */
"Unable to connect to host %@ because the port connection via SSH was refused.\n\nPlease ensure that your MySQL host is set up to allow TCP/IP connections (no --skip-networking) and is configured to allow connections from the host you are tunnelling via.\n\nYou may also want to check the port is correct and that you have the necessary privileges.\n\nChecking the error detail will show the SSH debug log which may provide more details.\n\nMySQL said: %@" = "غير قادر على الاتصال بالمضيف %1$@ لأن اتصال المنفذ عبر SSH تم رفضه.\n\nالرجاء التأكد من أن مضيف MySQL الخاص بك يتم إعداده للسماح باتصالات TCP/IP (no - تخطي الشبكات) ويتم إعداده للسماح باتصالات من المضيف الذي تقوم بتحميله فيا.\n\nقد ترغب أيضًا في التحقق من أن المنفذ صحيح وأن لديك الامتيازات اللازمة.\n\nالتحقق من تفاصيل الخطأ سيظهر سجل تصحيح أخطاء SSH الذي قد يوفر المزيد من التفاصيل.\n\nMySQL قال: %2$@";

/* message of panel when connection to host failed */
"Unable to connect to host %@, or the request timed out.\n\nBe sure that the address is correct and that you have the necessary privileges, or try increasing the connection timeout (currently %ld seconds).\n\nMySQL said: %@" = "غير قادر على الاتصال باستضافة %1$@أو انتهت مهلة الطلب.\n\nتأكد من أن العنوان صحيح وأن لديك الامتيازات اللازمة، أو حاول زيادة مهلة الاتصال (حاليا %2$ld ثوان).\n\nMySQL قال: %3$@";

/* message of panel when connection to host failed */
"Unable to connect via the socket, or the request timed out.\n\nDouble-check that the socket path is correct and that you have the necessary privileges, and that the server is running.\n\nMySQL said: %@" = "غير قادر على الاتصال عبر المقطع، أو انتهت مهلة الطلب.\n\nتحقق من أن مسار المقبس صحيح وأن لديك الامتيازات اللازمة، وأن الخادم قيد التشغيل.\n\nMySQL قال: %@";

/* unable to copy database message */
"Unable to copy database" = "تعذر نسخ قاعدة البيانات";

/* error deleting index message */
"Unable to delete index" = "غير قادر على حذف الفهرس";

/* error deleting relation message */
"Unable to delete relation" = "غير قادر على حذف العلاقة";

/* error deleting trigger message */
"Unable to delete trigger" = "غير قادر على حذف المشغل";

/* unable to flush selected items message */
"Unable to flush selected items" = "غير قادر على مسح العناصر المحددة";

/* unable to flush table message */
"Unable to flush table" = "غير قادر على مسح الجدول";

/* unable to get list of users message */
"Unable to get list of users" = "غير قادر على الحصول على قائمة المستخدمين";

/* error killing connection message */
"Unable to kill connection" = "غير قادر على قتل الاتصال";

/* error killing query message */
"Unable to kill query" = "غير قادر على قتل الاستعلام";

/* unable to optimze selected items message */
"Unable to optimze selected items" = "غير قادر على تحسين العناصر المحددة";

/* unable to optimze table message */
"Unable to optimze table" = "غير قادر على تحسين الجدول";

/* unable to perform the checksum */
"Unable to perform the checksum" = "غير قادر على تنفيذ المجموع الاختباري";

/* error removing host message */
"Unable to remove host" = "غير قادر على إزالة المضيف";

/* unable to rename database message */
"Unable to rename database" = "غير قادر على إعادة تسمية قاعدة البيانات";

/* unable to repair selected items message */
"Unable to repair selected items" = "غير قادر على إصلاح العناصر المحددة";

/* unable to repair table message */
"Unable to repair table" = "غير قادر على إصلاح الجدول";

/* message of panel when connection to db failed after selecting from popupbutton */
"Unable to select database %@.\nPlease check you have the necessary privileges to view the database, and that the database still exists." = "غير قادر على تحديد قاعدة البيانات %@.\nالرجاء التحقق من أن لديك الامتيازات اللازمة لعرض قاعدة البيانات، وأن قاعدة البيانات لا تزال موجودة.";

/* Unable to write row error */
"Unable to write row" = "غير قادر على كتابة الصف";

/* Table Content : Remove Row : Result : n Error title */
"Unexpected number of rows removed!" = "تم إزالة عدد غير متوقع من الصفوف!";

/* warning */
"Unknown file format" = "تنسيق ملف غير معروف";

/* unsaved changes message */
"Unsaved changes" = "التغييرات غير المحفوظة";

/* unsaved theme message */
"Unsaved Theme" = "سمة غير محفوظة";

/* Preferences : Network : Custom SSH client : warning dialog title */
"Unsupported configuration!" = "تكوين غير مدعوم!";

/* export : import settings : file version error title */
"Unsupported version for export settings!" = "إصدار غير مدعوم لإعدادات التصدير!";

/* Name for an untitled connection */
"Untitled" = "بدون عنوان";

/* Title of a new Sequel Ace Document */
"Untitled %ld" = "%ld بدون عنوان";

/* range for mediumblob type */
"Up to %@ bytes (16 MiB)" = "حتى %@ بايت (16 ميجابايت)";

/* range for longblob type */
"Up to %@ bytes (4 GiB)" = "حتى %@ بايت (4 GiB)";

/* range for mediumtext type */
"Up to %@ characters (16 MiB)" = "حتى %@ أحرف (16 ميجابايت)";

/* range for enum type */
"Up to %@ distinct members (<%@ in practice)\n1-2 bytes storage" = "حتى %1$@ أعضاء متميزين (<%2$@ في الممارسة)\n1-2 بايت التخزين";

/* range for tinyblob type */
"Up to 255 bytes" = "حتى 255 بايت";

/* range for tinytext type */
"Up to 255 characters" = "ما يصل إلى 255 حرفاً";

/* Open Files : Bundle : Already-Installed : Update button */
"Update" = "تحديث";

/* updated: %@ */
"updated: %@" = "تحديث: %@";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field of table `%2$@` was changed." = "فشل تحديث محتوى الحقل. تعذر تحديد مصدر الحقل بشكل لا لبس فيه ( مطابقة%1$ld ). من المحتمل جدا أنه أثناء تحرير هذا المجال من الجدول '%2$@` تم تغييره.";

/* message of panel when error while updating field to db after enabling it */
"Updating field content failed. Couldn't identify field origin unambiguously (%1$ld matches). It's very likely that while editing this field the table `%2$@` was changed by an other user." = "فشل تحديث محتوى الحقل. تعذر تحديد مصدر الحقل بشكل لا لبس فيه ( مطابقة%1$ld ). من المحتمل جداً أنه أثناء تحرير هذا الحقل تم تغيير الجدول `%2$@` بواسطة مستخدم آخر.";

/* updating field task description */
"Updating field data..." = "تحديث البيانات الحقل...";

/* URL scheme command couldn't authenticated */
"URL scheme command couldn't authenticated" = "تعذر مصادقة أمر مخطط URL";

/* URL scheme command was terminated by user */
"URL scheme command was terminated by user" = "تم إنهاء أمر مخطط URL من قبل المستخدم";

/* URL scheme command %@ unsupported */
"URL scheme command %@ unsupported" = "أمر مخطط URL %@ غير مدعوم";

/* Use 127.0.0.1 button */
"Use 127.0.0.1" = "استخدام 127.0.0.1";

/* use standard connection button */
"Use Standard Connection" = "استخدام اتصال قياسي";

/* user has no hosts message */
"User has no hosts" = "المستخدم ليس لديه مضيفين";

/* user-defined value */
"User-defined value" = "القيمة المحددة للمستخدم";

/* toolbar item label for switching to the User Manager tab */
"Users" = "المستخدمون";

/* CSV Field Mapping : Table View : Tooltip for fields with NULL value */
"Value will be imported as MySQL NULL" = "سيتم استيراد القيمة كـ MySQL NULL";

/* variable label for color table (Prefs > Editor) */
"Variable" = "متغير";

/* version */
"version" = "الإصدار";

/* export header version label */
"Version" = "الإصدار";

/* view */
"view" = "عرض";

/* Release notes button title */
"View full release notes" = "عرض ملاحظات الإصدار الكامل";

/* header for view info pane */
"VIEW INFORMATION" = "عرض المعلومات";

/* view html source code menu item title */
"View Source" = "عرض المصدر";

/* view structure print heading */
"View Structure" = "عرض الهيكل";

/* views */
"views" = "مشاهدة";

/* warning */
"Warning" = "تحذير";

/* Short important release note for why password prompts may occur */
"We've changed Sequel Ace's digital signature for GateKeeper compatibility; you'll have to allow access to your passwords again." = "لقد قمنا بتغيير التوقيع الرقمي لـ Sequel Ace، من أجل توافق GateKeeper ، سيكون عليك السماح بالوصول إلى كلمات المرور الخاصة بك مرة أخرى.";

/* Important release notes informational text, single change */
"We've made a few changes but we thought you should know about one particularly important one:" = "لقد قمنا ببضع تغييرات لكننا اعتقدنا أنه يجب عليك معرفة واحدة ذات أهمية خاصة:";

/* Important release notes informational text, multiple changes */
"We've made a few changes but we thought you should know about some particularly important ones:" = "لقد قمنا ببضع تغييرات لكننا اعتقدنا انه يجب عليك معرفة بعض التغييرات المهمة بشكل خاص:";

/* WHERE clause not valid */
"WHERE clause not valid" = "لماذا شرط غير صالح";

/* Title of filter preview area when the query WHERE is negated */
"WHERE NOT query" = "ما لم يتم الاستعلام";

/* Title of filter preview area when the query WHERE is normal */
"WHERE query" = "ما هو الاستعلام";

/* Generic working description */
"Working..." = "يعمل...";

/* export label showing app is writing data */
"Writing data..." = "كتابة البيانات...";

/* text showing that app is writing text file */
"Writing..." = "الكتابة...";

/* wrong data format or password */
"Wrong data format or password." = "تنسيق البيانات أو كلمة المرور خاطئة.";

/* wrong data format */
"Wrong data format." = "تنسيق البيانات خاطئ.";

/* export : import settings : spf content type error title */
"Wrong SPF content type!" = "نوع محتوى SPF غير صحيح!";

/* export filename date token */
"Year" = "السنة";

/* message of panel when trying to copy multiple rows */
"You can only copy single rows." = "يمكنك نسخ صف واحد فقط.";

/* message of panel when trying to edit tables without index and with hidden blob/text fields */
"You can't hide blob and text fields when working with tables without index." = "لا يمكنك إخفاء حقول الكتلة والنص عند العمل مع الجداول بدون فهرسة.";

/* You cannot delete the last field in a table. Delete the table instead. */
"You cannot delete the last field in a table. Delete the table instead." = "لا يمكنك حذف الحقل الأخير في الجدول. احذف الجدول بدلا من ذلك.";

/* SSL connection requested but not established error detail */
"You requested that the connection should be established using SSL, but MySQL made the connection without SSL.\n\nThis may be because the server does not support SSL connections, or has SSL disabled; or insufficient details were supplied to establish an SSL connection.\n\nThis connection is not encrypted." = "لقد طلبت أن يتم إنشاء الاتصال باستخدام SSL، ولكن MySQL قام بالاتصال بدون SSL.\n\nقد يكون ذلك لأن الخادم لا يدعم اتصالات SSL، أو معطل SSL؛ أو لم تقدم تفاصيل كافية لإقامة صلة بين شريحة SSL.\n\nهذا الاتصال غير مشفر.";

/* Query Favorites : List : Section Heading : current connection document : tooltip (arg is the name of the spf file) */
"‘%@’ based favorites" = "'مفضلة%@القائمة على أساس";

/* table column header. Read: 'Showing all content filters for fields of type %@' (ContentFilterManager) */
"‘%@’ Fields Content Filters" = "'%@مرشحات محتوى الحقول";

/* Export file already exists message */
"%@ already exists. Do you want to replace it?" = "%@ موجود بالفعل. هل تريد استبداله؟";

/* Bundle Editor : Outline View : Bundle item : tooltip */
"%@ Bundle" = "حزمة %@";

/* Export file creation error title */
"%@ could not be created" = "تعذر إنشاء %@";

/* %@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item. */
"%@ couldn't be parsed. You can edit the column setup but the column will not be shown in the Content view; please report this issue to the Sequel Ace team using the Help menu item." = "تعذر تحليل %@ . يمكنك تعديل إعداد العمود ولكن العمود لن يظهر في عرض المحتوى؛ يرجى الإبلاغ عن هذه المشكلة لفريق Sequel Ace باستخدام عنصر قائمة المساعدة.";

/* connection view : ssl : client cert file picker : wrong format error title */
"%@ is not a valid client certificate file." = "%@ ليس ملف شهادة عميل صالح.";

/* connection view : ssl : key file picker : wrong format error title */
"%@ is not a valid private key file." = "%@ ليس ملف مفتاح خاص صالح.";

/* text shown when there are App Sandbox Issues */
"App Sandbox Issue" = "مشكلة صندوق التطبيقات";

/* Stale Bookmarks error title */
"Stale Bookmarks" = "العلامات المعتادة";

/* App Sandbox info link text */
"App Sandbox Info" = "معلومات صندوق التطبيقات";

/* error while selecting file title */
"File Selection Error" = "خطأ في اختيار الملف";

/* error while selecting file message */
"The selected file is not a valid file.\n\nPlease try again.\n\nClass: %@" = "الملف المحدد ليس ملف صالح.\n\nالرجاء المحاولة مرة أخرى.\n\nclass: %@";

/* known hosts not writable error message */
"The selected known hosts file is not writable.\n\n%@\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "ملف المضيفين المعروفين المحدد غير قابل للكتابة.\n\n%@\n\nالرجاء إعادة تحديد الملف في تفضيلات Sequel Ace، وحاول مرة أخرى.";

/* known hosts is invalid message */
"The selected known hosts file is invalid.\n\nPlease re-select the file in Sequel Ace's Preferences and try again." = "ملف المضيفين المعروفين المحدد غير صالح.\n\nالرجاء إعادة تحديد الملف في تفضيلات Sequel Ace، وحاول مرة أخرى.";

/* known hosts contains quote message */
"The selected known hosts file contains a quote (\") in its file path which is not supported.\n\n%@\n\nPlease select a different file in Sequel Ace's Preferences or rename the file/path to remove the quote." = "ملف المضيفين المعروفين المحدد يحتوي على اقتباس (\") في مسار الملف الخاص به والذي غير مدعوم.\n\n%@\n\nالرجاء تحديد ملف مختلف في تفضيلات Sequel Ace، أو إعادة تسمية الملف/المسار لإزالة الاقتباس.";

/* SSH Tunnel Debugging menu and window title */
"SSH Tunnel Debugging Info" = "معلومات تصحيح أخطاء نفق SSH";

/* Question to user to see if they would like to re-request access */
"You have stale secure bookmarks:\n\n%@\n\nWould you like to re-request access now?" = "لديك إشارات مرجعية ثابتة آمنة:\n\n%@\n\nهل ترغب في إعادة طلب الوصول الآن؟";

/* Question to user to see if they would like to request access to missing bookmarks */
"You have missing secure bookmarks:\n\n%@\n\nWould you like to request access now?" = "لديك إشارات مرجعية آمنة مفقودة:\n\n%@\n\nهل ترغب في طلب الوصول الآن؟";

/* known hosts advance menu item title */
"Use known hosts from ssh config (ADVANCED)" = "استخدام المضيفين المعروفين من إعدادات ssh (ADVANCED)";

/* The answer, yes */
"Yes" = "نعم";

/* Shown to remind users of their stale bookmarks */
"A reminder of your stale secure bookmarks:<br /><br />%@<br />" = "تذكير بإشارات مرجعية ثابتة آمنة:<br /><br />%@<br />";

/* Title for Stale Secure Bookmarks help window */
"Stale Secure Bookmarks" = "الإشارات المرجعية الآمنة";

/* Title for Export Error alert */
"Export Error" = "خطأ في التصدير";

/* Message for Export Error alert */
"Error while writing to the export file. Could not open file: %@" = "حدث خطأ أثناء الكتابة على ملف التصدير. تعذر فتح الملف: %@";

/* Message for User window error */
"Resultset from mysql.user contains neither 'Password' nor 'authentication_string' column." = "النتيجة من mysql.user لا تحتوي على عمود 'Password' أو 'authentication_string'.";

/* Title for User window error */
"User Data Error" = "خطأ في بيانات المستخدم";

/* Title for Stale Bookmark file selection dialog */
"Please re-select the file '%@' in order to restore Sequel Ace's access." = "الرجاء إعادة تحديد الملف '%@' من أجل استعادة وصول Sequel Ace.";

/* Title for File Preference file selection dialog */
"Please choose a file or folder to grant Sequel Ace access to." = "الرجاء اختيار ملف أو مجلد لمنح حق الوصول لـ Sequel Ace";

/* Title for Network Preference ssh config file selection dialog */
"Please choose your ssh config files(s)" = "الرجاء اختيار ملفات تهيئة ssh الخاصة بك";

/* Title for Network Preference ssh known hosts file selection dialog */
"Please choose your known hosts file" = "الرجاء اختيار ملف المضيفين المعروفين";

/* Message for field editor JSON segment when JSON is invalid */
"Invalid JSON" = "JSON غير صالح";

/* Applying syntax highlighting task description */
"Applying syntax highlighting..." = "تطبيق تسليط الضوء على الجملة...";

/* Error message when a bash task fails to start */
"Couldn't launch task.\nException reason: %@\n ENV length: %lu" = "Couldn't launch task.\nException reason: %@\n ENV length: %lu";

/* Error message title when we failed to create new connection view */
"New Connection Error" = "خطأ اتصال جديد";

/* New Connection Error informative message */
"Failed to create new database connection window. Please restart Sequel Ace and try again." = "فشل إنشاء نافذة اتصال قاعدة بيانات جديدة. الرجاء إعادة تشغيل Sequel Ace وحاول مرة أخرى.";

/* new version is available alert title */
"A new version is available" = "يتوفر إصدار جديد";

/* new version is available download button title */
"Download" = "تنزيل";

/* new version is available alert informativeText */
"Version %@ is available. You are currently running %@" = "الإصدار %@ متوفر. أنت حاليا قيد التشغيل %@";

/* downloading new version window title */
"Download Progress" = "تقدم التحميل";

/* downloading new version time remaining placeholder text */
"Calculating time remaining..." = "حساب الوقت المتبقي...";

/* downloading new version window message */
"Downloading Sequel Ace - %@" = "تحميل ثلج سيكل - %@";

/* downloading new version time remaining estimate text */
"About %.1f seconds left" = "حول %.1f ثواني متبقية";

/* downloading new version failure alert title */
"Download Failed" = "فشل التحميل";

/* Tool tip for the Show alert when update is available preference on the Notifications pane */
"Only available for GitHub downloads" = "متوفر فقط لتنزيلات GitHub";

/* Tool tip warning for when the user sets auto-complete delay to zero */
"WARNING: Setting the auto-complete delay to 0.0 can result in strange output." = "تحذير: تعيين تأخير الإكمال التلقائي إلى 0.0 يمكن أن يؤدي إلى إخراج غريب.";

/* downloading new version bytes of bytes done */
"%@ of %@" = "%@ من %@";

/* Time zone set failed informative message */
"\n\ntime_zone will be set to SYSTEM." = "\n\ntime_zone سيتم تعيينه إلى SYSTEM.";

/* Menu item title for checking for updates */
"Check for Updates..." = "التحقق من وجود تحديثات...";

/* Warning message during connection in case the variable skip-show-database is set to ON */
"The skip-show-database variable of the database server is set to ON. Thus, you won't be able to list databases unless you have the SHOW DATABASES privilege.\n\nHowever, the databases are still accessible directly through SQL queries depending on your privileges." = "تم تعيين متغير قاعدة البيانات لخادم قاعدة البيانات على تشغيل. وبالتالي لن تتمكن من إدراج قواعد البيانات ما لم يكن لديك امتياز SHOW DATABASES.\n\nومع ذلك، لا تزال قواعد البيانات متاحة مباشرة من خلال استفسارات SQL اعتماداً على امتيازاتك.";

/* Error alert title when the request to GitHub fails */
"GitHub Request Failed" = "فشل طلب GitHub";

/* Info alert title when there are Newer Releases Available */
"No Newer Release Available" = "لا يوجد إصدار أحدث متوفر";

/* Info alert message when there are Newer Releases Available */
"You are currently running the latest release." = "أنت حاليا تقوم بتشغيل الإصدار الأخير.";

/* Alert button, disable the warning when skip-show-database is set to on */
"Never show this again" = "لا تظهر هذا مرة أخرى";

/* Info alert when editing a generated column */
"The current field \"%@\" is a generated column and therefore cannot be edited." = "الحقل الحالي \"%@\" هو عمود تم إنشاؤه وبالتالي لا يمكن تحريره.";

/* Release note / info modal about change in latest release about column behavior */
"The use of the \"default\" column has changed since the last version of Sequel ACE:\n\n- No default value : Leave it blank.\n- String value : Use single '' or double quotes \"\" if you want an empty string or to wrap a string\n- Expression : Use parentheses (). Except for the TIMESTAMP and DATETIME columns where you can specify the CURRENT_TIMESTAMP function without enclosing parentheses." = "لقد تغير استخدام العمود \"الافتراضي\" منذ النسخة الأخيرة من Sequel ACE:\n\n- لا قيمة افتراضية : تركها فارغة.\n- قيمة السلسلة: استخدام علامة اقتباس واحدة '' أو علامة اقتباس مزدوجة \"\" إذا كنت ترغب في سلسلة فارغة أو لإغلاق سلسلة\n- التعبير : استخدام الأقواس (). باستثناء أعمدة TIMESTAMP و DATETIME حيث يمكنك تحديد دالة CURRENT_TIMESTAMP دون إرفاق الأقواس.";

/* Menu option to pin a view to the top of the list of tables */
"Pin View" = "عرض التثبيت";

/* Menu option to pin a table to the top of the list of tables */
"Pin Table" = "تثبيت الجدول";

/* Menu option to pin a procedure to the top of the list of tables */
"Pin Procedure" = "إجراء المثبت";

/* Menu option to pin a function to the top of the list of tables */
"Pin Function" = "دالة تثبيت";

/* Menu option to unpin a view which is currently pinned at the top of the list of tables */
"Unpin View" = "إلغاء تثبيت العرض";

/* Menu option to unpin a table which is currently pinned at the top of the list of tables */
"Unpin Table" = "فك تثبيت الجدول";

/* Menu option to unpin a procedure which is currently pinned at the top of the list of tables */
"Unpin Procedure" = "إجراءات إلغاء التثبيت";

/* Menu option to unpin a function which is currently pinned at the top of the list of tables */
"Unpin Function" = "إلغاء تثبيت الدالة";

/* header for pinned table list */
"PINNED" = "منبثق";

/* Menu option to copy a table's name with database name in the list of tables */
"Copy Table Name" = "نسخ اسم الجدول";

/* sequelace URL scheme error: LaunchFavorite alert title (favorite name not found) */
"LaunchFavorite URL Scheme Error" = "خطأ في مخطط URL المفضلة";

/* sequelace URL scheme error: LaunchFavorite alert description (favorite name not found) */
"The variable in the ?name= query parameter could not be matched with any of your favorites." = "المتغير في معلمة الاستعلام ?name= لا يمكن مطابقته مع أي من المفضلة لديك.";
