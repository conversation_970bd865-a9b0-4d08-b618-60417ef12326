<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>BlobTextEditorSpellCheckingEnabled</key>
	<false/>
	<key>ConnectionTimeoutValue</key>
	<integer>10</integer>
	<key>ConsoleEnableCustomQueryLogging</key>
	<true/>
	<key>ConsoleEnableErrorLogging</key>
	<true/>
	<key>ConsoleEnableImportExportLogging</key>
	<false/>
	<key>ConsoleEnableInterfaceLogging</key>
	<true/>
	<key>ConsoleEnableLogging</key>
	<true/>
	<key>ConsoleShowConnections</key>
	<true/>
	<key>ConsoleShowDatabases</key>
	<true/>
	<key>ConsoleShowHelps</key>
	<true/>
	<key>ConsoleShowSelectsAndShows</key>
	<true/>
	<key>ConsoleShowTimestamps</key>
	<true/>
	<key>ContentFilters</key>
	<dict>
		<key>number</key>
		<array/>
		<key>string</key>
		<array/>
		<key>date</key>
		<array/>
	</dict>
	<key>CSVFieldImportMappingAlignment</key>
	<integer>2</integer>
	<key>CSVImportFieldEnclosedBy</key>
	<string>&quot;</string>
	<key>CSVImportFieldEscapeCharacter</key>
	<string>\ or &quot;</string>
	<key>CSVImportFieldTerminator</key>
	<string>,</string>
	<key>CSVImportFirstLineIsHeader</key>
	<true/>
	<key>CSVImportLineTerminator</key>
	<string>\n</string>
	<key>CustomQueryAutoComplete</key>
	<true/>
	<key>CustomQueryAutoCompleteDelay</key>
	<real>1.5</real>
	<key>CustomQueryAutoCompleteFuzzy</key>
	<false/>
	<key>CustomQueryAutoHelpDelay</key>
	<real>1</real>
	<key>CustomQueryAutoIndent</key>
	<true/>
	<key>CustomQueryAutoPairCharacters</key>
	<true/>
	<key>SPCustomQueryEditorCompleteWithBackticks</key>
	<true/>
	<key>CustomQueryAutoUppercaseKeywords</key>
	<false/>
	<key>CustomQueryEditorBackgroundColor</key>
	<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMBhARmZmZmAQEBAYY=</data>
	<key>CustomQueryEditorBacktickColor</key>
	<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMBhARmZmZmAACDqagoPwGG</data>
	<key>CustomQueryEditorCaretColor</key>
	<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMBhARmZmZmAAAAAYY=</data>
	<key>CustomQueryEditorCommentColor</key>
	<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMBhARmZmZmAIPp6Og+AAGG</data>
	<key>CustomQueryEditorFont</key>
	<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAZOU0ZvbnQehIQITlNPYmplY3QAhYQBaSSEBVszNmNdBgAAABwAAAD//k0AZQBuAGwAbwAtAFIAZQBnAHUAbABhAHIAhAFmDoQBYwCYAZgAmACG</data>
	<key>CustomQueryEditorHighlightQueryColor</key>
	<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMChARmZmZmgzMzcz+DMzNzP4MzM3M/AYY=</data>
	<key>CustomQueryEditorNumericColor</key>
	<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMBhARmZmZmg4GAAD+Dh4aGPgABhg==</data>
	<key>CustomQueryEditorQuoteColor</key>
	<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMBhARmZmZmg8bFRT+DnXPOPYNba609AYY=</data>
	<key>CustomQueryEditorSelectionColor</key>
	<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMChARmZmZmg7a1NT+D1tVVPwEBhg==</data>
	<key>CustomQueryEditorSQLKeywordColor</key>
	<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMBhARmZmZmg83MTD6DnXPOPQEBhg==</data>
	<key>CustomQueryEditorTabStopWidth</key>
	<integer>4</integer>
	<key>CustomQueryEditorTextColor</key>
	<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMBhARmZmZmAAAAAYY=</data>
	<key>CustomQueryEditorVariableColor</key>
	<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMBhARmZmZmg3zv/T6DfO/9PoN87/0+AYY=</data>
	<key>CustomQueryFunctionCompletionInsertsArguments</key>
	<true/>
	<key>CustomQueryHighlightCurrentQuery</key>
	<true/>
	<key>CustomQueryEnableSyntaxHighlighting</key>
	<true/>
	<key>CustomQueryEnableBracketHighlighting</key>
	<true/>
	<key>CustomQueryMaxHistoryItems</key>
	<integer>20</integer>
	<key>EditInSheetForMultiLineText</key>
	<true/>
	<key>SaveApplicationUsageAnalytics</key>
	<false/>
	<key>EditInSheetForLongText</key>
	<true/>
	<key>EditInSheetForLongTextLengthThreshold</key>
	<integer>15</integer>
	<key>CustomQuerySaveHistoryIndividually</key>
	<true/>
	<key>CustomQuerySoftIndent</key>
	<false/>
	<key>CustomQuerySoftIndentWidth</key>
	<integer>2</integer>
	<key>CustomQueryUpdateAutoHelp</key>
	<false/>
	<key>DefaultEncodingTag</key>
	<real>0</real>
	<key>DefaultViewMode</key>
	<integer>0</integer>
	<key>deletedDefaultBundles</key>
	<array/>
	<key>ApplicationPromptOnQuit</key>
	<true/>
	<key>DisplayServerVersionInWindowTitle</key>
	<true/>
	<key>DisplayTableViewVerticalGridlines</key>
	<false/>
	<key>DisplayTableViewColumnTypes</key>
	<true/>
	<key>EditInSheetEnabled</key>
	<false/>
	<key>FavoriteColorList</key>
	<array>
		<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMChARmZmZmg+XkZD+D6ejoPoPNzMw+AYY=</data>
		<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMChARmZmZmg+7tbT+Dr64uP4PX1tY+AYY=</data>
		<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMChARmZmZmg+TjYz+D1tVVP4Pv7u4+AYY=</data>
		<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMChARmZmZmg7CvLz+D2NdXP4Pv7u4+AYY=</data>
		<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMChARmZmZmg+3s7D6Durk5P4Pp6Gg/AYY=</data>
		<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMChARmZmZmg8vKSj+DmZgYP4Ph4GA/AYY=</data>
		<data>BAtzdHJlYW10eXBlZIHoA4QBQISEhAdOU0NvbG9yAISECE5TT2JqZWN0AIWEAWMChARmZmZmg7e2Nj+Dt7Y2P4O3tjY/AYY=</data>
	</array>
	<key>FavoritesSortedBy</key>
	<integer>0</integer>
	<key>FavoritesSortedInReverse</key>
	<false/>
	<key>FilterTableDefaultOperator</key>
	<string>LIKE &apos;%@%&apos;</string>
	<key>KeepAliveInterval</key>
	<integer>60</integer>
	<key>LastFavoriteIndex</key>
	<integer>0</integer>
	<key>lastSqlFileEncoding</key>
	<integer>4</integer>
	<key>LastViewMode</key>
	<integer>1</integer>
	<key>LimitResults</key>
	<true/>
	<key>LimitResultsValue</key>
	<integer>1000</integer>
	<key>LoadBlobsAsNeeded</key>
	<false/>
	<key>LongRunningQueryNotificationTime</key>
	<real>3</real>
	<key>NewFieldsAllowNulls</key>
	<true/>
	<key>NullValue</key>
	<string>NULL</string>
	<key>PrintBackground</key>
	<true/>
	<key>PrintImagePreviews</key>
	<true/>
	<key>PrintWarningRowLimit</key>
	<integer>1000</integer>
	<key>ProcessListShowProcessID</key>
	<true/>
	<key>ProcessListShowFullProcessList</key>
	<false/>
	<key>ProcessListEnableAutoRefresh</key>
	<false/>
	<key>ProcessListAutoRrefreshInterval</key>
	<integer>10</integer>
	<key>QueryFavoriteReplacesContent</key>
	<false/>
	<key>QueryHistoryReplacesContent</key>
	<false/>
	<key>QueryPrimaryControlRunsAll</key>
	<false/>
	<key>QuickLookTypes</key>
	<array/>
	<key>ReloadAfterAddingRow</key>
	<true/>
	<key>ReloadAfterEditingRow</key>
	<true/>
	<key>ReloadAfterRemovingRow</key>
	<false/>
	<key>ResetAutoIncrementAfterDeletionOfAllRows</key>
	<true/>
	<key>SelectLastFavoriteUsed</key>
	<true/>
	<key>ShowNoAffectedRowsError</key>
	<true/>
	<key>SSHMultiplexingEnabled</key>
	<false/>
	<key>TableInformationPanelCollapsed</key>
	<false/>
	<key>TableRowCountCheapLookupSizeBoundary</key>
	<integer>5242880</integer>
	<key>TableRowCountQueryLevel</key>
	<integer>1</integer>
	<key>UseKeepAlive</key>
	<true/>
	<key>WebKitDeveloperExtras</key>
	<true/>
	<key>DisplayBinaryDataAsHex</key>
	<false/>
	<key>CopyContentOnTableCopy</key>
	<false/>
	<key>ShowWarningBeforeExecuteQuery</key>
	<false/>
	<key>ShowWarningSkipShowDatabase</key>
	<true/>
	<key>ShowUpdateAvailable</key>
	<true/>
	<key>NSApplicationCrashOnExceptions</key>
	<true/>
	<key>ShowWarningBeforeDeleteQuery</key>
	<true/>
</dict>
</plist>
