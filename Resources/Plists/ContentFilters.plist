<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>number</key>
	<array>
		<dict>
			<key>MenuLabel</key>
			<string>=</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>= &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>Menu<PERSON>abel</key>
			<string>≠</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>!= &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>&gt;</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>&gt; &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>&lt;</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>&lt; &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>≥</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>&gt;= &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>≤</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>&lt;= &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>IN</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>IN (${})</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>NOT IN __ OR NULL</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>SuppressLeadingFieldPlaceholder</key>
			<true/>
			<key>Clause</key>
			<string>($CURRENT_FIELD NOT IN (${}) OR $CURRENT_FIELD IS NULL)</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>LIKE</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>LIKE &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>BETWEEN</string>
			<key>NumberOfArguments</key>
			<integer>2</integer>
			<key>ConjunctionLabels</key>
			<array>
				<string>AND</string>
			</array>
			<key>Clause</key>
			<string>BETWEEN &apos;${}&apos; AND &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>IS NULL</string>
			<key>NumberOfArguments</key>
			<integer>0</integer>
			<key>Clause</key>
			<string>IS NULL</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>IS NOT NULL</string>
			<key>NumberOfArguments</key>
			<integer>0</integer>
			<key>Clause</key>
			<string>IS NOT NULL</string>
		</dict>
	</array>
	<key>string</key>
	<array>
		<dict>
			<key>MenuLabel</key>
			<string>=</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>= $BINARY &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>≠</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>!= $BINARY &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>LIKE</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>LIKE $BINARY &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>NOT LIKE</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>NOT LIKE $BINARY &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>contains</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>LIKE $BINARY &apos;%${}%&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>does not contain</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>NOT LIKE $BINARY &apos;%${}%&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>starts with</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>LIKE $BINARY &apos;${}%&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>does not start with</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>NOT LIKE $BINARY &apos;${}%&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>ends with</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>LIKE $BINARY &apos;%${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>does not end with</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>NOT LIKE $BINARY &apos;%${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>matches RegExp</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>REGEXP $BINARY &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>does not match RegExp</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>NOT REGEXP $BINARY &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>IN</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>IN (${})</string>
			<key>Tooltip</key>
			<string>IN ([arg])</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>NOT IN __ OR NULL</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>SuppressLeadingFieldPlaceholder</key>
			<true/>
			<key>Clause</key>
			<string>($CURRENT_FIELD NOT IN (${}) OR $CURRENT_FIELD IS NULL)</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>BETWEEN</string>
			<key>NumberOfArguments</key>
			<integer>2</integer>
			<key>ConjunctionLabels</key>
			<array>
				<string>AND</string>
			</array>
			<key>Clause</key>
			<string>BETWEEN $BINARY &apos;${}&apos; AND &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>IS NULL</string>
			<key>NumberOfArguments</key>
			<integer>0</integer>
			<key>Clause</key>
			<string>IS NULL</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>IS NOT NULL</string>
			<key>NumberOfArguments</key>
			<integer>0</integer>
			<key>Clause</key>
			<string>IS NOT NULL</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>is empty</string>
			<key>NumberOfArguments</key>
			<integer>0</integer>
			<key>Clause</key>
			<string>LIKE &apos;&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>is not empty</string>
			<key>NumberOfArguments</key>
			<integer>0</integer>
			<key>Clause</key>
			<string>NOT LIKE &apos;&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>= (Hex String)</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>= $BINARY UNHEX(&apos;${}&apos;)</string>
		</dict>
	</array>
	<key>date</key>
	<array>
		<dict>
			<key>MenuLabel</key>
			<string>=</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>= &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>≠</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>!= &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>is after</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>&gt; &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>is before</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>&lt; &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>is after or equal to</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>&gt;= &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>is before or equal to</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>Clause</key>
			<string>&lt;= &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>BETWEEN</string>
			<key>NumberOfArguments</key>
			<integer>2</integer>
			<key>ConjunctionLabels</key>
			<array>
				<string>AND</string>
			</array>
			<key>Clause</key>
			<string>BETWEEN &apos;${}&apos; AND &apos;${}&apos;</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>IS NULL</string>
			<key>NumberOfArguments</key>
			<integer>0</integer>
			<key>Clause</key>
			<string>IS NULL</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>IS NOT NULL</string>
			<key>NumberOfArguments</key>
			<integer>0</integer>
			<key>Clause</key>
			<string>IS NOT NULL</string>
		</dict>
	</array>
	<key>spatial</key>
	<array>
		<dict>
			<key>MenuLabel</key>
			<string>contains</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>SuppressLeadingFieldPlaceholder</key>
			<true/>
			<key>Clause</key>
			<string>MBRContains(ST_GeomFromText(&apos;${}&apos;),$CURRENT_FIELD)</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>within</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>SuppressLeadingFieldPlaceholder</key>
			<true/>
			<key>Clause</key>
			<string>MBRWithin(ST_GeomFromText(&apos;${}&apos;),$CURRENT_FIELD)</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>disjoint</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>SuppressLeadingFieldPlaceholder</key>
			<true/>
			<key>Clause</key>
			<string>MBRDisjoint(ST_GeomFromText(&apos;${}&apos;),$CURRENT_FIELD)</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>equal</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>SuppressLeadingFieldPlaceholder</key>
			<true/>
			<key>Clause</key>
			<string>MBREqual(ST_GeomFromText(&apos;${}&apos;),$CURRENT_FIELD)</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>intersects</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>SuppressLeadingFieldPlaceholder</key>
			<true/>
			<key>Clause</key>
			<string>MBRIntersects(ST_GeomFromText(&apos;${}&apos;),$CURRENT_FIELD)</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>overlaps</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>SuppressLeadingFieldPlaceholder</key>
			<true/>
			<key>Clause</key>
			<string>MBROverlaps(ST_GeomFromText(&apos;${}&apos;),$CURRENT_FIELD)</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>touches</string>
			<key>NumberOfArguments</key>
			<integer>1</integer>
			<key>SuppressLeadingFieldPlaceholder</key>
			<true/>
			<key>Clause</key>
			<string>MBRTouches(ST_GeomFromText(&apos;${}&apos;),$CURRENT_FIELD)</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>IS NULL</string>
			<key>NumberOfArguments</key>
			<integer>0</integer>
			<key>Clause</key>
			<string>IS NULL</string>
		</dict>
		<dict>
			<key>MenuLabel</key>
			<string>IS NOT NULL</string>
			<key>NumberOfArguments</key>
			<integer>0</integer>
			<key>Clause</key>
			<string>IS NOT NULL</string>
		</dict>
	</array>
</dict>
</plist>
