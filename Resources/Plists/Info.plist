<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>ATSApplicationFontsPath</key>
	<string>Fonts</string>
	<key>AppCenterApplicationForwarderEnabled</key>
	<false/>
	<key>CFBundleDevelopmentRegion</key>
	<string>English</string>
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>spf</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string></string>
			<key>CFBundleTypeIconSystemGenerated</key>
			<integer>1</integer>
			<key>CFBundleTypeName</key>
			<string>Sequel Ace Connection</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>spf</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.sequelpro.SequelPro.spf</string>
				<string>com.sequel-ace.sequel-ace.spf</string>
			</array>
			<key>NSDocumentClass</key>
			<string>SPDocumentController</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>spfs</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string></string>
			<key>CFBundleTypeIconSystemGenerated</key>
			<integer>1</integer>
			<key>CFBundleTypeName</key>
			<string>Sequel Ace Session</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>spfs</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.sequelpro.SequelPro.spfs</string>
				<string>com.sequel-ace.sequel-ace.spfs</string>
			</array>
			<key>LSTypeIsPackage</key>
			<true/>
			<key>NSDocumentClass</key>
			<string>SPDocumentController</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>saBundle</string>
				<string>sabundle</string>
				<string>spBundle</string>
				<string>spbundle</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string></string>
			<key>CFBundleTypeIconSystemGenerated</key>
			<integer>1</integer>
			<key>CFBundleTypeName</key>
			<string>Sequel Ace Bundle</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>spbn</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.sequel-ace.sequel-ace.sabundle</string>
				<string>com.sequel-ace.sequel-ace.spbundle</string>
			</array>
			<key>LSTypeIsPackage</key>
			<true/>
			<key>NSDocumentClass</key>
			<string>SPDocumentController</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>spTheme</string>
				<string>sptheme</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string></string>
			<key>CFBundleTypeIconSystemGenerated</key>
			<integer>1</integer>
			<key>CFBundleTypeName</key>
			<string>Sequel Ace Color Theme</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>spct</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>com.sequel-ace.sequel-ace.spTheme</string>
				<string>com.google.code.sequel-pro.spTheme</string>
			</array>
			<key>NSDocumentClass</key>
			<string>SPDocumentController</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>sql</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string></string>
			<key>CFBundleTypeIconSystemGenerated</key>
			<integer>1</integer>
			<key>CFBundleTypeName</key>
			<string>SQL File</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>sqlt</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Default</string>
			<key>NSDocumentClass</key>
			<string>SPDocumentController</string>
		</dict>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>mysql</string>
			</array>
			<key>CFBundleTypeIconFile</key>
			<string></string>
			<key>CFBundleTypeIconSystemGenerated</key>
			<integer>1</integer>
			<key>CFBundleTypeName</key>
			<string>MySQL File</string>
			<key>CFBundleTypeOSTypes</key>
			<array>
				<string>msql</string>
			</array>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Default</string>
			<key>LSTypeIsPackage</key>
			<false/>
			<key>NSDocumentClass</key>
			<string>SPDocumentController</string>
			<key>NSPersistentStoreTypeKey</key>
			<string>Binary</string>
		</dict>
	</array>
	<key>CFBundleExecutable</key>
	<string>${EXECUTABLE_NAME}</string>
	<key>CFBundleHelpBookFolder</key>
	<string>english_help</string>
	<key>CFBundleHelpBookName</key>
	<string>Sequel Ace Help</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>${PRODUCT_NAME}</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>5.0.9</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>Sequel Ace URL scheme</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>sequelace</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleURLName</key>
			<string>MySQL URL scheme</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>mysql</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>20095</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.developer-tools</string>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsArbitraryLoadsInWebContent</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>appcenter.ms</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSIncludeSubdomains</key>
				<true/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
			</dict>
			<key>github.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.3</string>
				<key>NSIncludeSubdomains</key>
				<true/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
			</dict>
			<key>mysql.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.2</string>
				<key>NSIncludeSubdomains</key>
				<true/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
			</dict>
			<key>sequel-ace.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.3</string>
				<key>NSIncludeSubdomains</key>
				<true/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
			</dict>
			<key>www.apple.com</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.3</string>
				<key>NSIncludeSubdomains</key>
				<true/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
			</dict>
			<key>www.w3.org</key>
			<dict>
				<key>NSExceptionAllowsInsecureHTTPLoads</key>
				<false/>
				<key>NSExceptionMinimumTLSVersion</key>
				<string>TLSv1.3</string>
				<key>NSIncludeSubdomains</key>
				<true/>
				<key>NSRequiresCertificateTransparency</key>
				<false/>
			</dict>
		</dict>
	</dict>
	<key>NSAppleScriptEnabled</key>
	<true/>
	<key>NSHumanReadableCopyright</key>
	<string>Copyright 2020 Moballo, LLC</string>
	<key>NSMainNibFile</key>
	<string>MainMenu</string>
	<key>NSPrincipalClass</key>
	<string>ReportExceptionApplication</string>
	<key>NSServices</key>
	<array>
		<dict>
			<key>NSKeyEquivalent</key>
			<dict>
				<key>default</key>
				<string>M</string>
			</dict>
			<key>NSMenuItem</key>
			<dict>
				<key>default</key>
				<string>Sequel Ace/Perform selection as MySQL query</string>
			</dict>
			<key>NSMessage</key>
			<string>doPerformQueryService</string>
			<key>NSPortName</key>
			<string>Sequel Ace</string>
			<key>NSSendTypes</key>
			<array>
				<string>NSPasteboardTypeString</string>
				<string>NSURLPboardType</string>
			</array>
		</dict>
	</array>
	<key>NSSupportsAutomaticGraphicsSwitching</key>
	<true/>
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.plain-text</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Sequel Ace Connection</string>
			<key>UTTypeIconFile</key>
			<string></string>
			<key>UTTypeIcons</key>
			<dict>
				<key>UTTypeIconBackgroundName</key>
				<string></string>
				<key>UTTypeIconBadgeName</key>
				<string>Document_icon</string>
				<key>UTTypeIconText</key>
				<string>SPF</string>
			</dict>
			<key>UTTypeIdentifier</key>
			<string>com.sequel-ace.sequel-ace.spf</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>spf</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>com.apple.package</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Sequel Ace Session</string>
			<key>UTTypeIconFile</key>
			<string></string>
			<key>UTTypeIcons</key>
			<dict>
				<key>UTTypeIconBadgeName</key>
				<string>Document_icon</string>
				<key>UTTypeIconText</key>
				<string>SPFS</string>
			</dict>
			<key>UTTypeIdentifier</key>
			<string>com.sequel-ace.sequel-ace.spfs</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>spfs</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>com.apple.package</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Sequel Ace Bundle</string>
			<key>UTTypeIconFile</key>
			<string></string>
			<key>UTTypeIcons</key>
			<dict>
				<key>UTTypeIconBackgroundName</key>
				<string></string>
				<key>UTTypeIconBadgeName</key>
				<string>Document_icon</string>
				<key>UTTypeIconText</key>
				<string>SPBUNDLE</string>
			</dict>
			<key>UTTypeIdentifier</key>
			<string>com.sequel-ace.sequel-ace.sabundle</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>saBundle</string>
					<string>sabundle</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>com.apple.package</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Sequel Ace Bundle</string>
			<key>UTTypeIconFile</key>
			<string></string>
			<key>UTTypeIcons</key>
			<dict>
				<key>UTTypeIconBackgroundName</key>
				<string></string>
				<key>UTTypeIconBadgeName</key>
				<string>Document_icon</string>
				<key>UTTypeIconText</key>
				<string>SPBUNDLE</string>
			</dict>
			<key>UTTypeIdentifier</key>
			<string>com.sequel-ace.sequel-ace.spbundle</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>spbundle</string>
					<string>spBundle</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.plain-text</string>
			</array>
			<key>UTTypeDescription</key>
			<string>SQL File</string>
			<key>UTTypeIconFile</key>
			<string></string>
			<key>UTTypeIcons</key>
			<dict>
				<key>UTTypeIconBadgeName</key>
				<string>Document_icon</string>
				<key>UTTypeIconText</key>
				<string>SQL</string>
			</dict>
			<key>UTTypeIdentifier</key>
			<string>com.sequel-ace.sequel-ace.sql</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>sql</string>
				</array>
			</dict>
		</dict>
		<dict>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.plain-text</string>
			</array>
			<key>UTTypeDescription</key>
			<string>Sequel Ace Color Theme</string>
			<key>UTTypeIconFile</key>
			<string></string>
			<key>UTTypeIcons</key>
			<dict>
				<key>UTTypeIconBadgeName</key>
				<string>Document_icon</string>
				<key>UTTypeIconText</key>
				<string>SPTHEME</string>
			</dict>
			<key>UTTypeIdentifier</key>
			<string>com.sequel-ace.sequel-ace.spTheme</string>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>spTheme</string>
					<string>sptheme</string>
				</array>
			</dict>
		</dict>
	</array>
</dict>
</plist>
