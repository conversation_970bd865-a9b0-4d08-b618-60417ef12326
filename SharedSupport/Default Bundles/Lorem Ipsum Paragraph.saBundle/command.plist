<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>author</key>
	<string><PERSON></string>
	<key>category</key>
	<string>Lorem Ipsum</string>
	<key>command</key>
	<string>#!/usr/bin/ruby

print "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed vel mi lacus. Sed vitae lacus et mauris vulputate auctor. Nulla facilisi. Phasellus ac lacus mi, cursus dapibus odio. Suspendisse sem justo, elementum ut interdum ut, auctor in velit. Donec id purus id urna vestibulum mollis. Nunc pellentesque sapien et lorem fermentum lacinia. Vivamus erat nisl, auctor mollis pretium id, dapibus et ipsum. Nunc convallis sodales massa, vitae tincidunt elit accumsan in. Duis sit amet lorem nunc, vel viverra eros. Integer scelerisque gravida quam ut venenatis. Etiam sit amet purus metus, quis rhoncus libero."</string>
	<key>contact</key>
	<string><EMAIL></string>
	<key>description</key>
	<string>Generate a Lorem Ipsum paragraph of  92 words and 611 characters, best suited for TEXT type.</string>
	<key>internalKeyEquivalent</key>
	<dict>
		<key>characters</key>
		<string>P</string>
		<key>keyCode</key>
		<integer>35</integer>
		<key>modifierFlags</key>
		<integer>393216</integer>
	</dict>
	<key>keyEquivalent</key>
	<string>^$P</string>
	<key>name</key>
	<string>Paragraph</string>
	<key>output</key>
	<string>insertastext</string>
	<key>scope</key>
	<string>inputfield</string>
	<key>uuid</key>
	<string>C73F7EA3-4CDC-4F3F-B9EE-FEED7E3F0B71</string>
	<key>isDefaultBundle</key>
	<true/>
	<key>bundleVersion</key>
	<integer>2</integer>
</dict>
</plist>
