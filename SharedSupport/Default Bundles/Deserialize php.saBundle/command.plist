<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>author</key>
	<string><PERSON></string>
	<key>category</key>
	<string>Format</string>
	<key>command</key>
	<string>#!/usr/bin/php

&lt;pre&gt;
&lt;?php
function goFormatVarExport($string) {
	return preg_replace(array(
		'/=&gt;\s+array\(/im',
		'/array\(\s+\)/im',
	), array(
		'=&gt; array(',
		'array()',
	), str_replace('array (', 'array(', $string));
}

print_r(goFormatVarExport(var_export(unserialize(fgets(STDIN)), TRUE)));
?&gt;
&lt;/pre&gt;</string>
	<key>contact</key>
	<string><EMAIL></string>
	<key>description</key>
	<string>Deserializes those PHP objects that drupal loves so much.</string>
	<key>input</key>
	<string>selectedtext</string>
	<key>internalKeyEquivalent</key>
	<dict>
		<key>characters</key>
		<string>D</string>
		<key>keyCode</key>
		<integer>2</integer>
		<key>modifierFlags</key>
		<integer>1179648</integer>
	</dict>
	<key>keyEquivalent</key>
	<string>$@D</string>
	<key>name</key>
	<string>Deserialize PHP</string>
	<key>output</key>
	<string>showashtml</string>
	<key>scope</key>
	<string>inputfield</string>
	<key>uuid</key>
	<string>4E0126ED-3AC6-4BB5-B836-5F9718FE36D5</string>
	<key>isDefaultBundle</key>
	<true/>
	<key>bundleVersion</key>
	<integer>2</integer>
</dict>
</plist>
