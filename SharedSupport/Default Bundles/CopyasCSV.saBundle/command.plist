<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>author</key>
	<string>Liviu Tudor</string>
	<key>category</key>
	<string>Copy</string>
	<key>command</key>
	<string>cat | /usr/bin/perl -e '

# read first line to get the column names (header)
$firstLine = &lt;&gt;;

# bail if nothing could read
if(!defined($firstLine)) {
	exit 0;
}

# store the column names
chomp($firstLine);
$firstLine =~ s/\"/\\\"/g;  # escape "
@header = split(/\t/, $firstLine);

$h_cnt = $#header;     # number of columns

# get the column definitions
open(META, $ENV{"SP_BUNDLE_INPUT_TABLE_METADATA"}) or die $!;
@meta = ();
while(&lt;META&gt;) {
	chomp();
	my @arr = split(/\t/);
	push @meta, \@arr;
}
close(META);

for($i=0; $i&lt;=$h_cnt; $i++) {
	print "\"$header[$i]\"";
	if( $i&lt;$h_cnt) {
		print ";";
	} else {
		print "\n";
	}
}

# read row data of each selected row
$rowData=&lt;&gt;;
while($rowData) {

	# remove line ending
	chomp($rowData);

	# escape "
	$rowData=~s/\"/\\\"/g;

	# split column data which are tab-delimited
	@data = split(/\t/, $rowData);
	for($i=0; $i&lt;=$h_cnt; $i++) {
		# re-escape \t and \n
		$cellData = $data[$i];
		$cellData =~ s/↵/\n/g;
		$cellData =~ s/⇥/\t/g;

		# check for data types
		if($cellData eq "NULL") {
			print "NULL";
		} else {
			chomp($cellData);
			print "\"$cellData\"";
		}
		if($i&lt;$h_cnt) {
			print ";";
		} else {
			print "\n";
		}
	}

	# get next row
	$rowData=&lt;&gt;;
}

print "\n";
' | __CF_USER_TEXT_ENCODING=$UID:0x8000100:0x8000100 pbcopy</string>
	<key>contact</key>
	<string><EMAIL></string>
	<key>description</key>
	<string>Copies the selected rows to clipboard as CSV separating data by semi-colon</string>
	<key>input</key>
	<string>selectedtablerowsastab</string>
	<key>keyEquivalent</key>
	<string>^c</string>
	<key>name</key>
	<string>Copy as CSV</string>
	<key>output</key>
	<string>none</string>
	<key>scope</key>
	<string>datatable</string>
	<key>tooltip</key>
	<string>Copy as CSV</string>
	<key>trigger</key>
	<string>none</string>
	<key>uuid</key>
	<string>E24C4537-0AAB-4C17-B5F9-C5054CADA77C</string>
	<key>isDefaultBundle</key>
	<true/>
	<key>bundleVersion</key>
	<integer>2</integer>
</dict>
</plist>
