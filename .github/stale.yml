# Configuration for probot-stale - https://github.com/probot/stale

# Number of days of inactivity before an Issue or Pull Request becomes stale
daysUntilStale: 58

# Number of days of inactivity before a stale Issue or Pull Request is closed
daysUntilClose: 7

# Issues or Pull Requests with these labels will never be considered stale
exemptLabels:
  - "Feature Request"
  - "bug"
  - "good first issue"
  - "help wanted"
  - "Planned Release"
  - "Fixed & Pending Release"
  - "work in progress"

# Label to use when marking as stale
staleLabel: stale

# Comment to post when marking as stale. Set to `false` to disable
markComment: >
  This issue has been marked as stale because it has not had
  recent activity. It will be closed if no further activity occurs. 

# Comment to post when removing the stale label. Set to `false` to disable
unmarkComment: false

# Comment to post when closing a stale Issue or Pull Request. Set to `false` to disable
closeComment: >
  This issue has been auto-closed because there hasn't been any activity for at least 35 days.
  However, we really appreciate your contribution, so thank you for that! 🙏
  Also, feel free to [open a new issue](https://github.com/Sequel-Ace/Sequel-Ace/issues/new) if you still experience this problem 👍.

# Limit to only `issues`
only: issues
