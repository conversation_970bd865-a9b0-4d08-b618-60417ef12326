---
name: Bug report
about: Create a report to help us improve

---

<!-- Please search existing issues to avoid creating duplicates. -->
<!-- Please fill out ALL ITEMS in the bug report. We simply can't fix bugs if we don't have all the information -->

- Sequel Ace Version (including build number):
- Sequel Ace Source (App Store/GitHub/Homebrew):
- macOS Version:
- Processor Type (Intel/Apple):
- MySQL Version:
- macOS Localization:

**Is Issue Present in [Latest Beta](https://github.com/Sequel-Ace/Sequel-Ace/releases)?**
<!-- Please try the [latest beta](https://github.com/Sequel-Ace/Sequel-Ace/releases). If the latest beta works, we've fixed the issue already and will get the fix out soon. There's no need to open a new issue for a bug already fixed in the latest beta. -->

**Description**
A clear and concise description of what the bug is.

**Steps To Reproduce**
1.
2.

**Expected Behaviour**
<!-- A clear and concise description of what you expected to happen. -->

**Related Issues**
<!-- Please search [Issues on GitHub](https://github.com/Sequel-Ace/Sequel-Ace/issues?q=is%3Aissue), including Closed Issues, for the bug. If the bug has been fixed and is pending release, there may already be a closed issue for it. If an existing report of your issue already exists, please don't open a new issue and refer to the existing issue. -->

**Additional Context**
<!-- Add any other context about the problem here. -->
<!-- Please ATTACH any SCREENSHOTS AND CRASH LOGS that might help investigating and addressing the issue. -->
<!-- Please please please attach crash logs (as zip files if needed) and don't paste the content of the crash itself, it's much easier for us to work with files than massive text blocks -->
