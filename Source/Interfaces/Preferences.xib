<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="22690"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPPreferenceController">
            <connections>
                <outlet property="appearanceMacOSVersionLabel" destination="A54-fI-5jD" id="Nlw-9j-e2K"/>
                <outlet property="appearancePopUp" destination="YpU-KB-oKj" id="FIo-db-OfV"/>
                <outlet property="editorPreferencePane" destination="2144" id="2148"/>
                <outlet property="filePreferencePane" destination="bKg-6I-v9A" id="wt8-L0-QxT"/>
                <outlet property="generalPreferencePane" destination="2074" id="2076"/>
                <outlet property="networkPreferencePane" destination="2146" id="2150"/>
                <outlet property="notificationsPreferencePane" destination="2143" id="2147"/>
                <outlet property="tablesPreferencePane" destination="2080" id="2082"/>
                <outlet property="window" destination="1" id="610"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <customObject id="2074" userLabel="General" customClass="SPGeneralPreferencePane">
            <connections>
                <outlet property="defaultFavoritePopup" destination="569" id="2079"/>
                <outlet property="globalResultFontName" destination="yhD-8W-lrG" id="TZ4-ty-cQa"/>
                <outlet property="view" destination="17" id="2075"/>
            </connections>
        </customObject>
        <customObject id="2080" userLabel="Tables" customClass="SPTablesPreferencePane">
            <connections>
                <outlet property="view" destination="512" id="2081"/>
            </connections>
        </customObject>
        <customObject id="2143" userLabel="Notifications" customClass="SPNotificationsPreferencePane">
            <connections>
                <outlet property="updateAvailableButton" destination="nmG-Wb-i0O" id="fqz-MO-Gum"/>
                <outlet property="view" destination="56" id="2151"/>
            </connections>
        </customObject>
        <customObject id="2144" userLabel="Editor" customClass="SPEditorPreferencePane">
            <connections>
                <outlet property="colorSettingTableView" destination="1685" id="2171"/>
                <outlet property="colorThemeName" destination="1740" id="2161"/>
                <outlet property="colorThemeNameLabel" destination="1738" id="2160"/>
                <outlet property="duplicateThemeButton" destination="2253" id="2261"/>
                <outlet property="editThemeListTable" destination="1774" id="2168"/>
                <outlet property="editThemeListWindow" destination="1756" id="2158"/>
                <outlet property="editorFontName" destination="1037" id="2159"/>
                <outlet property="enterNameAlertField" destination="1824" id="2156"/>
                <outlet property="enterNameInputField" destination="1722" id="2164"/>
                <outlet property="enterNameLabel" destination="1721" id="2165"/>
                <outlet property="enterNameWindow" destination="1716" id="2157"/>
                <outlet property="removeThemeButton" destination="2255" id="2265"/>
                <outlet property="themeNameSaveButton" destination="1718" id="2163"/>
                <outlet property="themeSelectionMenu" destination="1710" id="2162"/>
                <outlet property="view" destination="802" id="2152"/>
            </connections>
        </customObject>
        <customObject id="2146" userLabel="Network" customClass="SPNetworkPreferencePane">
            <connections>
                <outlet property="hiddenFileView" destination="GEn-6y-U3F" id="r1S-eS-uJK"/>
                <outlet property="knownHostsChooser" destination="l1q-C8-ytL" id="V6x-e1-Io4"/>
                <outlet property="sshClientPath" destination="yQV-9I-7us" id="eUn-5b-NBa"/>
                <outlet property="sshClientPickerView" destination="D6L-76-0Ik" id="gUn-jl-Tfb"/>
                <outlet property="sshConfigChooser" destination="P6m-dR-d6a" id="zw4-Lr-8KG"/>
                <outlet property="sslCipherView" destination="RuH-Yq-cdH" id="pF5-VG-sY0"/>
                <outlet property="view" destination="641" id="2154"/>
            </connections>
        </customObject>
        <customObject id="bKg-6I-v9A" userLabel="File" customClass="SPFilePreferencePane">
            <connections>
                <outlet property="fileView" destination="eH2-Ed-Xh3" id="4ea-J5-z5n"/>
                <outlet property="hiddenFileView" destination="GEn-6y-U3F" id="YAa-9u-hpT"/>
                <outlet property="revokeButton" destination="lHn-Wy-DTi" id="n10-yF-Qzc"/>
                <outlet property="staleLabel" destination="C8B-BP-39F" id="4xj-uj-EZc"/>
                <outlet property="view" destination="5dZ-tO-Ldn" id="VI1-LP-ygg"/>
            </connections>
        </customObject>
        <userDefaultsController representsSharedInstance="YES" id="117"/>
        <window title="Preferences" allowsToolTipsWhenApplicationIsInactive="NO" visibleAtLaunch="NO" animationBehavior="default" toolbarStyle="preference" id="1" userLabel="PreferencesPanel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowCollectionBehavior key="collectionBehavior" fullScreenAuxiliary="YES"/>
            <rect key="contentRect" x="430" y="486" width="700" height="100"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1920" height="1055"/>
            <value key="minSize" type="size" width="540" height="100"/>
            <value key="maxSize" type="size" width="1500" height="700"/>
            <value key="minFullScreenContentSize" type="size" width="540" height="100"/>
            <value key="maxFullScreenContentSize" type="size" width="1500" height="700"/>
            <view key="contentView" id="2">
                <rect key="frame" x="0.0" y="0.0" width="700" height="100"/>
                <autoresizingMask key="autoresizingMask"/>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="599"/>
            </connections>
            <point key="canvasLocation" x="701" y="-372"/>
        </window>
        <window allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="1716" userLabel="Enter Name Sheet">
            <windowStyleMask key="styleMask" titled="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="343" y="481" width="227" height="114"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1920" height="1055"/>
            <value key="minSize" type="size" width="227" height="114"/>
            <value key="maxSize" type="size" width="247" height="124"/>
            <view key="contentView" id="1717">
                <rect key="frame" x="0.0" y="0.0" width="227" height="114"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1824">
                        <rect key="frame" x="18" y="45" width="191" height="11"/>
                        <textFieldCell key="cell" controlSize="mini" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="left" title="Already exists and will be overwritten!" id="1825">
                            <font key="font" metaFont="label" size="9"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                        <connections>
                            <binding destination="117" name="fontBold" keyPath="values._dummy" id="1834">
                                <dictionary key="options">
                                    <integer key="NSMultipleValuesPlaceholder" value="1"/>
                                    <integer key="NSNoSelectionPlaceholder" value="1"/>
                                    <integer key="NSNotApplicablePlaceholder" value="1"/>
                                    <integer key="NSNullPlaceholder" value="1"/>
                                </dictionary>
                            </binding>
                        </connections>
                    </textField>
                    <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1722">
                        <rect key="frame" x="20" y="60" width="187" height="22"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="22" id="UHl-Kd-pAa"/>
                        </constraints>
                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" alignment="left" drawsBackground="YES" usesSingleLineMode="YES" id="1725">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                        <connections>
                            <outlet property="delegate" destination="2144" id="2181"/>
                        </connections>
                    </textField>
                    <button tag="1" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1718">
                        <rect key="frame" x="112" y="13" width="100" height="28"/>
                        <buttonCell key="cell" type="push" title="Save" bezelStyle="rounded" alignment="center" controlSize="small" enabled="NO" borderStyle="border" tag="1" inset="2" id="1730">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closePanelSheet:" target="2144" id="2180"/>
                            <outlet property="nextKeyView" destination="1722" id="1734"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1723">
                        <rect key="frame" x="15" y="13" width="99" height="28"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" inset="2" id="1724">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <constraints>
                            <constraint firstAttribute="width" constant="87" id="bxt-Mj-yRM"/>
                            <constraint firstAttribute="height" constant="17" id="fx7-P6-vKc"/>
                        </constraints>
                        <connections>
                            <action selector="closePanelSheet:" target="2144" id="2178"/>
                            <outlet property="nextKeyView" destination="1718" id="1733"/>
                        </connections>
                    </button>
                    <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1721">
                        <rect key="frame" x="18" y="90" width="191" height="14"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="left" title="Name:" id="1726">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                </subviews>
                <constraints>
                    <constraint firstItem="1718" firstAttribute="leading" secondItem="1723" secondAttribute="trailing" constant="10" id="9Zy-2A-NpC"/>
                    <constraint firstItem="1723" firstAttribute="firstBaseline" secondItem="1718" secondAttribute="firstBaseline" id="FeW-m1-sNn"/>
                    <constraint firstItem="1723" firstAttribute="leading" secondItem="1717" secondAttribute="leading" constant="21" id="H2t-U0-dl8"/>
                    <constraint firstItem="1721" firstAttribute="centerX" secondItem="1722" secondAttribute="centerX" id="U7w-zQ-n5L"/>
                    <constraint firstItem="1722" firstAttribute="leading" secondItem="1824" secondAttribute="leading" id="XUP-jg-XJQ"/>
                    <constraint firstItem="1722" firstAttribute="trailing" secondItem="1824" secondAttribute="trailing" id="ZDb-Wd-lCD"/>
                    <constraint firstItem="1723" firstAttribute="baseline" secondItem="1718" secondAttribute="baseline" id="eCf-VZ-u9B"/>
                    <constraint firstAttribute="trailing" secondItem="1721" secondAttribute="trailing" constant="20" symbolic="YES" id="mEj-WO-HJ4"/>
                    <constraint firstAttribute="trailing" secondItem="1718" secondAttribute="trailing" constant="21" id="nha-YY-skB"/>
                    <constraint firstItem="1722" firstAttribute="top" secondItem="1721" secondAttribute="bottom" constant="8" symbolic="YES" id="ojm-6P-roo"/>
                    <constraint firstAttribute="bottom" secondItem="1723" secondAttribute="bottom" constant="20" symbolic="YES" id="snE-AD-SOJ"/>
                    <constraint firstItem="1723" firstAttribute="top" secondItem="1824" secondAttribute="bottom" constant="8" symbolic="YES" id="uhZ-MM-6gm"/>
                    <constraint firstItem="1721" firstAttribute="trailing" secondItem="1722" secondAttribute="trailing" id="y5i-Rq-sPo"/>
                    <constraint firstItem="1824" firstAttribute="top" secondItem="1722" secondAttribute="bottom" constant="4" id="yf7-CF-8hp"/>
                    <constraint firstItem="1722" firstAttribute="leading" secondItem="1717" secondAttribute="leading" constant="20" symbolic="YES" id="z0H-vA-r7t"/>
                </constraints>
            </view>
            <point key="canvasLocation" x="41" y="-147"/>
        </window>
        <window allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="1756" userLabel="Edit Theme List Sheet">
            <windowStyleMask key="styleMask" titled="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="343" y="370" width="264" height="234"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1920" height="1055"/>
            <value key="minSize" type="size" width="264" height="225"/>
            <value key="maxSize" type="size" width="264" height="274"/>
            <view key="contentView" id="1757">
                <rect key="frame" x="0.0" y="0.0" width="264" height="234"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="19" horizontalPageScroll="10" verticalLineScroll="19" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1771">
                        <rect key="frame" x="0.0" y="81" width="265" height="154"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <clipView key="contentView" id="eRr-LP-c79">
                            <rect key="frame" x="1" y="1" width="263" height="152"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <subviews>
                                <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" alternatingRowBackgroundColors="YES" columnReordering="NO" columnResizing="NO" multipleSelection="NO" emptySelection="NO" autosaveColumns="NO" id="1774">
                                    <rect key="frame" x="0.0" y="0.0" width="263" height="152"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <size key="intercellSpacing" width="3" height="2"/>
                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                    <tableColumns>
                                        <tableColumn width="223" minWidth="40" maxWidth="1000" id="1776">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="1779">
                                                <font key="font" metaFont="system"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                    </tableColumns>
                                    <connections>
                                        <outlet property="dataSource" destination="2144" id="2169"/>
                                        <outlet property="delegate" destination="2144" id="2170"/>
                                    </connections>
                                </tableView>
                            </subviews>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="1773">
                            <rect key="frame" x="-100" y="-100" width="223" height="15"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="1772">
                            <rect key="frame" x="224" y="17" width="15" height="102"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <connections>
                            <outlet property="nextKeyView" destination="2253" id="2262"/>
                        </connections>
                    </scrollView>
                    <box autoresizesSubviews="NO" fixedFrame="YES" boxType="custom" borderType="none" title="Box" translatesAutoresizingMaskIntoConstraints="NO" id="2252">
                        <rect key="frame" x="0.0" y="50" width="265" height="25"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                        <view key="contentView" id="Meu-6x-HwH">
                            <rect key="frame" x="0.0" y="0.0" width="265" height="25"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <button toolTip="Remove Theme" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="2255">
                                    <rect key="frame" x="30" y="0.0" width="27.5" height="25"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                    <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSRemoveTemplate" imagePosition="only" alignment="center" alternateImage="NSRemoveTemplate" inset="2" id="2256">
                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                        <font key="font" metaFont="system"/>
                                        <string key="keyEquivalent"></string>
                                    </buttonCell>
                                    <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                    <connections>
                                        <action selector="removeTheme:" target="2144" id="2264"/>
                                        <outlet property="nextKeyView" destination="1759" id="2263"/>
                                    </connections>
                                </button>
                                <button toolTip="Duplicate Theme" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="2253">
                                    <rect key="frame" x="0.0" y="1" width="26.5" height="25"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                    <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSAddTemplate" imagePosition="only" alignment="center" alternateImage="NSAddTemplate" inset="2" id="2258">
                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                        <font key="font" metaFont="system"/>
                                        <string key="keyEquivalent">d</string>
                                        <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                    </buttonCell>
                                    <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                    <connections>
                                        <action selector="duplicateTheme:" target="2144" id="2260"/>
                                        <outlet property="nextKeyView" destination="2255" id="2259"/>
                                    </connections>
                                </button>
                            </subviews>
                        </view>
                    </box>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1759">
                        <rect key="frame" x="161" y="12" width="87" height="29"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" widthSizable="YES"/>
                        <buttonCell key="cell" type="push" title="Close" bezelStyle="rounded" alignment="center" borderStyle="border" inset="2" id="1764">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closePanelSheet:" target="2144" id="2184"/>
                            <outlet property="nextKeyView" destination="1774" id="1823"/>
                        </connections>
                    </button>
                </subviews>
            </view>
            <connections>
                <outlet property="initialFirstResponder" destination="1774" id="1819"/>
            </connections>
            <point key="canvasLocation" x="-364" y="159"/>
        </window>
        <customView translatesAutoresizingMaskIntoConstraints="NO" id="17" userLabel="General">
            <rect key="frame" x="0.0" y="0.0" width="540" height="489"/>
            <subviews>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="XRZ-IQ-R00">
                    <rect key="frame" x="178" y="451" width="342" height="24"/>
                    <buttonCell key="cell" type="check" title="Prompt for confirmation before quitting" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="pLU-wO-HXa">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="MKj-fE-uDE"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ApplicationPromptOnQuit" id="iYk-v3-PGi"/>
                    </connections>
                </button>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="FJw-vU-VgL">
                    <rect key="frame" x="180" y="440" width="340" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="o4y-7V-ZEP"/>
                    </constraints>
                </box>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="1420">
                    <rect key="frame" x="180" y="368" width="340" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="jHx-SA-a0F"/>
                    </constraints>
                </box>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1419">
                    <rect key="frame" x="18" y="340" width="154" height="20"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="150" id="8zj-eb-GHj"/>
                        <constraint firstAttribute="height" constant="20" id="GHh-gf-gj6"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Default View:" id="1421">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1418">
                    <rect key="frame" x="177" y="335" width="347" height="27"/>
                    <popUpButtonCell key="cell" type="push" title="Last Used" bezelStyle="rounded" alignment="left" lineBreakMode="clipping" state="on" borderStyle="borderAndBezel" inset="2" arrowPosition="arrowAtCenter" preferredEdge="maxY" selectedItem="1449" id="1422">
                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="menu"/>
                        <menu key="menu" title="OtherViews" id="1423">
                            <items>
                                <menuItem title="Last Used" state="on" id="1449">
                                    <modifierMask key="keyEquivalentModifierMask"/>
                                </menuItem>
                                <menuItem isSeparatorItem="YES" id="1450"/>
                                <menuItem title="Structure" tag="1" id="1451">
                                    <modifierMask key="keyEquivalentModifierMask"/>
                                </menuItem>
                                <menuItem title="Content" tag="2" id="1452">
                                    <modifierMask key="keyEquivalentModifierMask"/>
                                </menuItem>
                                <menuItem title="Relations" tag="3" id="1453">
                                    <modifierMask key="keyEquivalentModifierMask"/>
                                </menuItem>
                                <menuItem title="Table Info" tag="4" id="1455">
                                    <modifierMask key="keyEquivalentModifierMask"/>
                                </menuItem>
                                <menuItem title="Query Editor" tag="5" id="1454">
                                    <modifierMask key="keyEquivalentModifierMask"/>
                                </menuItem>
                                <menuItem title="Triggers" tag="6" id="1546">
                                    <modifierMask key="keyEquivalentModifierMask"/>
                                </menuItem>
                            </items>
                        </menu>
                    </popUpButtonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="GTX-3c-yQF"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="selectedTag" keyPath="values.DefaultViewMode" id="1464"/>
                    </connections>
                </popUpButton>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="582">
                    <rect key="frame" x="180" y="286" width="340" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="epJ-gi-vWC"/>
                    </constraints>
                </box>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="581">
                    <rect key="frame" x="180" y="327" width="340" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="Lfj-js-9td"/>
                    </constraints>
                </box>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="947">
                    <rect key="frame" x="178" y="232" width="342" height="24"/>
                    <buttonCell key="cell" type="check" title="Display comments in tables list" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="948">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="D5k-pg-HI7"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.DisplayCommentsInTablesList" id="962"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="hMm-gH-Xw4">
                    <rect key="frame" x="178" y="208" width="342" height="24"/>
                    <buttonCell key="cell" type="check" title="Save query history individually" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="3B6-Jd-2pW">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="hzI-1g-IJw"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.CustomQuerySaveHistoryIndividually" id="W9v-ez-5Wj"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="957">
                    <rect key="frame" x="178" y="256" width="342" height="24"/>
                    <buttonCell key="cell" type="check" title="Display vertical grid lines" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="958">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="GzU-Bz-1Le"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.DisplayTableViewVerticalGridlines" id="961"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="bHb-uY-JgS">
                    <rect key="frame" x="178" y="185" width="342" height="24"/>
                    <buttonCell key="cell" type="check" title="Display column types on content view" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="Z7f-0u-a0e">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="SEg-Kq-YYH"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.DisplayTableViewColumnTypes" id="Hbs-3n-vUt"/>
                    </connections>
                </button>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="577">
                    <rect key="frame" x="18" y="299" width="154" height="20"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="2fJ-uV-yWE"/>
                        <constraint firstAttribute="width" constant="150" id="H3R-fZ-7pu"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Default Encoding:" id="578">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1465">
                    <rect key="frame" x="18" y="258" width="154" height="20"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="150" id="Pth-OX-OBR"/>
                        <constraint firstAttribute="height" constant="20" id="jzh-o6-wrC"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Table Views:" id="1466">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="569">
                    <rect key="frame" x="177" y="407" width="347" height="27"/>
                    <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" inset="2" autoenablesItems="NO" id="570">
                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="menu"/>
                        <menu key="menu" title="OtherViews" autoenablesItems="NO" id="571"/>
                    </popUpButtonCell>
                    <constraints>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="100" id="c8F-90-8rt"/>
                        <constraint firstAttribute="height" constant="22" id="qGo-nM-5Lp"/>
                    </constraints>
                    <connections>
                        <action selector="updateDefaultFavorite:" target="2074" id="2077"/>
                    </connections>
                </popUpButton>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="568">
                    <rect key="frame" x="18" y="412" width="154" height="20"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="150" id="iwU-u3-PUC"/>
                        <constraint firstAttribute="height" constant="20" id="y3H-yc-8cM"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Default Favorite:" id="575">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="567">
                    <rect key="frame" x="178" y="379" width="342" height="24"/>
                    <buttonCell key="cell" type="check" title="Connect to default on startup" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="576">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="OPe-tH-ihg"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.AutoConnectToDefault" id="629"/>
                    </connections>
                </button>
                <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="24" userLabel="Default Encoding Dropdown">
                    <rect key="frame" x="177" y="294" width="347" height="27"/>
                    <popUpButtonCell key="cell" type="push" title="Autodetect" bezelStyle="rounded" alignment="left" lineBreakMode="clipping" state="on" borderStyle="borderAndBezel" inset="2" arrowPosition="arrowAtCenter" preferredEdge="maxY" selectedItem="50" id="25">
                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="menu"/>
                        <menu key="menu" title="OtherViews" id="26">
                            <items>
                                <menuItem title="Autodetect" state="on" id="50"/>
                                <menuItem isSeparatorItem="YES" tag="-1" id="49">
                                    <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                </menuItem>
                                <menuItem title="UCS-2 Unicode (ucs2)" tag="10" id="33"/>
                                <menuItem title="UTF-8 Unicode BMP (utf8)" tag="20" id="34"/>
                                <menuItem title="UTF-8 Full Unicode (utf8mb4)" tag="190" id="7W6-DM-PG1"/>
                                <menuItem isSeparatorItem="YES" tag="-1" id="38">
                                    <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                </menuItem>
                                <menuItem title="US ASCII (ascii)" tag="40" id="40"/>
                                <menuItem title="ISO Latin 1 (latin1)" tag="50" id="42"/>
                                <menuItem title="Mac Roman (macroman)" tag="60" id="45"/>
                                <menuItem isSeparatorItem="YES" tag="-1" id="35">
                                    <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                </menuItem>
                                <menuItem title="Windows Latin 2 (cp1250)" tag="70" id="36"/>
                                <menuItem title="ISO Latin 2 (latin2)" tag="80" id="37"/>
                                <menuItem isSeparatorItem="YES" tag="-1" id="44">
                                    <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                </menuItem>
                                <menuItem title="Windows Arabic (cp1256)" tag="90" id="31"/>
                                <menuItem title="ISO Greek (greek)" tag="100" id="32"/>
                                <menuItem title="ISO Hebrew (hebrew)" tag="110" id="28"/>
                                <menuItem title="ISO Turkish (latin5)" tag="120" id="41"/>
                                <menuItem isSeparatorItem="YES" tag="-1" id="48">
                                    <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                </menuItem>
                                <menuItem title="Windows Baltic (cp1257)" tag="130" id="39"/>
                                <menuItem isSeparatorItem="YES" tag="-1" id="29">
                                    <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                </menuItem>
                                <menuItem title="Windows Cyrillic (cp1251)" tag="140" id="43"/>
                                <menuItem isSeparatorItem="YES" tag="-1" id="27">
                                    <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                </menuItem>
                                <menuItem title="Big5 Traditional Chinese (big5)" tag="150" id="30"/>
                                <menuItem title="Shift-JIS Japanese (sjis)" tag="160" id="46"/>
                                <menuItem title="EUC-JP Japanese (ujis)" tag="170" id="47"/>
                                <menuItem title="EUC-KR Korean (euckr)" tag="180" id="1385">
                                    <modifierMask key="keyEquivalentModifierMask"/>
                                </menuItem>
                            </items>
                        </menu>
                    </popUpButtonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="zsc-aw-Grt"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="selectedTag" keyPath="values.DefaultEncodingTag" id="1635">
                            <dictionary key="options">
                                <bool key="NSAllowsEditingMultipleValuesSelection" value="NO"/>
                            </dictionary>
                        </binding>
                    </connections>
                </popUpButton>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="784">
                    <rect key="frame" x="180" y="176" width="340" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="vus-7q-stB"/>
                    </constraints>
                </box>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="790">
                    <rect key="frame" x="398" y="154" width="124" height="14"/>
                    <constraints>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="120" id="nGd-yZ-I7g"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="queries (100 max)" id="791">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField toolTip="Enter the maximum number of entries to remember in the Custom Query query history, from 0 to 99" focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="787">
                    <rect key="frame" x="180" y="150" width="215" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="1Oz-rT-AdX"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" alignment="left" title="20" drawsBackground="YES" usesSingleLineMode="YES" id="788">
                        <numberFormatter key="formatter" formatterBehavior="custom10_4" allowsFloats="NO" usesGroupingSeparator="NO" groupingSize="0" minimumIntegerDigits="0" maximumIntegerDigits="3" id="792">
                            <nil key="negativeInfinitySymbol"/>
                            <nil key="positiveInfinitySymbol"/>
                            <real key="minimum" value="0.0"/>
                            <real key="maximum" value="100"/>
                        </numberFormatter>
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.CustomQueryMaxHistoryItems" id="800"/>
                    </connections>
                </textField>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="785">
                    <rect key="frame" x="18" y="150" width="154" height="20"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="150" id="iNE-8S-1ug"/>
                        <constraint firstAttribute="height" constant="20" id="m9i-Kw-gLk"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Remember Last:" id="786">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <box boxType="custom" borderType="none" cornerRadius="4" title="Box" titlePosition="noTitle" translatesAutoresizingMaskIntoConstraints="NO" id="7J9-Yp-qMZ">
                    <rect key="frame" x="0.0" y="101" width="540" height="41"/>
                    <view key="contentView" id="8QO-1r-5jB">
                        <rect key="frame" x="0.0" y="0.0" width="540" height="41"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="m2t-AW-wZp">
                                <rect key="frame" x="180" y="38" width="340" height="5"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="1" id="w2r-U9-xzl"/>
                                </constraints>
                            </box>
                            <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="zSn-Pq-u9O">
                                <rect key="frame" x="18" y="10" width="154" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="KTX-VG-c9A"/>
                                    <constraint firstAttribute="width" constant="150" id="NuT-p2-IHy"/>
                                </constraints>
                                <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Appearance:" id="tAt-dD-bAQ">
                                    <font key="font" metaFont="system"/>
                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                </textFieldCell>
                            </textField>
                            <textField hidden="YES" focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="A54-fI-5jD">
                                <rect key="frame" x="398" y="12" width="124" height="16"/>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="120" id="oJR-dz-ggb"/>
                                </constraints>
                                <textFieldCell key="cell" lineBreakMode="clipping" title="macOS 10.14+" id="GI3-PC-8Vn">
                                    <font key="font" metaFont="system"/>
                                    <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                </textFieldCell>
                            </textField>
                            <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="YpU-KB-oKj">
                                <rect key="frame" x="177" y="5" width="222" height="27"/>
                                <popUpButtonCell key="cell" type="push" title="Light" bezelStyle="rounded" alignment="left" lineBreakMode="clipping" enabled="NO" state="on" borderStyle="border" tag="1" inset="2" arrowPosition="arrowAtCenter" preferredEdge="maxY" selectedItem="eKV-qw-xZ9" id="qEs-4C-bPo">
                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                    <font key="font" metaFont="menu"/>
                                    <menu key="menu" title="OtherViews" id="kke-hm-U3J">
                                        <items>
                                            <menuItem title="Light" state="on" tag="1" id="eKV-qw-xZ9">
                                                <modifierMask key="keyEquivalentModifierMask"/>
                                            </menuItem>
                                            <menuItem title="Dark" tag="2" id="fo9-Ml-cF0">
                                                <modifierMask key="keyEquivalentModifierMask"/>
                                            </menuItem>
                                            <menuItem title="Auto" id="dTB-Qx-h4w">
                                                <modifierMask key="keyEquivalentModifierMask"/>
                                            </menuItem>
                                        </items>
                                    </menu>
                                </popUpButtonCell>
                                <constraints>
                                    <constraint firstAttribute="height" constant="22" id="mxo-GC-50A"/>
                                </constraints>
                                <connections>
                                    <binding destination="117" name="selectedTag" keyPath="values.Appearance" id="ETY-Mb-qdJ">
                                        <dictionary key="options">
                                            <bool key="NSAllowsEditingMultipleValuesSelection" value="NO"/>
                                        </dictionary>
                                    </binding>
                                </connections>
                            </popUpButton>
                        </subviews>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="m2t-AW-wZp" secondAttribute="trailing" constant="20" id="5va-08-JI4"/>
                            <constraint firstItem="YpU-KB-oKj" firstAttribute="leading" secondItem="m2t-AW-wZp" secondAttribute="leading" id="6li-My-U6Q"/>
                            <constraint firstItem="A54-fI-5jD" firstAttribute="leading" secondItem="YpU-KB-oKj" secondAttribute="trailing" constant="5" id="ES9-bt-d8F"/>
                            <constraint firstItem="YpU-KB-oKj" firstAttribute="centerY" secondItem="zSn-Pq-u9O" secondAttribute="centerY" id="NhH-JG-F7e"/>
                            <constraint firstItem="A54-fI-5jD" firstAttribute="centerY" secondItem="zSn-Pq-u9O" secondAttribute="centerY" id="RSy-6K-GNB"/>
                            <constraint firstItem="m2t-AW-wZp" firstAttribute="top" secondItem="8QO-1r-5jB" secondAttribute="top" id="cjR-xY-u0O"/>
                            <constraint firstItem="YpU-KB-oKj" firstAttribute="leading" secondItem="zSn-Pq-u9O" secondAttribute="trailing" constant="10" id="jbv-tQ-cWP"/>
                            <constraint firstAttribute="bottom" secondItem="YpU-KB-oKj" secondAttribute="bottom" constant="9" id="k6U-6l-mIv"/>
                            <constraint firstItem="YpU-KB-oKj" firstAttribute="top" secondItem="m2t-AW-wZp" secondAttribute="bottom" constant="9" id="qvF-s5-WKM"/>
                            <constraint firstAttribute="trailing" secondItem="A54-fI-5jD" secondAttribute="trailing" constant="20" id="wZU-Cc-BI5"/>
                            <constraint firstItem="zSn-Pq-u9O" firstAttribute="leading" secondItem="8QO-1r-5jB" secondAttribute="leading" constant="20" id="wsp-nB-hpQ"/>
                        </constraints>
                    </view>
                </box>
                <box boxType="custom" borderType="none" cornerRadius="4" title="Box" titlePosition="noTitle" translatesAutoresizingMaskIntoConstraints="NO" id="F3U-SQ-W2d">
                    <rect key="frame" x="0.0" y="70" width="540" height="31"/>
                    <view key="contentView" id="typ-rs-Tmp">
                        <rect key="frame" x="0.0" y="0.0" width="540" height="31"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="zo3-G0-Lj6">
                                <rect key="frame" x="180" y="28" width="340" height="5"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="1" id="esL-SH-GKA"/>
                                </constraints>
                            </box>
                            <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="lsf-qy-07D">
                                <rect key="frame" x="18" y="0.0" width="154" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="1mZ-E2-f6l"/>
                                    <constraint firstAttribute="width" constant="150" id="Wic-EO-nDe"/>
                                </constraints>
                                <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Font:" id="zlZ-Z2-Dhp">
                                    <font key="font" metaFont="system"/>
                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                </textFieldCell>
                            </textField>
                            <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="yhD-8W-lrG" customClass="SPFontPreviewTextField">
                                <rect key="frame" x="180" y="-1" width="215" height="22"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="22" id="DcB-XL-DMt"/>
                                </constraints>
                                <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" id="cY2-s3-8OD">
                                    <font key="font" metaFont="system"/>
                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                </textFieldCell>
                            </textField>
                            <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="zBm-kj-adR">
                                <rect key="frame" x="393" y="-7" width="134" height="32"/>
                                <buttonCell key="cell" type="push" title="Change…" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="V62-2t-uwh">
                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                    <font key="font" metaFont="system"/>
                                </buttonCell>
                                <constraints>
                                    <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="120" id="I6V-7f-rdZ"/>
                                    <constraint firstAttribute="height" constant="20" id="d3f-DE-oUX"/>
                                </constraints>
                                <connections>
                                    <action selector="showGlobalResultFontPanel:" target="2074" id="kbT-Z1-jUN"/>
                                </connections>
                            </button>
                        </subviews>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="zBm-kj-adR" secondAttribute="trailing" constant="20" id="BH4-9V-9OU"/>
                            <constraint firstItem="zBm-kj-adR" firstAttribute="centerY" secondItem="lsf-qy-07D" secondAttribute="centerY" id="Kpt-iW-R3s"/>
                            <constraint firstItem="yhD-8W-lrG" firstAttribute="leading" secondItem="zo3-G0-Lj6" secondAttribute="leading" id="QIm-KO-shv"/>
                            <constraint firstItem="zBm-kj-adR" firstAttribute="leading" secondItem="yhD-8W-lrG" secondAttribute="trailing" constant="5" id="VdS-XF-ama"/>
                            <constraint firstItem="yhD-8W-lrG" firstAttribute="top" secondItem="zo3-G0-Lj6" secondAttribute="bottom" constant="9" id="VoI-wv-LCe"/>
                            <constraint firstItem="lsf-qy-07D" firstAttribute="leading" secondItem="typ-rs-Tmp" secondAttribute="leading" constant="20" id="X7b-sv-ZtT"/>
                            <constraint firstItem="yhD-8W-lrG" firstAttribute="centerY" secondItem="lsf-qy-07D" secondAttribute="centerY" id="j3q-mR-FbZ"/>
                            <constraint firstItem="zo3-G0-Lj6" firstAttribute="top" secondItem="typ-rs-Tmp" secondAttribute="top" id="j8K-Vy-xA6"/>
                            <constraint firstItem="yhD-8W-lrG" firstAttribute="leading" secondItem="lsf-qy-07D" secondAttribute="trailing" constant="10" id="rDw-N8-wjH"/>
                            <constraint firstAttribute="trailing" secondItem="zo3-G0-Lj6" secondAttribute="trailing" constant="20" id="sem-qe-8r6"/>
                            <constraint firstAttribute="bottom" secondItem="zBm-kj-adR" secondAttribute="bottom" id="ste-5b-NEm"/>
                        </constraints>
                    </view>
                </box>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="gcQ-a7-Kqd">
                    <rect key="frame" x="18" y="29" width="154" height="20"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="150" id="UXx-vH-pTm"/>
                        <constraint firstAttribute="height" constant="20" id="cZz-2P-Xzw"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Analytics" id="6mk-ml-lFe">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="2aR-JM-Lbr">
                    <rect key="frame" x="178" y="27" width="342" height="24"/>
                    <buttonCell key="cell" type="check" title="Share anonymized usage analytics with developers" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="mZ1-oa-LuT">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="f9S-3k-CY3"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.SaveApplicationUsageAnalytics" id="PGy-Vc-fm7"/>
                    </connections>
                </button>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="XAX-zM-z61">
                    <rect key="frame" x="180" y="57" width="340" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="GuS-Im-MmS"/>
                    </constraints>
                </box>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="NBD-nG-VYi">
                    <rect key="frame" x="18" y="453" width="154" height="20"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="150" id="I93-gu-1Cg"/>
                        <constraint firstAttribute="height" constant="20" id="Iqw-5P-eDM"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Application:" id="4Ci-Qa-l0l">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
            </subviews>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="569" secondAttribute="trailing" constant="20" id="07m-Wu-Wdm"/>
                <constraint firstItem="hMm-gH-Xw4" firstAttribute="trailing" secondItem="569" secondAttribute="trailing" id="0rT-dJ-Sum"/>
                <constraint firstItem="bHb-uY-JgS" firstAttribute="centerY" secondItem="784" secondAttribute="centerY" constant="-18.5" id="1CY-fX-02V"/>
                <constraint firstItem="2aR-JM-Lbr" firstAttribute="leading" secondItem="569" secondAttribute="leading" id="1Lj-aM-mDB"/>
                <constraint firstItem="XAX-zM-z61" firstAttribute="trailing" secondItem="784" secondAttribute="trailing" id="2xg-Eg-bq1"/>
                <constraint firstItem="FJw-vU-VgL" firstAttribute="top" secondItem="XRZ-IQ-R00" secondAttribute="bottom" constant="9" id="5pR-lk-i3p"/>
                <constraint firstItem="1418" firstAttribute="top" secondItem="1420" secondAttribute="bottom" constant="9" id="6hc-io-AZE"/>
                <constraint firstItem="582" firstAttribute="leading" secondItem="569" secondAttribute="leading" id="8xS-ld-NA8"/>
                <constraint firstAttribute="trailing" secondItem="F3U-SQ-W2d" secondAttribute="trailing" id="93x-P2-EBJ"/>
                <constraint firstItem="787" firstAttribute="top" secondItem="784" secondAttribute="bottom" constant="6" id="9Mn-N1-aSY"/>
                <constraint firstItem="957" firstAttribute="centerY" secondItem="1465" secondAttribute="centerY" id="9lb-a9-Siy"/>
                <constraint firstItem="790" firstAttribute="trailing" secondItem="569" secondAttribute="trailing" id="9vT-H7-QwT"/>
                <constraint firstItem="567" firstAttribute="trailing" secondItem="569" secondAttribute="trailing" id="AIy-lZ-vhT"/>
                <constraint firstAttribute="trailing" secondItem="7J9-Yp-qMZ" secondAttribute="trailing" id="Av7-od-lr4"/>
                <constraint firstItem="787" firstAttribute="leading" secondItem="569" secondAttribute="leading" id="Bbt-nF-FyG"/>
                <constraint firstItem="gcQ-a7-Kqd" firstAttribute="leading" secondItem="17" secondAttribute="leading" constant="20" id="Bfj-MK-Pq1"/>
                <constraint firstItem="7J9-Yp-qMZ" firstAttribute="leading" secondItem="17" secondAttribute="leading" id="ES6-K4-QTe"/>
                <constraint firstItem="F3U-SQ-W2d" firstAttribute="top" secondItem="7J9-Yp-qMZ" secondAttribute="bottom" id="G6O-Ql-CaV"/>
                <constraint firstItem="567" firstAttribute="leading" secondItem="569" secondAttribute="leading" id="GpK-3f-RXE"/>
                <constraint firstItem="790" firstAttribute="top" secondItem="bHb-uY-JgS" secondAttribute="bottom" constant="18" id="GxQ-uz-O2B"/>
                <constraint firstItem="1418" firstAttribute="trailing" secondItem="569" secondAttribute="trailing" id="H5T-g6-PU5"/>
                <constraint firstItem="947" firstAttribute="top" secondItem="957" secondAttribute="bottom" constant="2" id="HC4-0f-RPy"/>
                <constraint firstItem="24" firstAttribute="top" secondItem="581" secondAttribute="bottom" constant="9" id="HLY-rd-SfS"/>
                <constraint firstItem="947" firstAttribute="trailing" secondItem="569" secondAttribute="trailing" id="IYp-aC-TVp"/>
                <constraint firstItem="F3U-SQ-W2d" firstAttribute="leading" secondItem="17" secondAttribute="leading" id="Im0-9V-68O"/>
                <constraint firstItem="784" firstAttribute="trailing" secondItem="569" secondAttribute="trailing" id="JVt-FZ-AWU"/>
                <constraint firstItem="785" firstAttribute="leading" secondItem="17" secondAttribute="leading" constant="20" id="JuX-m1-yDO"/>
                <constraint firstItem="947" firstAttribute="leading" secondItem="569" secondAttribute="leading" id="KeG-OR-BDE"/>
                <constraint firstItem="XAX-zM-z61" firstAttribute="leading" secondItem="784" secondAttribute="leading" id="LGi-Au-NT4"/>
                <constraint firstItem="hMm-gH-Xw4" firstAttribute="top" secondItem="947" secondAttribute="bottom" constant="2" id="Liq-UM-y1F"/>
                <constraint firstItem="569" firstAttribute="top" secondItem="FJw-vU-VgL" secondAttribute="bottom" constant="9" id="MDZ-Ip-zCe"/>
                <constraint firstItem="568" firstAttribute="leading" secondItem="17" secondAttribute="leading" constant="20" id="N22-K7-HF7"/>
                <constraint firstItem="hMm-gH-Xw4" firstAttribute="leading" secondItem="569" secondAttribute="leading" id="NDk-wb-BH3"/>
                <constraint firstItem="NBD-nG-VYi" firstAttribute="leading" secondItem="17" secondAttribute="leading" constant="20" id="NUW-9z-7y6"/>
                <constraint firstItem="581" firstAttribute="leading" secondItem="569" secondAttribute="leading" id="Ogb-QW-1uw"/>
                <constraint firstItem="957" firstAttribute="trailing" secondItem="569" secondAttribute="trailing" id="Orb-W6-T4J"/>
                <constraint firstItem="957" firstAttribute="top" secondItem="582" secondAttribute="bottom" constant="9" id="PIX-w3-EeM"/>
                <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="F3U-SQ-W2d" secondAttribute="bottom" constant="70" id="PYb-fK-u5u"/>
                <constraint firstItem="577" firstAttribute="leading" secondItem="17" secondAttribute="leading" constant="20" id="QFi-wi-jse"/>
                <constraint firstItem="FJw-vU-VgL" firstAttribute="leading" secondItem="569" secondAttribute="leading" id="Quq-iv-5iD"/>
                <constraint firstItem="7J9-Yp-qMZ" firstAttribute="top" secondItem="787" secondAttribute="bottom" constant="8" id="VDV-pe-DL0"/>
                <constraint firstItem="784" firstAttribute="leading" secondItem="569" secondAttribute="leading" id="VOf-4w-xgO"/>
                <constraint firstItem="bHb-uY-JgS" firstAttribute="leading" secondItem="784" secondAttribute="leading" id="W4N-5b-uqF"/>
                <constraint firstItem="569" firstAttribute="trailing" secondItem="XRZ-IQ-R00" secondAttribute="trailing" id="WCa-as-XxN"/>
                <constraint firstItem="1420" firstAttribute="leading" secondItem="569" secondAttribute="leading" id="WiS-oj-FT3"/>
                <constraint firstItem="2aR-JM-Lbr" firstAttribute="top" secondItem="XAX-zM-z61" secondAttribute="bottom" constant="9" id="XYl-mN-TCg"/>
                <constraint firstItem="957" firstAttribute="leading" secondItem="569" secondAttribute="leading" id="ZaW-If-0qM"/>
                <constraint firstItem="582" firstAttribute="trailing" secondItem="569" secondAttribute="trailing" id="aCS-ZA-klL"/>
                <constraint firstItem="2aR-JM-Lbr" firstAttribute="centerY" secondItem="gcQ-a7-Kqd" secondAttribute="centerY" id="awJ-f0-Gff"/>
                <constraint firstItem="XRZ-IQ-R00" firstAttribute="centerY" secondItem="NBD-nG-VYi" secondAttribute="centerY" id="eAA-eu-122"/>
                <constraint firstItem="569" firstAttribute="leading" secondItem="568" secondAttribute="trailing" constant="10" id="fHu-Lc-of4"/>
                <constraint firstItem="581" firstAttribute="trailing" secondItem="569" secondAttribute="trailing" id="fbn-bh-QjY"/>
                <constraint firstItem="1420" firstAttribute="trailing" secondItem="569" secondAttribute="trailing" id="fih-1c-iuM"/>
                <constraint firstItem="1420" firstAttribute="top" secondItem="567" secondAttribute="bottom" constant="9" id="fkZ-p1-9jM"/>
                <constraint firstItem="XRZ-IQ-R00" firstAttribute="top" secondItem="17" secondAttribute="top" constant="15" id="ftf-aq-hDi"/>
                <constraint firstItem="581" firstAttribute="top" secondItem="1418" secondAttribute="bottom" constant="9" id="g77-uo-NcY"/>
                <constraint firstItem="1418" firstAttribute="centerY" secondItem="1419" secondAttribute="centerY" id="kgZ-ie-BB3"/>
                <constraint firstItem="24" firstAttribute="trailing" secondItem="569" secondAttribute="trailing" id="mAI-R5-gQn"/>
                <constraint firstItem="787" firstAttribute="centerY" secondItem="785" secondAttribute="centerY" constant="-1" id="p04-KA-lJN"/>
                <constraint firstItem="569" firstAttribute="centerY" secondItem="568" secondAttribute="centerY" id="pWk-dV-dVi"/>
                <constraint firstItem="24" firstAttribute="centerY" secondItem="577" secondAttribute="centerY" id="qG8-7Q-fau"/>
                <constraint firstItem="569" firstAttribute="leading" secondItem="XRZ-IQ-R00" secondAttribute="leading" id="qO0-a4-ZOl"/>
                <constraint firstItem="FJw-vU-VgL" firstAttribute="trailing" secondItem="569" secondAttribute="trailing" id="rUU-Mm-xTt"/>
                <constraint firstItem="1418" firstAttribute="leading" secondItem="569" secondAttribute="leading" id="uC9-Cn-qrQ"/>
                <constraint firstItem="bHb-uY-JgS" firstAttribute="trailing" secondItem="784" secondAttribute="trailing" id="uQo-zm-fsC"/>
                <constraint firstItem="790" firstAttribute="leading" secondItem="787" secondAttribute="trailing" constant="5" id="uet-C7-lc2"/>
                <constraint firstItem="1465" firstAttribute="leading" secondItem="17" secondAttribute="leading" constant="20" id="uzH-fb-Utd"/>
                <constraint firstItem="567" firstAttribute="top" secondItem="569" secondAttribute="bottom" constant="9" id="v89-ty-GLV"/>
                <constraint firstItem="790" firstAttribute="centerY" secondItem="787" secondAttribute="centerY" id="wfq-CS-s72"/>
                <constraint firstItem="24" firstAttribute="leading" secondItem="569" secondAttribute="leading" id="x1x-TL-91g"/>
                <constraint firstItem="582" firstAttribute="top" secondItem="24" secondAttribute="bottom" constant="9" id="x21-dW-TG6"/>
                <constraint firstItem="2aR-JM-Lbr" firstAttribute="trailing" secondItem="569" secondAttribute="trailing" id="xnW-28-sTv"/>
                <constraint firstItem="784" firstAttribute="top" secondItem="hMm-gH-Xw4" secondAttribute="bottom" constant="30" id="xsB-vw-oOb"/>
                <constraint firstItem="XAX-zM-z61" firstAttribute="top" secondItem="yhD-8W-lrG" secondAttribute="bottom" constant="9" id="yri-Jh-GCb"/>
                <constraint firstItem="1419" firstAttribute="leading" secondItem="17" secondAttribute="leading" constant="20" id="zZB-QF-61w"/>
            </constraints>
            <point key="canvasLocation" x="-52" y="1082"/>
        </customView>
        <customView translatesAutoresizingMaskIntoConstraints="NO" id="512" userLabel="Tables">
            <rect key="frame" x="0.0" y="0.0" width="540" height="359"/>
            <userGuides>
                <userLayoutGuide location="195" affinity="minX"/>
            </userGuides>
            <subviews>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="950">
                    <rect key="frame" x="178" y="187" width="342" height="24"/>
                    <buttonCell key="cell" type="check" title="New fields should allow NULL values" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="951">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="jKC-sK-N4K"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.NewFieldsAllowNulls" id="953"/>
                    </connections>
                </button>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="554">
                    <rect key="frame" x="180" y="135" width="340" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="9Oq-Ng-kvf"/>
                    </constraints>
                </box>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="553">
                    <rect key="frame" x="180" y="176" width="340" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="C6E-tR-dLf"/>
                    </constraints>
                </box>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="552">
                    <rect key="frame" x="180" y="248" width="340" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="XQn-Hf-WgQ"/>
                    </constraints>
                </box>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="535">
                    <rect key="frame" x="178" y="218" width="342" height="24"/>
                    <buttonCell key="cell" type="check" title="Don't load blob and text fields until needed" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="538">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="250" id="S6J-1c-jeX"/>
                        <constraint firstAttribute="height" constant="22" id="poT-TR-Yy5"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.LoadBlobsAsNeeded" id="614"/>
                    </connections>
                </button>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="532">
                    <rect key="frame" x="180" y="106" width="340" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="br0-Qy-rrh"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="NULL" drawsBackground="YES" usesSingleLineMode="YES" id="533">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.NullValue" id="628"/>
                    </connections>
                </textField>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="531">
                    <rect key="frame" x="18" y="107" width="154" height="20"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="qhE-Ia-yxO"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Show NULL values as:" id="534">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="520">
                    <rect key="frame" x="295" y="147" width="175" height="22"/>
                    <constraints>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="75" id="0em-nN-sPX"/>
                        <constraint firstAttribute="height" constant="22" id="c07-Tp-IM8"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" alignment="right" title="100" placeholderString="100" drawsBackground="YES" usesSingleLineMode="YES" id="521">
                        <numberFormatter key="formatter" formatterBehavior="custom10_4" positiveFormat="#,##1" allowsFloats="NO" lenient="YES" minimumIntegerDigits="1" maximumIntegerDigits="309" id="522">
                            <integer key="roundingIncrement" value="1"/>
                            <nil key="negativeInfinitySymbol"/>
                            <nil key="positiveInfinitySymbol"/>
                            <real key="minimum" value="0.0"/>
                            <real key="maximum" value="100000"/>
                        </numberFormatter>
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="117" name="enabled" keyPath="values.LimitResults" id="626"/>
                        <binding destination="117" name="value" keyPath="values.LimitResultsValue" id="621"/>
                    </connections>
                </textField>
                <stepper horizontalHuggingPriority="750" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="519">
                    <rect key="frame" x="468" y="146" width="17" height="24"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="16" id="4O1-HE-XH8"/>
                        <constraint firstAttribute="width" constant="11" id="BgY-gn-Agc"/>
                    </constraints>
                    <stepperCell key="cell" continuous="YES" alignment="left" minValue="1" maxValue="100000" doubleValue="100" valueWraps="YES" id="523"/>
                    <connections>
                        <action selector="takeIntValueFrom:" target="520" id="530"/>
                        <binding destination="117" name="enabled" keyPath="values.LimitResults" id="625"/>
                        <binding destination="117" name="value" keyPath="values.LimitResultsValue" id="623"/>
                    </connections>
                </stepper>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="518">
                    <rect key="frame" x="178" y="290" width="342" height="24"/>
                    <buttonCell key="cell" type="check" title="Editing a row" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="524">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="dJC-h5-h8k"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ReloadAfterEditingRow" id="617"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="517">
                    <rect key="frame" x="178" y="146" width="112" height="24"/>
                    <buttonCell key="cell" type="check" title="Limit result to:" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="525">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="E5b-gA-NPS"/>
                        <constraint firstAttribute="width" constant="110" id="FbY-Rf-3Kz"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.LimitResults" id="619"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="516">
                    <rect key="frame" x="178" y="321" width="342" height="24"/>
                    <buttonCell key="cell" type="check" title="Adding a row" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="526">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="ekR-ID-Ft2"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ReloadAfterAddingRow" id="618"/>
                    </connections>
                </button>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="515">
                    <rect key="frame" x="483" y="150" width="39" height="16"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="35" id="0fh-Y8-SkD"/>
                    </constraints>
                    <textFieldCell key="cell" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="rows" id="527">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="117" name="enabled" keyPath="values.LimitResults" id="624"/>
                    </connections>
                </textField>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="514">
                    <rect key="frame" x="18" y="323" width="154" height="20"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="atp-2o-EmZ"/>
                        <constraint firstAttribute="width" constant="150" id="uia-kW-GYj"/>
                    </constraints>
                    <textFieldCell key="cell" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Reload table after:" id="528">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="513">
                    <rect key="frame" x="178" y="259" width="342" height="24"/>
                    <buttonCell key="cell" type="check" title="Removing a row" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="529">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="3B8-bR-NuH"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ReloadAfterRemovingRow" id="616"/>
                    </connections>
                </button>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="1467">
                    <rect key="frame" x="180" y="94" width="340" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="cvo-x6-AXc"/>
                    </constraints>
                </box>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1468">
                    <rect key="frame" x="18" y="66" width="154" height="20"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="FXJ-gj-57u"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Table row counts:" id="1469">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1470">
                    <rect key="frame" x="177" y="61" width="347" height="27"/>
                    <popUpButtonCell key="cell" type="push" title="Only use additional queries for small tables" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="1" imageScaling="proportionallyDown" inset="2" selectedItem="1474" id="1471">
                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="menu"/>
                        <menu key="menu" title="OtherViews" id="1472">
                            <items>
                                <menuItem title="Never use additional queries for table row counts" id="1473"/>
                                <menuItem title="Only use additional queries for small tables" state="on" tag="1" id="1474"/>
                                <menuItem title="Always get accurate row counts (slow for InnoDB)" tag="2" id="1475"/>
                            </items>
                        </menu>
                    </popUpButtonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="8Hk-DM-XZ6"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="selectedTag" keyPath="values.TableRowCountQueryLevel" id="1484"/>
                    </connections>
                </popUpButton>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="ykf-r8-q6R">
                    <rect key="frame" x="178" y="39" width="200" height="18"/>
                    <buttonCell key="cell" type="check" title="Intelligently switch at length:" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="xxI-Ka-WSh">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.EditInSheetForLongText" id="4mq-WL-uM1"/>
                    </connections>
                </button>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="vFL-gu-iZ8">
                    <rect key="frame" x="18" y="43" width="154" height="16"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" allowsUndo="NO" sendsActionOnEndEditing="YES" alignment="right" title="Edit inline or popup:" id="59O-pr-57t">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="suA-e3-Xus">
                    <rect key="frame" x="386" y="37" width="134" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="zNt-Ga-qF7"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" alignment="right" drawsBackground="YES" id="7Ds-4r-iZP">
                        <numberFormatter key="formatter" formatterBehavior="default10_4" usesGroupingSeparator="NO" formatWidth="-1" groupingSize="0" minimumIntegerDigits="1" maximumIntegerDigits="42" id="m5a-Nv-oHW"/>
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="117" name="enabled" keyPath="values.EditInSheetForLongText" id="Rxq-3f-Vpo"/>
                        <binding destination="117" name="value" keyPath="values.EditInSheetForLongTextLengthThreshold" id="X5r-l4-CMf"/>
                    </connections>
                </textField>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Fnh-E7-zuv">
                    <rect key="frame" x="178" y="14" width="342" height="18"/>
                    <buttonCell key="cell" type="check" title="Always use popup for multi-line content" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="9qv-rY-7Hz">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.EditInSheetForMultiLineText" id="mbU-Zr-d1A"/>
                    </connections>
                </button>
            </subviews>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="Fnh-E7-zuv" secondAttribute="trailing" constant="20" id="1FZ-17-F1T"/>
                <constraint firstItem="554" firstAttribute="top" secondItem="517" secondAttribute="bottom" constant="9" id="2Qu-5h-UB6"/>
                <constraint firstItem="suA-e3-Xus" firstAttribute="centerY" secondItem="ykf-r8-q6R" secondAttribute="centerY" id="49j-gt-VYq"/>
                <constraint firstAttribute="bottom" secondItem="Fnh-E7-zuv" secondAttribute="bottom" constant="15" id="4Kl-86-osg"/>
                <constraint firstItem="1468" firstAttribute="leading" secondItem="514" secondAttribute="leading" id="5oZ-xu-Ici"/>
                <constraint firstItem="1470" firstAttribute="trailing" secondItem="535" secondAttribute="trailing" id="7QM-tH-vMJ"/>
                <constraint firstItem="520" firstAttribute="centerY" secondItem="517" secondAttribute="centerY" id="A30-fC-QR3"/>
                <constraint firstItem="1470" firstAttribute="centerY" secondItem="1468" secondAttribute="centerY" id="BVX-Dj-FxY"/>
                <constraint firstItem="514" firstAttribute="leading" secondItem="512" secondAttribute="leading" constant="20" id="BqZ-gS-0X3"/>
                <constraint firstItem="ykf-r8-q6R" firstAttribute="top" secondItem="1470" secondAttribute="bottom" constant="9" id="CVv-DN-vX6"/>
                <constraint firstItem="vFL-gu-iZ8" firstAttribute="trailing" secondItem="1468" secondAttribute="trailing" id="DsK-5k-IqI"/>
                <constraint firstItem="vFL-gu-iZ8" firstAttribute="leading" secondItem="1468" secondAttribute="leading" id="EjQ-R3-ZCU"/>
                <constraint firstItem="535" firstAttribute="trailing" secondItem="516" secondAttribute="trailing" id="FBz-f3-0c4"/>
                <constraint firstItem="1467" firstAttribute="leading" secondItem="552" secondAttribute="leading" id="FQi-kJ-Hsh"/>
                <constraint firstItem="1470" firstAttribute="top" secondItem="1467" secondAttribute="bottom" constant="9" id="FUP-RI-Zoa"/>
                <constraint firstItem="Fnh-E7-zuv" firstAttribute="top" secondItem="ykf-r8-q6R" secondAttribute="bottom" constant="9" id="Heg-3b-S7W"/>
                <constraint firstItem="552" firstAttribute="leading" secondItem="516" secondAttribute="leading" id="I4s-wN-vPG"/>
                <constraint firstAttribute="trailing" secondItem="535" secondAttribute="trailing" constant="20" id="KNZ-Ux-lhm"/>
                <constraint firstItem="554" firstAttribute="leading" secondItem="552" secondAttribute="leading" id="LHp-OK-WrB"/>
                <constraint firstItem="535" firstAttribute="leading" secondItem="516" secondAttribute="leading" id="Lvi-2c-V92"/>
                <constraint firstItem="1467" firstAttribute="trailing" secondItem="552" secondAttribute="trailing" id="MW8-hk-nSp"/>
                <constraint firstItem="532" firstAttribute="centerY" secondItem="531" secondAttribute="centerY" id="MaI-gW-JYf"/>
                <constraint firstItem="513" firstAttribute="top" secondItem="518" secondAttribute="bottom" constant="9" id="Mr0-xL-7lL"/>
                <constraint firstItem="519" firstAttribute="centerY" secondItem="517" secondAttribute="centerY" id="Nrc-Oh-JjS"/>
                <constraint firstItem="553" firstAttribute="trailing" secondItem="552" secondAttribute="trailing" id="Pfc-28-NE4"/>
                <constraint firstItem="535" firstAttribute="trailing" secondItem="518" secondAttribute="trailing" id="Prb-2M-s2K"/>
                <constraint firstItem="515" firstAttribute="trailing" secondItem="535" secondAttribute="trailing" id="Q4L-Wc-m65"/>
                <constraint firstItem="517" firstAttribute="top" secondItem="553" secondAttribute="bottom" constant="9" id="QMo-la-sjs"/>
                <constraint firstItem="950" firstAttribute="top" secondItem="535" secondAttribute="bottom" constant="9" id="SLx-mS-ImY"/>
                <constraint firstItem="516" firstAttribute="top" secondItem="512" secondAttribute="top" constant="15" id="T2u-oA-dCw"/>
                <constraint firstItem="1467" firstAttribute="top" secondItem="532" secondAttribute="bottom" constant="9" id="Te7-4g-Xzx"/>
                <constraint firstItem="515" firstAttribute="centerY" secondItem="517" secondAttribute="centerY" id="Vat-H9-p6s"/>
                <constraint firstItem="518" firstAttribute="top" secondItem="516" secondAttribute="bottom" constant="9" id="Ve4-Jv-EkZ"/>
                <constraint firstItem="519" firstAttribute="leading" secondItem="520" secondAttribute="trailing" constant="1" id="Z1R-fz-BJU"/>
                <constraint firstItem="532" firstAttribute="leading" secondItem="516" secondAttribute="leading" id="ZtR-pG-Mgg"/>
                <constraint firstItem="513" firstAttribute="leading" secondItem="516" secondAttribute="leading" id="d79-vW-qhB"/>
                <constraint firstItem="531" firstAttribute="leading" secondItem="514" secondAttribute="leading" id="dSb-nT-KlY"/>
                <constraint firstItem="532" firstAttribute="trailing" secondItem="535" secondAttribute="trailing" id="deG-L2-Slq"/>
                <constraint firstItem="515" firstAttribute="leading" secondItem="519" secondAttribute="trailing" constant="3" id="fqu-hb-Zkb"/>
                <constraint firstItem="vFL-gu-iZ8" firstAttribute="baseline" secondItem="ykf-r8-q6R" secondAttribute="centerY" constant="2" id="g4a-8F-WeI"/>
                <constraint firstItem="535" firstAttribute="trailing" secondItem="552" secondAttribute="trailing" id="gEf-Yt-4oK"/>
                <constraint firstItem="517" firstAttribute="leading" secondItem="516" secondAttribute="leading" id="gbh-Ii-Zzd"/>
                <constraint firstItem="535" firstAttribute="top" secondItem="552" secondAttribute="bottom" constant="9" id="id8-2Z-Cwy"/>
                <constraint firstItem="suA-e3-Xus" firstAttribute="trailing" secondItem="1470" secondAttribute="trailing" id="k0P-6r-dl3"/>
                <constraint firstItem="518" firstAttribute="leading" secondItem="516" secondAttribute="leading" id="kgA-Tk-nbF"/>
                <constraint firstItem="950" firstAttribute="leading" secondItem="516" secondAttribute="leading" id="lri-W5-gQr"/>
                <constraint firstItem="553" firstAttribute="top" secondItem="950" secondAttribute="bottom" constant="9" id="m1c-cx-CDX"/>
                <constraint firstItem="1468" firstAttribute="trailing" secondItem="514" secondAttribute="trailing" id="mDY-Ra-RD2"/>
                <constraint firstItem="520" firstAttribute="leading" secondItem="517" secondAttribute="trailing" constant="5" id="nYT-kd-3cg"/>
                <constraint firstItem="516" firstAttribute="centerY" secondItem="514" secondAttribute="centerY" id="o4s-29-9LM"/>
                <constraint firstItem="950" firstAttribute="trailing" secondItem="535" secondAttribute="trailing" id="ovl-go-Ylb"/>
                <constraint firstItem="532" firstAttribute="top" secondItem="554" secondAttribute="bottom" constant="9" id="pGf-OD-zLB"/>
                <constraint firstItem="553" firstAttribute="leading" secondItem="552" secondAttribute="leading" id="qSC-wN-gPI"/>
                <constraint firstItem="ykf-r8-q6R" firstAttribute="leading" secondItem="vFL-gu-iZ8" secondAttribute="trailing" constant="10" id="qq6-PH-PF4"/>
                <constraint firstItem="Fnh-E7-zuv" firstAttribute="leading" secondItem="vFL-gu-iZ8" secondAttribute="trailing" constant="10" id="s6s-zt-pIh"/>
                <constraint firstItem="1470" firstAttribute="leading" secondItem="516" secondAttribute="leading" id="ssS-7N-zcY"/>
                <constraint firstItem="554" firstAttribute="trailing" secondItem="552" secondAttribute="trailing" id="t7A-JD-g8C"/>
                <constraint firstItem="suA-e3-Xus" firstAttribute="leading" secondItem="ykf-r8-q6R" secondAttribute="trailing" constant="8" symbolic="YES" id="ujC-Bf-p6l"/>
                <constraint firstItem="552" firstAttribute="top" secondItem="513" secondAttribute="bottom" constant="9" id="vwL-4b-XyP"/>
                <constraint firstItem="531" firstAttribute="trailing" secondItem="514" secondAttribute="trailing" id="wRJ-uF-qSn"/>
                <constraint firstItem="516" firstAttribute="leading" secondItem="514" secondAttribute="trailing" constant="10" id="wd5-tH-69R"/>
                <constraint firstItem="535" firstAttribute="trailing" secondItem="513" secondAttribute="trailing" id="zPM-7o-ZJu"/>
            </constraints>
            <point key="canvasLocation" x="-52" y="615.5"/>
        </customView>
        <customView translatesAutoresizingMaskIntoConstraints="NO" id="56" userLabel="Notifications">
            <rect key="frame" x="0.0" y="0.0" width="500" height="344"/>
            <userGuides>
                <userLayoutGuide location="365" affinity="minX"/>
            </userGuides>
            <subviews>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="1154">
                    <rect key="frame" x="48" y="14" width="432" height="24"/>
                    <buttonCell key="cell" type="check" title="Errors" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="1155">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="RiX-eM-1xw"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ConsoleEnableErrorLogging" id="1382"/>
                        <binding destination="117" name="enabled" keyPath="values.ConsoleEnableLogging" id="1160"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="1152">
                    <rect key="frame" x="48" y="76" width="432" height="24"/>
                    <buttonCell key="cell" type="check" title="Custom Query Editor" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="1153">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="lty-eG-CJ7"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ConsoleEnableCustomQueryLogging" id="1381"/>
                        <binding destination="117" name="enabled" keyPath="values.ConsoleEnableLogging" id="1159"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="1150">
                    <rect key="frame" x="48" y="45" width="432" height="24"/>
                    <buttonCell key="cell" type="check" title="Import &amp; Export" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="1151">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="CuD-yl-11v"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ConsoleEnableImportExportLogging" id="1380"/>
                        <binding destination="117" name="enabled" keyPath="values.ConsoleEnableLogging" id="1158"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="1148">
                    <rect key="frame" x="48" y="107" width="432" height="24"/>
                    <buttonCell key="cell" type="check" title="Interface" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="1149">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="wAZ-J7-Kn8"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ConsoleEnableInterfaceLogging" id="1379"/>
                        <binding destination="117" name="enabled" keyPath="values.ConsoleEnableLogging" id="1157"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="1143">
                    <rect key="frame" x="18" y="135" width="462" height="24"/>
                    <buttonCell key="cell" type="check" title="Enable logging for queries" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="1144">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="Exg-tK-o1C"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ConsoleEnableLogging" id="1145"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="345">
                    <rect key="frame" x="18" y="306" width="462" height="24"/>
                    <buttonCell key="cell" type="check" title="Show error when no rows are affected" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="346">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="Lnx-83-Yt5"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ShowNoAffectedRowsError" id="632"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="7O5-LC-aVK">
                    <rect key="frame" x="18" y="275" width="462" height="24"/>
                    <buttonCell key="cell" type="check" title="Show warning before executing a query" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="D4P-Ln-K9P">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="6fj-Ot-Cxl"/>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="200" id="6gV-bD-sZv"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ShowWarningBeforeExecuteQuery" id="vid-6Z-YSC"/>
                    </connections>
                </button>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="1156">
                    <rect key="frame" x="20" y="168" width="460" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="74B-wM-eJc"/>
                    </constraints>
                </box>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="nmG-Wb-i0O">
                    <rect key="frame" x="18" y="213" width="462" height="24"/>
                    <buttonCell key="cell" type="check" title="Show alert when update is available" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="aXv-Nb-zHe">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="sLt-fe-2TR"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ShowUpdateAvailable" id="1oJ-hg-ND4"/>
                    </connections>
                </button>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="wue-rM-fp8">
                    <rect key="frame" x="18" y="182" width="462" height="24"/>
                    <buttonCell key="cell" type="check" title="Show warning when skip-show-database is set to ON" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="ekC-Ch-sc1">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="mXS-o1-Dcv"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ShowWarningSkipShowDatabase" id="0Sk-fz-fz5"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="qGE-Xx-6rd">
                    <rect key="frame" x="18" y="244" width="462" height="24"/>
                    <buttonCell key="cell" type="check" title="Show double check warning when deleting rows" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="sps-wq-L9i">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="200" id="QKM-ze-6EJ"/>
                        <constraint firstAttribute="height" constant="22" id="qIL-wv-okd"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ShowWarningBeforeDeleteQuery" id="pPL-GF-FrJ"/>
                    </connections>
                </button>
            </subviews>
            <constraints>
                <constraint firstItem="qGE-Xx-6rd" firstAttribute="leading" secondItem="7O5-LC-aVK" secondAttribute="leading" id="3BZ-zy-sWn"/>
                <constraint firstItem="qGE-Xx-6rd" firstAttribute="trailing" secondItem="7O5-LC-aVK" secondAttribute="trailing" id="3Zp-aZ-kHF"/>
                <constraint firstItem="7O5-LC-aVK" firstAttribute="top" secondItem="345" secondAttribute="bottom" constant="9" id="4hk-QI-vYg"/>
                <constraint firstItem="1154" firstAttribute="top" secondItem="1150" secondAttribute="bottom" constant="9" id="5xt-fT-Nra"/>
                <constraint firstItem="7O5-LC-aVK" firstAttribute="leading" secondItem="1156" secondAttribute="leading" id="6LL-xO-Ipe"/>
                <constraint firstItem="1156" firstAttribute="top" secondItem="wue-rM-fp8" secondAttribute="bottom" constant="12" id="8y9-U6-0ks"/>
                <constraint firstItem="1148" firstAttribute="width" secondItem="1156" secondAttribute="width" constant="-30" id="AUV-oU-dRf"/>
                <constraint firstItem="345" firstAttribute="top" secondItem="56" secondAttribute="top" constant="15" id="FaQ-vm-fTJ"/>
                <constraint firstItem="nmG-Wb-i0O" firstAttribute="leading" secondItem="1156" secondAttribute="leading" id="Gtd-lM-WPR"/>
                <constraint firstItem="nmG-Wb-i0O" firstAttribute="trailing" secondItem="1156" secondAttribute="trailing" id="K0H-8x-FNf"/>
                <constraint firstItem="1156" firstAttribute="leading" secondItem="56" secondAttribute="leading" constant="20" id="LJG-qx-FKU"/>
                <constraint firstItem="1150" firstAttribute="trailing" secondItem="1156" secondAttribute="trailing" id="Lsc-pn-thj"/>
                <constraint firstItem="1143" firstAttribute="leading" secondItem="1156" secondAttribute="leading" id="MfT-bw-sdu"/>
                <constraint firstItem="1152" firstAttribute="trailing" secondItem="1156" secondAttribute="trailing" id="N41-h7-ADA"/>
                <constraint firstItem="nmG-Wb-i0O" firstAttribute="top" secondItem="qGE-Xx-6rd" secondAttribute="bottom" constant="9" id="NrY-fB-IP0"/>
                <constraint firstItem="wue-rM-fp8" firstAttribute="leading" secondItem="1156" secondAttribute="leading" id="PW6-pm-PGb"/>
                <constraint firstItem="1143" firstAttribute="trailing" secondItem="1156" secondAttribute="trailing" id="Slw-5B-F9E"/>
                <constraint firstAttribute="trailing" secondItem="1156" secondAttribute="trailing" constant="20" id="WYZ-zS-Rpv"/>
                <constraint firstItem="1143" firstAttribute="top" secondItem="1156" secondAttribute="bottom" constant="12" id="Wht-jN-U7j"/>
                <constraint firstItem="1152" firstAttribute="leading" secondItem="1148" secondAttribute="leading" id="ZOt-Le-XIK"/>
                <constraint firstItem="1150" firstAttribute="leading" secondItem="1148" secondAttribute="leading" id="aIy-wz-dZg"/>
                <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="1154" secondAttribute="bottom" constant="15" id="arD-8s-VKF"/>
                <constraint firstItem="1150" firstAttribute="top" secondItem="1152" secondAttribute="bottom" constant="9" id="eTF-EK-Z2n"/>
                <constraint firstItem="1154" firstAttribute="trailing" secondItem="1156" secondAttribute="trailing" id="gDz-zf-ZSa"/>
                <constraint firstItem="1152" firstAttribute="top" secondItem="1148" secondAttribute="bottom" constant="9" id="pKc-k0-Baa"/>
                <constraint firstItem="wue-rM-fp8" firstAttribute="trailing" secondItem="1156" secondAttribute="trailing" id="qwL-ww-iaC"/>
                <constraint firstItem="345" firstAttribute="leading" secondItem="1156" secondAttribute="leading" id="r5i-Dc-ygG"/>
                <constraint firstItem="qGE-Xx-6rd" firstAttribute="top" secondItem="7O5-LC-aVK" secondAttribute="bottom" constant="9" id="rZY-VQ-JlH"/>
                <constraint firstItem="1148" firstAttribute="trailing" secondItem="1156" secondAttribute="trailing" id="tEh-zH-OaF"/>
                <constraint firstItem="345" firstAttribute="trailing" secondItem="1156" secondAttribute="trailing" id="tnb-WX-b20"/>
                <constraint firstItem="1154" firstAttribute="leading" secondItem="1148" secondAttribute="leading" id="ujG-Zl-wft"/>
                <constraint firstItem="7O5-LC-aVK" firstAttribute="trailing" secondItem="1156" secondAttribute="trailing" id="vhW-dH-m4v"/>
                <constraint firstItem="1148" firstAttribute="top" secondItem="1143" secondAttribute="bottom" constant="6" id="wbi-xL-mtz"/>
                <constraint firstItem="wue-rM-fp8" firstAttribute="top" secondItem="nmG-Wb-i0O" secondAttribute="bottom" constant="9" id="x4k-LN-e54"/>
            </constraints>
            <point key="canvasLocation" x="751" y="166.5"/>
        </customView>
        <customView translatesAutoresizingMaskIntoConstraints="NO" id="641" userLabel="Network">
            <rect key="frame" x="0.0" y="-10" width="540" height="410"/>
            <subviews>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="681">
                    <rect key="frame" x="180" y="362" width="340" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="FAC-Fz-417"/>
                    </constraints>
                </box>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="HWX-AV-xGx">
                    <rect key="frame" x="180" y="321" width="340" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="ymN-vI-0Q4"/>
                    </constraints>
                </box>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="676">
                    <rect key="frame" x="460" y="374" width="62" height="20"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="58" id="f3A-8C-BEH"/>
                        <constraint firstAttribute="height" constant="20" id="n7Z-Qs-SGV"/>
                    </constraints>
                    <textFieldCell key="cell" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="seconds" id="677">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField toolTip="Enter how long to attempt to connect or send queries for; set a value of 0 to disable the timeout" focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="674">
                    <rect key="frame" x="180" y="373" width="279" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="xam-24-4m5"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="60" drawsBackground="YES" usesSingleLineMode="YES" id="675">
                        <numberFormatter key="formatter" formatterBehavior="custom10_4" usesGroupingSeparator="NO" groupingSize="0" minimumIntegerDigits="0" maximumIntegerDigits="42" id="789">
                            <nil key="negativeInfinitySymbol"/>
                            <nil key="positiveInfinitySymbol"/>
                            <real key="minimum" value="0.0"/>
                            <real key="maximum" value="10000"/>
                        </numberFormatter>
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.ConnectionTimeoutValue" id="679"/>
                    </connections>
                </textField>
                <button toolTip="Enable to send a MySQL ping to ensure the connection is kept alive if it has not been used for a while" translatesAutoresizingMaskIntoConstraints="NO" id="652">
                    <rect key="frame" x="178" y="332" width="342" height="24"/>
                    <buttonCell key="cell" type="check" title="Keep connections alive" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="661">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="hcU-16-gmv"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.UseKeepAlive" id="680"/>
                    </connections>
                </button>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="642">
                    <rect key="frame" x="18" y="374" width="154" height="20"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="150" id="Of5-7z-bpZ"/>
                        <constraint firstAttribute="height" constant="20" id="wde-4k-ftL"/>
                    </constraints>
                    <textFieldCell key="cell" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Connection Timeout:" id="669">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="9CM-2a-2JU">
                    <rect key="frame" x="18" y="295" width="154" height="20"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="9Wx-yi-soy"/>
                        <constraint firstAttribute="width" constant="150" id="tTC-vV-0K5"/>
                    </constraints>
                    <textFieldCell key="cell" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="SSH Client:" id="4Us-KF-Ysj">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Rsg-4x-YV2">
                    <rect key="frame" x="411" y="295" width="109" height="19"/>
                    <buttonCell key="cell" type="roundRect" title="Change…" bezelStyle="roundedRect" alignment="center" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="MhF-tS-nrE">
                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="cellTitle"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="width" constant="109" id="Gjl-91-TCa"/>
                        <constraint firstAttribute="height" constant="18" id="yDD-xr-H6c"/>
                    </constraints>
                    <connections>
                        <action selector="pickSSHClient:" target="2146" id="zcA-5j-KFq"/>
                    </connections>
                </button>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="GcY-yy-sZn">
                    <rect key="frame" x="178" y="294" width="232" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="qwT-fc-Nfx"/>
                    </constraints>
                    <textFieldCell key="cell" lineBreakMode="truncatingMiddle" sendsActionOnEndEditing="YES" title="/path/to/ssh/binary" id="29D-bG-y15">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.SSHClientPath" id="89d-3h-dgq">
                            <dictionary key="options">
                                <string key="NSNullPlaceholder">System default</string>
                            </dictionary>
                        </binding>
                    </connections>
                </textField>
                <scrollView autohidesScrollers="YES" horizontalLineScroll="19" horizontalPageScroll="10" verticalLineScroll="19" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9lg-Fd-6u7">
                    <rect key="frame" x="180" y="50" width="340" height="160"/>
                    <clipView key="contentView" id="M9x-3Q-tiD">
                        <rect key="frame" x="1" y="1" width="338" height="158"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <subviews>
                            <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" alternatingRowBackgroundColors="YES" columnReordering="NO" columnResizing="NO" autosaveColumns="NO" id="RuH-Yq-cdH">
                                <rect key="frame" x="0.0" y="0.0" width="338" height="158"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <size key="intercellSpacing" width="3" height="2"/>
                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                <tableColumns>
                                    <tableColumn editable="NO" width="297" minWidth="40" maxWidth="1000" id="2w5-RF-2LR">
                                        <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Cipher Suite">
                                            <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                        </tableHeaderCell>
                                        <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="NtR-tj-yZm">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                    </tableColumn>
                                </tableColumns>
                                <connections>
                                    <outlet property="dataSource" destination="2146" id="rQW-7Y-Mrg"/>
                                    <outlet property="delegate" destination="2146" id="ptn-Zs-Gd6"/>
                                </connections>
                            </tableView>
                        </subviews>
                    </clipView>
                    <constraints>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="200" id="8Wc-f4-fRs"/>
                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="160" id="fLw-5T-pxg"/>
                    </constraints>
                    <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="ZqJ-4c-bqV">
                        <rect key="frame" x="1" y="203" width="298" height="16"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </scroller>
                    <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="fcH-lG-egx">
                        <rect key="frame" x="224" y="17" width="15" height="102"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </scroller>
                </scrollView>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" setsMaxLayoutWidthAtFirstLayout="YES" translatesAutoresizingMaskIntoConstraints="NO" id="TG6-Q3-g30">
                    <rect key="frame" x="220" y="5" width="302" height="30"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="30" id="nha-fA-ndl"/>
                    </constraints>
                    <textFieldCell key="cell" sendsActionOnEndEditing="YES" title="Drag the items in the list to reorder their preference and/or disable them." id="Ukm-oW-lSS">
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="Rn3-Bt-QGE">
                    <rect key="frame" x="18" y="266" width="154" height="20"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="9A7-cl-zj5"/>
                        <constraint firstAttribute="width" constant="150" id="fWd-KS-Oqo"/>
                    </constraints>
                    <textFieldCell key="cell" lineBreakMode="clipping" alignment="right" title="SSH Config:" id="Ziz-Vf-7cz">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="3Fg-G1-BWj">
                    <rect key="frame" x="180" y="217" width="340" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="Woc-dz-xx3"/>
                    </constraints>
                </box>
                <button toolTip="Reset the cipher list to the default configuration" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="jJw-LF-gtW">
                    <rect key="frame" x="180" y="16" width="32" height="25"/>
                    <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="resetTemplate" imagePosition="overlaps" alignment="center" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="7JN-Nf-GsD">
                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="width" constant="32" id="eZr-IW-gWT"/>
                        <constraint firstAttribute="height" constant="23" id="pv7-dA-TIJ"/>
                    </constraints>
                    <connections>
                        <action selector="resetCipherList:" target="2146" id="czy-4m-Kuo"/>
                        <binding destination="117" name="enabled" keyPath="values.SSLCipherList" id="8HU-V9-I2q">
                            <dictionary key="options">
                                <string key="NSValueTransformerName">NSIsNotNil</string>
                            </dictionary>
                        </binding>
                    </connections>
                </button>
                <popUpButton tag="1" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="P6m-dR-d6a">
                    <rect key="frame" x="177" y="261" width="347" height="27"/>
                    <popUpButtonCell key="cell" type="push" title="Item 1" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="X7v-6X-VbB" id="KPx-1M-u3e">
                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="menu"/>
                        <menu key="menu" id="X6f-Ih-yPF">
                            <items>
                                <menuItem title="Item 1" state="on" id="X7v-6X-VbB"/>
                                <menuItem title="Item 2" id="iac-DX-oNn"/>
                                <menuItem title="Item 3" id="diE-aL-9zb"/>
                            </items>
                        </menu>
                    </popUpButtonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="jKo-lu-g39"/>
                    </constraints>
                    <connections>
                        <action selector="updateSSHConfig:" target="2146" id="vEn-BP-8c9"/>
                        <binding destination="117" name="selectedTag" keyPath="values.ssh_config" id="NXN-uJ-sTe">
                            <dictionary key="options">
                                <bool key="NSAllowsEditingMultipleValuesSelection" value="NO"/>
                                <string key="NSNullPlaceholder">Sequel Ace default</string>
                            </dictionary>
                        </binding>
                    </connections>
                </popUpButton>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="4Aw-q4-73u">
                    <rect key="frame" x="18" y="190" width="154" height="20"/>
                    <string key="toolTip">The cipher suites are used for SSL/TLS-secured connections and specify which algorithms Sequel Ace will use to encrypt a connection, as well as their preference.
Warning: If Sequel Ace and the server do not have at least one cipher suite in common, you will no longer be able to connect!</string>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="CoN-5P-X6h"/>
                        <constraint firstAttribute="width" constant="150" id="fpe-rE-AMc"/>
                    </constraints>
                    <textFieldCell key="cell" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="SSL Cipher Suites:" id="PDB-0g-8jg">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="5Vs-H5-Nca">
                    <rect key="frame" x="18" y="230" width="154" height="21"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" alignment="right" title="Known Hosts:" id="dyI-8y-FNW">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <popUpButton tag="2" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="l1q-C8-ytL">
                    <rect key="frame" x="177" y="226" width="347" height="27"/>
                    <popUpButtonCell key="cell" type="push" title="Item 1" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="geP-YV-B0g" id="hmJ-F4-sdq">
                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="menu"/>
                        <menu key="menu" id="jFV-QJ-gOA">
                            <items>
                                <menuItem title="Item 1" state="on" id="geP-YV-B0g"/>
                                <menuItem title="Item 2" id="IYc-iy-xas"/>
                                <menuItem title="Item 3" id="vG7-NQ-ak6"/>
                            </items>
                        </menu>
                    </popUpButtonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="tk9-kk-iZA"/>
                    </constraints>
                    <connections>
                        <action selector="updateKnownHostsConfig:" target="2146" id="U2t-D8-vr8"/>
                        <binding destination="117" name="selectedTag" keyPath="values.known_hosts" id="1uC-TG-LLr">
                            <dictionary key="options">
                                <bool key="NSAllowsEditingMultipleValuesSelection" value="NO"/>
                                <string key="NSNullPlaceholder">Sequel Ace default</string>
                            </dictionary>
                        </binding>
                    </connections>
                </popUpButton>
            </subviews>
            <constraints>
                <constraint firstItem="P6m-dR-d6a" firstAttribute="leading" secondItem="674" secondAttribute="leading" id="0bA-qB-3lq"/>
                <constraint firstItem="5Vs-H5-Nca" firstAttribute="bottom" secondItem="l1q-C8-ytL" secondAttribute="bottom" id="0qK-im-Jy7"/>
                <constraint firstItem="HWX-AV-xGx" firstAttribute="top" secondItem="652" secondAttribute="bottom" constant="9" id="1dQ-fg-k25"/>
                <constraint firstItem="HWX-AV-xGx" firstAttribute="leading" secondItem="674" secondAttribute="leading" id="3Hg-YJ-Hn4"/>
                <constraint firstItem="9CM-2a-2JU" firstAttribute="leading" secondItem="641" secondAttribute="leading" constant="20" id="3b9-2b-LqV"/>
                <constraint firstItem="3Fg-G1-BWj" firstAttribute="top" secondItem="l1q-C8-ytL" secondAttribute="bottom" constant="10" id="3hs-ey-VbC"/>
                <constraint firstItem="P6m-dR-d6a" firstAttribute="trailing" secondItem="681" secondAttribute="trailing" id="3lY-KJ-e21"/>
                <constraint firstItem="9lg-Fd-6u7" firstAttribute="leading" secondItem="674" secondAttribute="leading" id="4P6-Go-lyC"/>
                <constraint firstItem="5Vs-H5-Nca" firstAttribute="leading" secondItem="641" secondAttribute="leading" constant="20" id="4oq-4y-wkk"/>
                <constraint firstItem="TG6-Q3-g30" firstAttribute="top" secondItem="9lg-Fd-6u7" secondAttribute="bottom" constant="15" id="5Zj-6C-bW5"/>
                <constraint firstItem="5Vs-H5-Nca" firstAttribute="firstBaseline" secondItem="l1q-C8-ytL" secondAttribute="firstBaseline" id="6bc-BQ-FtG"/>
                <constraint firstItem="652" firstAttribute="top" secondItem="681" secondAttribute="bottom" constant="9" id="88K-qP-zB5"/>
                <constraint firstItem="jJw-LF-gtW" firstAttribute="leading" secondItem="674" secondAttribute="leading" id="9oi-kf-DQM"/>
                <constraint firstItem="TG6-Q3-g30" firstAttribute="trailing" secondItem="681" secondAttribute="trailing" id="A0b-vy-lrC"/>
                <constraint firstItem="TG6-Q3-g30" firstAttribute="top" secondItem="jJw-LF-gtW" secondAttribute="top" constant="5" id="C2G-02-FBf"/>
                <constraint firstItem="Rsg-4x-YV2" firstAttribute="centerY" secondItem="9CM-2a-2JU" secondAttribute="centerY" id="D6v-YG-WzF"/>
                <constraint firstItem="Rsg-4x-YV2" firstAttribute="centerY" secondItem="GcY-yy-sZn" secondAttribute="centerY" id="FDQ-ZG-T5Y"/>
                <constraint firstItem="9lg-Fd-6u7" firstAttribute="top" secondItem="3Fg-G1-BWj" secondAttribute="bottom" constant="9" id="Hmn-IF-E0D"/>
                <constraint firstItem="P6m-dR-d6a" firstAttribute="top" secondItem="Rsg-4x-YV2" secondAttribute="bottom" constant="9" id="Kmf-ZX-Bsb"/>
                <constraint firstItem="9lg-Fd-6u7" firstAttribute="leading" secondItem="4Aw-q4-73u" secondAttribute="trailing" constant="10" id="LwL-GT-zsg"/>
                <constraint firstItem="Rsg-4x-YV2" firstAttribute="top" secondItem="HWX-AV-xGx" secondAttribute="bottom" constant="9" id="NHV-YR-QtW"/>
                <constraint firstItem="674" firstAttribute="top" secondItem="641" secondAttribute="top" constant="15" id="Qd3-0Y-91Z"/>
                <constraint firstAttribute="trailing" secondItem="676" secondAttribute="trailing" constant="20" id="RiB-Ay-Npz"/>
                <constraint firstItem="Rn3-Bt-QGE" firstAttribute="leading" secondItem="641" secondAttribute="leading" constant="20" id="Y6O-wH-JHK"/>
                <constraint firstItem="674" firstAttribute="centerY" secondItem="642" secondAttribute="centerY" id="YED-5n-XeB"/>
                <constraint firstAttribute="bottom" secondItem="TG6-Q3-g30" secondAttribute="bottom" constant="5" id="Zrq-4l-6wh"/>
                <constraint firstItem="P6m-dR-d6a" firstAttribute="centerY" secondItem="Rn3-Bt-QGE" secondAttribute="centerY" id="a6H-bu-Wm2"/>
                <constraint firstItem="642" firstAttribute="leading" secondItem="641" secondAttribute="leading" constant="20" id="acG-4P-0ml"/>
                <constraint firstItem="HWX-AV-xGx" firstAttribute="trailing" secondItem="681" secondAttribute="trailing" id="bzg-j5-66W"/>
                <constraint firstItem="TG6-Q3-g30" firstAttribute="leading" secondItem="jJw-LF-gtW" secondAttribute="trailing" constant="10" id="cKF-Wu-HT7"/>
                <constraint firstItem="676" firstAttribute="centerY" secondItem="674" secondAttribute="centerY" id="cMG-DG-yHJ"/>
                <constraint firstItem="676" firstAttribute="leading" secondItem="674" secondAttribute="trailing" constant="3" id="fvI-KT-Mdv"/>
                <constraint firstItem="Rsg-4x-YV2" firstAttribute="trailing" secondItem="681" secondAttribute="trailing" id="gLC-jU-Cqj"/>
                <constraint firstItem="l1q-C8-ytL" firstAttribute="leading" secondItem="5Vs-H5-Nca" secondAttribute="trailing" constant="10" id="gcj-Ka-D8e"/>
                <constraint firstItem="652" firstAttribute="trailing" secondItem="681" secondAttribute="trailing" id="hdE-As-bYU"/>
                <constraint firstItem="5Vs-H5-Nca" firstAttribute="trailing" secondItem="Rn3-Bt-QGE" secondAttribute="trailing" id="hq5-vD-epK"/>
                <constraint firstItem="4Aw-q4-73u" firstAttribute="leading" secondItem="641" secondAttribute="leading" constant="20" id="kcK-VB-0J5"/>
                <constraint firstAttribute="trailing" secondItem="l1q-C8-ytL" secondAttribute="trailing" constant="20" symbolic="YES" id="mIP-fB-4Co"/>
                <constraint firstItem="5Vs-H5-Nca" firstAttribute="top" secondItem="Rn3-Bt-QGE" secondAttribute="bottom" constant="15" id="o2j-pK-ert"/>
                <constraint firstItem="681" firstAttribute="leading" secondItem="674" secondAttribute="leading" id="ost-3h-0Vc"/>
                <constraint firstItem="GcY-yy-sZn" firstAttribute="leading" secondItem="674" secondAttribute="leading" id="rB2-48-YNf"/>
                <constraint firstItem="9lg-Fd-6u7" firstAttribute="trailing" secondItem="681" secondAttribute="trailing" id="rWV-h3-8i3"/>
                <constraint firstItem="Rsg-4x-YV2" firstAttribute="leading" secondItem="GcY-yy-sZn" secondAttribute="trailing" constant="3" id="rq7-Q1-rMb"/>
                <constraint firstItem="3Fg-G1-BWj" firstAttribute="leading" secondItem="674" secondAttribute="leading" id="s4L-I7-EuB"/>
                <constraint firstItem="652" firstAttribute="leading" secondItem="674" secondAttribute="leading" id="sqe-to-cg4"/>
                <constraint firstItem="674" firstAttribute="leading" secondItem="642" secondAttribute="trailing" constant="10" id="tBg-Vk-7qD"/>
                <constraint firstItem="681" firstAttribute="top" secondItem="676" secondAttribute="bottom" constant="9" id="uON-jl-bz4"/>
                <constraint firstItem="3Fg-G1-BWj" firstAttribute="trailing" secondItem="681" secondAttribute="trailing" id="usr-Wf-mf5"/>
                <constraint firstItem="9lg-Fd-6u7" firstAttribute="top" secondItem="4Aw-q4-73u" secondAttribute="top" id="wo7-6R-jfR"/>
                <constraint firstItem="676" firstAttribute="trailing" secondItem="681" secondAttribute="trailing" id="xPT-Wg-qIe"/>
            </constraints>
            <point key="canvasLocation" x="732" y="551"/>
        </customView>
        <customView translatesAutoresizingMaskIntoConstraints="NO" id="802" userLabel="Editor">
            <rect key="frame" x="0.0" y="0.0" width="540" height="487"/>
            <userGuides>
                <userLayoutGuide location="146" affinity="minX"/>
                <userLayoutGuide location="315" affinity="minX"/>
            </userGuides>
            <subviews>
                <box autoresizesSubviews="NO" verticalHuggingPriority="750" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="1085">
                    <rect key="frame" x="20" y="438" width="500" height="5"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="1" id="mJZ-MM-v9M"/>
                    </constraints>
                </box>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1083">
                    <rect key="frame" x="472" y="256" width="50" height="14"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="46" id="4mD-hS-s3t"/>
                    </constraints>
                    <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" enabled="NO" sendsActionOnEndEditing="YES" title="seconds" id="1084">
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="117" name="enabled" keyPath="values.CustomQueryUpdateAutoHelp" id="1112"/>
                    </connections>
                </textField>
                <stepper horizontalHuggingPriority="750" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1046">
                    <rect key="frame" x="458" y="251" width="15" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="16" id="EEE-Hl-ffx"/>
                        <constraint firstAttribute="width" constant="11" id="teM-Rz-MWp"/>
                    </constraints>
                    <stepperCell key="cell" controlSize="small" continuous="YES" enabled="NO" alignment="left" increment="0.10000000000000001" minValue="0.10000000000000001" maxValue="5" doubleValue="1" id="1047">
                        <font key="font" metaFont="message" size="11"/>
                    </stepperCell>
                    <connections>
                        <action selector="takeFloatValueFrom:" target="1044" id="1114"/>
                        <binding destination="117" name="value" keyPath="values.CustomQueryAutoHelpDelay" id="1100"/>
                        <binding destination="117" name="enabled" keyPath="values.CustomQueryUpdateAutoHelp" id="1110"/>
                    </connections>
                </stepper>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1045">
                    <rect key="frame" x="279" y="247" width="59" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="J1h-3t-Frd"/>
                        <constraint firstAttribute="width" constant="55" id="wmw-DW-vkB"/>
                    </constraints>
                    <textFieldCell key="cell" controlSize="small" lineBreakMode="truncatingTail" enabled="NO" sendsActionOnEndEditing="YES" alignment="right" title="Delay by:" usesSingleLineMode="YES" id="1048">
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="117" name="enabled" keyPath="values.CustomQueryUpdateAutoHelp" id="1108"/>
                    </connections>
                </textField>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1044">
                    <rect key="frame" x="341" y="252" width="118" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="7Gy-54-e6x"/>
                    </constraints>
                    <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" enabled="NO" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" alignment="right" title=".1" drawsBackground="YES" usesSingleLineMode="YES" id="1049">
                        <numberFormatter key="formatter" formatterBehavior="custom10_4" positiveFormat="#.0" alwaysShowsDecimalSeparator="YES" usesGroupingSeparator="NO" groupingSize="0" minimumIntegerDigits="0" maximumIntegerDigits="1" minimumFractionDigits="1" maximumFractionDigits="1" decimalSeparator="." groupingSeparator="" id="1050">
                            <nil key="negativeInfinitySymbol"/>
                            <nil key="positiveInfinitySymbol"/>
                            <real key="minimum" value="0.10000000000000001"/>
                            <real key="maximum" value="5"/>
                        </numberFormatter>
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <action selector="takeFloatValueFrom:" target="1046" id="1113"/>
                        <binding destination="117" name="value" keyPath="values.CustomQueryAutoHelpDelay" id="1104"/>
                        <binding destination="117" name="enabled" keyPath="values.CustomQueryUpdateAutoHelp" id="1109"/>
                    </connections>
                </textField>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="1128">
                    <rect key="frame" x="259" y="224" width="261" height="24"/>
                    <buttonCell key="cell" type="check" title="Highlight current query" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="1129">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="o8a-4g-ciG"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.CustomQueryHighlightCurrentQuery" id="1134"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="1042">
                    <rect key="frame" x="259" y="315" width="261" height="24"/>
                    <buttonCell key="cell" type="check" title="Auto uppercase keywords" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="1052">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="Nm1-1L-fkL"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.CustomQueryAutoUppercaseKeywords" id="1093"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="1041">
                    <rect key="frame" x="259" y="273" width="261" height="35"/>
                    <buttonCell key="cell" type="check" title="Update help while typing" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="1053">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="33" id="LMj-pz-5qi"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.CustomQueryUpdateAutoHelp" id="1107"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="1040">
                    <rect key="frame" x="259" y="346" width="261" height="24"/>
                    <buttonCell key="cell" type="check" title="Auto pair characters" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="1054">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="okU-1B-JWL"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.CustomQueryAutoPairCharacters" id="1096"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="1039">
                    <rect key="frame" x="259" y="408" width="261" height="24"/>
                    <buttonCell key="cell" type="check" title="Indent new lines" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="1055">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="QM4-7n-4Uf"/>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="200" id="YuJ-Px-Th2"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.CustomQueryAutoIndent" id="1098"/>
                    </connections>
                </button>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1037" customClass="SPFontPreviewTextField">
                    <rect key="frame" x="106" y="449" width="200" height="22"/>
                    <textFieldCell key="cell" lineBreakMode="truncatingTail" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" alignment="left" drawsBackground="YES" id="1057">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1036">
                    <rect key="frame" x="309" y="443" width="114" height="32"/>
                    <buttonCell key="cell" type="push" title="Select…" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="1058">
                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="width" constant="100" id="nFC-4I-RVp"/>
                    </constraints>
                    <connections>
                        <action selector="showCustomQueryFontPanel:" target="2144" id="2174"/>
                    </connections>
                </button>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1034">
                    <rect key="frame" x="18" y="453" width="82" height="16"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="78" id="IcF-dQ-kV5"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Font:" id="1060">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <popUpButton toolTip="Manage Color Theme" translatesAutoresizingMaskIntoConstraints="NO" id="1669">
                    <rect key="frame" x="20" y="14" width="24" height="26"/>
                    <popUpButtonCell key="cell" type="smallSquare" bezelStyle="smallSquare" imagePosition="only" alignment="center" controlSize="small" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" pullsDown="YES" arrowPosition="noArrow" autoenablesItems="NO" selectedItem="1672" id="1670">
                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="message" size="11"/>
                        <menu key="menu" title="OtherViews" autoenablesItems="NO" id="1671">
                            <items>
                                <menuItem state="on" image="NSAdvanced" hidden="YES" id="1672"/>
                                <menuItem title="Save Current Theme As…" toolTip="Save the current set color theme by providing an unique name into Sequel Ace's Application Support folder." id="1796">
                                    <modifierMask key="keyEquivalentModifierMask"/>
                                    <connections>
                                        <action selector="saveAsColorScheme:" target="2144" id="2175"/>
                                    </connections>
                                </menuItem>
                                <menuItem isSeparatorItem="YES" id="2215"/>
                                <menuItem title="Load Theme" id="1709">
                                    <modifierMask key="keyEquivalentModifierMask"/>
                                    <menu key="submenu" title="Load Theme" autoenablesItems="NO" id="1710"/>
                                </menuItem>
                                <menuItem isSeparatorItem="YES" id="1712"/>
                                <menuItem title="Import Color Theme…" tag="1" toolTip="Import a theme saved as 'spTheme' or 'tmTheme' (TextMate™) file." id="1674">
                                    <connections>
                                        <action selector="importColorScheme:" target="2144" id="2177"/>
                                    </connections>
                                </menuItem>
                                <menuItem title="Export Current Color Theme…" tag="2" toolTip="Export the current set color theme as an 'spTheme' file to disk." id="1673">
                                    <connections>
                                        <action selector="exportColorScheme:" target="2144" id="2176"/>
                                    </connections>
                                </menuItem>
                            </items>
                        </menu>
                    </popUpButtonCell>
                    <constraints>
                        <constraint firstAttribute="width" constant="24" id="2sk-Sr-ZGg"/>
                        <constraint firstAttribute="height" constant="24" id="5dQ-lD-em1"/>
                    </constraints>
                </popUpButton>
                <scrollView horizontalLineScroll="22" horizontalPageScroll="10" verticalLineScroll="22" verticalPageScroll="10" hasHorizontalScroller="NO" hasVerticalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1682">
                    <rect key="frame" x="20" y="49" width="231" height="382"/>
                    <clipView key="contentView" drawsBackground="NO" copiesOnScroll="NO" id="oEY-Qs-g9y">
                        <rect key="frame" x="1" y="1" width="229" height="380"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="firstColumnOnly" tableStyle="fullWidth" columnReordering="NO" columnResizing="NO" multipleSelection="NO" autosaveColumns="NO" rowHeight="20" id="1685">
                                <rect key="frame" x="0.0" y="0.0" width="229" height="380"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <size key="intercellSpacing" width="3" height="2"/>
                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                <tableColumns>
                                    <tableColumn identifier="name" width="194" minWidth="40" maxWidth="10000" id="1687">
                                        <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left">
                                            <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                        </tableHeaderCell>
                                        <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" allowsUndo="NO" alignment="left" title="Text Cell" allowsEditingTextAttributes="YES" id="1690">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                    </tableColumn>
                                    <tableColumn identifier="color" editable="NO" width="20" minWidth="20" maxWidth="20" id="1688">
                                        <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left">
                                            <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                        </tableHeaderCell>
                                        <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="1689">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                    </tableColumn>
                                </tableColumns>
                                <connections>
                                    <outlet property="dataSource" destination="2144" id="2172"/>
                                    <outlet property="delegate" destination="2144" id="2173"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <nil key="backgroundColor"/>
                    </clipView>
                    <constraints>
                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="200" id="Fuh-SQ-jA3"/>
                    </constraints>
                    <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="1684">
                        <rect key="frame" x="-100" y="-100" width="223" height="15"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </scroller>
                    <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="1683">
                        <rect key="frame" x="-100" y="-100" width="15" height="102"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </scroller>
                </scrollView>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1738">
                    <rect key="frame" x="47" y="20" width="84" height="14"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="80" id="wPl-hr-CIG"/>
                    </constraints>
                    <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Color Theme:" id="1739">
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField toolTip="The name of the current set color theme" focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1740">
                    <rect key="frame" x="132" y="20" width="121" height="14"/>
                    <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="theme name" id="1741">
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.CustomQueryEditorThemeName" id="1751">
                            <dictionary key="options">
                                <string key="NSMultipleValuesPlaceholder">User-defined</string>
                                <string key="NSNoSelectionPlaceholder">User-defined</string>
                                <string key="NSNotApplicablePlaceholder">User-defined</string>
                                <string key="NSNullPlaceholder">User-defined</string>
                            </dictionary>
                        </binding>
                    </connections>
                </textField>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="2185">
                    <rect key="frame" x="259" y="377" width="97" height="24"/>
                    <buttonCell key="cell" type="check" title="Soft indent:" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="2186">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="OaX-My-oPP"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.CustomQuerySoftIndent" id="2200"/>
                    </connections>
                </button>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="2188">
                    <rect key="frame" x="361" y="378" width="147" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="VGh-Jf-FAt"/>
                    </constraints>
                    <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" alignment="right" drawsBackground="YES" usesSingleLineMode="YES" id="2189">
                        <numberFormatter key="formatter" formatterBehavior="custom10_4" allowsFloats="NO" usesGroupingSeparator="NO" groupingSize="0" minimumIntegerDigits="0" maximumIntegerDigits="42" id="2203">
                            <real key="minimum" value="1"/>
                            <real key="maximum" value="32"/>
                        </numberFormatter>
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="117" name="enabled" keyPath="values.CustomQuerySoftIndent" id="MfW-f7-kwf"/>
                        <binding destination="117" name="value" keyPath="values.CustomQuerySoftIndentWidth" id="2209"/>
                    </connections>
                </textField>
                <stepper horizontalHuggingPriority="750" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="2196">
                    <rect key="frame" x="507" y="377" width="15" height="22"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="11" id="Epk-vt-fev"/>
                        <constraint firstAttribute="height" constant="16" id="fyK-95-4bF"/>
                    </constraints>
                    <stepperCell key="cell" controlSize="small" continuous="YES" alignment="left" minValue="1" maxValue="32" doubleValue="1" id="2197">
                        <font key="font" metaFont="message" size="11"/>
                    </stepperCell>
                    <connections>
                        <action selector="takeIntegerValueFrom:" target="2188" id="2214"/>
                        <binding destination="117" name="value" keyPath="values.CustomQuerySoftIndentWidth" id="2205"/>
                    </connections>
                </stepper>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="xIN-qP-ogl">
                    <rect key="frame" x="259" y="193" width="261" height="24"/>
                    <buttonCell key="cell" type="check" title="Syntax highlighting" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="VHx-Tf-Rw2">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="NJU-0g-hK3"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.CustomQueryEnableSyntaxHighlighting" id="kJw-7M-n52"/>
                    </connections>
                </button>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="UnY-1b-NTN">
                    <rect key="frame" x="259" y="162" width="261" height="24"/>
                    <buttonCell key="cell" type="check" title="Bracket highlighting" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="vxK-rA-lkM">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                        <connections>
                            <binding destination="117" name="value" keyPath="values.CustomQueryEnableBracketHighliting" id="VaB-NN-oGg"/>
                        </connections>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="LQ3-Eq-hy0"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.CustomQueryEnableBracketHighlighting" id="635-un-YeG"/>
                    </connections>
                </button>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1517">
                    <rect key="frame" x="472" y="109" width="50" height="14"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="46" id="Wt0-DH-0yD"/>
                    </constraints>
                    <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" enabled="NO" sendsActionOnEndEditing="YES" title="seconds" id="1527">
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="117" name="enabled" keyPath="values.CustomQueryUpdateAutoHelp" id="1534"/>
                    </connections>
                </textField>
                <stepper horizontalHuggingPriority="750" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1518">
                    <rect key="frame" x="458" y="104" width="15" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="16" id="UHj-Wq-xiC"/>
                        <constraint firstAttribute="width" constant="11" id="nHg-43-jBJ"/>
                    </constraints>
                    <stepperCell key="cell" controlSize="small" continuous="YES" enabled="NO" alignment="left" increment="0.10000000000000001" maxValue="5" doubleValue="1" id="1526">
                        <font key="font" metaFont="message" size="11"/>
                    </stepperCell>
                    <connections>
                        <action selector="delayStepperChanged:" target="2144" id="jTJ-rr-cCf"/>
                        <binding destination="117" name="enabled" keyPath="values.CustomQueryAutoComplete" id="1541"/>
                        <binding destination="117" name="value" keyPath="values.CustomQueryAutoCompleteDelay" id="1540"/>
                    </connections>
                </stepper>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1519">
                    <rect key="frame" x="279" y="100" width="59" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="6rD-Fb-Wtt"/>
                        <constraint firstAttribute="width" constant="55" id="sCD-jX-6bG"/>
                    </constraints>
                    <textFieldCell key="cell" controlSize="small" lineBreakMode="truncatingTail" enabled="NO" sendsActionOnEndEditing="YES" alignment="right" title="Delay by:" usesSingleLineMode="YES" id="1525">
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="117" name="enabled" keyPath="values.CustomQueryUpdateAutoHelp" id="1535"/>
                    </connections>
                </textField>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1520">
                    <rect key="frame" x="341" y="105" width="118" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="UbU-HI-GT2"/>
                    </constraints>
                    <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" enabled="NO" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" alignment="right" title="0.5" drawsBackground="YES" usesSingleLineMode="YES" id="1523">
                        <numberFormatter key="formatter" formatterBehavior="custom10_4" positiveFormat="#.0" allowsFloats="NO" alwaysShowsDecimalSeparator="YES" usesGroupingSeparator="NO" groupingSize="0" minimumIntegerDigits="1" maximumIntegerDigits="1" minimumFractionDigits="1" maximumFractionDigits="1" decimalSeparator="." groupingSeparator="" id="1524">
                            <nil key="negativeInfinitySymbol"/>
                            <nil key="positiveInfinitySymbol"/>
                            <real key="minimum" value="0.0"/>
                            <real key="maximum" value="20"/>
                        </numberFormatter>
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <action selector="takeFloatValueFrom:" target="1518" id="1528"/>
                        <binding destination="117" name="enabled" keyPath="values.CustomQueryAutoComplete" id="1539"/>
                        <binding destination="117" name="value" keyPath="values.CustomQueryAutoCompleteDelay" id="1538"/>
                    </connections>
                </textField>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="1521">
                    <rect key="frame" x="259" y="131" width="261" height="24"/>
                    <buttonCell key="cell" type="check" title="Auto-Completion" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="1522">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="JL1-kC-qdF"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.CustomQueryAutoComplete" id="1537"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="q6h-n2-3a8">
                    <rect key="frame" x="259" y="77" width="261" height="24"/>
                    <buttonCell key="cell" type="check" title="Complete with backticks" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="vGo-VU-lIt">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="Hbi-dI-piB"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.SPCustomQueryEditorCompleteWithBackticks" id="ZhP-mB-7aF"/>
                    </connections>
                </button>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="1542">
                    <rect key="frame" x="259" y="46" width="261" height="24"/>
                    <buttonCell key="cell" type="check" title="Insert function arguments" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="1543">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="Vdm-3z-iY0"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.CustomQueryFunctionCompletionInsertsArguments" id="1545"/>
                    </connections>
                </button>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1499">
                    <rect key="frame" x="259" y="18" width="97" height="20"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="OHa-nn-g2y"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="left" title="Tab stop width:" id="1500">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1505">
                    <rect key="frame" x="359" y="17" width="149" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="VkS-BX-Qdr"/>
                    </constraints>
                    <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" alignment="right" drawsBackground="YES" usesSingleLineMode="YES" id="1506">
                        <numberFormatter key="formatter" formatterBehavior="default10_4" localizesFormat="NO" numberStyle="decimal" lenient="YES" minimumIntegerDigits="1" maximumIntegerDigits="309" maximumFractionDigits="3" id="1507">
                            <real key="minimum" value="1"/>
                            <real key="maximum" value="20"/>
                        </numberFormatter>
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <action selector="takeIntegerValueFrom:" target="1497" id="1512"/>
                        <binding destination="117" name="value" keyPath="values.CustomQueryEditorTabStopWidth" id="1511"/>
                    </connections>
                </textField>
                <stepper horizontalHuggingPriority="750" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="1497">
                    <rect key="frame" x="507" y="16" width="15" height="22"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="11" id="kU9-CG-jyc"/>
                        <constraint firstAttribute="height" constant="16" id="ua9-l2-nxz"/>
                    </constraints>
                    <stepperCell key="cell" controlSize="small" continuous="YES" alignment="left" minValue="1" maxValue="20" doubleValue="1" id="1498">
                        <font key="font" metaFont="message" size="11"/>
                    </stepperCell>
                    <connections>
                        <action selector="takeIntegerValueFrom:" target="1505" id="1509"/>
                        <binding destination="117" name="value" keyPath="values.CustomQueryEditorTabStopWidth" id="1516"/>
                    </connections>
                </stepper>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="v54-2I-2wr">
                    <rect key="frame" x="421" y="443" width="96" height="32"/>
                    <buttonCell key="cell" type="push" title="Reset" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="ixc-ll-0TU">
                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="width" constant="82" id="ojj-R1-kpt"/>
                    </constraints>
                    <connections>
                        <action selector="resetSystemFont:" target="2144" id="xfA-aq-TbG"/>
                    </connections>
                </button>
            </subviews>
            <constraints>
                <constraint firstItem="1040" firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" id="07h-Be-Qb7"/>
                <constraint firstItem="1517" firstAttribute="centerY" secondItem="1519" secondAttribute="centerY" constant="-5" id="0uR-Wu-RhC"/>
                <constraint firstItem="1682" firstAttribute="leading" secondItem="1085" secondAttribute="leading" id="0zs-YT-pk7"/>
                <constraint firstItem="1042" firstAttribute="top" secondItem="1040" secondAttribute="bottom" constant="9" id="1um-mL-Szt"/>
                <constraint firstItem="1520" firstAttribute="leading" secondItem="1519" secondAttribute="trailing" constant="5" id="30q-zf-qRY"/>
                <constraint firstItem="1497" firstAttribute="leading" secondItem="1505" secondAttribute="trailing" constant="1" id="32R-Ww-Wta"/>
                <constraint firstItem="1497" firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" id="3aN-VN-IMz"/>
                <constraint firstItem="1519" firstAttribute="leading" secondItem="1045" secondAttribute="leading" id="3x9-GR-xQ1"/>
                <constraint firstItem="1520" firstAttribute="centerY" secondItem="1519" secondAttribute="centerY" constant="-5" id="4vP-JR-hye"/>
                <constraint firstItem="2185" firstAttribute="leading" secondItem="1039" secondAttribute="leading" id="51B-Rx-1DL"/>
                <constraint firstItem="1517" firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" id="58G-Zl-88J"/>
                <constraint firstItem="1041" firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" id="6Gc-GN-jaM"/>
                <constraint firstItem="1499" firstAttribute="leading" secondItem="1039" secondAttribute="leading" id="6Gd-YR-UwY"/>
                <constraint firstItem="1128" firstAttribute="leading" secondItem="1039" secondAttribute="leading" id="6Rt-Dl-nEH"/>
                <constraint firstItem="q6h-n2-3a8" firstAttribute="top" secondItem="1519" secondAttribute="bottom" id="753-w2-5rN"/>
                <constraint firstItem="1037" firstAttribute="top" secondItem="802" secondAttribute="top" constant="16" id="7iM-gH-svh"/>
                <constraint firstItem="1040" firstAttribute="leading" secondItem="1039" secondAttribute="leading" id="88v-JT-MZi"/>
                <constraint firstItem="1044" firstAttribute="centerY" secondItem="1045" secondAttribute="centerY" constant="-5" id="8Nx-5g-ekY"/>
                <constraint firstItem="1669" firstAttribute="top" secondItem="1682" secondAttribute="bottom" constant="10" id="8X8-bM-jcO"/>
                <constraint firstAttribute="trailing" secondItem="v54-2I-2wr" secondAttribute="trailing" constant="30" id="9BW-v0-fwT"/>
                <constraint firstItem="1518" firstAttribute="centerY" secondItem="1519" secondAttribute="centerY" constant="-5" id="CM5-5a-OYN"/>
                <constraint firstItem="1034" firstAttribute="leading" secondItem="1085" secondAttribute="leading" id="CNn-vp-M3D"/>
                <constraint firstItem="1085" firstAttribute="leading" secondItem="802" secondAttribute="leading" constant="20" id="E3j-XR-Tr3"/>
                <constraint firstItem="q6h-n2-3a8" firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" id="FMR-XC-hJi"/>
                <constraint firstItem="1521" firstAttribute="leading" secondItem="1039" secondAttribute="leading" id="Ff1-Nj-8e8"/>
                <constraint firstAttribute="bottom" secondItem="1669" secondAttribute="bottom" constant="15" id="GRk-eU-Obz"/>
                <constraint firstItem="1499" firstAttribute="top" secondItem="1542" secondAttribute="bottom" constant="9" id="I3G-h6-HEr"/>
                <constraint firstItem="1040" firstAttribute="top" secondItem="2185" secondAttribute="bottom" constant="9" id="ISt-G5-WmP"/>
                <constraint firstItem="1036" firstAttribute="baseline" secondItem="v54-2I-2wr" secondAttribute="baseline" id="LJx-fA-JCi"/>
                <constraint firstItem="1517" firstAttribute="leading" secondItem="1518" secondAttribute="trailing" constant="3" id="LrC-x9-AzB"/>
                <constraint firstItem="1740" firstAttribute="centerY" secondItem="1669" secondAttribute="centerY" id="Mxl-Bt-Lmm"/>
                <constraint firstItem="xIN-qP-ogl" firstAttribute="top" secondItem="1128" secondAttribute="bottom" constant="9" id="N4F-tp-Ye3"/>
                <constraint firstItem="1519" firstAttribute="top" secondItem="1521" secondAttribute="bottom" constant="10" id="OBh-aS-TeB"/>
                <constraint firstItem="2196" firstAttribute="leading" secondItem="2188" secondAttribute="trailing" constant="1" id="Qx4-WP-Nw8"/>
                <constraint firstItem="1041" firstAttribute="leading" secondItem="1039" secondAttribute="leading" id="RN4-RH-L81"/>
                <constraint firstItem="2188" firstAttribute="centerY" secondItem="2185" secondAttribute="centerY" id="Rpe-nI-N5h"/>
                <constraint firstItem="1518" firstAttribute="leading" secondItem="1520" secondAttribute="trailing" constant="1" id="S1I-B9-WbE"/>
                <constraint firstItem="1682" firstAttribute="top" secondItem="1085" secondAttribute="bottom" constant="9" id="Tak-ON-k22"/>
                <constraint firstItem="1045" firstAttribute="leading" secondItem="1682" secondAttribute="trailing" constant="30" id="TiG-wn-eVq"/>
                <constraint firstItem="1128" firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" id="U1K-di-gV4"/>
                <constraint firstItem="1740" firstAttribute="trailing" secondItem="1682" secondAttribute="trailing" id="V1T-vE-bku"/>
                <constraint firstItem="UnY-1b-NTN" firstAttribute="leading" secondItem="1039" secondAttribute="leading" id="V8I-rs-sZ6"/>
                <constraint firstItem="2196" firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" id="VM2-Ha-wgU"/>
                <constraint firstItem="1505" firstAttribute="leading" secondItem="1499" secondAttribute="trailing" constant="5" id="Veq-vf-zA9"/>
                <constraint firstItem="UnY-1b-NTN" firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" id="VhL-v5-EOm"/>
                <constraint firstItem="2196" firstAttribute="centerY" secondItem="2185" secondAttribute="centerY" id="W50-pu-PZf"/>
                <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="1499" secondAttribute="bottom" constant="15" id="WqO-mA-gjk"/>
                <constraint firstItem="q6h-n2-3a8" firstAttribute="leading" secondItem="1039" secondAttribute="leading" id="agH-Ty-N6d"/>
                <constraint firstItem="v54-2I-2wr" firstAttribute="baseline" secondItem="1036" secondAttribute="firstBaseline" id="bAf-2i-MTh"/>
                <constraint firstItem="1039" firstAttribute="top" secondItem="1085" secondAttribute="bottom" constant="9" id="bwP-pO-qGY"/>
                <constraint firstItem="1085" firstAttribute="top" secondItem="1037" secondAttribute="bottom" constant="8" symbolic="YES" id="cau-FV-c1C"/>
                <constraint firstItem="1083" firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" id="czE-lF-bOr"/>
                <constraint firstItem="1034" firstAttribute="baseline" secondItem="1036" secondAttribute="baseline" id="gnd-q3-Ilc"/>
                <constraint firstItem="1521" firstAttribute="top" secondItem="UnY-1b-NTN" secondAttribute="bottom" constant="9" id="h60-Jm-iq9"/>
                <constraint firstItem="1039" firstAttribute="leading" secondItem="1682" secondAttribute="trailing" constant="10" id="hgk-h2-1CQ"/>
                <constraint firstItem="1083" firstAttribute="leading" secondItem="1046" secondAttribute="trailing" constant="3" id="hj3-hN-nDf"/>
                <constraint firstItem="1042" firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" id="jSN-iU-XcU"/>
                <constraint firstItem="1738" firstAttribute="centerY" secondItem="1669" secondAttribute="centerY" id="js1-Pb-T93"/>
                <constraint firstItem="1037" firstAttribute="centerY" secondItem="1036" secondAttribute="centerY" id="k11-dq-m5q"/>
                <constraint firstItem="1542" firstAttribute="leading" secondItem="1039" secondAttribute="leading" id="kO2-0M-Ugk"/>
                <constraint firstItem="1046" firstAttribute="leading" secondItem="1044" secondAttribute="trailing" constant="1" id="lI8-cX-zTo"/>
                <constraint firstItem="1669" firstAttribute="leading" secondItem="1085" secondAttribute="leading" id="lep-L6-nCB"/>
                <constraint firstItem="1542" firstAttribute="top" secondItem="q6h-n2-3a8" secondAttribute="bottom" constant="9" id="lgL-hz-Q1E"/>
                <constraint firstItem="1037" firstAttribute="leading" secondItem="1034" secondAttribute="trailing" constant="8" symbolic="YES" id="mKe-Hn-ebQ"/>
                <constraint firstItem="1521" firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" id="mdL-tz-Eyr"/>
                <constraint firstItem="1505" firstAttribute="centerY" secondItem="1499" secondAttribute="centerY" id="mi6-lk-Hoo"/>
                <constraint firstItem="2188" firstAttribute="leading" secondItem="2185" secondAttribute="trailing" constant="5" id="mm3-tG-9b9"/>
                <constraint firstItem="1083" firstAttribute="centerY" secondItem="1045" secondAttribute="centerY" constant="-5" id="oSA-hL-2he"/>
                <constraint firstItem="1046" firstAttribute="centerY" secondItem="1045" secondAttribute="centerY" constant="-5" id="ohG-5j-dFT"/>
                <constraint firstItem="v54-2I-2wr" firstAttribute="leading" secondItem="1036" secondAttribute="trailing" constant="12" symbolic="YES" id="oxL-Wb-OXr"/>
                <constraint firstItem="1682" firstAttribute="width" secondItem="802" secondAttribute="width" multiplier="3/7" id="oyD-c7-AK6"/>
                <constraint firstItem="UnY-1b-NTN" firstAttribute="top" secondItem="xIN-qP-ogl" secondAttribute="bottom" constant="9" id="qSa-Cf-Vj8"/>
                <constraint firstItem="1045" firstAttribute="top" secondItem="1041" secondAttribute="bottom" constant="5" id="rGS-R2-10k"/>
                <constraint firstItem="1740" firstAttribute="leading" secondItem="1738" secondAttribute="trailing" constant="5" id="rkP-ab-hmB"/>
                <constraint firstItem="1497" firstAttribute="centerY" secondItem="1499" secondAttribute="centerY" id="sig-Tk-KWs"/>
                <constraint firstItem="xIN-qP-ogl" firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" id="vAd-Sh-CJY"/>
                <constraint firstItem="1042" firstAttribute="leading" secondItem="1039" secondAttribute="leading" id="vgo-wx-KMP"/>
                <constraint firstItem="1542" firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" id="vyL-HG-CgB"/>
                <constraint firstItem="xIN-qP-ogl" firstAttribute="leading" secondItem="1039" secondAttribute="leading" id="vyO-eH-iNc"/>
                <constraint firstItem="1044" firstAttribute="leading" secondItem="1045" secondAttribute="trailing" constant="5" id="w43-rk-Ykx"/>
                <constraint firstItem="1738" firstAttribute="leading" secondItem="1669" secondAttribute="trailing" constant="5" id="x8S-qI-1x7"/>
                <constraint firstItem="1036" firstAttribute="leading" secondItem="1037" secondAttribute="trailing" constant="10" id="xPc-Pi-SU3"/>
                <constraint firstItem="1034" firstAttribute="baseline" secondItem="1037" secondAttribute="firstBaseline" id="xk7-gj-cHh"/>
                <constraint firstItem="2185" firstAttribute="top" secondItem="1039" secondAttribute="bottom" constant="9" id="xyX-Pw-PqC"/>
                <constraint firstItem="1041" firstAttribute="top" secondItem="1042" secondAttribute="bottom" constant="9" id="y6U-1y-Z26"/>
                <constraint firstItem="1128" firstAttribute="top" secondItem="1045" secondAttribute="bottom" id="yPB-4e-MCn"/>
                <constraint firstAttribute="trailing" secondItem="1085" secondAttribute="trailing" constant="20" id="ydA-be-JIe"/>
                <constraint firstAttribute="trailing" secondItem="1039" secondAttribute="trailing" constant="20" id="yoh-2E-iOA"/>
            </constraints>
            <point key="canvasLocation" x="751" y="1093.5"/>
        </customView>
        <menu id="1547" userLabel="Context Menu">
            <items>
                <menuItem title="Remove" id="1548">
                    <modifierMask key="keyEquivalentModifierMask"/>
                </menuItem>
                <menuItem title="Duplicate" id="1549">
                    <modifierMask key="keyEquivalentModifierMask"/>
                </menuItem>
                <menuItem isSeparatorItem="YES" id="1551"/>
                <menuItem title="Make Default" id="1640">
                    <modifierMask key="keyEquivalentModifierMask"/>
                </menuItem>
            </items>
            <connections>
                <outlet property="delegate" destination="-2" id="1555"/>
            </connections>
            <point key="canvasLocation" x="616" y="837"/>
        </menu>
        <customView id="D6L-76-0Ik" userLabel="Pick SSH client binary Accessory View">
            <rect key="frame" x="0.0" y="0.0" width="404" height="44"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <subviews>
                <textField focusRingType="none" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="yQV-9I-7us">
                    <rect key="frame" x="0.0" y="22" width="274" height="22"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="DA7-oj-oCa"/>
                    </constraints>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="System default" drawsBackground="YES" usesSingleLineMode="YES" id="eS9-G4-zgt">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" setsMaxLayoutWidthAtFirstLayout="YES" translatesAutoresizingMaskIntoConstraints="NO" id="ZHD-xB-ee1">
                    <rect key="frame" x="-2" y="0.0" width="408" height="14"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="14" id="1tl-8Z-ZyL"/>
                    </constraints>
                    <textFieldCell key="cell" sendsActionOnEndEditing="YES" title="Enter the full absolute path to your SSH binary." id="yLf-F5-exv">
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="hEB-5Q-jZ8">
                    <rect key="frame" x="276" y="16" width="134" height="32"/>
                    <buttonCell key="cell" type="push" title="Change…" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="1nt-aH-gM4">
                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="width" constant="120" id="LCk-ny-bZd"/>
                        <constraint firstAttribute="height" constant="20" id="Xvw-M5-qBT"/>
                    </constraints>
                    <connections>
                        <action selector="pickSSHClientViaFileBrowser:" target="2146" id="w0e-lI-UbS"/>
                    </connections>
                </button>
            </subviews>
            <constraints>
                <constraint firstItem="ZHD-xB-ee1" firstAttribute="leading" secondItem="D6L-76-0Ik" secondAttribute="leading" id="8FK-sx-cfb"/>
                <constraint firstAttribute="trailing" secondItem="ZHD-xB-ee1" secondAttribute="trailing" id="Ibq-XS-ZHz"/>
                <constraint firstItem="hEB-5Q-jZ8" firstAttribute="leading" secondItem="yQV-9I-7us" secondAttribute="trailing" constant="9" id="Kah-Z1-7H3"/>
                <constraint firstAttribute="trailing" secondItem="hEB-5Q-jZ8" secondAttribute="trailing" constant="1" id="PBc-Ji-5OD"/>
                <constraint firstItem="yQV-9I-7us" firstAttribute="leading" secondItem="D6L-76-0Ik" secondAttribute="leading" id="QAE-Au-ceJ"/>
                <constraint firstItem="hEB-5Q-jZ8" firstAttribute="top" secondItem="D6L-76-0Ik" secondAttribute="top" constant="1" id="bff-7z-Sej"/>
                <constraint firstItem="yQV-9I-7us" firstAttribute="top" secondItem="D6L-76-0Ik" secondAttribute="top" id="c6w-vy-Xxv"/>
                <constraint firstItem="ZHD-xB-ee1" firstAttribute="top" secondItem="yQV-9I-7us" secondAttribute="bottom" constant="8" symbolic="YES" id="lmr-XV-doG"/>
                <constraint firstAttribute="bottom" secondItem="ZHD-xB-ee1" secondAttribute="bottom" id="n10-Sr-FMW"/>
            </constraints>
            <point key="canvasLocation" x="40" y="-5"/>
        </customView>
        <customView id="GEn-6y-U3F" userLabel="Show Hidden Files Accessory View">
            <rect key="frame" x="0.0" y="0.0" width="309" height="54"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <subviews>
                <button translatesAutoresizingMaskIntoConstraints="NO" id="bAQ-gF-4V2">
                    <rect key="frame" x="16" y="18" width="275" height="18"/>
                    <buttonCell key="cell" type="check" title="Show hidden files" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="apR-gz-ej3">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="16" id="1gs-sa-Z6Q"/>
                    </constraints>
                    <connections>
                        <binding destination="117" name="value" keyPath="values.KeySelectionHiddenFilesVisibility" id="WP6-C7-FqP">
                            <dictionary key="options">
                                <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                <integer key="NSNoSelectionPlaceholder" value="0"/>
                                <integer key="NSNotApplicablePlaceholder" value="0"/>
                                <integer key="NSNullPlaceholder" value="0"/>
                            </dictionary>
                        </binding>
                    </connections>
                </button>
            </subviews>
            <constraints>
                <constraint firstItem="bAQ-gF-4V2" firstAttribute="top" secondItem="GEn-6y-U3F" secondAttribute="top" constant="19" id="GDo-tV-3cN"/>
                <constraint firstAttribute="bottom" secondItem="bAQ-gF-4V2" secondAttribute="bottom" constant="19" id="Yt4-FX-HX9"/>
                <constraint firstAttribute="trailing" secondItem="bAQ-gF-4V2" secondAttribute="trailing" constant="18" id="Z26-h6-mxb"/>
                <constraint firstItem="bAQ-gF-4V2" firstAttribute="leading" secondItem="GEn-6y-U3F" secondAttribute="leading" constant="18" id="jdJ-Qe-R39"/>
            </constraints>
            <point key="canvasLocation" x="41" y="97"/>
        </customView>
        <customView translatesAutoresizingMaskIntoConstraints="NO" id="5dZ-tO-Ldn" userLabel="File">
            <rect key="frame" x="0.0" y="0.0" width="540" height="316"/>
            <userGuides>
                <userLayoutGuide location="146" affinity="minX"/>
                <userLayoutGuide location="315" affinity="minX"/>
            </userGuides>
            <subviews>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="YVZ-mE-SYx">
                    <rect key="frame" x="427" y="8" width="100" height="34"/>
                    <buttonCell key="cell" type="push" title="Add Files…" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="KyI-4e-Nx9">
                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="sC9-8y-2jy"/>
                    </constraints>
                    <connections>
                        <action selector="addBookmark:" target="bKg-6I-v9A" id="LBd-Si-eCg"/>
                    </connections>
                </button>
                <textField focusRingType="none" horizontalHuggingPriority="251" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="kZG-fE-IMo">
                    <rect key="frame" x="18" y="281" width="504" height="20"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="20" id="1Ju-aK-9xO"/>
                    </constraints>
                    <textFieldCell key="cell" lineBreakMode="clipping" title="Accessible Files" id="kpg-Xc-INC">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField focusRingType="none" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="fk8-1b-YVh">
                    <rect key="frame" x="18" y="258" width="504" height="23"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="23" id="2Uu-on-XzS"/>
                    </constraints>
                    <textFieldCell key="cell" title="This list shows all the files that Sequel Ace has been granted access to." id="g1t-1Q-PEO">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <scrollView autohidesScrollers="YES" horizontalLineScroll="19" horizontalPageScroll="10" verticalLineScroll="19" verticalPageScroll="10" translatesAutoresizingMaskIntoConstraints="NO" id="807-4b-o8N">
                    <rect key="frame" x="20" y="46" width="500" height="212"/>
                    <clipView key="contentView" id="u3q-XC-nUM">
                        <rect key="frame" x="1" y="1" width="498" height="210"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <subviews>
                            <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="firstColumnOnly" tableStyle="fullWidth" alternatingRowBackgroundColors="YES" columnReordering="NO" columnSelection="YES" columnResizing="NO" autosaveColumns="NO" id="eH2-Ed-Xh3">
                                <rect key="frame" x="0.0" y="0.0" width="498" height="210"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <size key="intercellSpacing" width="3" height="2"/>
                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                <tableColumns>
                                    <tableColumn editable="NO" width="486" minWidth="40" maxWidth="1000" id="oAq-cQ-smW" userLabel="Granted Files">
                                        <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Granted Files">
                                            <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                        </tableHeaderCell>
                                        <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" title="Text Cell" id="XpU-Pg-wan">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <tableColumnResizingMask key="resizingMask" resizeWithTable="YES"/>
                                    </tableColumn>
                                </tableColumns>
                                <connections>
                                    <action trigger="doubleAction" selector="doubleClick:" target="bKg-6I-v9A" id="iVZ-w8-KbV"/>
                                    <binding destination="bKg-6I-v9A" name="doubleClickTarget" keyPath="self" id="7TT-wF-8RQ">
                                        <dictionary key="options">
                                            <string key="NSSelectorName">doubleClick:</string>
                                        </dictionary>
                                    </binding>
                                    <outlet property="dataSource" destination="bKg-6I-v9A" id="Xl3-F4-bfN"/>
                                    <outlet property="delegate" destination="bKg-6I-v9A" id="gdA-Pt-K3z"/>
                                </connections>
                            </tableView>
                        </subviews>
                    </clipView>
                    <constraints>
                        <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="200" id="LQg-hs-gBr"/>
                    </constraints>
                    <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="a0h-TA-vlQ">
                        <rect key="frame" x="1" y="195" width="498" height="16"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </scroller>
                    <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="MIJ-1S-T0a">
                        <rect key="frame" x="224" y="17" width="15" height="102"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </scroller>
                </scrollView>
                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="jKz-jR-d80">
                    <rect key="frame" x="261" y="8" width="166" height="34"/>
                    <buttonCell key="cell" type="push" title="Revoke Selected Files" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="lHn-Wy-DTi">
                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="system"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="boolean" keyPath="enabled" value="NO"/>
                        </userDefinedRuntimeAttributes>
                    </buttonCell>
                    <constraints>
                        <constraint firstAttribute="height" constant="22" id="fOZ-7m-1BD"/>
                    </constraints>
                    <connections>
                        <action selector="revokeBookmark:" target="bKg-6I-v9A" id="hfz-ao-mRF"/>
                    </connections>
                </button>
                <textField hidden="YES" focusRingType="none" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" translatesAutoresizingMaskIntoConstraints="NO" id="C8B-BP-39F">
                    <rect key="frame" x="18" y="14" width="244" height="23"/>
                    <constraints>
                        <constraint firstAttribute="height" constant="23" id="p5V-R8-dvE"/>
                    </constraints>
                    <textFieldCell key="cell" title="Double-click red items to refresh bookmark" id="oft-dR-p0X">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="secondaryLabelColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
            </subviews>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="YVZ-mE-SYx" secondAttribute="trailing" constant="20" id="0sG-Bb-3VF"/>
                <constraint firstItem="fk8-1b-YVh" firstAttribute="top" secondItem="kZG-fE-IMo" secondAttribute="bottom" id="37t-4e-4qr"/>
                <constraint firstItem="YVZ-mE-SYx" firstAttribute="top" secondItem="807-4b-o8N" secondAttribute="bottom" constant="9" id="5Rh-QK-Ytq"/>
                <constraint firstAttribute="bottom" secondItem="YVZ-mE-SYx" secondAttribute="bottom" constant="15" id="6O0-tL-XM2"/>
                <constraint firstAttribute="trailing" secondItem="kZG-fE-IMo" secondAttribute="trailing" constant="20" id="8hL-HV-IqL"/>
                <constraint firstAttribute="trailing" secondItem="807-4b-o8N" secondAttribute="trailing" constant="20" id="CIe-pG-zSE"/>
                <constraint firstItem="jKz-jR-d80" firstAttribute="leading" secondItem="C8B-BP-39F" secondAttribute="trailing" constant="8" symbolic="YES" id="Gnn-bh-ZCx"/>
                <constraint firstItem="fk8-1b-YVh" firstAttribute="leading" secondItem="5dZ-tO-Ldn" secondAttribute="leading" constant="20" id="Koj-wh-pl1"/>
                <constraint firstAttribute="bottom" secondItem="jKz-jR-d80" secondAttribute="bottom" constant="15" id="UCX-4f-vRk"/>
                <constraint firstItem="kZG-fE-IMo" firstAttribute="leading" secondItem="5dZ-tO-Ldn" secondAttribute="leading" constant="20" id="WJp-mb-u9B"/>
                <constraint firstAttribute="trailing" secondItem="fk8-1b-YVh" secondAttribute="trailing" constant="20" id="WVv-Al-b2G"/>
                <constraint firstItem="807-4b-o8N" firstAttribute="top" secondItem="fk8-1b-YVh" secondAttribute="bottom" id="aDB-Zp-xlo"/>
                <constraint firstItem="C8B-BP-39F" firstAttribute="top" secondItem="jKz-jR-d80" secondAttribute="top" id="d3R-mF-dQW"/>
                <constraint firstItem="YVZ-mE-SYx" firstAttribute="leading" secondItem="jKz-jR-d80" secondAttribute="trailing" constant="14" id="gtw-Zb-LRj"/>
                <constraint firstItem="C8B-BP-39F" firstAttribute="leading" secondItem="807-4b-o8N" secondAttribute="leading" id="ud8-7i-UNP"/>
                <constraint firstItem="807-4b-o8N" firstAttribute="leading" secondItem="5dZ-tO-Ldn" secondAttribute="leading" constant="20" id="vpk-T1-tvm"/>
                <constraint firstItem="kZG-fE-IMo" firstAttribute="top" secondItem="5dZ-tO-Ldn" secondAttribute="top" constant="15" id="wk8-pr-kYO"/>
            </constraints>
            <point key="canvasLocation" x="751" y="1549"/>
        </customView>
    </objects>
    <resources>
        <image name="NSAddTemplate" width="18" height="16"/>
        <image name="NSAdvanced" width="32" height="32"/>
        <image name="NSRemoveTemplate" width="18" height="4"/>
        <image name="resetTemplate" width="16" height="16"/>
    </resources>
</document>
