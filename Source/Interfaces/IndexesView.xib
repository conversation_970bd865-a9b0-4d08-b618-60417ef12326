<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="17506" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="17506"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPIndexesController">
            <connections>
                <outlet property="addIndexButton" destination="80" id="98"/>
                <outlet property="addIndexedColumnButton" destination="144" id="149"/>
                <outlet property="anchoredButtonBar" destination="140" id="152"/>
                <outlet property="confirmAddIndexButton" destination="80" id="130"/>
                <outlet property="indexAdvancedOptionsView" destination="62" id="99"/>
                <outlet property="indexAdvancedOptionsViewButton" destination="63" id="94"/>
                <outlet property="indexAdvancedOptionsViewLabelButton" destination="64" id="95"/>
                <outlet property="indexKeyBlockSizeTextField" destination="134" id="137"/>
                <outlet property="indexNameLabel" destination="7" id="103"/>
                <outlet property="indexNameTextField" destination="11" id="29"/>
                <outlet property="indexSizeTableColumn" destination="58" id="112"/>
                <outlet property="indexStorageTypePopUpButton" destination="86" id="115"/>
                <outlet property="indexTypeLabel" destination="6" id="104"/>
                <outlet property="indexTypePopUpButton" destination="12" id="28"/>
                <outlet property="indexedColumnsScrollView" destination="52" id="96"/>
                <outlet property="indexedColumnsTableView" destination="55" id="97"/>
                <outlet property="removeIndexedColumnButton" destination="143" id="151"/>
                <outlet property="window" destination="3" id="26"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="New Index" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="3" userLabel="New Index Dialog">
            <windowStyleMask key="styleMask" titled="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="512" y="244" width="330" height="274"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2880" height="1595"/>
            <value key="minSize" type="size" width="330" height="274"/>
            <view key="contentView" id="4">
                <rect key="frame" x="0.0" y="0.0" width="330" height="274"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <popUpButton toolTip="Choose the type of the index" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="12">
                        <rect key="frame" x="119" y="233" width="194" height="22"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                        <popUpButtonCell key="cell" type="push" title="UNIQUE" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="clipping" state="on" borderStyle="borderAndBezel" tag="2" inset="2" arrowPosition="arrowAtCenter" preferredEdge="maxY" selectedItem="17" id="13">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <menu key="menu" title="OtherViews" id="14">
                                <items>
                                    <menuItem title="PRIMARY KEY" id="16"/>
                                    <menuItem title="INDEX" tag="1" id="138"/>
                                    <menuItem title="UNIQUE" state="on" tag="2" id="17"/>
                                </items>
                            </menu>
                        </popUpButtonCell>
                        <connections>
                            <action selector="chooseIndexType:" target="-2" id="31"/>
                        </connections>
                    </popUpButton>
                    <textField toolTip="Choose the name of the index or leave blank to use column name" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="11">
                        <rect key="frame" x="122" y="211" width="188" height="19"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" enabled="NO" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" alignment="left" title="PRIMARY" placeholderString="optional" drawsBackground="YES" usesSingleLineMode="YES" id="19">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                        <connections>
                            <outlet property="delegate" destination="-2" id="35"/>
                        </connections>
                    </textField>
                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="108" translatesAutoresizingMaskIntoConstraints="NO" id="7">
                        <rect key="frame" x="5" y="213" width="112" height="14"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" sendsActionOnEndEditing="YES" alignment="right" title="Key Name:" id="23">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="108" translatesAutoresizingMaskIntoConstraints="NO" id="6">
                        <rect key="frame" x="5" y="235" width="112" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" sendsActionOnEndEditing="YES" alignment="right" title="Key Type:" id="24">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="16" horizontalPageScroll="10" verticalLineScroll="16" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="52">
                        <rect key="frame" x="0.0" y="99" width="330" height="104"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <clipView key="contentView" id="aWU-a0-hZQ">
                            <rect key="frame" x="1" y="1" width="328" height="102"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" alternatingRowBackgroundColors="YES" columnReordering="NO" columnResizing="NO" multipleSelection="NO" emptySelection="NO" autosaveColumns="NO" typeSelect="NO" rowHeight="14" headerView="56" id="55">
                                    <rect key="frame" x="0.0" y="0.0" width="328" height="85"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <size key="intercellSpacing" width="3" height="2"/>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                    <tableViewGridLines key="gridStyleMask" vertical="YES"/>
                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                    <tableColumns>
                                        <tableColumn identifier="name" editable="NO" width="210" minWidth="40" maxWidth="1000" id="57">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Field">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                            </tableHeaderCell>
                                            <comboBoxCell key="dataCell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" sendsActionOnEndEditing="YES" buttonBordered="NO" completes="NO" usesDataSource="YES" numberOfVisibleItems="5" id="61">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                <connections>
                                                    <outlet property="dataSource" destination="-2" id="122"/>
                                                </connections>
                                            </comboBoxCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="Size" width="76" minWidth="40" maxWidth="1000" id="58">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Size">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" placeholderString="optional" id="59">
                                                <numberFormatter key="formatter" formatterBehavior="default10_4" usesGroupingSeparator="NO" groupingSize="0" minimumIntegerDigits="0" maximumIntegerDigits="42" id="139">
                                                    <real key="minimum" value="0.0"/>
                                                </numberFormatter>
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                        </tableColumn>
                                    </tableColumns>
                                    <connections>
                                        <outlet property="dataSource" destination="-2" id="120"/>
                                        <outlet property="delegate" destination="-2" id="121"/>
                                        <outlet property="menu" destination="123" id="128"/>
                                    </connections>
                                </tableView>
                            </subviews>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="YES" id="54">
                            <rect key="frame" x="-100" y="-100" width="225" height="11"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="53">
                            <rect key="frame" x="224" y="17" width="15" height="102"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <tableHeaderView key="headerView" wantsLayer="YES" id="56">
                            <rect key="frame" x="0.0" y="0.0" width="328" height="17"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </tableHeaderView>
                    </scrollView>
                    <customView hidden="YES" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="62" userLabel="Advanced View for INSERT">
                        <rect key="frame" x="-2" y="-30" width="335" height="67"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                        <subviews>
                            <box autoresizesSubviews="NO" fixedFrame="YES" borderType="line" title="Box" titlePosition="noTitle" translatesAutoresizingMaskIntoConstraints="NO" id="67">
                                <rect key="frame" x="-5" y="-4" width="343" height="73"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                                <view key="contentView" id="1q6-1R-7qD">
                                    <rect key="frame" x="3" y="3" width="337" height="67"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <subviews>
                                        <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="92">
                                            <rect key="frame" x="15" y="38" width="169" height="14"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                            <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Storage Type:" id="93">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                        </textField>
                                        <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="86">
                                            <rect key="frame" x="186" y="33" width="136" height="22"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                            <popUpButtonCell key="cell" type="push" title="Default" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="89" id="87">
                                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                <font key="font" metaFont="message" size="11"/>
                                                <menu key="menu" title="OtherViews" id="88">
                                                    <items>
                                                        <menuItem title="Default" state="on" id="89"/>
                                                        <menuItem isSeparatorItem="YES" id="129"/>
                                                        <menuItem title="BTREE" id="90"/>
                                                        <menuItem title="HASH" id="91"/>
                                                    </items>
                                                </menu>
                                            </popUpButtonCell>
                                        </popUpButton>
                                        <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="132">
                                            <rect key="frame" x="15" y="12" width="169" height="14"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                            <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Key Block Size:" id="133">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                        </textField>
                                        <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="134">
                                            <rect key="frame" x="189" y="10" width="59" height="19"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                            <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="135">
                                                <numberFormatter key="formatter" formatterBehavior="default10_4" localizesFormat="NO" numberStyle="decimal" minimumIntegerDigits="1" maximumIntegerDigits="309" maximumFractionDigits="3" id="136">
                                                    <real key="minimum" value="0.0"/>
                                                    <real key="maximum" value="10000"/>
                                                </numberFormatter>
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                        </textField>
                                    </subviews>
                                </view>
                            </box>
                        </subviews>
                    </customView>
                    <button toolTip="Advanced index settings" horizontalHuggingPriority="750" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="63">
                        <rect key="frame" x="0.0" y="40" width="29" height="26"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="disclosureTriangle" bezelStyle="disclosure" imagePosition="left" alignment="center" borderStyle="border" imageScaling="proportionallyUpOrDown" inset="2" id="66">
                            <behavior key="behavior" pushIn="YES" changeBackground="YES" changeGray="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent">a</string>
                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                        </buttonCell>
                        <connections>
                            <action selector="toggleAdvancedIndexOptionsView:" target="-2" id="100"/>
                        </connections>
                    </button>
                    <button toolTip="Advanced index settings" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="64">
                        <rect key="frame" x="24" y="41" width="288" height="22"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="bevel" title="Advanced" bezelStyle="rounded" alignment="left" controlSize="small" state="on" inset="2" id="65">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                        </buttonCell>
                        <connections>
                            <action selector="toggleAdvancedIndexOptionsView:" target="-2" id="101"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="80">
                        <rect key="frame" x="215" y="13" width="100" height="28"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Add" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" inset="2" id="83">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSheet:" target="-2" id="84"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="81">
                        <rect key="frame" x="117" y="13" width="100" height="28"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" inset="2" id="82">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSheet:" target="-2" id="85"/>
                        </connections>
                    </button>
                    <box autoresizesSubviews="NO" fixedFrame="YES" boxType="custom" borderType="none" title="Box" translatesAutoresizingMaskIntoConstraints="NO" id="140">
                        <rect key="frame" x="0.0" y="74" width="330" height="25"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                        <view key="contentView" id="KuD-m4-ZkI">
                            <rect key="frame" x="0.0" y="0.0" width="330" height="25"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <button toolTip="Remove indexed field" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="143">
                                    <rect key="frame" x="31" y="5" width="23.5" height="17"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                    <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSRemoveTemplate" imagePosition="only" alignment="center" alternateImage="NSRemoveTemplate" inset="2" id="146">
                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                        <font key="font" metaFont="system"/>
                                    </buttonCell>
                                    <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                    <connections>
                                        <action selector="removeIndexedField:" target="-2" id="150"/>
                                    </connections>
                                </button>
                                <button toolTip="Add indexed field" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="144">
                                    <rect key="frame" x="0.0" y="0.5" width="25.5" height="25"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                    <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSAddTemplate" imagePosition="only" alignment="center" alternateImage="NSAddTemplate" inset="2" id="145">
                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                        <font key="font" metaFont="system"/>
                                    </buttonCell>
                                    <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                    <connections>
                                        <action selector="addIndexedField:" target="-2" id="148"/>
                                    </connections>
                                </button>
                            </subviews>
                        </view>
                    </box>
                </subviews>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="27"/>
            </connections>
            <point key="canvasLocation" x="139" y="147"/>
        </window>
        <menu id="123" userLabel="Context Menu">
            <items>
                <menuItem title="Remove Field" id="124">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="removeIndexedField:" target="-2" id="127"/>
                    </connections>
                </menuItem>
            </items>
        </menu>
    </objects>
    <resources>
        <image name="NSAddTemplate" width="14" height="13"/>
        <image name="NSRemoveTemplate" width="14" height="4"/>
    </resources>
</document>
