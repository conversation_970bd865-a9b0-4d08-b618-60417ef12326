<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="17701" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="17701"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPQueryController">
            <connections>
                <outlet property="clearConsoleButton" destination="73" id="76"/>
                <outlet property="consoleSearchField" destination="8" id="27"/>
                <outlet property="consoleTableView" destination="14" id="58"/>
                <outlet property="includeConnectionButton" destination="115" id="117"/>
                <outlet property="includeDatabaseButton" destination="bTU-iR-4Vf" id="yvJ-aZ-4by"/>
                <outlet property="includeTimeStampsButton" destination="50" id="70"/>
                <outlet property="loggingDisabledTextField" destination="108" id="110"/>
                <outlet property="progressIndicator" destination="62" id="63"/>
                <outlet property="saveConsoleButton" destination="9" id="25"/>
                <outlet property="saveLogView" destination="49" id="52"/>
                <outlet property="window" destination="3" id="23"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="Console" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" visibleAtLaunch="NO" frameAutosaveName="" animationBehavior="default" titlebarAppearsTransparent="YES" id="3" userLabel="Console" customClass="NSPanel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowCollectionBehavior key="collectionBehavior" participatesInCycle="YES" fullScreenPrimary="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="426" y="331" width="575" height="203"/>
            <rect key="screenRect" x="0.0" y="0.0" width="1680" height="1050"/>
            <value key="minSize" type="size" width="575" height="130"/>
            <view key="contentView" id="4">
                <rect key="frame" x="0.0" y="0.0" width="575" height="203"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <searchField wantsLayer="YES" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="8">
                        <rect key="frame" x="10" y="174" width="531" height="19"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                        <searchFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" borderStyle="bezel" placeholderString="Filter" usesSingleLineMode="YES" bezelStyle="round" id="13">
                            <font key="font" metaFont="menu" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </searchFieldCell>
                        <connections>
                            <outlet property="delegate" destination="-2" id="61"/>
                        </connections>
                    </searchField>
                    <progressIndicator hidden="YES" horizontalHuggingPriority="750" verticalHuggingPriority="750" fixedFrame="YES" minValue="16" maxValue="100" doubleValue="16" bezeled="NO" indeterminate="YES" controlSize="small" style="spinning" translatesAutoresizingMaskIntoConstraints="NO" id="62">
                        <rect key="frame" x="549" y="175" width="16" height="16"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                    </progressIndicator>
                    <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="18" horizontalPageScroll="10" verticalLineScroll="18" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7">
                        <rect key="frame" x="0.0" y="32" width="576" height="134"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <clipView key="contentView" id="Jw2-4l-Mr3">
                            <rect key="frame" x="1" y="1" width="574" height="132"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" autosaveColumns="NO" rowHeight="16" headerView="112" id="14">
                                    <rect key="frame" x="0.0" y="0.0" width="574" height="115"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <size key="intercellSpacing" width="3" height="2"/>
                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                    <tableColumns>
                                        <tableColumn identifier="messageDate" editable="NO" width="100" minWidth="50" maxWidth="1000" id="18">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Time">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" truncatesLastVisibleLine="YES" alignment="left" title="Text Cell" id="19">
                                                <font key="font" metaFont="menu" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="messageConnection" editable="NO" width="100" minWidth="50" maxWidth="1000" id="113">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Connection">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" truncatesLastVisibleLine="YES" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="114">
                                                <font key="font" metaFont="menu" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="messageDatabase" editable="NO" width="64" minWidth="50" maxWidth="1000" id="TqS-5i-3PL">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Database">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="REt-h0-g0b">
                                                <font key="font" metaFont="menu" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="message" editable="NO" width="260" minWidth="50" maxWidth="3.4028234663852886e+38" id="wyq-H4-GHT">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Query">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" truncatesLastVisibleLine="YES" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="g2D-wF-fcB">
                                                <font key="font" metaFont="menu" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                    </tableColumns>
                                    <connections>
                                        <outlet property="dataSource" destination="-2" id="59"/>
                                        <outlet property="delegate" destination="-2" id="60"/>
                                        <outlet property="menu" destination="64" id="68"/>
                                    </connections>
                                </tableView>
                            </subviews>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="15">
                            <rect key="frame" x="1" y="124" width="569" height="16"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="16">
                            <rect key="frame" x="539" y="1" width="15" height="124"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <tableHeaderView key="headerView" wantsLayer="YES" id="112">
                            <rect key="frame" x="0.0" y="0.0" width="574" height="17"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <connections>
                                <outlet property="menu" destination="DCA-JR-zjw" id="2UH-9N-pS2"/>
                            </connections>
                        </tableHeaderView>
                    </scrollView>
                    <button toolTip="Clear Console" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="73">
                        <rect key="frame" x="40" y="0.0" width="25" height="25"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSStopProgressFreestandingTemplate" imagePosition="only" alignment="center" alternateImage="NSStopProgressFreestandingTemplate" state="on" inset="2" id="74">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <connections>
                            <action selector="clearConsole:" target="-2" id="75"/>
                        </connections>
                    </button>
                    <popUpButton fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="77" userLabel="Gear Menu Button">
                        <rect key="frame" x="0.0" y="0.0" width="35" height="25"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <popUpButtonCell key="cell" type="smallSquare" bezelStyle="smallSquare" imagePosition="right" alignment="center" lineBreakMode="truncatingTail" inset="2" pullsDown="YES" selectedItem="80" id="78">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="menu"/>
                            <menu key="menu" title="OtherViews" id="79">
                                <items>
                                    <menuItem image="NSActionTemplate" hidden="YES" id="83"/>
                                    <menuItem title="Show Time Stamps" state="on" id="80">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                        <connections>
                                            <action selector="toggleShowTimeStamps:" target="-2" id="102"/>
                                            <binding destination="89" name="value" keyPath="values.ConsoleShowTimestamps" id="101">
                                                <dictionary key="options">
                                                    <bool key="NSConditionallySetsEnabled" value="NO"/>
                                                </dictionary>
                                            </binding>
                                        </connections>
                                    </menuItem>
                                    <menuItem title="Show Connections" state="on" id="118">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                        <connections>
                                            <action selector="toggleShowConnections:" target="-2" id="119"/>
                                            <binding destination="89" name="value" keyPath="values.ConsoleShowConnections" id="124">
                                                <dictionary key="options">
                                                    <bool key="NSConditionallySetsEnabled" value="NO"/>
                                                </dictionary>
                                            </binding>
                                        </connections>
                                    </menuItem>
                                    <menuItem title="Show Databases" state="on" id="QeH-Fu-VaB">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                        <connections>
                                            <action selector="toggleShowDatabases:" target="-2" id="qJH-at-hGq"/>
                                            <binding destination="89" name="value" keyPath="values.ConsoleShowDatabases" id="pY8-9m-mAK">
                                                <dictionary key="options">
                                                    <bool key="NSConditionallySetsEnabled" value="NO"/>
                                                </dictionary>
                                            </binding>
                                        </connections>
                                    </menuItem>
                                    <menuItem isSeparatorItem="YES" id="111"/>
                                    <menuItem title="Show SELECT/SHOW Statements" state="on" id="82">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                        <connections>
                                            <action selector="toggleShowSelectShowStatements:" target="-2" id="85"/>
                                            <binding destination="89" name="value" keyPath="values.ConsoleShowSelectsAndShows" id="96">
                                                <dictionary key="options">
                                                    <bool key="NSConditionallySetsEnabled" value="NO"/>
                                                </dictionary>
                                            </binding>
                                        </connections>
                                    </menuItem>
                                    <menuItem title="Show HELP Statements" state="on" id="103">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                        <connections>
                                            <action selector="toggleShowHelpStatements:" target="-2" id="104"/>
                                            <binding destination="89" name="value" keyPath="values.ConsoleShowHelps" id="107"/>
                                        </connections>
                                    </menuItem>
                                </items>
                            </menu>
                        </popUpButtonCell>
                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                    </popUpButton>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="108">
                        <rect key="frame" x="66" y="4" width="326" height="25"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" id="109">
                            <font key="font" metaFont="menu" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="9">
                        <rect key="frame" x="432" y="0.0" width="140" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Save as..." bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="12">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="saveConsoleAs:" target="-2" id="54"/>
                        </connections>
                    </button>
                </subviews>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="9BU-PA-RsE"/>
            </connections>
            <point key="canvasLocation" x="-55" y="139"/>
        </window>
        <customView id="49" userLabel="saveLogView">
            <rect key="frame" x="0.0" y="0.0" width="278" height="79"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <subviews>
                <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="50">
                    <rect key="frame" x="10" y="55" width="260" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="check" title="Include Time Stamps" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="51">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                </button>
                <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="115">
                    <rect key="frame" x="10" y="31" width="260" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="check" title="Include Connections" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="116">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                </button>
                <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="bTU-iR-4Vf">
                    <rect key="frame" x="10" y="7" width="136" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="check" title="Include Databases" bezelStyle="regularSquare" imagePosition="left" state="on" inset="2" id="kLs-LH-NAM">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                </button>
            </subviews>
            <point key="canvasLocation" x="139" y="-70"/>
        </customView>
        <menu id="64" userLabel="Table Row Context Menu">
            <items>
                <menuItem title="Copy" id="65">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="copy:" target="-2" id="69"/>
                    </connections>
                </menuItem>
                <menuItem title="Copy Raw Query" id="dsy-Yk-yWd">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="copyQueryOnly:" target="-2" id="h9k-0G-NSI"/>
                    </connections>
                </menuItem>
            </items>
            <point key="canvasLocation" x="-197" y="150"/>
        </menu>
        <userDefaultsController representsSharedInstance="YES" id="89"/>
        <menu id="DCA-JR-zjw" userLabel="Table Header Context Menu">
            <items>
                <menuItem title="Time" state="on" id="Eqs-r2-Xz7">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="toggleShowTimeStamps:" target="-2" id="OMU-jP-CeY"/>
                        <binding destination="89" name="value" keyPath="values.ConsoleShowTimestamps" id="62b-Yd-Tlr">
                            <dictionary key="options">
                                <bool key="NSConditionallySetsEnabled" value="NO"/>
                            </dictionary>
                        </binding>
                    </connections>
                </menuItem>
                <menuItem title="Connection" state="on" id="xEF-YU-b4J">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="toggleShowConnections:" target="-2" id="Hhw-0n-qWg"/>
                        <binding destination="89" name="value" keyPath="values.ConsoleShowConnections" id="YKo-wm-6u8">
                            <dictionary key="options">
                                <bool key="NSConditionallySetsEnabled" value="NO"/>
                            </dictionary>
                        </binding>
                    </connections>
                </menuItem>
                <menuItem title="Database" state="on" id="kNX-td-f8A">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="toggleShowDatabases:" target="-2" id="1z7-qp-tCQ"/>
                        <binding destination="89" name="value" keyPath="values.ConsoleShowDatabases" id="0w1-96-uH9">
                            <dictionary key="options">
                                <bool key="NSConditionallySetsEnabled" value="NO"/>
                            </dictionary>
                        </binding>
                    </connections>
                </menuItem>
                <menuItem title="Query" state="on" enabled="NO" id="VWx-bC-aFb">
                    <modifierMask key="keyEquivalentModifierMask"/>
                </menuItem>
            </items>
        </menu>
    </objects>
    <resources>
        <image name="NSActionTemplate" width="15" height="15"/>
        <image name="NSStopProgressFreestandingTemplate" width="15" height="15"/>
    </resources>
</document>
