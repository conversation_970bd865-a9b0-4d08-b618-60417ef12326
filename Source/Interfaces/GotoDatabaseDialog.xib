<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="16097" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="16097"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPGotoDatabaseController">
            <connections>
                <outlet property="cancelButton" destination="44U-6X-ZGG" id="dkD-wk-aid"/>
                <outlet property="databaseListView" destination="cDW-lq-Q6e" id="aXN-gQ-VPQ"/>
                <outlet property="okButton" destination="Qwd-4g-Aav" id="lhQ-gz-P3m"/>
                <outlet property="searchField" destination="7dG-5r-Xs2" id="jrs-Y3-ixn"/>
                <outlet property="window" destination="QvC-M9-y7g" id="3iD-lu-WNH"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="Go to Database" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" restorable="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="QvC-M9-y7g">
            <windowStyleMask key="styleMask" titled="YES"/>
            <rect key="contentRect" x="196" y="240" width="480" height="280"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2560" height="1417"/>
            <value key="minSize" type="size" width="480" height="280"/>
            <view key="contentView" id="EiT-Mj-1SZ">
                <rect key="frame" x="0.0" y="0.0" width="480" height="280"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Qwd-4g-Aav">
                        <rect key="frame" x="407" y="13" width="59" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="OK" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="K2E-vc-mDT">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="okClicked:" target="-2" id="yNe-4p-Qs8"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="44U-6X-ZGG">
                        <rect key="frame" x="14" y="13" width="82" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="6cJ-4a-khl">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="cancelClicked:" target="-2" id="HtW-wh-O9q"/>
                        </connections>
                    </button>
                    <searchField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="7dG-5r-Xs2">
                        <rect key="frame" x="20" y="238" width="440" height="22"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                        <searchFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" borderStyle="bezel" placeholderString="Database Name" usesSingleLineMode="YES" bezelStyle="round" sendsSearchStringImmediately="YES" id="3iD-8e-Wt2">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </searchFieldCell>
                        <connections>
                            <action selector="searchChanged:" target="-2" id="keN-UZ-6QR"/>
                            <outlet property="delegate" destination="-2" id="DIs-zq-6Cv"/>
                            <outlet property="searchMenuTemplate" destination="Ktg-oV-3Ad" id="aXK-2C-8Fh"/>
                        </connections>
                    </searchField>
                    <scrollView focusRingType="none" fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="19" horizontalPageScroll="10" verticalLineScroll="19" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LoZ-sS-bTj">
                        <rect key="frame" x="20" y="50" width="440" height="180"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <clipView key="contentView" drawsBackground="NO" id="U4W-8P-JTO">
                            <rect key="frame" x="1" y="1" width="438" height="178"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" selectionHighlightStyle="sourceList" alternatingRowBackgroundColors="YES" columnReordering="NO" columnResizing="NO" multipleSelection="NO" autosaveColumns="NO" typeSelect="NO" id="cDW-lq-Q6e">
                                    <rect key="frame" x="0.0" y="0.0" width="438" height="178"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <size key="intercellSpacing" width="3" height="2"/>
                                    <color key="backgroundColor" name="_sourceListBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                    <tableColumns>
                                        <tableColumn editable="NO" width="435" minWidth="40" maxWidth="1000" id="Ift-DE-ytF">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Database">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" refusesFirstResponder="YES" alignment="left" title="Text Cell" id="mE5-9u-hCv">
                                                <font key="font" metaFont="system"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES"/>
                                        </tableColumn>
                                    </tableColumns>
                                    <connections>
                                        <outlet property="dataSource" destination="-2" id="O6w-uT-xK7"/>
                                        <outlet property="delegate" destination="-2" id="6Ur-iZ-LRv"/>
                                    </connections>
                                </tableView>
                            </subviews>
                            <nil key="backgroundColor"/>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="H6G-xb-0Tc">
                            <rect key="frame" x="1" y="164" width="423" height="15"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="GxF-Wp-RA1">
                            <rect key="frame" x="424" y="17" width="15" height="147"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                    </scrollView>
                </subviews>
            </view>
            <point key="canvasLocation" x="139" y="147"/>
        </window>
        <menu id="Ktg-oV-3Ad" userLabel="Search Context Menu">
            <items>
                <menuItem title="Search as word" tag="1" id="ymj-Sv-49C">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="toggleWordSearch:" target="-2" id="Ue7-RW-Esm"/>
                    </connections>
                </menuItem>
            </items>
            <connections>
                <outlet property="delegate" destination="-2" id="Aa3-t8-5Q0"/>
            </connections>
            <point key="canvasLocation" x="352" y="435.5"/>
        </menu>
    </objects>
</document>
