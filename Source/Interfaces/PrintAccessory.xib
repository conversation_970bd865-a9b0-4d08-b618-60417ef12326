<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="16097" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="16097"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPPrintAccessory">
            <connections>
                <outlet property="printAccessoryView" destination="1" id="4"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <customView id="1">
            <rect key="frame" x="0.0" y="0.0" width="400" height="54"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <userGuides>
                <userLayoutGuide location="160" affinity="minX"/>
                <userLayoutGuide location="151" affinity="minX"/>
            </userGuides>
            <subviews>
                <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="2">
                    <rect key="frame" x="158" y="18" width="226" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="check" title="Print Backgrounds" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="3">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <binding destination="5" name="value" keyPath="values.PrintBackground" id="7"/>
                    </connections>
                </button>
            </subviews>
            <point key="canvasLocation" x="139" y="154"/>
        </customView>
        <userDefaultsController representsSharedInstance="YES" id="5"/>
    </objects>
</document>
