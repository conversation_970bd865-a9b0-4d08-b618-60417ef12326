<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="17506" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="17506"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPUserManager">
            <connections>
                <outlet property="addSchemaPrivButton" destination="782" id="883"/>
                <outlet property="availableController" destination="788" id="789"/>
                <outlet property="availableTableView" destination="777" id="855"/>
                <outlet property="errorsSheet" destination="983" id="993"/>
                <outlet property="errorsTextView" destination="990" id="994"/>
                <outlet property="grantedController" destination="787" id="790"/>
                <outlet property="grantedTableView" destination="770" id="854"/>
                <outlet property="maxConnectionsTextField" destination="968" id="981"/>
                <outlet property="maxQuestionsTextField" destination="971" id="982"/>
                <outlet property="maxUpdatesTextField" destination="965" id="980"/>
                <outlet property="outlineView" destination="31" id="102"/>
                <outlet property="privsSupportedByServer" destination="648" id="649"/>
                <outlet property="removeSchemaPrivButton" destination="918" id="922"/>
                <outlet property="schemasTableView" destination="748" id="827"/>
                <outlet property="splitView" destination="25" id="1000"/>
                <outlet property="tabView" destination="37" id="103"/>
                <outlet property="treeController" destination="48" id="101"/>
                <outlet property="userNameTextField" destination="112" id="893"/>
                <outlet property="window" destination="3" id="733"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="User Managment" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" deferred="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="3" userLabel="UserManagerView">
            <windowStyleMask key="styleMask" titled="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="459" y="282" width="815" height="506"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2880" height="1595"/>
            <value key="minSize" type="size" width="752" height="506"/>
            <view key="contentView" id="4">
                <rect key="frame" x="0.0" y="0.0" width="815" height="506"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <splitView fixedFrame="YES" dividerStyle="thin" vertical="YES" translatesAutoresizingMaskIntoConstraints="NO" id="25" customClass="SPSplitView">
                        <rect key="frame" x="0.0" y="0.0" width="815" height="506"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <customView fixedFrame="YES" id="26">
                                <rect key="frame" x="0.0" y="0.0" width="177" height="506"/>
                                <autoresizingMask key="autoresizingMask" heightSizable="YES"/>
                                <subviews>
                                    <scrollView fixedFrame="YES" borderType="none" autohidesScrollers="YES" horizontalLineScroll="20" horizontalPageScroll="10" verticalLineScroll="20" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="28">
                                        <rect key="frame" x="0.0" y="30" width="177" height="476"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        <clipView key="contentView" drawsBackground="NO" id="VfC-c0-tv4">
                                            <rect key="frame" x="0.0" y="0.0" width="177" height="476"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <subviews>
                                                <outlineView focusRingType="none" verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" selectionHighlightStyle="sourceList" columnReordering="NO" multipleSelection="NO" autosaveColumns="NO" rowHeight="20" headerView="i5I-fd-SyY" indentationPerLevel="14" autoresizesOutlineColumn="YES" outlineTableColumn="33" id="31" customClass="SPOutlineView">
                                                    <rect key="frame" x="0.0" y="0.0" width="177" height="448"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <size key="intercellSpacing" width="3" height="0.0"/>
                                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                                    <tableColumns>
                                                        <tableColumn identifier="NameColumn" width="145" minWidth="16" maxWidth="1000" id="33">
                                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Accounts">
                                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" white="0.*****************" alpha="1" colorSpace="calibratedWhite"/>
                                                            </tableHeaderCell>
                                                            <textFieldCell key="dataCell" lineBreakMode="truncatingTail" truncatesLastVisibleLine="YES" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="36">
                                                                <font key="font" metaFont="system"/>
                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                            </textFieldCell>
                                                            <sortDescriptor key="sortDescriptorPrototype" selector="compare:" sortKey="displayName"/>
                                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                            <connections>
                                                                <binding destination="48" name="value" keyPath="arrangedObjects.displayName" id="887">
                                                                    <dictionary key="options">
                                                                        <bool key="NSAllowsEditingMultipleValuesSelection" value="NO"/>
                                                                        <string key="NSNullPlaceholder">Anonymous</string>
                                                                        <bool key="NSValidatesImmediately" value="YES"/>
                                                                    </dictionary>
                                                                </binding>
                                                            </connections>
                                                        </tableColumn>
                                                    </tableColumns>
                                                    <connections>
                                                        <outlet property="delegate" destination="-2" id="168"/>
                                                        <outlet property="menu" destination="894" id="902"/>
                                                    </connections>
                                                </outlineView>
                                            </subviews>
                                            <nil key="backgroundColor"/>
                                        </clipView>
                                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="YES" id="30">
                                            <rect key="frame" x="0.0" y="462" width="171" height="14"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="NO" id="29">
                                            <rect key="frame" x="183" y="17" width="11" height="456"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <tableHeaderView key="headerView" wantsLayer="YES" id="i5I-fd-SyY">
                                            <rect key="frame" x="0.0" y="0.0" width="177" height="28"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableHeaderView>
                                    </scrollView>
                                    <popUpButton fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1006">
                                        <rect key="frame" x="60" y="0.0" width="35" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <popUpButtonCell key="cell" type="bevel" bezelStyle="regularSquare" imagePosition="only" alignment="center" alternateImage="NSActionTemplate" lineBreakMode="truncatingTail" state="on" inset="2" pullsDown="YES" selectedItem="1019" id="1011">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="menu"/>
                                            <menu key="menu" title="OtherViews" id="1012">
                                                <items>
                                                    <menuItem state="on" image="NSActionTemplate" hidden="YES" id="1019"/>
                                                    <menuItem title="Add Host" id="1029">
                                                        <connections>
                                                            <action selector="addHost:" target="-2" id="1034"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Remove Host" id="1030">
                                                        <connections>
                                                            <action selector="removeHost:" target="-2" id="1035"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem isSeparatorItem="YES" id="1031"/>
                                                    <menuItem title="Refresh" id="1032">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="refresh:" target="-2" id="1033"/>
                                                        </connections>
                                                    </menuItem>
                                                </items>
                                            </menu>
                                        </popUpButtonCell>
                                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                    </popUpButton>
                                    <button toolTip="Remove User" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1007">
                                        <rect key="frame" x="30" y="0.0" width="25" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSRemoveTemplate" imagePosition="only" alignment="center" alternateImage="NSRemoveTemplate" inset="2" id="1010">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                        <connections>
                                            <action selector="removeUser:" target="-2" id="1025"/>
                                            <binding destination="48" name="enabled" keyPath="selection.parent" id="1028">
                                                <dictionary key="options">
                                                    <string key="NSValueTransformerName">NSIsNil</string>
                                                </dictionary>
                                            </binding>
                                        </connections>
                                    </button>
                                    <button toolTip="Add User" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1008">
                                        <rect key="frame" x="0.0" y="0.0" width="25" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSAddTemplate" imagePosition="only" alignment="center" alternateImage="NSAddTemplate" inset="2" id="1009">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="system"/>
                                        </buttonCell>
                                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                        <connections>
                                            <action selector="addUser:" target="-2" id="1021"/>
                                            <binding destination="48" name="enabled" keyPath="selection.parent" id="1024">
                                                <dictionary key="options">
                                                    <string key="NSValueTransformerName">NSIsNil</string>
                                                </dictionary>
                                            </binding>
                                        </connections>
                                    </button>
                                    <imageView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1003">
                                        <rect key="frame" x="152" y="0.0" width="25" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="button_bar_handleTemplate" id="1004"/>
                                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                    </imageView>
                                </subviews>
                            </customView>
                            <customView fixedFrame="YES" id="27">
                                <rect key="frame" x="178" y="0.0" width="637" height="506"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="124">
                                        <rect key="frame" x="329" y="12" width="147" height="32"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="125">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="system"/>
                                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                                        </buttonCell>
                                        <connections>
                                            <action selector="doCancel:" target="-2" id="400"/>
                                        </connections>
                                    </button>
                                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="122">
                                        <rect key="frame" x="476" y="12" width="147" height="32"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="push" title="Apply" bezelStyle="rounded" alignment="center" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="123">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="system"/>
                                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                                        </buttonCell>
                                        <connections>
                                            <action selector="doApply:" target="-2" id="399"/>
                                        </connections>
                                    </button>
                                    <tabView fixedFrame="YES" allowsTruncatedLabels="NO" initialItem="38" translatesAutoresizingMaskIntoConstraints="NO" id="37">
                                        <rect key="frame" x="13" y="40" width="611" height="460"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        <font key="font" metaFont="system"/>
                                        <tabViewItems>
                                            <tabViewItem label="General" identifier="General" id="38">
                                                <view key="view" id="41">
                                                    <rect key="frame" x="10" y="33" width="591" height="414"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <subviews>
                                                        <box autoresizesSubviews="NO" fixedFrame="YES" borderType="line" title="Login Information" translatesAutoresizingMaskIntoConstraints="NO" id="44">
                                                            <rect key="frame" x="112" y="175" width="366" height="92"/>
                                                            <autoresizingMask key="autoresizingMask" flexibleMinX="YES" widthSizable="YES" flexibleMaxX="YES" flexibleMinY="YES" flexibleMaxY="YES"/>
                                                            <view key="contentView" id="1ol-rC-cAr">
                                                                <rect key="frame" x="3" y="3" width="360" height="74"/>
                                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                <subviews>
                                                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="110">
                                                                        <rect key="frame" x="3" y="44" width="134" height="17"/>
                                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Username:" id="111">
                                                                            <font key="font" metaFont="system"/>
                                                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="114">
                                                                        <rect key="frame" x="3" y="14" width="134" height="17"/>
                                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Password:" id="115">
                                                                            <font key="font" metaFont="system"/>
                                                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="112">
                                                                        <rect key="frame" x="142" y="42" width="200" height="22"/>
                                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="113">
                                                                            <font key="font" metaFont="system"/>
                                                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                        <connections>
                                                                            <binding destination="48" name="value" keyPath="selection.displayName" id="892">
                                                                                <dictionary key="options">
                                                                                    <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                    <string key="NSNoSelectionPlaceholder">No Selection</string>
                                                                                    <string key="NSNotApplicablePlaceholder">User Not Selected</string>
                                                                                    <string key="NSNullPlaceholder">Anonymous</string>
                                                                                    <bool key="NSRaisesForNotApplicableKeys" value="NO"/>
                                                                                    <bool key="NSValidatesImmediately" value="YES"/>
                                                                                </dictionary>
                                                                            </binding>
                                                                        </connections>
                                                                    </textField>
                                                                    <secureTextField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="116">
                                                                        <rect key="frame" x="142" y="12" width="200" height="22"/>
                                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                                                        <secureTextFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="117">
                                                                            <font key="font" metaFont="system"/>
                                                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                            <allowedInputSourceLocales>
                                                                                <string>NSAllRomanInputSourcesLocaleIdentifier</string>
                                                                            </allowedInputSourceLocales>
                                                                        </secureTextFieldCell>
                                                                        <connections>
                                                                            <binding destination="48" name="value" keyPath="selection.password" id="736">
                                                                                <dictionary key="options">
                                                                                    <bool key="NSConditionallySetsHidden" value="YES"/>
                                                                                    <string key="NSNoSelectionPlaceholder">No Selection</string>
                                                                                    <string key="NSNullPlaceholder">Empty Password</string>
                                                                                    <bool key="NSRaisesForNotApplicableKeys" value="NO"/>
                                                                                    <bool key="NSValidatesImmediately" value="YES"/>
                                                                                </dictionary>
                                                                            </binding>
                                                                        </connections>
                                                                    </secureTextField>
                                                                </subviews>
                                                            </view>
                                                        </box>
                                                    </subviews>
                                                </view>
                                            </tabViewItem>
                                            <tabViewItem label="Global Privileges" identifier="Global Privileges" id="189">
                                                <view key="view" id="190">
                                                    <rect key="frame" x="10" y="33" width="591" height="414"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <subviews>
                                                        <customView translatesAutoresizingMaskIntoConstraints="NO" id="592">
                                                            <rect key="frame" x="8" y="12" width="574" height="399"/>
                                                            <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                            <subviews>
                                                                <box autoresizesSubviews="NO" borderType="line" title="Views and Procedures" translatesAutoresizingMaskIntoConstraints="NO" id="452">
                                                                    <rect key="frame" x="273" y="253" width="293" height="134"/>
                                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                                                    <view key="contentView" id="kIh-Ff-5kV">
                                                                        <rect key="frame" x="3" y="3" width="287" height="116"/>
                                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                        <subviews>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="473">
                                                                                <rect key="frame" x="16" y="90" width="259" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Create View" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="474">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="create_view_priv" id="705">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.create_view_priv" id="525"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="475">
                                                                                <rect key="frame" x="16" y="70" width="259" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Show View" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="476">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="48" name="value" keyPath="selection.show_view_priv" id="526"/>
                                                                                    <binding destination="648" name="enabled" keyPath="show_view_priv" id="707">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="477">
                                                                                <rect key="frame" x="16" y="50" width="259" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Create Routine" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="478">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="create_routine_priv" id="709">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.create_routine_priv" id="527"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="479">
                                                                                <rect key="frame" x="16" y="30" width="259" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Alter Routine" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="480">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="alter_routine_priv" id="711">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.alter_routine_priv" id="528"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="481">
                                                                                <rect key="frame" x="16" y="10" width="259" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Execute" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="482">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="execute_priv" id="713">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.execute_priv" id="529"/>
                                                                                </connections>
                                                                            </button>
                                                                        </subviews>
                                                                    </view>
                                                                </box>
                                                                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="511">
                                                                    <rect key="frame" x="66" y="12" width="157" height="32"/>
                                                                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                                                    <buttonCell key="cell" type="push" title="Uncheck All" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="512">
                                                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                                        <font key="font" metaFont="system"/>
                                                                    </buttonCell>
                                                                    <connections>
                                                                        <action selector="uncheckAllPrivileges:" target="-2" id="732"/>
                                                                    </connections>
                                                                </button>
                                                                <box autoresizesSubviews="NO" borderType="line" title="Database and Tables" translatesAutoresizingMaskIntoConstraints="NO" id="451">
                                                                    <rect key="frame" x="17" y="153" width="254" height="234"/>
                                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                                                    <view key="contentView" id="BCE-hZ-s0G">
                                                                        <rect key="frame" x="3" y="3" width="248" height="216"/>
                                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                        <subviews>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="455">
                                                                                <rect key="frame" x="16" y="190" width="220" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Select" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="456">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="select_priv" id="653">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.select_priv" id="514"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="457">
                                                                                <rect key="frame" x="16" y="170" width="220" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Insert" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="458">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="insert_priv" id="657">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.insert_priv" id="516"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="459">
                                                                                <rect key="frame" x="16" y="150" width="220" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Update" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="460">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="48" name="value" keyPath="selection.update_priv" id="518"/>
                                                                                    <binding destination="648" name="enabled" keyPath="update_priv" id="661">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="461">
                                                                                <rect key="frame" x="16" y="130" width="220" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Delete" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="462">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="delete_priv" id="665">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.delete_priv" id="519"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="463">
                                                                                <rect key="frame" x="16" y="110" width="220" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="References" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="464">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="references_priv" id="669">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.references_priv" id="520"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="465">
                                                                                <rect key="frame" x="16" y="90" width="220" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Create" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="466">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="create_priv" id="673">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.create_priv" id="521"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="467">
                                                                                <rect key="frame" x="16" y="70" width="220" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Drop" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="468">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="drop_priv" id="677">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.drop_priv" id="522"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="469">
                                                                                <rect key="frame" x="16" y="50" width="220" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Alter" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="470">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="alter_priv" id="679">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.alter_priv" id="523"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="471">
                                                                                <rect key="frame" x="16" y="30" width="220" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Index" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="472">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="index_priv" id="683">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.index_priv" id="524"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="719">
                                                                                <rect key="frame" x="16" y="10" width="220" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Trigger" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="720">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="48" name="value" keyPath="selection.trigger_priv" id="723"/>
                                                                                    <binding destination="648" name="enabled" keyPath="trigger_priv" id="724">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                </connections>
                                                                            </button>
                                                                        </subviews>
                                                                    </view>
                                                                </box>
                                                                <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="509">
                                                                    <rect key="frame" x="65" y="44" width="157" height="32"/>
                                                                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                                                    <buttonCell key="cell" type="push" title="Check All" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="510">
                                                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                                        <font key="font" metaFont="system"/>
                                                                    </buttonCell>
                                                                    <connections>
                                                                        <action selector="checkAllPrivileges:" target="-2" id="731"/>
                                                                    </connections>
                                                                </button>
                                                                <box autoresizesSubviews="NO" borderType="line" title="Administration" translatesAutoresizingMaskIntoConstraints="NO" id="453">
                                                                    <rect key="frame" x="273" y="15" width="293" height="234"/>
                                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                                                    <view key="contentView" id="F8k-eh-RHk">
                                                                        <rect key="frame" x="3" y="3" width="287" height="216"/>
                                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                        <subviews>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="487">
                                                                                <rect key="frame" x="16" y="190" width="112" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Reload" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="488">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="reload_priv" id="685">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.reload_priv" id="530"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="489">
                                                                                <rect key="frame" x="16" y="170" width="112" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Shutdown" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="490">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="48" name="value" keyPath="selection.shutdown_priv" id="531"/>
                                                                                    <binding destination="648" name="enabled" keyPath="shutdown_priv" id="687">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="491">
                                                                                <rect key="frame" x="16" y="150" width="259" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="File" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="492">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="file_priv" id="689">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.file_priv" id="532"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="493">
                                                                                <rect key="frame" x="16" y="130" width="259" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Process" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="494">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="process_priv" id="691">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.process_priv" id="533"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="495">
                                                                                <rect key="frame" x="16" y="110" width="259" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Super" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="496">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="48" name="value" keyPath="selection.super_priv" id="534"/>
                                                                                    <binding destination="648" name="enabled" keyPath="super_priv" id="693">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="497">
                                                                                <rect key="frame" x="16" y="90" width="259" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Create Temp Table" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="498">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="create_temporary_tables_priv" id="718">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.create_temporary_tables_priv" id="606"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="499">
                                                                                <rect key="frame" x="16" y="70" width="259" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Lock Tables" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="500">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="lock_tables_priv" id="697">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.lock_tables_priv" id="537"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="501">
                                                                                <rect key="frame" x="16" y="50" width="259" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Show Databases" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="502">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="48" name="value" keyPath="selection.show_databases_priv" id="538"/>
                                                                                    <binding destination="648" name="enabled" keyPath="show_databases_priv" id="699">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="503">
                                                                                <rect key="frame" x="16" y="30" width="259" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Create User" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="504">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="create_user_priv" id="701">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.create_user_priv" id="539"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="505">
                                                                                <rect key="frame" x="16" y="10" width="259" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Grant" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="506">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="grant_option_priv" id="703">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.grant_option_priv" id="540"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="725">
                                                                                <rect key="frame" x="132" y="190" width="143" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Event" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="726">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="event_priv" id="729">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.event_priv" id="730"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="ecH-YW-CVE">
                                                                                <rect key="frame" x="132" y="170" width="143" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Create Tablespace" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="ftz-WX-x7r">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="create_tablespace_priv" id="Pyl-hz-npp">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.create_tablespace_priv" id="6GM-JO-bwm"/>
                                                                                </connections>
                                                                            </button>
                                                                        </subviews>
                                                                    </view>
                                                                </box>
                                                                <box autoresizesSubviews="NO" borderType="line" title="Replication" translatesAutoresizingMaskIntoConstraints="NO" id="454">
                                                                    <rect key="frame" x="17" y="75" width="255" height="74"/>
                                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                                                    <view key="contentView" id="mKr-0m-x8O">
                                                                        <rect key="frame" x="3" y="3" width="249" height="56"/>
                                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                        <subviews>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="483">
                                                                                <rect key="frame" x="16" y="30" width="221" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Replication Client" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="484">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="replication_client_priv" id="715">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.replication_client_priv" id="541"/>
                                                                                </connections>
                                                                            </button>
                                                                            <button translatesAutoresizingMaskIntoConstraints="NO" id="485">
                                                                                <rect key="frame" x="16" y="10" width="221" height="18"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                <buttonCell key="cell" type="check" title="Replication Slave" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="486">
                                                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                    <font key="font" metaFont="system"/>
                                                                                </buttonCell>
                                                                                <connections>
                                                                                    <binding destination="648" name="enabled" keyPath="replication_slave_priv" id="717">
                                                                                        <dictionary key="options">
                                                                                            <string key="NSValueTransformerName">NSIsNotNil</string>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                    <binding destination="48" name="value" keyPath="selection.replication_slave_priv" id="542"/>
                                                                                </connections>
                                                                            </button>
                                                                        </subviews>
                                                                    </view>
                                                                </box>
                                                            </subviews>
                                                        </customView>
                                                    </subviews>
                                                </view>
                                            </tabViewItem>
                                            <tabViewItem label="Schema Privileges" identifier="Schema Privileges" id="743">
                                                <view key="view" id="744">
                                                    <rect key="frame" x="10" y="33" width="591" height="414"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <subviews>
                                                        <scrollView autohidesScrollers="YES" horizontalLineScroll="16" horizontalPageScroll="10" verticalLineScroll="16" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="745">
                                                            <rect key="frame" x="17" y="17" width="165" height="381"/>
                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                            <clipView key="contentView" id="RTM-KZ-MAG">
                                                                <rect key="frame" x="1" y="1" width="163" height="379"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                                <subviews>
                                                                    <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" columnSelection="YES" multipleSelection="NO" autosaveColumns="NO" rowHeight="14" headerView="749" id="748">
                                                                        <rect key="frame" x="0.0" y="0.0" width="163" height="362"/>
                                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                        <size key="intercellSpacing" width="3" height="2"/>
                                                                        <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                                                        <tableColumns>
                                                                            <tableColumn identifier="Schemas" editable="NO" width="131" minWidth="40" maxWidth="1000" id="750">
                                                                                <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Schemas">
                                                                                    <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                                    <color key="backgroundColor" white="0.*****************" alpha="1" colorSpace="calibratedWhite"/>
                                                                                </tableHeaderCell>
                                                                                <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="753">
                                                                                    <font key="font" metaFont="message" size="11"/>
                                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                </textFieldCell>
                                                                                <tableColumnResizingMask key="resizingMask" resizeWithTable="YES"/>
                                                                            </tableColumn>
                                                                        </tableColumns>
                                                                        <connections>
                                                                            <outlet property="dataSource" destination="-2" id="1036"/>
                                                                            <outlet property="delegate" destination="-2" id="826"/>
                                                                        </connections>
                                                                    </tableView>
                                                                </subviews>
                                                            </clipView>
                                                            <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="YES" id="747">
                                                                <rect key="frame" x="1" y="369" width="109" height="11"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                            </scroller>
                                                            <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" controlSize="small" horizontal="NO" id="746">
                                                                <rect key="frame" x="110" y="17" width="11" height="352"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                            </scroller>
                                                            <tableHeaderView key="headerView" id="749">
                                                                <rect key="frame" x="0.0" y="0.0" width="163" height="17"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                            </tableHeaderView>
                                                        </scrollView>
                                                        <scrollView autohidesScrollers="YES" horizontalLineScroll="16" horizontalPageScroll="10" verticalLineScroll="16" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="768">
                                                            <rect key="frame" x="190" y="17" width="165" height="381"/>
                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                            <clipView key="contentView" id="ViA-Kx-MtG">
                                                                <rect key="frame" x="1" y="1" width="163" height="379"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                                <subviews>
                                                                    <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" columnReordering="NO" columnSelection="YES" autosaveColumns="NO" rowHeight="14" headerView="867" id="770">
                                                                        <rect key="frame" x="0.0" y="0.0" width="163" height="362"/>
                                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                        <size key="intercellSpacing" width="3" height="2"/>
                                                                        <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                                                        <tableColumns>
                                                                            <tableColumn identifier="Granted Privilege" editable="NO" width="131" minWidth="40" maxWidth="1000" id="773">
                                                                                <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Granted Privileges">
                                                                                    <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                                    <color key="backgroundColor" white="0.*****************" alpha="1" colorSpace="calibratedWhite"/>
                                                                                </tableHeaderCell>
                                                                                <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="774">
                                                                                    <font key="font" metaFont="message" size="11"/>
                                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                </textFieldCell>
                                                                                <tableColumnResizingMask key="resizingMask" resizeWithTable="YES"/>
                                                                                <connections>
                                                                                    <binding destination="787" name="value" keyPath="arrangedObjects.displayName" id="861">
                                                                                        <dictionary key="options">
                                                                                            <bool key="NSConditionallySetsEditable" value="YES"/>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                </connections>
                                                                            </tableColumn>
                                                                        </tableColumns>
                                                                        <connections>
                                                                            <outlet property="dataSource" destination="787" id="848"/>
                                                                            <outlet property="delegate" destination="-2" id="849"/>
                                                                        </connections>
                                                                    </tableView>
                                                                </subviews>
                                                            </clipView>
                                                            <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="YES" id="771">
                                                                <rect key="frame" x="1" y="369" width="109" height="11"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                            </scroller>
                                                            <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="NO" id="772">
                                                                <rect key="frame" x="110" y="17" width="11" height="352"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                            </scroller>
                                                            <tableHeaderView key="headerView" id="867">
                                                                <rect key="frame" x="0.0" y="0.0" width="163" height="17"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                            </tableHeaderView>
                                                        </scrollView>
                                                        <scrollView autohidesScrollers="YES" horizontalLineScroll="16" horizontalPageScroll="10" verticalLineScroll="16" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="775">
                                                            <rect key="frame" x="409" y="17" width="165" height="381"/>
                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                            <clipView key="contentView" id="87D-Qy-CRv">
                                                                <rect key="frame" x="1" y="1" width="163" height="379"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                                <subviews>
                                                                    <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" columnReordering="NO" columnSelection="YES" autosaveColumns="NO" enabled="NO" rowHeight="14" headerView="776" id="777">
                                                                        <rect key="frame" x="0.0" y="0.0" width="163" height="362"/>
                                                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                        <size key="intercellSpacing" width="3" height="2"/>
                                                                        <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                                                        <tableColumns>
                                                                            <tableColumn identifier="Available Privilige" editable="NO" width="131" minWidth="40" maxWidth="1000" id="780">
                                                                                <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" enabled="NO" borderStyle="border" alignment="left" title="Available Privileges">
                                                                                    <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                                    <color key="backgroundColor" white="0.*****************" alpha="1" colorSpace="calibratedWhite"/>
                                                                                </tableHeaderCell>
                                                                                <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" enabled="NO" alignment="left" title="Text Cell" id="781">
                                                                                    <font key="font" metaFont="message" size="11"/>
                                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                </textFieldCell>
                                                                                <tableColumnResizingMask key="resizingMask" resizeWithTable="YES"/>
                                                                                <connections>
                                                                                    <binding destination="788" name="value" keyPath="arrangedObjects.displayName" id="866">
                                                                                        <dictionary key="options">
                                                                                            <bool key="NSConditionallySetsEditable" value="YES"/>
                                                                                        </dictionary>
                                                                                    </binding>
                                                                                </connections>
                                                                            </tableColumn>
                                                                        </tableColumns>
                                                                        <connections>
                                                                            <outlet property="dataSource" destination="788" id="850"/>
                                                                            <outlet property="delegate" destination="-2" id="851"/>
                                                                        </connections>
                                                                    </tableView>
                                                                </subviews>
                                                            </clipView>
                                                            <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="YES" id="778">
                                                                <rect key="frame" x="1" y="369" width="109" height="11"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                            </scroller>
                                                            <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="NO" id="779">
                                                                <rect key="frame" x="110" y="17" width="11" height="352"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                            </scroller>
                                                            <tableHeaderView key="headerView" id="776">
                                                                <rect key="frame" x="0.0" y="0.0" width="163" height="17"/>
                                                                <autoresizingMask key="autoresizingMask"/>
                                                            </tableHeaderView>
                                                        </scrollView>
                                                        <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="782">
                                                            <rect key="frame" x="363" y="206" width="31" height="32"/>
                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                            <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSLeftFacingTriangleTemplate" imagePosition="overlaps" alignment="center" enabled="NO" borderStyle="border" inset="2" id="783">
                                                                <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                                <font key="font" metaFont="system"/>
                                                            </buttonCell>
                                                            <connections>
                                                                <action selector="addSchemaPriv:" target="-2" id="792"/>
                                                            </connections>
                                                        </button>
                                                        <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="918">
                                                            <rect key="frame" x="363" y="177" width="31" height="32"/>
                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                            <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSRightFacingTriangleTemplate" imagePosition="overlaps" alignment="center" enabled="NO" borderStyle="border" inset="2" id="919">
                                                                <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                                <font key="font" metaFont="system"/>
                                                            </buttonCell>
                                                            <connections>
                                                                <action selector="removeSchemaPriv:" target="-2" id="921"/>
                                                            </connections>
                                                        </button>
                                                    </subviews>
                                                </view>
                                            </tabViewItem>
                                            <tabViewItem label="Resources" identifier="Resources" id="42">
                                                <view key="view" id="43">
                                                    <rect key="frame" x="10" y="33" width="591" height="414"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <subviews>
                                                        <box borderType="line" title="Resource Limits" translatesAutoresizingMaskIntoConstraints="NO" id="593">
                                                            <rect key="frame" x="119" y="140" width="353" height="129"/>
                                                            <autoresizingMask key="autoresizingMask" flexibleMinX="YES" widthSizable="YES" flexibleMaxX="YES" flexibleMinY="YES" flexibleMaxY="YES"/>
                                                            <view key="contentView" id="4X9-CI-2WL">
                                                                <rect key="frame" x="3" y="3" width="347" height="111"/>
                                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                <subviews>
                                                                    <textField verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="953">
                                                                        <rect key="frame" x="15" y="82" width="164" height="17"/>
                                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Max Updates:" id="954">
                                                                            <font key="font" metaFont="system"/>
                                                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <textField verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="955">
                                                                        <rect key="frame" x="15" y="47" width="164" height="17"/>
                                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Max Connections:" id="956">
                                                                            <font key="font" metaFont="system"/>
                                                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <textField verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="957">
                                                                        <rect key="frame" x="15" y="15" width="164" height="17"/>
                                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Max Questions:" id="958">
                                                                            <font key="font" metaFont="system"/>
                                                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                    </textField>
                                                                    <textField verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="965">
                                                                        <rect key="frame" x="184" y="79" width="149" height="22"/>
                                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="966">
                                                                            <numberFormatter key="formatter" formatterBehavior="custom10_4" numberStyle="decimal" allowsFloats="NO" usesGroupingSeparator="NO" minimumIntegerDigits="1" maximumIntegerDigits="309" maximumFractionDigits="3" id="967">
                                                                                <real key="minimum" value="0.0"/>
                                                                            </numberFormatter>
                                                                            <font key="font" metaFont="system"/>
                                                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                        <connections>
                                                                            <binding destination="48" name="value" keyPath="selection.max_updates" id="975"/>
                                                                        </connections>
                                                                    </textField>
                                                                    <textField verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="968">
                                                                        <rect key="frame" x="184" y="44" width="149" height="22"/>
                                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="969">
                                                                            <numberFormatter key="formatter" formatterBehavior="custom10_4" numberStyle="decimal" allowsFloats="NO" usesGroupingSeparator="NO" minimumIntegerDigits="1" maximumIntegerDigits="309" maximumFractionDigits="3" id="970">
                                                                                <real key="minimum" value="0.0"/>
                                                                            </numberFormatter>
                                                                            <font key="font" metaFont="system"/>
                                                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                        <connections>
                                                                            <binding destination="48" name="value" keyPath="selection.max_connections" id="977"/>
                                                                        </connections>
                                                                    </textField>
                                                                    <textField verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="971">
                                                                        <rect key="frame" x="184" y="12" width="149" height="22"/>
                                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="972">
                                                                            <numberFormatter key="formatter" formatterBehavior="custom10_4" numberStyle="decimal" allowsFloats="NO" usesGroupingSeparator="NO" minimumIntegerDigits="1" maximumIntegerDigits="309" maximumFractionDigits="3" id="973">
                                                                                <real key="minimum" value="0.0"/>
                                                                            </numberFormatter>
                                                                            <font key="font" metaFont="system"/>
                                                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                        </textFieldCell>
                                                                        <connections>
                                                                            <binding destination="48" name="value" keyPath="selection.max_questions" id="979"/>
                                                                        </connections>
                                                                    </textField>
                                                                </subviews>
                                                            </view>
                                                        </box>
                                                    </subviews>
                                                </view>
                                            </tabViewItem>
                                        </tabViewItems>
                                        <connections>
                                            <outlet property="delegate" destination="-2" id="924"/>
                                        </connections>
                                    </tabView>
                                </subviews>
                            </customView>
                        </subviews>
                        <holdingPriorities>
                            <real value="250"/>
                            <real value="250"/>
                        </holdingPriorities>
                        <connections>
                            <outlet property="additionalDragHandleView" destination="1003" id="1005"/>
                            <outlet property="delegate" destination="-2" id="735"/>
                        </connections>
                    </splitView>
                </subviews>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="734"/>
            </connections>
            <point key="canvasLocation" x="138.5" y="147"/>
        </window>
        <treeController mode="entity" entityName="SPUser" fetchPredicateFormat="parent == nil" automaticallyPreparesContent="YES" childrenKeyPath="children" id="48">
            <declaredKeys>
                <string>username</string>
                <string>children</string>
                <string>host</string>
                <string>itemTitle</string>
                <string>password</string>
                <string>globalPrivileges</string>
                <string>globalPrivileges.Select_priv</string>
                <string>globalPrivileges.Insert_priv</string>
                <string>globalPrivileges.Update_priv</string>
                <string>globalPrivileges.Delete_priv</string>
                <string>globalPrivileges.Create_priv</string>
                <string>globalPrivileges.Drop_priv</string>
                <string>globalPrivileges.Reload_priv</string>
                <string>globalPrivileges.Shutdown_priv</string>
                <string>globalPrivileges.Process_priv</string>
                <string>globalPrivileges.File_priv</string>
                <string>globalPrivileges.Grant_priv</string>
                <string>globalPrivileges.References_priv</string>
                <string>globalPrivileges.Index_priv</string>
                <string>globalPrivileges.Alter_priv</string>
                <string>globalPrivileges.Show_db_priv</string>
                <string>globalPrivileges.Super_priv</string>
                <string>globalPrivileges.Create_tmp_table_priv</string>
                <string>globalPrivileges.Lock_tables_priv</string>
                <string>globalPrivileges.Execute_priv</string>
                <string>globalPrivileges.Repl_slav_priv</string>
                <string>globalPrivileges.Repl_client_priv</string>
                <string>globalPrivileges.Create_view_priv</string>
                <string>globalPrivileges.Show_view_priv</string>
                <string>globalPrivileges.Create_routine_priv</string>
                <string>globalPrivileges.Alter_routine_priv</string>
                <string>globalPrivileges.Create_user_priv</string>
                <string>globalPrivileges.Event_pri</string>
                <string>user</string>
                <string>content</string>
                <string>contents</string>
                <string>hosts.Select_priv</string>
                <string>hosts.Insert_priv</string>
                <string>displayName</string>
                <string>create_priv</string>
                <string>delete_priv</string>
                <string>drop_priv</string>
                <string>insert_priv</string>
                <string>reload_priv</string>
                <string>select_priv</string>
                <string>update_priv</string>
                <string>maxUserConnections</string>
                <string>maxConnections</string>
                <string>maxQuestions</string>
                <string>max_user_connections</string>
                <string>max_connections</string>
                <string>max_questions</string>
                <string>references_priv</string>
                <string>alter_priv</string>
                <string>index_priv</string>
                <string>create_view_priv</string>
                <string>show_view_priv</string>
                <string>create_routine_priv</string>
                <string>alter_routine_priv</string>
                <string>execute_priv</string>
                <string>shutdown_priv</string>
                <string>file_priv</string>
                <string>process_priv</string>
                <string>super_priv</string>
                <string>create_temporary_table_priv</string>
                <string>lock_table_priv</string>
                <string>lock_tables_priv</string>
                <string>show_databases_priv</string>
                <string>create_user_priv</string>
                <string>grant_option_priv</string>
                <string>replication_client_priv</string>
                <string>replication_slave_priv</string>
                <string>parent</string>
                <string>create_temporary_tables_priv</string>
                <string>select_priv.optional</string>
                <string>insert_priv.optional</string>
                <string>insert_priv.isOptional</string>
                <string>select_priv.isOptional</string>
                <string>trigger_priv</string>
                <string>event_priv</string>
                <string>name</string>
                <string>max_updates</string>
            </declaredKeys>
            <connections>
                <binding destination="-2" name="managedObjectContext" keyPath="managedObjectContext" id="355"/>
                <binding destination="-2" name="sortDescriptors" keyPath="treeSortDescriptors" id="740"/>
            </connections>
        </treeController>
        <customObject id="648" userLabel="SupportedPrivileges" customClass="NSMutableDictionary"/>
        <arrayController preservesSelection="NO" selectsInsertedObjects="NO" avoidsEmptySelection="NO" automaticallyRearrangesObjects="YES" id="787" userLabel="GrantedPrivs">
            <declaredKeys>
                <string>displayName</string>
            </declaredKeys>
            <connections>
                <binding destination="-2" name="contentArray" keyPath="grantedSchemaPrivs" id="929">
                    <dictionary key="options">
                        <bool key="NSDeletesObjectsOnRemove" value="YES"/>
                    </dictionary>
                </binding>
            </connections>
        </arrayController>
        <arrayController automaticallyPreparesContent="YES" preservesSelection="NO" selectsInsertedObjects="NO" avoidsEmptySelection="NO" automaticallyRearrangesObjects="YES" id="788" userLabel="AvailablePrivs">
            <declaredKeys>
                <string>displayName</string>
            </declaredKeys>
            <connections>
                <binding destination="-2" name="contentArray" keyPath="availablePrivs" id="821"/>
            </connections>
        </arrayController>
        <menu id="894" userLabel="Context Menu">
            <items>
                <menuItem title="Remove User" id="896">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="removeUser:" target="-2" id="905"/>
                        <binding destination="48" name="enabled" keyPath="selection.parent" id="913">
                            <dictionary key="options">
                                <string key="NSValueTransformerName">NSIsNil</string>
                            </dictionary>
                        </binding>
                    </connections>
                </menuItem>
                <menuItem title="Remove Host" id="899">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="removeHost:" target="-2" id="907"/>
                    </connections>
                </menuItem>
                <menuItem isSeparatorItem="YES" id="900"/>
                <menuItem title="Refresh" id="901">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="refresh:" target="-2" id="903"/>
                    </connections>
                </menuItem>
            </items>
            <connections>
                <outlet property="delegate" destination="-2" id="914"/>
            </connections>
        </menu>
        <window title="Errors Sheet" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="983" userLabel="Errors Sheet">
            <windowStyleMask key="styleMask" titled="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="157" y="161" width="447" height="300"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2880" height="1595"/>
            <value key="minSize" type="size" width="447" height="300"/>
            <view key="contentView" id="984">
                <rect key="frame" x="0.0" y="0.0" width="447" height="300"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="409" translatesAutoresizingMaskIntoConstraints="NO" id="985">
                        <rect key="frame" x="17" y="186" width="413" height="94"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" controlSize="mini" sendsActionOnEndEditing="YES" id="986">
                            <font key="font" metaFont="system"/>
                            <string key="title">Errors occurred when applying your changes to the server.

Some changes may have already been applied; please review the errors below before proceeding.</string>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="987">
                        <rect key="frame" x="20" y="61" width="407" height="117"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <clipView key="contentView" drawsBackground="NO" id="LJe-rd-bC0">
                            <rect key="frame" x="1" y="1" width="405" height="115"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <subviews>
                                <textView editable="NO" importsGraphics="NO" verticallyResizable="YES" allowsNonContiguousLayout="YES" id="990">
                                    <rect key="frame" x="0.0" y="0.0" width="405" height="115"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <size key="minSize" width="405" height="115"/>
                                    <size key="maxSize" width="463" height="10000000"/>
                                    <color key="insertionPointColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                </textView>
                            </subviews>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="989">
                            <rect key="frame" x="-100" y="-100" width="87" height="18"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="NO" id="988">
                            <rect key="frame" x="424" y="1" width="15" height="257"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                    </scrollView>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="995">
                        <rect key="frame" x="308" y="13" width="125" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="OK" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="996">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeErrorsSheet:" target="-2" id="998"/>
                        </connections>
                    </button>
                </subviews>
            </view>
        </window>
    </objects>
    <resources>
        <image name="NSActionTemplate" width="15" height="15"/>
        <image name="NSAddTemplate" width="14" height="13"/>
        <image name="NSLeftFacingTriangleTemplate" width="10" height="14"/>
        <image name="NSRemoveTemplate" width="14" height="4"/>
        <image name="NSRightFacingTriangleTemplate" width="10" height="14"/>
        <image name="button_bar_handleTemplate" width="6" height="10"/>
    </resources>
</document>
