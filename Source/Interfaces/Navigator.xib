<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="17701" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="17701"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPNavigatorController">
            <connections>
                <outlet property="infoTable" destination="137" id="145"/>
                <outlet property="outlineSchema2" destination="153" id="164"/>
                <outlet property="schemaStatusSplitView" destination="13" id="133"/>
                <outlet property="searchField" destination="8" id="106"/>
                <outlet property="syncButton" destination="108" id="110"/>
                <outlet property="window" destination="3" id="53"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="Navigator" autorecalculatesKeyViewLoop="NO" hidesOnDeactivate="YES" visibleAtLaunch="NO" frameAutosaveName="" animationBehavior="default" id="3" userLabel="Navigator Window" customClass="NSPanel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="131" y="70" width="395" height="359"/>
            <rect key="screenRect" x="0.0" y="0.0" width="3008" height="1667"/>
            <value key="minSize" type="size" width="200" height="200"/>
            <view key="contentView" id="4">
                <rect key="frame" x="0.0" y="0.0" width="395" height="359"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="146" userLabel="Apple F Dummy Button">
                        <rect key="frame" x="149" y="163" width="96" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="push" title="Button" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="147">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent">f</string>
                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                        </buttonCell>
                        <connections>
                            <action selector="selectText:" target="8" id="149"/>
                        </connections>
                    </button>
                    <searchField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="8">
                        <rect key="frame" x="20" y="336" width="174" height="19"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <searchFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" borderStyle="bezel" focusRingType="none" usesSingleLineMode="YES" bezelStyle="round" id="9">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </searchFieldCell>
                        <connections>
                            <action selector="filterTree:" target="-2" id="107"/>
                            <outlet property="nextKeyView" destination="108" id="124"/>
                        </connections>
                    </searchField>
                    <splitView fixedFrame="YES" autosaveName="SPNavigatorSchemaStatus" dividerStyle="thin" translatesAutoresizingMaskIntoConstraints="NO" id="13" userLabel="Split View (Schema / Status)" customClass="SPSplitView">
                        <rect key="frame" x="0.0" y="0.0" width="395" height="334"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view fixedFrame="YES" id="14" userLabel="Custom View Schema">
                                <rect key="frame" x="0.0" y="0.0" width="395" height="233"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <scrollView focusRingType="none" fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="14" horizontalPageScroll="10" verticalLineScroll="14" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="150">
                                        <rect key="frame" x="0.0" y="0.0" width="396" height="233"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        <clipView key="contentView" drawsBackground="NO" id="YTv-7h-hUD">
                                            <rect key="frame" x="1" y="1" width="394" height="231"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <subviews>
                                                <outlineView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" selectionHighlightStyle="sourceList" columnReordering="NO" emptySelection="NO" autosaveColumns="NO" autosaveName="SPNavigatorSchema2" rowHeight="14" headerView="154" indentationPerLevel="12" autoresizesOutlineColumn="YES" outlineTableColumn="155" id="153" customClass="SPNavigatorOutlineView">
                                                    <rect key="frame" x="0.0" y="0.0" width="394" height="214"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <size key="intercellSpacing" width="3" height="0.0"/>
                                                    <color key="backgroundColor" name="_sourceListBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                                    <tableColumns>
                                                        <tableColumn identifier="field" width="188" minWidth="16" maxWidth="1000" id="155">
                                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left">
                                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                                            </tableHeaderCell>
                                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" alignment="left" title="Text Cell" id="158" customClass="ImageAndTextCell">
                                                                <font key="font" metaFont="message" size="11"/>
                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                            </textFieldCell>
                                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                        </tableColumn>
                                                        <tableColumn identifier="type" width="171" minWidth="40" maxWidth="1000" id="156">
                                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left">
                                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                                            </tableHeaderCell>
                                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" alignment="left" title="Text Cell" id="157" customClass="ImageAndTextCell">
                                                                <font key="font" metaFont="message" size="11"/>
                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                            </textFieldCell>
                                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                        </tableColumn>
                                                    </tableColumns>
                                                    <connections>
                                                        <action selector="outlineViewAction:" target="-2" id="161"/>
                                                        <outlet property="dataSource" destination="-2" id="160"/>
                                                        <outlet property="delegate" destination="-2" id="159"/>
                                                        <outlet property="nextKeyView" destination="8" id="165"/>
                                                    </connections>
                                                </outlineView>
                                            </subviews>
                                            <nil key="backgroundColor"/>
                                        </clipView>
                                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="YES" id="152">
                                            <rect key="frame" x="1" y="65" width="120" height="11"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="NO" id="151">
                                            <rect key="frame" x="224" y="17" width="11" height="102"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <tableHeaderView key="headerView" wantsLayer="YES" id="154">
                                            <rect key="frame" x="0.0" y="0.0" width="394" height="17"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableHeaderView>
                                    </scrollView>
                                </subviews>
                            </view>
                            <view fixedFrame="YES" id="15" userLabel="Custom View Status">
                                <rect key="frame" x="0.0" y="234" width="395" height="100"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="20" horizontalPageScroll="10" verticalLineScroll="20" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="135" userLabel="Bordered Scroll View (Table View Status Info)">
                                        <rect key="frame" x="0.0" y="0.0" width="396" height="100"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        <clipView key="contentView" drawsBackground="NO" id="yBx-ea-p19">
                                            <rect key="frame" x="1" y="1" width="394" height="98"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <subviews>
                                                <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" selectionHighlightStyle="sourceList" columnReordering="NO" multipleSelection="NO" autosaveColumns="NO" rowHeight="18" headerView="136" id="137">
                                                    <rect key="frame" x="0.0" y="0.0" width="394" height="81"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <size key="intercellSpacing" width="3" height="2"/>
                                                    <color key="backgroundColor" name="_sourceListBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                                    <tableColumns>
                                                        <tableColumn editable="NO" width="362" minWidth="40" maxWidth="1000" id="140">
                                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Information">
                                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                                            </tableHeaderCell>
                                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" title="Text Cell" id="141" customClass="SPTableTextFieldCell">
                                                                <font key="font" metaFont="message" size="11"/>
                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                            </textFieldCell>
                                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                        </tableColumn>
                                                    </tableColumns>
                                                    <connections>
                                                        <outlet property="dataSource" destination="-2" id="143"/>
                                                        <outlet property="delegate" destination="-2" id="142"/>
                                                    </connections>
                                                </tableView>
                                            </subviews>
                                            <nil key="backgroundColor"/>
                                        </clipView>
                                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="YES" id="138">
                                            <rect key="frame" x="1" y="38" width="393" height="11"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="NO" id="139">
                                            <rect key="frame" x="224" y="17" width="11" height="102"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <tableHeaderView key="headerView" wantsLayer="YES" id="136">
                                            <rect key="frame" x="0.0" y="0.0" width="394" height="17"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableHeaderView>
                                    </scrollView>
                                </subviews>
                            </view>
                        </subviews>
                        <holdingPriorities>
                            <real value="250"/>
                            <real value="250"/>
                        </holdingPriorities>
                    </splitView>
                    <button toolTip="Reload all (⌘R)" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="66">
                        <rect key="frame" x="355" y="337" width="20" height="20"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSRefreshFreestandingTemplate" imagePosition="overlaps" alignment="center" state="on" imageScaling="proportionallyDown" inset="2" id="67">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent">r</string>
                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                        </buttonCell>
                        <connections>
                            <action selector="reloadAllStructures:" target="-2" id="99"/>
                            <outlet property="nextKeyView" destination="153" id="166"/>
                        </connections>
                    </button>
                    <button toolTip="Synchronize connection window selection with Navigator (⌥⌘S)" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="108">
                        <rect key="frame" x="252" y="337" width="95" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="recessed" title="sync" bezelStyle="recessed" imagePosition="overlaps" alignment="center" controlSize="small" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="109">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES" changeBackground="YES" changeGray="YES"/>
                            <font key="font" metaFont="systemBold" size="12"/>
                            <string key="keyEquivalent">s</string>
                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                        </buttonCell>
                        <connections>
                            <action selector="syncButtonAction:" target="-2" id="111"/>
                            <outlet property="nextKeyView" destination="66" id="167"/>
                        </connections>
                    </button>
                </subviews>
            </view>
            <connections>
                <outlet property="initialFirstResponder" destination="153" id="162"/>
            </connections>
            <point key="canvasLocation" x="140" y="148"/>
        </window>
    </objects>
    <resources>
        <image name="NSRefreshFreestandingTemplate" width="15" height="15"/>
    </resources>
</document>
