<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="17506" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="17506"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPBundleEditorController">
            <connections>
                <outlet property="addButton" destination="733" id="992"/>
                <outlet property="authorTextField" destination="1136" id="1150"/>
                <outlet property="cancelButton" destination="817" id="1038"/>
                <outlet property="categoryTextField" destination="691" id="849"/>
                <outlet property="commandBundleTreeController" destination="982" id="983"/>
                <outlet property="commandLabelField" destination="938" id="1039"/>
                <outlet property="commandScrollView" destination="1214" id="1221"/>
                <outlet property="commandTextView" destination="1217" id="1219"/>
                <outlet property="commandsOutlineView" destination="965" id="971"/>
                <outlet property="contactTextField" destination="1139" id="1151"/>
                <outlet property="descriptionTextView" destination="1147" id="1152"/>
                <outlet property="disabledCheckbox" destination="996" id="1028"/>
                <outlet property="displayMetaInfoButton" destination="1156" id="1167"/>
                <outlet property="duplicateMenuItem" destination="1269" id="1280"/>
                <outlet property="fallbackLabelField" destination="795" id="837"/>
                <outlet property="helpButton" destination="690" id="1036"/>
                <outlet property="inputFallbackPopupButton" destination="780" id="838"/>
                <outlet property="inputPopupButton" destination="768" id="836"/>
                <outlet property="keyEquivalentField" destination="804" id="852"/>
                <outlet property="metaInfoSheet" destination="1132" id="1153"/>
                <outlet property="metaInfoSummary" destination="1171" id="1173"/>
                <outlet property="nameTextField" destination="695" id="834"/>
                <outlet property="outputPopupButton" destination="774" id="848"/>
                <outlet property="removeButton" destination="732" id="878"/>
                <outlet property="revealInFinderMenuItem" destination="1270" id="1281"/>
                <outlet property="saveButton" destination="859" id="1037"/>
                <outlet property="scopePopupButton" destination="932" id="956"/>
                <outlet property="splitView" destination="683" id="1130"/>
                <outlet property="tooltipTextField" destination="799" id="1072"/>
                <outlet property="triggerPopupButton" destination="1077" id="1086"/>
                <outlet property="undeleteSheet" destination="1101" id="1123"/>
                <outlet property="undeleteTableView" destination="1106" id="1116"/>
                <outlet property="window" destination="521" id="527"/>
                <outlet property="withBlobLabelField" destination="1088" id="1090"/>
                <outlet property="withBlobPopupButton" destination="1091" id="1096"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="Bundle Editor" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" visibleAtLaunch="NO" frameAutosaveName="SPBundleEditor" animationBehavior="default" id="521" userLabel="Bundle Editor Window" customClass="SPWindow">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="341" y="207" width="900" height="550"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2880" height="1595"/>
            <value key="minSize" type="size" width="730" height="494"/>
            <view key="contentView" id="522">
                <rect key="frame" x="0.0" y="0.0" width="900" height="550"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <splitView focusRingType="none" fixedFrame="YES" autosaveName="SPBundleEditorSplitView" dividerStyle="thin" vertical="YES" translatesAutoresizingMaskIntoConstraints="NO" id="683" customClass="SPSplitView">
                        <rect key="frame" x="0.0" y="0.0" width="900" height="550"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view fixedFrame="YES" id="685" userLabel="Bundle Table View">
                                <rect key="frame" x="0.0" y="0.0" width="142" height="550"/>
                                <autoresizingMask key="autoresizingMask" heightSizable="YES"/>
                                <subviews>
                                    <scrollView focusRingType="none" fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="20" horizontalPageScroll="10" verticalLineScroll="20" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="962">
                                        <rect key="frame" x="0.0" y="25" width="142" height="525"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        <clipView key="contentView" drawsBackground="NO" id="SvP-QH-EgG">
                                            <rect key="frame" x="1" y="1" width="140" height="523"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <outlineView focusRingType="none" verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="firstColumnOnly" selectionHighlightStyle="sourceList" columnReordering="NO" multipleSelection="NO" emptySelection="NO" autosaveName="SPBundleEditorOutlineView" rowHeight="20" indentationPerLevel="14" autoresizesOutlineColumn="YES" outlineTableColumn="967" id="965" customClass="SPOutlineView">
                                                    <rect key="frame" x="0.0" y="0.0" width="140" height="523"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <size key="intercellSpacing" width="3" height="0.0"/>
                                                    <color key="backgroundColor" name="_sourceListBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                                    <tableColumns>
                                                        <tableColumn identifier="bundleName" width="108" minWidth="16" maxWidth="1000" id="967">
                                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left">
                                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                                            </tableHeaderCell>
                                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="970">
                                                                <font key="font" metaFont="message" size="11"/>
                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                            </textFieldCell>
                                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                            <connections>
                                                                <binding destination="982" name="value" keyPath="arrangedObjects.bundleName" id="985"/>
                                                            </connections>
                                                        </tableColumn>
                                                    </tableColumns>
                                                    <connections>
                                                        <outlet property="dataSource" destination="-2" id="976"/>
                                                        <outlet property="delegate" destination="-2" id="977"/>
                                                        <outlet property="menu" destination="899" id="991"/>
                                                    </connections>
                                                </outlineView>
                                            </subviews>
                                            <nil key="backgroundColor"/>
                                        </clipView>
                                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="964">
                                            <rect key="frame" x="-100" y="-100" width="192" height="15"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="963">
                                            <rect key="frame" x="-100" y="-100" width="15" height="102"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                    </scrollView>
                                    <popUpButton fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1247">
                                        <rect key="frame" x="60" y="0.0" width="35" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <popUpButtonCell key="cell" type="bevel" bezelStyle="regularSquare" imagePosition="only" alignment="center" lineBreakMode="truncatingTail" state="on" inset="2" pullsDown="YES" selectedItem="1250" id="1248">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="menu"/>
                                            <menu key="menu" title="OtherViews" id="1249">
                                                <items>
                                                    <menuItem state="on" image="NSActionTemplate" hidden="YES" id="1250"/>
                                                    <menuItem title="Duplicate Bundle" keyEquivalent="d" id="1269">
                                                        <connections>
                                                            <action selector="duplicateCommandBundle:" target="-2" id="1275"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Reveal Bundle in Finder" keyEquivalent="O" id="1270">
                                                        <connections>
                                                            <action selector="revealCommandBundleInFinder:" target="-2" id="1278"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Export Bundle…" toolTip="Save the selected Bundle to disk" id="1271">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="saveBundle:" target="-2" id="1276"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem isSeparatorItem="YES" id="1272"/>
                                                    <menuItem title="Undelete Default Bundles…" id="1273">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="undeleteDefaultBundles:" target="-2" id="1277"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Reload Bundles without Saving" id="1274">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="reloadBundles:" target="-2" id="1279"/>
                                                        </connections>
                                                    </menuItem>
                                                </items>
                                            </menu>
                                        </popUpButtonCell>
                                    </popUpButton>
                                    <button toolTip="Remove selected Bundle (⌘⌫)" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="732">
                                        <rect key="frame" x="30" y="0.0" width="25" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSRemoveTemplate" imagePosition="only" alignment="center" alternateImage="NSRemoveTemplate" inset="2" id="735">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="system"/>
                                            <string key="keyEquivalent"></string>
                                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                        </buttonCell>
                                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                        <connections>
                                            <action selector="removeCommandBundle:" target="-2" id="828"/>
                                        </connections>
                                    </button>
                                    <button toolTip="Add Bundle (⌥⌘A)" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="733">
                                        <rect key="frame" x="0.0" y="0.0" width="25" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSAddTemplate" imagePosition="only" alignment="center" alternateImage="NSAddTemplate" inset="2" id="734">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="system"/>
                                            <string key="keyEquivalent">a</string>
                                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                        </buttonCell>
                                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                        <connections>
                                            <action selector="addCommandBundle:" target="-2" id="827"/>
                                            <outlet property="nextKeyView" destination="732" id="952"/>
                                        </connections>
                                    </button>
                                    <imageView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1267">
                                        <rect key="frame" x="117" y="0.0" width="25" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="button_bar_handleTemplate" id="1268"/>
                                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                    </imageView>
                                </subviews>
                            </view>
                            <view focusRingType="none" fixedFrame="YES" id="686" userLabel="Bundle Content View">
                                <rect key="frame" x="143" y="0.0" width="757" height="550"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1214">
                                        <rect key="frame" x="20" y="52" width="717" height="289"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        <clipView key="contentView" drawsBackground="NO" id="p0q-lB-EBP">
                                            <rect key="frame" x="1" y="1" width="715" height="287"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <textView drawsBackground="NO" importsGraphics="NO" richText="NO" verticallyResizable="YES" usesFontPanel="YES" findStyle="panel" allowsUndo="YES" allowsNonContiguousLayout="YES" id="1217" customClass="SPBundleCommandTextView">
                                                    <rect key="frame" x="0.0" y="0.0" width="715" height="287"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    <size key="minSize" width="715" height="287"/>
                                                    <size key="maxSize" width="10000000" height="10000000"/>
                                                    <color key="insertionPointColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                    <connections>
                                                        <binding destination="982" name="value" keyPath="selection.command" id="1241"/>
                                                        <binding destination="982" name="editable" keyPath="selection.disabled" id="1244">
                                                            <dictionary key="options">
                                                                <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                            </dictionary>
                                                        </binding>
                                                        <outlet property="commandScrollView" destination="1214" id="1220"/>
                                                        <outlet property="delegate" destination="-2" id="1222"/>
                                                        <outlet property="nextKeyView" destination="996" id="1235"/>
                                                    </connections>
                                                </textView>
                                            </subviews>
                                        </clipView>
                                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="1216">
                                            <rect key="frame" x="-100" y="-100" width="87" height="18"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="1215">
                                            <rect key="frame" x="701" y="1" width="15" height="289"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                    </scrollView>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1171">
                                        <rect key="frame" x="23" y="516" width="556" height="14"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" lineBreakMode="truncatingTail" sendsActionOnEndEditing="YES" alignment="left" title="[bundle summary]" id="1172">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1156">
                                        <rect key="frame" x="579" y="508" width="163" height="28"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                                        <buttonCell key="cell" type="push" title="Bundle Info" bezelStyle="rounded" alignment="center" controlSize="small" enabled="NO" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="1157">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="displayBundleMetaInfo:" target="-2" id="1158"/>
                                        </connections>
                                    </button>
                                    <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1077">
                                        <rect key="frame" x="411" y="485" width="159" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" id="1078">
                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                            <menu key="menu" title="OtherViews" id="1079"/>
                                        </popUpButtonCell>
                                        <connections>
                                            <action selector="triggerButtonChanged:" target="-2" id="1087"/>
                                            <binding destination="982" name="enabled" keyPath="selection.disabled" id="1186">
                                                <dictionary key="options">
                                                    <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                </dictionary>
                                            </binding>
                                            <outlet property="nextKeyView" destination="695" id="1228"/>
                                        </connections>
                                    </popUpButton>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="996">
                                        <rect key="frame" x="17" y="21" width="248" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="check" title="Disable Bundle" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="997">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                        <connections>
                                            <binding destination="982" name="value" keyPath="selection.disabled" id="999"/>
                                            <binding destination="982" name="fontBold" keyPath="selection.disabled" id="1213"/>
                                            <outlet property="nextKeyView" destination="859" id="1236"/>
                                        </connections>
                                    </button>
                                    <button toolTip="Save any changes and close the Bundle Editor (⌘S)" verticalHuggingPriority="750" fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="859">
                                        <rect key="frame" x="617" y="15" width="125" height="28"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="push" title="Save" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="860">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                            <string key="keyEquivalent">s</string>
                                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="saveAndCloseWindow:" target="-2" id="875"/>
                                            <outlet property="nextKeyView" destination="817" id="950"/>
                                        </connections>
                                    </button>
                                    <button toolTip="Close the Bundle Editor without saving" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="817">
                                        <rect key="frame" x="494" y="15" width="125" height="28"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="818">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="performClose:" target="-2" id="1098"/>
                                            <outlet property="nextKeyView" destination="733" id="951"/>
                                        </connections>
                                    </button>
                                    <control verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="804" customClass="SRRecorderControl">
                                        <rect key="frame" x="127" y="434" width="157" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <string key="toolTip">Click to record a shortcut for the Bundle command (Please note, not every shortcut will work due to global or Sequel Ace key bindings)</string>
                                        <actionCell key="cell" controlSize="mini" alignment="left" id="824" customClass="SRRecorderCell">
                                            <font key="font" metaFont="miniSystem"/>
                                        </actionCell>
                                        <connections>
                                            <binding destination="982" name="enabled" keyPath="selection.disabled" id="1199">
                                                <dictionary key="options">
                                                    <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                </dictionary>
                                            </binding>
                                            <binding destination="982" name="value" keyPath="selection.internalKeyEquivalent" id="990"/>
                                            <outlet property="delegate" destination="-2" id="851"/>
                                        </connections>
                                    </control>
                                    <popUpButton toolTip="Choose the handling of BLOB fields" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1091" userLabel="Pop Up Button (Input Fallback)">
                                        <rect key="frame" x="411" y="392" width="159" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" id="1092">
                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                            <menu key="menu" title="OtherViews" id="1093"/>
                                        </popUpButtonCell>
                                        <connections>
                                            <action selector="withBlobButtonChanged:" target="-2" id="1097"/>
                                            <binding destination="982" name="enabled" keyPath="selection.disabled" id="1208">
                                                <dictionary key="options">
                                                    <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                </dictionary>
                                            </binding>
                                        </connections>
                                    </popUpButton>
                                    <popUpButton toolTip="Choose the fallback input source if nothing was selected" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="780" userLabel="Pop Up Button (Input Fallback)">
                                        <rect key="frame" x="411" y="392" width="159" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" id="781">
                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                            <menu key="menu" title="OtherViews" id="782"/>
                                        </popUpButtonCell>
                                        <connections>
                                            <action selector="inputFallbackPopupButtonChanged:" target="-2" id="916"/>
                                        </connections>
                                    </popUpButton>
                                    <popUpButton toolTip="Choose the output action of the Bundle command" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="774" userLabel="Pop Up Button (Output)">
                                        <rect key="frame" x="126" y="367" width="159" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" id="775">
                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                            <menu key="menu" title="OtherViews" id="776"/>
                                        </popUpButtonCell>
                                        <connections>
                                            <binding destination="982" name="enabled" keyPath="selection.disabled" id="1211">
                                                <dictionary key="options">
                                                    <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                </dictionary>
                                            </binding>
                                            <outlet property="nextKeyView" destination="1217" id="1234"/>
                                        </connections>
                                    </popUpButton>
                                    <popUpButton toolTip="Choose the scope on which the Bundle command will run" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="932" userLabel="Pop Up Button (Input)">
                                        <rect key="frame" x="126" y="485" width="159" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" id="933">
                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                            <menu key="menu" title="OtherViews" id="934"/>
                                        </popUpButtonCell>
                                        <connections>
                                            <action selector="scopeButtonChanged:" target="-2" id="955"/>
                                            <binding destination="982" name="enabled" keyPath="selection.disabled" id="1183">
                                                <dictionary key="options">
                                                    <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                </dictionary>
                                            </binding>
                                            <outlet property="nextKeyView" destination="1077" id="1227"/>
                                        </connections>
                                    </popUpButton>
                                    <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="768" userLabel="Pop Up Button (Input)">
                                        <rect key="frame" x="126" y="392" width="159" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <string key="toolTip">Choose the input source for the command. The desired data will be available as UTF-8 encoded file whose name will be passed as shell variable SP_BUNDLE_INPUT which will be piped to the script (STDIN)</string>
                                        <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" id="769">
                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                            <menu key="menu" title="OtherViews" id="770"/>
                                        </popUpButtonCell>
                                        <connections>
                                            <action selector="inputPopupButtonChanged:" target="-2" id="911"/>
                                            <binding destination="982" name="enabled" keyPath="selection.disabled" id="1205">
                                                <dictionary key="options">
                                                    <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                </dictionary>
                                            </binding>
                                            <outlet property="nextKeyView" destination="774" id="1233"/>
                                        </connections>
                                    </popUpButton>
                                    <button horizontalHuggingPriority="750" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="690">
                                        <rect key="frame" x="465" y="16" width="25" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="help" bezelStyle="helpButton" alignment="center" controlSize="small" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="703">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="showHelp:" target="-2" id="853"/>
                                        </connections>
                                    </button>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1088">
                                        <rect key="frame" x="332" y="398" width="32" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="center" title="AND" id="1089">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="795">
                                        <rect key="frame" x="332" y="398" width="32" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="center" title="OR" id="796">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField toolTip="Enter a menu category which will be displayed as submenu containing the Bundle command" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="691" userLabel="Text Field (Menu Category)">
                                        <rect key="frame" x="414" y="462" width="323" height="19"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="[sub menu category]" drawsBackground="YES" usesSingleLineMode="YES" id="702">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <connections>
                                            <binding destination="982" name="value" keyPath="selection.category" id="1170">
                                                <dictionary key="options">
                                                    <string key="NSNullPlaceholder">optional</string>
                                                </dictionary>
                                            </binding>
                                            <binding destination="982" name="enabled" keyPath="selection.disabled" id="1194">
                                                <dictionary key="options">
                                                    <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                </dictionary>
                                            </binding>
                                            <outlet property="delegate" destination="-2" id="993"/>
                                            <outlet property="nextKeyView" destination="799" id="1231"/>
                                        </connections>
                                    </textField>
                                    <textField toolTip="Alphanumeric character string which inserts the favorite query string after expansion by pressing the ⇥ key." verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1083">
                                        <rect key="frame" x="287" y="491" width="122" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Trigger:" id="1084">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="788">
                                        <rect key="frame" x="21" y="372" width="103" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Output:" id="789">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField toolTip="Alphanumeric character string which inserts the favorite query string after expansion by pressing the ⇥ key." verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="692">
                                        <rect key="frame" x="287" y="464" width="122" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Menu Category:" id="701">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="936">
                                        <rect key="frame" x="21" y="490" width="103" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Bundle Scope:" id="937">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="938">
                                        <rect key="frame" x="17" y="349" width="447" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="left" title="Command:" id="939">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="797">
                                        <rect key="frame" x="21" y="398" width="103" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Input:" id="798">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="800">
                                        <rect key="frame" x="287" y="437" width="122" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Tooltip:" id="801">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField toolTip="Enter the tooltip for the Bundle command menu item" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="799" userLabel="Text Field (Tooltip)">
                                        <rect key="frame" x="414" y="435" width="323" height="19"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="[menu label tooltip]" drawsBackground="YES" usesSingleLineMode="YES" id="802">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <connections>
                                            <binding destination="982" name="enabled" keyPath="selection.disabled" id="1202">
                                                <dictionary key="options">
                                                    <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                </dictionary>
                                            </binding>
                                            <binding destination="982" name="value" keyPath="selection.tooltip" id="989"/>
                                            <outlet property="nextKeyView" destination="768" id="1232"/>
                                        </connections>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="693">
                                        <rect key="frame" x="21" y="464" width="103" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Menu Label:" id="700">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField toolTip="Enter the menu label name" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="695" userLabel="Text Field (Menu Label)">
                                        <rect key="frame" x="129" y="462" width="153" height="19"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="[menu label name]" drawsBackground="YES" usesSingleLineMode="YES" id="696">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <connections>
                                            <binding destination="982" name="enabled" keyPath="selection.disabled" id="1191">
                                                <dictionary key="options">
                                                    <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                </dictionary>
                                            </binding>
                                            <binding destination="982" name="value" keyPath="selection.name" id="1027"/>
                                            <outlet property="delegate" destination="-2" id="833"/>
                                            <outlet property="nextKeyView" destination="691" id="1229"/>
                                        </connections>
                                    </textField>
                                </subviews>
                            </view>
                        </subviews>
                        <holdingPriorities>
                            <real value="250"/>
                            <real value="250"/>
                        </holdingPriorities>
                        <connections>
                            <outlet property="additionalDragHandleView" destination="1267" id="1282"/>
                            <outlet property="delegate" destination="-2" id="1128"/>
                        </connections>
                    </splitView>
                </subviews>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="523"/>
            </connections>
            <point key="canvasLocation" x="139" y="147"/>
        </window>
        <menu id="899" userLabel="Outline Context Menu">
            <items>
                <menuItem title="Bundle Info" id="994">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="displayBundleMetaInfo:" target="-2" id="1160"/>
                    </connections>
                </menuItem>
                <menuItem title="Duplicate" keyEquivalent="d" id="903">
                    <connections>
                        <action selector="duplicateCommandBundle:" target="-2" id="906"/>
                    </connections>
                </menuItem>
                <menuItem title="Reveal Bundle in Finder" keyEquivalent="O" id="904">
                    <connections>
                        <action selector="revealCommandBundleInFinder:" target="-2" id="905"/>
                    </connections>
                </menuItem>
                <menuItem isSeparatorItem="YES" id="908"/>
                <menuItem title="Remove Selected Bundle" id="909">
                    <string key="keyEquivalent" base64-UTF8="YES">
CA
</string>
                    <connections>
                        <action selector="removeCommandBundle:" target="-2" id="910"/>
                    </connections>
                </menuItem>
            </items>
        </menu>
        <window title="Window" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" visibleAtLaunch="NO" animationBehavior="default" id="1101" userLabel="Undelete Default Bundles Sheet" customClass="NSPanel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" resizable="YES" utility="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="140" y="260" width="341" height="198"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2880" height="1595"/>
            <value key="minSize" type="size" width="341" height="198"/>
            <view key="contentView" id="1102">
                <rect key="frame" x="0.0" y="0.0" width="341" height="198"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="19" horizontalPageScroll="10" verticalLineScroll="19" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1103">
                        <rect key="frame" x="-1" y="60" width="343" height="139"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <clipView key="contentView" drawsBackground="NO" id="NAc-yU-wEX">
                            <rect key="frame" x="1" y="1" width="341" height="137"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" selectionHighlightStyle="sourceList" columnReordering="NO" columnResizing="NO" multipleSelection="NO" emptySelection="NO" autosaveColumns="NO" headerView="1118" id="1106">
                                    <rect key="frame" x="0.0" y="0.0" width="341" height="120"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <size key="intercellSpacing" width="3" height="2"/>
                                    <color key="backgroundColor" name="_sourceListBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                    <tableColumns>
                                        <tableColumn width="309" minWidth="40" maxWidth="1000" id="1108">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Deleted Default Bundles">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="1111">
                                                <font key="font" metaFont="system"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                    </tableColumns>
                                    <connections>
                                        <outlet property="dataSource" destination="-2" id="1119"/>
                                        <outlet property="delegate" destination="-2" id="1120"/>
                                    </connections>
                                </tableView>
                            </subviews>
                            <nil key="backgroundColor"/>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="1105">
                            <rect key="frame" x="1" y="119" width="223" height="15"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="1104">
                            <rect key="frame" x="224" y="17" width="15" height="102"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <tableHeaderView key="headerView" wantsLayer="YES" id="1118">
                            <rect key="frame" x="0.0" y="0.0" width="341" height="17"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </tableHeaderView>
                    </scrollView>
                    <button toolTip="Undelete selected default Bundles (↩)" verticalHuggingPriority="750" fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="1112">
                        <rect key="frame" x="213" y="12" width="114" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Undelete" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="1113">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeUndeleteDefaultBundlesSheet:" target="-2" id="1124"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1114">
                        <rect key="frame" x="99" y="12" width="114" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Close" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="1115">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeUndeleteDefaultBundlesSheet:" target="-2" id="1125"/>
                        </connections>
                    </button>
                </subviews>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="1126"/>
                <outlet property="initialFirstResponder" destination="1106" id="1127"/>
            </connections>
            <point key="canvasLocation" x="-352" y="-338"/>
        </window>
        <treeController childrenKeyPath="_children_" id="982">
            <declaredKeys>
                <string>bundleName</string>
                <string>disabled</string>
                <string>name</string>
                <string>command</string>
                <string>contact</string>
                <string>author</string>
                <string>description</string>
            </declaredKeys>
        </treeController>
        <window title="Window" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" visibleAtLaunch="NO" animationBehavior="default" id="1132" userLabel="Meta Info">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="196" y="266" width="297" height="244"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2880" height="1595"/>
            <value key="minSize" type="size" width="297" height="244"/>
            <view key="contentView" id="1133">
                <rect key="frame" x="0.0" y="0.0" width="297" height="244"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1134">
                        <rect key="frame" x="41" y="207" width="45" height="14"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Author:" id="1135">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1136">
                        <rect key="frame" x="91" y="205" width="186" height="19"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="1137">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                        <connections>
                            <binding destination="982" name="value" keyPath="selection.author" id="1162"/>
                        </connections>
                    </textField>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1138">
                        <rect key="frame" x="37" y="182" width="49" height="14"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Contact:" id="1141">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1139">
                        <rect key="frame" x="91" y="180" width="186" height="19"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="1140">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                        <connections>
                            <binding destination="982" name="value" keyPath="selection.contact" id="1164"/>
                        </connections>
                    </textField>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1142">
                        <rect key="frame" x="17" y="158" width="69" height="14"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Description:" id="1143">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1144">
                        <rect key="frame" x="20" y="56" width="257" height="94"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <clipView key="contentView" id="Z67-rH-TXQ">
                            <rect key="frame" x="1" y="1" width="255" height="92"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <subviews>
                                <textView editable="NO" importsGraphics="NO" richText="NO" verticallyResizable="YES" allowsNonContiguousLayout="YES" id="1147">
                                    <rect key="frame" x="0.0" y="0.0" width="255" height="92"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <size key="minSize" width="255" height="92"/>
                                    <size key="maxSize" width="463" height="10000000"/>
                                    <color key="insertionPointColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                    <connections>
                                        <binding destination="982" name="value" keyPath="selection.description" id="1166"/>
                                    </connections>
                                </textView>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="1146">
                            <rect key="frame" x="-100" y="-100" width="87" height="18"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" controlSize="small" horizontal="NO" id="1145">
                            <rect key="frame" x="245" y="1" width="11" height="92"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                    </scrollView>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1148">
                        <rect key="frame" x="186" y="13" width="96" height="28"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Close" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="1149">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSheet:" target="-2" id="1159"/>
                        </connections>
                    </button>
                </subviews>
            </view>
        </window>
    </objects>
    <resources>
        <image name="NSActionTemplate" width="15" height="15"/>
        <image name="NSAddTemplate" width="14" height="13"/>
        <image name="NSRemoveTemplate" width="14" height="4"/>
        <image name="button_bar_handleTemplate" width="6" height="10"/>
    </resources>
</document>
