<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="17506" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="17506"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPContentFilterManager">
            <connections>
                <outlet property="contentFilterArrayController" destination="284" id="336"/>
                <outlet property="contentFilterConjunctionLabel" destination="321" id="339"/>
                <outlet property="contentFilterConjunctionTextField" destination="319" id="323"/>
                <outlet property="contentFilterNameTextField" destination="123" id="332"/>
                <outlet property="contentFilterSplitView" destination="278" id="410"/>
                <outlet property="contentFilterTableView" destination="23" id="316"/>
                <outlet property="contentFilterTextView" destination="130" id="333"/>
                <outlet property="insertPlaceholderButton" destination="340" id="365"/>
                <outlet property="numberOfArgsLabel" destination="350" id="354"/>
                <outlet property="removeButton" destination="387" id="389"/>
                <outlet property="resultingClauseContentLabel" destination="352" id="363"/>
                <outlet property="resultingClauseLabel" destination="361" id="364"/>
                <outlet property="suppressLeadingFieldPlaceholderCheckbox" destination="369" id="gfj-J2-MZl"/>
                <outlet property="window" destination="1" id="310"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="Content Filter Manager" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" visibleAtLaunch="NO" frameAutosaveName="SPContentFilterManagerWindow" animationBehavior="default" id="1" userLabel="Content Filter Manager">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="593" y="356" width="500" height="371"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2880" height="1595"/>
            <value key="minSize" type="size" width="500" height="371"/>
            <view key="contentView" id="2">
                <rect key="frame" x="0.0" y="0.0" width="500" height="371"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <box autoresizesSubviews="NO" verticalHuggingPriority="750" fixedFrame="YES" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="142">
                        <rect key="frame" x="0.0" y="358" width="500" height="5"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                    </box>
                    <splitView focusRingType="none" fixedFrame="YES" autosaveName="SPContentFilterSplitView" dividerStyle="thin" vertical="YES" translatesAutoresizingMaskIntoConstraints="NO" id="278" customClass="SPSplitView">
                        <rect key="frame" x="0.0" y="0.0" width="500" height="361"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view fixedFrame="YES" id="279">
                                <rect key="frame" x="0.0" y="0.0" width="213" height="361"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <imageView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="382">
                                        <rect key="frame" x="188" y="0.0" width="25" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="button_bar_handleTemplate" id="383"/>
                                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                    </imageView>
                                    <button toolTip="Add filter (⌥⌘A)" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="384">
                                        <rect key="frame" x="0.0" y="0.0" width="25" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSAddTemplate" imagePosition="only" alignment="center" alternateImage="NSAddTemplate" inset="2" id="385">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="system"/>
                                            <string key="keyEquivalent">a</string>
                                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                        </buttonCell>
                                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                        <connections>
                                            <action selector="addContentFilter:" target="-2" id="386"/>
                                        </connections>
                                    </button>
                                    <button toolTip="Delete selected filter (⌫)" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="387">
                                        <rect key="frame" x="30" y="0.0" width="25" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSRemoveTemplate" imagePosition="only" alignment="center" alternateImage="NSRemoveTemplate" inset="2" id="388">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="system"/>
                                            <string key="keyEquivalent"></string>
                                        </buttonCell>
                                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                        <connections>
                                            <action selector="removeContentFilter:" target="-1" id="390"/>
                                        </connections>
                                    </button>
                                    <popUpButton toolTip="(⌥⎋)" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="391">
                                        <rect key="frame" x="60" y="0.0" width="35" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <popUpButtonCell key="cell" type="bevel" bezelStyle="regularSquare" imagePosition="right" alignment="center" alternateImage="NSActionTemplate" lineBreakMode="truncatingTail" state="on" inset="2" pullsDown="YES" selectedItem="400" id="392">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="menu"/>
                                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                                            <modifierMask key="keyEquivalentModifierMask" option="YES"/>
                                            <menu key="menu" title="OtherViews" id="393">
                                                <items>
                                                    <menuItem state="on" image="NSActionTemplate" hidden="YES" id="400"/>
                                                    <menuItem title="Duplicate" keyEquivalent="d" id="401">
                                                        <connections>
                                                            <action selector="duplicateContentFilter:" target="-1" id="408"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem isSeparatorItem="YES" id="402"/>
                                                    <menuItem title="Export Selected Filters…" toolTip="Export selected favorites as SPF file" id="403">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="exportContentFilter:" target="-1" id="407"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Import Filter by Replacing…" hidden="YES" toolTip="Import query favorites from SPF file and replace the current favorites" id="404"/>
                                                    <menuItem title="Import Filters…" toolTip="Import query favorites from SPF file and append them to the favorite list" id="405">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="importContentFilterByAdding:" target="-1" id="406"/>
                                                        </connections>
                                                    </menuItem>
                                                </items>
                                            </menu>
                                        </popUpButtonCell>
                                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                    </popUpButton>
                                    <scrollView focusRingType="none" fixedFrame="YES" borderType="none" autohidesScrollers="YES" horizontalLineScroll="18" horizontalPageScroll="10" verticalLineScroll="18" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="14">
                                        <rect key="frame" x="0.0" y="25" width="213" height="336"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        <clipView key="contentView" drawsBackground="NO" id="LRz-rB-PIF">
                                            <rect key="frame" x="0.0" y="0.0" width="213" height="336"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <subviews>
                                                <tableView focusRingType="none" verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="firstColumnOnly" selectionHighlightStyle="sourceList" columnReordering="NO" emptySelection="NO" autosaveColumns="NO" autosaveName="SPContentFilterManagerTable" rowHeight="16" headerView="265" id="23" customClass="SPTableView">
                                                    <rect key="frame" x="0.0" y="0.0" width="213" height="319"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <size key="intercellSpacing" width="3" height="2"/>
                                                    <color key="backgroundColor" name="_sourceListBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                                    <tableColumns>
                                                        <tableColumn identifier="MenuLabel" width="181" minWidth="40" maxWidth="1000" id="26">
                                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Content Filters">
                                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                                            </tableHeaderCell>
                                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="27" customClass="ImageAndTextCell">
                                                                <font key="font" metaFont="menu" size="11"/>
                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                            </textFieldCell>
                                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                            <connections>
                                                                <binding destination="284" name="value" keyPath="arrangedObjects.MenuLabel" id="335"/>
                                                            </connections>
                                                        </tableColumn>
                                                    </tableColumns>
                                                    <connections>
                                                        <outlet property="dataSource" destination="-2" id="324"/>
                                                        <outlet property="delegate" destination="-2" id="325"/>
                                                        <outlet property="menu" destination="205" id="331"/>
                                                    </connections>
                                                </tableView>
                                            </subviews>
                                            <nil key="backgroundColor"/>
                                        </clipView>
                                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="25">
                                            <rect key="frame" x="-100" y="-100" width="311" height="15"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="0.14953270554542542" controlSize="small" horizontal="NO" id="24">
                                            <rect key="frame" x="158" y="17" width="11" height="322"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <tableHeaderView key="headerView" wantsLayer="YES" focusRingType="none" id="265">
                                            <rect key="frame" x="0.0" y="0.0" width="213" height="17"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableHeaderView>
                                    </scrollView>
                                </subviews>
                            </view>
                            <view fixedFrame="YES" id="280">
                                <rect key="frame" x="214" y="0.0" width="286" height="361"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="369">
                                        <rect key="frame" x="16" y="199" width="252" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="check" title="Suppress leading &lt;field&gt; placeholder" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="mini" inset="2" id="370">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="toolTip" size="9"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="suppressLeadingFieldPlaceholderWasChanged:" target="-2" id="qgF-WQ-540"/>
                                            <binding destination="284" name="value" keyPath="selection.SuppressLeadingFieldPlaceholder" id="379">
                                                <dictionary key="options">
                                                    <integer key="NSNullPlaceholder" value="0"/>
                                                </dictionary>
                                            </binding>
                                        </connections>
                                    </button>
                                    <textField hidden="YES" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="361">
                                        <rect key="frame" x="17" y="108" width="252" height="14"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="left" title="Label" id="362">
                                            <font key="font" metaFont="menu" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField hidden="YES" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="248" translatesAutoresizingMaskIntoConstraints="NO" id="352">
                                        <rect key="frame" x="17" y="45" width="252" height="55"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" truncatesLastVisibleLine="YES" sendsActionOnEndEditing="YES" alignment="left" title="Multiline Label" id="353">
                                            <font key="font" metaFont="menu" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField hidden="YES" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="350">
                                        <rect key="frame" x="63" y="306" width="206" height="14"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Label" id="351">
                                            <font key="font" metaFont="menu" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="340">
                                        <rect key="frame" x="20" y="165" width="146" height="25"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <popUpButtonCell key="cell" type="roundRect" title="Insert Placeholder" bezelStyle="roundedRect" alignment="left" controlSize="small" lineBreakMode="truncatingTail" enabled="NO" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" pullsDown="YES" arrowPosition="arrowAtCenter" selectedItem="343" id="341">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="menu" size="11"/>
                                            <menu key="menu" title="OtherViews" id="342">
                                                <items>
                                                    <menuItem title="Insert Placeholder" hidden="YES" id="358">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                    </menuItem>
                                                    <menuItem title="Argument" state="on" toolTip="${} – Argument placeholder." id="343"/>
                                                    <menuItem title="Quoted Argument" toolTip="'${}' – quoted argument placeholder." id="357">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                    </menuItem>
                                                    <menuItem title="Current Field" toolTip="$CURRENT_FIELD – placeholder. That placeholder will be replaced by the current field name." id="344"/>
                                                    <menuItem title="BINARY" toolTip="$BINARY – placeholder. The keyword BINARY will be inserted if ⇧ is pressed while invoking “Filter”." id="345"/>
                                                </items>
                                            </menu>
                                        </popUpButtonCell>
                                        <connections>
                                            <action selector="insertPlaceholder:" target="-1" id="356"/>
                                        </connections>
                                    </popUpButton>
                                    <textField hidden="YES" toolTip="Label which appears between the argument input fields (as short as possible)" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="321">
                                        <rect key="frame" x="17" y="149" width="252" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Conjunction Label:" id="322">
                                            <font key="font" metaFont="menu" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField hidden="YES" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="319">
                                        <rect key="frame" x="20" y="130" width="124" height="16"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                        <textFieldCell key="cell" controlSize="mini" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" id="320">
                                            <font key="font" metaFont="toolTip" size="9"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <connections>
                                            <binding destination="284" name="value" keyPath="selection.ConjunctionLabel" id="338"/>
                                        </connections>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="303">
                                        <rect key="frame" x="17" y="306" width="55" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Clause:" id="304">
                                            <font key="font" metaFont="menu" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="121">
                                        <rect key="frame" x="17" y="330" width="45" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Name:" id="122">
                                            <font key="font" metaFont="menu" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="123">
                                        <rect key="frame" x="56" y="328" width="210" height="19"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="[no selection]" drawsBackground="YES" usesSingleLineMode="YES" id="124">
                                            <font key="font" metaFont="menu" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <connections>
                                            <outlet property="delegate" destination="-2" id="188"/>
                                            <outlet property="nextKeyView" destination="130" id="366"/>
                                        </connections>
                                    </textField>
                                    <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" hasHorizontalScroller="NO" hasVerticalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="127">
                                        <rect key="frame" x="20" y="220" width="246" height="84"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        <clipView key="contentView" drawsBackground="NO" id="bHl-95-1da">
                                            <rect key="frame" x="1" y="1" width="244" height="82"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <subviews>
                                                <textView importsGraphics="NO" richText="NO" verticallyResizable="YES" usesFontPanel="YES" findStyle="panel" allowsUndo="YES" smartInsertDelete="YES" id="130" customClass="SPTextView">
                                                    <rect key="frame" x="0.0" y="0.0" width="244" height="82"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                    <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    <size key="minSize" width="244" height="82"/>
                                                    <size key="maxSize" width="592" height="10000000"/>
                                                    <color key="insertionPointColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                    <connections>
                                                        <binding destination="284" name="value" keyPath="selection.Clause" id="300"/>
                                                        <binding destination="143" name="font" keyPath="values.CustomQueryEditorFont" id="306">
                                                            <dictionary key="options">
                                                                <string key="NSValueTransformerName">NSUnarchiveFromData</string>
                                                            </dictionary>
                                                        </binding>
                                                        <outlet property="delegate" destination="-2" id="198"/>
                                                    </connections>
                                                </textView>
                                            </subviews>
                                        </clipView>
                                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="129">
                                            <rect key="frame" x="-100" y="-100" width="87" height="18"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="NO" id="128">
                                            <rect key="frame" x="-100" y="-100" width="11" height="133"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </scroller>
                                    </scrollView>
                                    <button verticalHuggingPriority="750" fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="131">
                                        <rect key="frame" x="187" y="13" width="84" height="28"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="push" title="Save" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="132">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="menu" size="11"/>
                                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                                        </buttonCell>
                                        <connections>
                                            <action selector="closeContentFilterManagerSheet:" target="-1" id="311"/>
                                        </connections>
                                    </button>
                                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="271">
                                        <rect key="frame" x="105" y="13" width="84" height="28"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="272">
                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="menu" size="11"/>
                                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                                        </buttonCell>
                                        <connections>
                                            <action selector="closeContentFilterManagerSheet:" target="-1" id="312"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </view>
                        </subviews>
                        <holdingPriorities>
                            <real value="250"/>
                            <real value="250"/>
                        </holdingPriorities>
                        <connections>
                            <outlet property="additionalDragHandleView" destination="382" id="409"/>
                            <outlet property="delegate" destination="-2" id="367"/>
                        </connections>
                    </splitView>
                </subviews>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="187"/>
            </connections>
            <point key="canvasLocation" x="139" y="147.5"/>
        </window>
        <userDefaultsController representsSharedInstance="YES" id="143"/>
        <menu id="205" userLabel="Favorite Context Menu">
            <items>
                <menuItem title="Remove" id="206">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="removeContentFilter:" target="-1" id="329"/>
                    </connections>
                </menuItem>
                <menuItem title="Duplicate" id="212">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="duplicateContentFilter:" target="-1" id="330"/>
                    </connections>
                </menuItem>
            </items>
            <point key="canvasLocation" x="139" y="435"/>
        </menu>
        <arrayController selectsInsertedObjects="NO" avoidsEmptySelection="NO" id="284" userLabel="Favorites Controller">
            <declaredKeys>
                <string>Clause</string>
                <string>MenuLabel</string>
                <string>ConjunctionLabel</string>
                <string>suppressLeadingFieldPlaceholder</string>
                <string>SuppressLeadingFieldPlaceholder</string>
            </declaredKeys>
        </arrayController>
    </objects>
    <resources>
        <image name="NSActionTemplate" width="15" height="15"/>
        <image name="NSAddTemplate" width="14" height="13"/>
        <image name="NSRemoveTemplate" width="14" height="4"/>
        <image name="button_bar_handleTemplate" width="6" height="10"/>
    </resources>
</document>
