<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="16097" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="16097"/>
        <plugIn identifier="com.apple.WebKitIBPlugin" version="16097"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPBundleHTMLOutputController">
            <connections>
                <outlet property="webView" destination="3" id="14"/>
                <outlet property="window" destination="1" id="12"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="Window" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" visibleAtLaunch="NO" frameAutosaveName="SPBundleHTMLOutputWindow" animationBehavior="default" id="1">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="196" y="240" width="480" height="270"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2560" height="1417"/>
            <value key="minSize" type="size" width="50" height="50"/>
            <view key="contentView" focusRingType="none" id="2">
                <rect key="frame" x="0.0" y="0.0" width="480" height="270"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <webView focusRingType="none" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="3">
                        <rect key="frame" x="0.0" y="0.0" width="480" height="270"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <webPreferences key="preferences" identifier="Sequel Ace BundleHTMLOutput" defaultFontSize="12" defaultFixedFontSize="12" minimumFontSize="5"/>
                        <connections>
                            <outlet property="UIDelegate" destination="-2" id="8"/>
                            <outlet property="downloadDelegate" destination="-2" id="15"/>
                            <outlet property="frameLoadDelegate" destination="-2" id="16"/>
                            <outlet property="policyDelegate" destination="-2" id="13"/>
                            <outlet property="resourceLoadDelegate" destination="-2" id="17"/>
                        </connections>
                    </webView>
                </subviews>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="25"/>
            </connections>
            <point key="canvasLocation" x="139" y="147"/>
        </window>
    </objects>
</document>
