<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="17156" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="17156"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPDataImport">
            <connections>
                <outlet property="importEncodingPopup" destination="115" id="123"/>
                <outlet property="importFieldNamesSwitch" destination="12" id="37"/>
                <outlet property="importFieldsEnclosedField" destination="11" id="32"/>
                <outlet property="importFieldsEscapedField" destination="9" id="29"/>
                <outlet property="importFieldsTerminatedField" destination="6" id="31"/>
                <outlet property="importFormatPopup" destination="2" id="36"/>
                <outlet property="importFromClipboardAccessoryView" destination="96" id="102"/>
                <outlet property="importFromClipboardSheet" destination="85" id="100"/>
                <outlet property="importFromClipboardTextView" destination="92" id="101"/>
                <outlet property="importLinesTerminatedField" destination="8" id="30"/>
                <outlet property="importSQLErrorHandlingPopup" destination="133" id="143"/>
                <outlet property="importTabView" destination="124" id="130"/>
                <outlet property="importView" destination="1" id="129"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <customView id="1" userLabel="Import Accessory View">
            <rect key="frame" x="0.0" y="0.0" width="563" height="168"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <textField verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="3">
                    <rect key="frame" x="18" y="142" width="261" height="16"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Format:" id="23">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="105">
                    <rect key="frame" x="18" y="116" width="261" height="16"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Encoding:" id="106">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="115">
                    <rect key="frame" x="284" y="110" width="263" height="25"/>
                    <popUpButtonCell key="cell" type="push" title="Item 1" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="119" id="116">
                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="menu"/>
                        <menu key="menu" title="OtherViews" id="117">
                            <items>
                                <menuItem title="Item 1" state="on" id="119"/>
                            </items>
                        </menu>
                    </popUpButtonCell>
                    <connections>
                        <outlet property="nextKeyView" destination="12" id="120"/>
                    </connections>
                </popUpButton>
                <tabView drawsBackground="NO" controlSize="mini" type="bottomTabsBezelBorder" translatesAutoresizingMaskIntoConstraints="NO" id="124">
                    <rect key="frame" x="-7" y="-2" width="577" height="110"/>
                    <font key="font" metaFont="toolTip" size="9"/>
                    <tabViewItems>
                        <tabViewItem label="CSV" identifier="1" id="125">
                            <view key="view" id="128">
                                <rect key="frame" x="10" y="7" width="557" height="84"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <button translatesAutoresizingMaskIntoConstraints="NO" id="12">
                                        <rect key="frame" x="19" y="63" width="518" height="16"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="14" id="1RZ-Bz-yvO"/>
                                        </constraints>
                                        <buttonCell key="cell" type="check" title="First line contains field names" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" state="on" inset="2" id="15">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                        <connections>
                                            <outlet property="nextKeyView" destination="6" id="53"/>
                                        </connections>
                                    </button>
                                    <comboBox toolTip="Character used to enclose fields" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="11">
                                        <rect key="frame" x="215" y="6" width="63" height="22"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="17" id="VOs-q6-vnd"/>
                                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="50" id="m0C-vY-hqz"/>
                                        </constraints>
                                        <comboBoxCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" truncatesLastVisibleLine="YES" selectable="YES" editable="YES" borderStyle="bezel" alignment="left" title="&quot;" drawsBackground="YES" usesSingleLineMode="YES" completes="NO" numberOfVisibleItems="2" id="16">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            <objectValues>
                                                <string>"</string>
                                                <string></string>
                                            </objectValues>
                                        </comboBoxCell>
                                        <connections>
                                            <outlet property="nextKeyView" destination="8" id="56"/>
                                        </connections>
                                    </comboBox>
                                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="10">
                                        <rect key="frame" x="18" y="10" width="189" height="17"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="17" id="drg-WF-sxF"/>
                                        </constraints>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Fields enclosed by:" id="17">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <comboBox toolTip="Character used to escape special characters" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="9">
                                        <rect key="frame" x="477" y="33" width="63" height="22"/>
                                        <constraints>
                                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="50" id="ptg-BK-Cf6"/>
                                            <constraint firstAttribute="height" constant="17" id="rEt-jo-ilw"/>
                                        </constraints>
                                        <comboBoxCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" truncatesLastVisibleLine="YES" selectable="YES" editable="YES" borderStyle="bezel" alignment="left" title="\ or &quot;" drawsBackground="YES" usesSingleLineMode="YES" completes="NO" numberOfVisibleItems="3" id="18">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            <objectValues>
                                                <string>\ or "</string>
                                                <string>\</string>
                                                <string>"</string>
                                            </objectValues>
                                        </comboBoxCell>
                                        <connections>
                                            <outlet property="nextKeyView" destination="11" id="55"/>
                                        </connections>
                                    </comboBox>
                                    <comboBox toolTip="Character used to terminate line" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="8">
                                        <rect key="frame" x="477" y="6" width="63" height="22"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="17" id="1ca-hh-izb"/>
                                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="50" id="alG-cH-5Yo"/>
                                        </constraints>
                                        <comboBoxCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" truncatesLastVisibleLine="YES" selectable="YES" editable="YES" borderStyle="bezel" alignment="left" title="\n" drawsBackground="YES" usesSingleLineMode="YES" completes="NO" numberOfVisibleItems="3" id="19">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            <objectValues>
                                                <string>\n</string>
                                                <string>\r\n</string>
                                                <string>\r</string>
                                            </objectValues>
                                        </comboBoxCell>
                                    </comboBox>
                                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="7">
                                        <rect key="frame" x="281" y="10" width="188" height="17"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="17" id="fVT-q7-Psm"/>
                                        </constraints>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Lines terminated by:" id="20">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <comboBox toolTip="Character used to separate fields" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="6">
                                        <rect key="frame" x="215" y="33" width="63" height="22"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="17" id="DEm-39-96P"/>
                                            <constraint firstAttribute="width" constant="60" id="V9C-Q7-z5Q"/>
                                        </constraints>
                                        <comboBoxCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" truncatesLastVisibleLine="YES" selectable="YES" editable="YES" borderStyle="bezel" alignment="left" title="," drawsBackground="YES" usesSingleLineMode="YES" completes="NO" numberOfVisibleItems="4" id="21">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            <objectValues>
                                                <string>,</string>
                                                <string>;</string>
                                                <string>\t</string>
                                                <string>|</string>
                                            </objectValues>
                                        </comboBoxCell>
                                        <connections>
                                            <outlet property="nextKeyView" destination="9" id="54"/>
                                        </connections>
                                    </comboBox>
                                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="5">
                                        <rect key="frame" x="18" y="37" width="189" height="17"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="17" id="wta-5F-ZEr"/>
                                        </constraints>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Fields terminated by:" id="22">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="1000" verticalCompressionResistancePriority="1000" translatesAutoresizingMaskIntoConstraints="NO" id="13">
                                        <rect key="frame" x="281" y="37" width="188" height="17"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="17" id="ZRs-1S-kdQ"/>
                                        </constraints>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Fields escaped by:" id="14">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="9" secondAttribute="trailing" constant="20" id="0RM-ZE-2o0"/>
                                    <constraint firstAttribute="trailing" secondItem="8" secondAttribute="trailing" constant="20" id="1K8-FF-utR"/>
                                    <constraint firstItem="9" firstAttribute="leading" secondItem="13" secondAttribute="trailing" constant="10" id="3Cy-Ij-TuU"/>
                                    <constraint firstAttribute="bottom" secondItem="11" secondAttribute="bottom" constant="10" id="3oZ-B0-cbg"/>
                                    <constraint firstItem="9" firstAttribute="centerY" secondItem="5" secondAttribute="centerY" id="5C3-hB-GMG"/>
                                    <constraint firstItem="11" firstAttribute="centerY" secondItem="10" secondAttribute="centerY" id="5jl-CS-cVb"/>
                                    <constraint firstItem="13" firstAttribute="top" secondItem="5" secondAttribute="top" id="A9p-Jh-yvv"/>
                                    <constraint firstItem="8" firstAttribute="centerY" secondItem="10" secondAttribute="centerY" id="CFx-S9-Xml"/>
                                    <constraint firstItem="11" firstAttribute="leading" secondItem="10" secondAttribute="trailing" constant="10" id="Hbf-AE-Cen"/>
                                    <constraint firstItem="7" firstAttribute="top" secondItem="10" secondAttribute="top" id="J13-JW-gfm"/>
                                    <constraint firstItem="10" firstAttribute="top" secondItem="5" secondAttribute="bottom" constant="10" id="KL9-FX-pwZ"/>
                                    <constraint firstItem="12" firstAttribute="top" secondItem="128" secondAttribute="top" constant="6" id="Ljg-1t-FZ4"/>
                                    <constraint firstAttribute="trailing" secondItem="12" secondAttribute="trailing" constant="20" id="M99-Zv-nv9"/>
                                    <constraint firstItem="12" firstAttribute="leading" secondItem="128" secondAttribute="leading" constant="20" id="MLX-2f-kNc"/>
                                    <constraint firstItem="7" firstAttribute="width" secondItem="10" secondAttribute="width" id="NbH-HQ-o0a"/>
                                    <constraint firstItem="10" firstAttribute="leading" secondItem="128" secondAttribute="leading" constant="20" id="VTY-Oh-xnM"/>
                                    <constraint firstItem="7" firstAttribute="leading" secondItem="11" secondAttribute="trailing" constant="8" symbolic="YES" id="WaF-Sr-LpO"/>
                                    <constraint firstItem="13" firstAttribute="leading" secondItem="6" secondAttribute="trailing" constant="8" symbolic="YES" id="Xzh-m1-KuC"/>
                                    <constraint firstItem="6" firstAttribute="centerY" secondItem="5" secondAttribute="centerY" id="Yr6-6r-Tuw"/>
                                    <constraint firstItem="5" firstAttribute="top" secondItem="12" secondAttribute="bottom" constant="10" id="ZZw-zY-kld"/>
                                    <constraint firstItem="11" firstAttribute="width" secondItem="6" secondAttribute="width" id="dBW-FD-Ane"/>
                                    <constraint firstItem="5" firstAttribute="leading" secondItem="128" secondAttribute="leading" constant="20" id="fGu-Cj-p5s"/>
                                    <constraint firstItem="8" firstAttribute="width" secondItem="6" secondAttribute="width" id="k7O-PJ-yTq"/>
                                    <constraint firstItem="8" firstAttribute="leading" secondItem="7" secondAttribute="trailing" constant="10" id="pv1-Cx-CXu"/>
                                    <constraint firstItem="6" firstAttribute="leading" secondItem="5" secondAttribute="trailing" constant="10" id="rrL-81-z2p"/>
                                    <constraint firstItem="13" firstAttribute="width" secondItem="5" secondAttribute="width" id="rsi-a1-NOK"/>
                                    <constraint firstItem="9" firstAttribute="width" secondItem="6" secondAttribute="width" id="xq4-UP-0ha"/>
                                </constraints>
                            </view>
                        </tabViewItem>
                        <tabViewItem label="SQL" identifier="2" id="126">
                            <view key="view" id="127">
                                <rect key="frame" x="10" y="7" width="557" height="100"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <textField verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="131">
                                        <rect key="frame" x="18" y="74" width="258" height="16"/>
                                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="On SQL Error:" id="132">
                                            <font key="font" metaFont="system"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="133">
                                        <rect key="frame" x="281" y="68" width="260" height="25"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="20" id="Noy-Qt-Cgz"/>
                                        </constraints>
                                        <popUpButtonCell key="cell" type="push" title="Ask me what to do" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="136" id="134">
                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="menu"/>
                                            <menu key="menu" title="OtherViews" id="135">
                                                <items>
                                                    <menuItem title="Ask me what to do" state="on" id="136"/>
                                                    <menuItem title="Ignore and continue" tag="1" id="137"/>
                                                </items>
                                            </menu>
                                        </popUpButtonCell>
                                        <connections>
                                            <binding destination="28" name="selectedTag" keyPath="values.SQLImportErrorHandlingSelection" id="145">
                                                <dictionary key="options">
                                                    <real key="NSNoSelectionPlaceholder" value="0.0"/>
                                                    <real key="NSNullPlaceholder" value="0.0"/>
                                                </dictionary>
                                            </binding>
                                        </connections>
                                    </popUpButton>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="133" firstAttribute="centerY" secondItem="131" secondAttribute="centerY" id="57n-eu-S11"/>
                                    <constraint firstItem="131" firstAttribute="top" secondItem="127" secondAttribute="top" constant="10" id="PUB-a7-zZY"/>
                                    <constraint firstItem="133" firstAttribute="width" secondItem="131" secondAttribute="width" id="nqo-57-HnO"/>
                                    <constraint firstItem="133" firstAttribute="leading" secondItem="131" secondAttribute="trailing" constant="10" id="pfT-V2-xWV"/>
                                    <constraint firstAttribute="trailing" secondItem="133" secondAttribute="trailing" constant="20" id="w0O-HZ-6rC"/>
                                    <constraint firstItem="131" firstAttribute="leading" secondItem="127" secondAttribute="leading" constant="20" id="xib-M2-KyC"/>
                                </constraints>
                            </view>
                        </tabViewItem>
                    </tabViewItems>
                </tabView>
                <popUpButton verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="2">
                    <rect key="frame" x="284" y="136" width="263" height="25"/>
                    <popUpButtonCell key="cell" type="push" title="CSV" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="26" id="24">
                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="menu"/>
                        <menu key="menu" title="OtherViews" id="25">
                            <items>
                                <menuItem title="CSV" state="on" id="26"/>
                                <menuItem title="SQL" id="27"/>
                            </items>
                        </menu>
                    </popUpButtonCell>
                    <connections>
                        <action selector="changeFormat:" target="-2" id="38"/>
                        <outlet property="nextKeyView" destination="115" id="122"/>
                    </connections>
                </popUpButton>
            </subviews>
            <constraints>
                <constraint firstItem="124" firstAttribute="leading" secondItem="1" secondAttribute="leading" id="6Od-yh-CyO"/>
                <constraint firstItem="105" firstAttribute="top" secondItem="3" secondAttribute="bottom" constant="10" id="9sa-LI-h2n"/>
                <constraint firstAttribute="trailing" secondItem="2" secondAttribute="trailing" constant="20" id="Bhm-Tp-ZWn"/>
                <constraint firstItem="115" firstAttribute="leading" secondItem="105" secondAttribute="trailing" constant="10" id="FqB-Go-xuY"/>
                <constraint firstItem="2" firstAttribute="leading" secondItem="3" secondAttribute="trailing" constant="10" id="GQC-aM-aGg"/>
                <constraint firstItem="124" firstAttribute="top" secondItem="115" secondAttribute="bottom" constant="10" id="LqD-3X-4Cf"/>
                <constraint firstItem="115" firstAttribute="centerY" secondItem="105" secondAttribute="centerY" id="Ocw-Oi-FJ5"/>
                <constraint firstItem="2" firstAttribute="centerY" secondItem="3" secondAttribute="centerY" id="Otz-ch-e7T"/>
                <constraint firstItem="3" firstAttribute="leading" secondItem="1" secondAttribute="leading" constant="20" id="W7a-3X-6rS"/>
                <constraint firstAttribute="trailing" secondItem="124" secondAttribute="trailing" id="azR-CO-b0b"/>
                <constraint firstAttribute="bottom" secondItem="124" secondAttribute="bottom" id="cWZ-mh-bNT"/>
                <constraint firstItem="3" firstAttribute="top" secondItem="1" secondAttribute="top" constant="10" id="eFF-F7-bK1"/>
                <constraint firstItem="115" firstAttribute="width" secondItem="3" secondAttribute="width" id="h3r-Qa-dil"/>
                <constraint firstAttribute="trailing" secondItem="115" secondAttribute="trailing" constant="20" id="m81-AD-k2J"/>
                <constraint firstItem="105" firstAttribute="leading" secondItem="1" secondAttribute="leading" constant="20" id="nr9-9e-siE"/>
                <constraint firstItem="105" firstAttribute="width" secondItem="3" secondAttribute="width" id="pPd-NK-4NV"/>
                <constraint firstItem="2" firstAttribute="width" secondItem="3" secondAttribute="width" id="ugE-fC-xqE"/>
            </constraints>
            <point key="canvasLocation" x="168" y="304"/>
        </customView>
        <userDefaultsController representsSharedInstance="YES" id="28"/>
        <window title="Window" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="85" userLabel="Import from Clipboard">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="131" y="16" width="557" height="563"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2880" height="1595"/>
            <value key="minSize" type="size" width="500" height="353"/>
            <view key="contentView" id="86">
                <rect key="frame" x="0.0" y="0.0" width="557" height="563"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="90">
                        <rect key="frame" x="350" y="13" width="94" height="32"/>
                        <constraints>
                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="80" id="9Mo-KS-DOL"/>
                        </constraints>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="95">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSheet:" target="-2" id="98"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="88">
                        <rect key="frame" x="450" y="13" width="94" height="32"/>
                        <buttonCell key="cell" type="push" title="Next" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="97">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSheet:" target="-2" id="99"/>
                        </connections>
                    </button>
                    <box autoresizesSubviews="NO" borderType="line" title="Box" titlePosition="noTitle" translatesAutoresizingMaskIntoConstraints="NO" id="89">
                        <rect key="frame" x="7" y="51" width="543" height="191"/>
                        <view key="contentView" id="Lvx-2T-4XA">
                            <rect key="frame" x="3" y="3" width="537" height="185"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <customView translatesAutoresizingMaskIntoConstraints="NO" id="96">
                                    <rect key="frame" x="0.0" y="0.0" width="537" height="185"/>
                                </customView>
                            </subviews>
                            <constraints>
                                <constraint firstAttribute="trailing" secondItem="96" secondAttribute="trailing" id="Buz-4d-c8U"/>
                                <constraint firstAttribute="bottom" secondItem="96" secondAttribute="bottom" id="Gob-JD-kEx"/>
                                <constraint firstItem="96" firstAttribute="leading" secondItem="Lvx-2T-4XA" secondAttribute="leading" id="HqC-Ih-Qqf"/>
                                <constraint firstItem="96" firstAttribute="top" secondItem="Lvx-2T-4XA" secondAttribute="top" id="qG1-Qa-OPc"/>
                            </constraints>
                        </view>
                        <constraints>
                            <constraint firstAttribute="height" constant="185" id="SS7-cy-vzO"/>
                        </constraints>
                    </box>
                    <textField verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="103">
                        <rect key="frame" x="18" y="533" width="521" height="20"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="20" id="VY8-Iw-JnL"/>
                        </constraints>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="center" title="Import from Clipboard" id="104">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <scrollView autohidesScrollers="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="91">
                        <rect key="frame" x="0.0" y="240" width="557" height="283"/>
                        <clipView key="contentView" drawsBackground="NO" id="vP2-ix-4RJ">
                            <rect key="frame" x="1" y="1" width="555" height="281"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <textView editable="NO" importsGraphics="NO" richText="NO" verticallyResizable="YES" allowsNonContiguousLayout="YES" id="92">
                                    <rect key="frame" x="0.0" y="0.0" width="555" height="281"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <size key="minSize" width="555" height="281"/>
                                    <size key="maxSize" width="1002" height="10000000"/>
                                    <color key="insertionPointColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                </textView>
                            </subviews>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="93">
                            <rect key="frame" x="1" y="265" width="555" height="16"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="94">
                            <rect key="frame" x="483" y="1" width="16" height="130"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                    </scrollView>
                </subviews>
                <constraints>
                    <constraint firstItem="89" firstAttribute="leading" secondItem="86" secondAttribute="leading" constant="10" id="228-y3-Efb"/>
                    <constraint firstAttribute="trailing" secondItem="103" secondAttribute="trailing" constant="20" id="2CC-v3-IhO"/>
                    <constraint firstItem="91" firstAttribute="top" secondItem="103" secondAttribute="bottom" constant="10" id="5Z7-Oh-GtL"/>
                    <constraint firstItem="103" firstAttribute="leading" secondItem="86" secondAttribute="leading" constant="20" id="9aV-i7-Thu"/>
                    <constraint firstItem="88" firstAttribute="leading" secondItem="90" secondAttribute="trailing" constant="20" id="Ccp-5O-bzA"/>
                    <constraint firstAttribute="bottom" secondItem="88" secondAttribute="bottom" constant="20" id="D4M-Rq-WY8"/>
                    <constraint firstAttribute="bottom" secondItem="90" secondAttribute="bottom" constant="20" id="Ga1-Ws-IIW"/>
                    <constraint firstAttribute="trailing" secondItem="91" secondAttribute="trailing" id="IPm-DU-SIL"/>
                    <constraint firstAttribute="trailing" secondItem="89" secondAttribute="trailing" constant="10" id="N7y-tV-0ut"/>
                    <constraint firstItem="88" firstAttribute="width" secondItem="90" secondAttribute="width" id="O5w-2H-RQQ"/>
                    <constraint firstItem="89" firstAttribute="top" secondItem="91" secondAttribute="bottom" id="RBu-Au-V2d"/>
                    <constraint firstItem="88" firstAttribute="top" secondItem="89" secondAttribute="bottom" constant="15" id="bQj-A6-7eZ"/>
                    <constraint firstItem="90" firstAttribute="top" secondItem="89" secondAttribute="bottom" constant="15" id="bcF-w3-cwR"/>
                    <constraint firstAttribute="trailing" secondItem="88" secondAttribute="trailing" constant="20" id="oMh-GX-IpN"/>
                    <constraint firstItem="91" firstAttribute="leading" secondItem="86" secondAttribute="leading" id="uLZ-IN-W1s"/>
                    <constraint firstItem="90" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="86" secondAttribute="leading" id="vq7-Ll-Kvl"/>
                    <constraint firstItem="103" firstAttribute="top" secondItem="86" secondAttribute="top" constant="10" id="xzj-UF-oo0"/>
                </constraints>
            </view>
            <point key="canvasLocation" x="167.5" y="-131.5"/>
        </window>
    </objects>
</document>
