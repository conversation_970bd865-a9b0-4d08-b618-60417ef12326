<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="17701" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="17701"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPExportController">
            <connections>
                <outlet property="errorsTextView" destination="451" id="456"/>
                <outlet property="errorsWindow" destination="441" id="454"/>
                <outlet property="exportAdvancedOptionsView" destination="1299" id="1319"/>
                <outlet property="exportAdvancedOptionsViewButton" destination="1300" id="1317"/>
                <outlet property="exportAdvancedOptionsViewLabelButton" destination="1301" id="1318"/>
                <outlet property="exportButton" destination="1297" id="1315"/>
                <outlet property="exportCSVFieldsEscapedField" destination="1187" id="1278"/>
                <outlet property="exportCSVFieldsTerminatedField" destination="1189" id="1279"/>
                <outlet property="exportCSVFieldsWrappedField" destination="1188" id="1280"/>
                <outlet property="exportCSVIncludeFieldNamesCheck" destination="1196" id="1281"/>
                <outlet property="exportCSVLinesTerminatedField" destination="1194" id="1282"/>
                <outlet property="exportCSVNULLValuesAsTextField" destination="1184" id="1283"/>
                <outlet property="exportCustomFilenameTokenField" destination="1110" id="1290"/>
                <outlet property="exportCustomFilenameTokenPool" destination="1114" id="1291"/>
                <outlet property="exportCustomFilenameView" destination="1102" id="1295"/>
                <outlet property="exportCustomFilenameViewButton" destination="1100" id="1292"/>
                <outlet property="exportCustomFilenameViewLabelButton" destination="1101" id="1293"/>
                <outlet property="exportDeselectAllTablesButton" destination="1414" id="1419"/>
                <outlet property="exportDotForceLowerTableNamesCheck" destination="1375" id="1377"/>
                <outlet property="exportFilePerTableCheck" destination="1092" id="1288"/>
                <outlet property="exportFilenameDividerBox" destination="1095" id="1294"/>
                <outlet property="exportFormatInfoText" destination="1378" id="1380"/>
                <outlet property="exportInputPopUpButton" destination="1103" id="1287"/>
                <outlet property="exportOptionsTabBar" destination="1088" id="1258"/>
                <outlet property="exportOutputCompressionFormatPopupButton" destination="1338" id="1348"/>
                <outlet property="exportPathField" destination="1094" id="1284"/>
                <outlet property="exportProcessLowMemoryButton" destination="1306" id="1316"/>
                <outlet property="exportProgressIndicator" destination="298" id="308"/>
                <outlet property="exportProgressText" destination="299" id="307"/>
                <outlet property="exportProgressTitle" destination="297" id="306"/>
                <outlet property="exportProgressWindow" destination="294" id="305"/>
                <outlet property="exportRefreshTablesButton" destination="1404" id="1417"/>
                <outlet property="exportSQLBLOBFieldsAsHexCheck" destination="1149" id="1269"/>
                <outlet property="exportSQLIncludeAutoIncrementValueButton" destination="1392" id="1394"/>
                <outlet property="exportSQLIncludeContentCheck" destination="1153" id="1268"/>
                <outlet property="exportSQLIncludeDropSyntaxCheck" destination="1395" id="1398"/>
                <outlet property="exportSQLIncludeErrorsCheck" destination="1154" id="1266"/>
                <outlet property="exportSQLIncludeGeneratedColumnsCheck" destination="OuX-71-K6z" id="1l4-oI-Axt"/>
                <outlet property="exportSQLIncludeStructureCheck" destination="1157" id="1265"/>
                <outlet property="exportSQLInsertDividerPopUpButton" destination="1146" id="1264"/>
                <outlet property="exportSQLInsertNValueTextField" destination="1147" id="1263"/>
                <outlet property="exportSelectAllTablesButton" destination="1410" id="1421"/>
                <outlet property="exportTableList" destination="1229" id="1273"/>
                <outlet property="exportTableListButtonBar" destination="1408" id="1409"/>
                <outlet property="exportTablelistScrollView" destination="1087" id="1275"/>
                <outlet property="exportTypeTabBar" destination="573" id="1257"/>
                <outlet property="exportUseUTF8BOMButton" destination="1150" id="1276"/>
                <outlet property="exportXMLFormatPopUpButton" destination="1365" id="1373"/>
                <outlet property="exportXMLIncludeContent" destination="1357" id="1363"/>
                <outlet property="exportXMLIncludeStructure" destination="1355" id="1364"/>
                <outlet property="exportXMLNULLValuesAsTextField" destination="1216" id="1321"/>
                <outlet property="exporterView" destination="1086" id="1255"/>
                <outlet property="window" destination="1" id="390"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="Export Window" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" visibleAtLaunch="NO" frameAutosaveName="SPExportWindow" animationBehavior="default" id="1" userLabel="Export Window">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="610" y="273" width="750" height="498"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2560" height="1417"/>
            <value key="minSize" type="size" width="730" height="498"/>
            <view key="contentView" id="2">
                <rect key="frame" x="0.0" y="0.0" width="750" height="498"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <tabView fixedFrame="YES" allowsTruncatedLabels="NO" initialItem="574" translatesAutoresizingMaskIntoConstraints="NO" id="573">
                        <rect key="frame" x="-8" y="69" width="766" height="423"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <font key="font" metaFont="system"/>
                        <tabViewItems>
                            <tabViewItem label="SQL" identifier="sql" id="574">
                                <view key="view" id="577">
                                    <rect key="frame" x="10" y="33" width="746" height="377"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                </view>
                            </tabViewItem>
                            <tabViewItem label="CSV" identifier="csv" id="575">
                                <view key="view" id="576">
                                    <rect key="frame" x="10" y="33" width="746" height="377"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                </view>
                            </tabViewItem>
                            <tabViewItem label="XML" identifier="xml" id="578">
                                <view key="view" id="579">
                                    <rect key="frame" x="10" y="33" width="513" height="377"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                </view>
                            </tabViewItem>
                            <tabViewItem label="Dot" identifier="dot" id="580">
                                <view key="view" id="581">
                                    <rect key="frame" x="10" y="33" width="513" height="377"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                </view>
                            </tabViewItem>
                        </tabViewItems>
                        <connections>
                            <outlet property="delegate" destination="-2" id="1256"/>
                        </connections>
                    </tabView>
                    <button verticalHuggingPriority="750" fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="1297">
                        <rect key="frame" x="630" y="12" width="104" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Export" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="1310">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSheet:" target="-2" id="1313"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1298">
                        <rect key="frame" x="526" y="12" width="104" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="1309">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSheet:" target="-2" id="1311"/>
                        </connections>
                    </button>
                    <customView hidden="YES" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1299" userLabel="Advanced View for INSERT">
                        <rect key="frame" x="21" y="-16" width="707" height="61"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                        <subviews>
                            <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1304">
                                <rect key="frame" x="-83" y="35" width="100" height="18"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                <buttonCell key="cell" type="check" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="1308">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" metaFont="message" size="11"/>
                                </buttonCell>
                            </button>
                            <box autoresizesSubviews="NO" fixedFrame="YES" borderType="line" title="Box" titlePosition="noTitle" translatesAutoresizingMaskIntoConstraints="NO" id="1305">
                                <rect key="frame" x="-3" y="-4" width="713" height="67"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                                <view key="contentView" id="hwO-O5-18U">
                                    <rect key="frame" x="3" y="3" width="707" height="61"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <subviews>
                                        <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1306">
                                            <rect key="frame" x="15" y="37" width="397" height="18"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                            <buttonCell key="cell" type="check" title="Low memory export (may block server)" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="1307">
                                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                <font key="font" metaFont="message" size="11"/>
                                            </buttonCell>
                                        </button>
                                        <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1336">
                                            <rect key="frame" x="15" y="11" width="117" height="14"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                            <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Output compression:" id="1337">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                        </textField>
                                        <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1338">
                                            <rect key="frame" x="134" y="6" width="232" height="22"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                            <popUpButtonCell key="cell" type="push" title="None" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="1341" id="1339">
                                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                <font key="font" metaFont="message" size="11"/>
                                                <menu key="menu" title="OtherViews" id="1340">
                                                    <items>
                                                        <menuItem title="None" state="on" id="1341"/>
                                                        <menuItem title="Gzip (Very fast, good compression)" tag="1" id="1342"/>
                                                        <menuItem title="Bzip2 (Slower, very good compression)" tag="2" id="1343"/>
                                                    </items>
                                                </menu>
                                            </popUpButtonCell>
                                            <connections>
                                                <action selector="changeExportCompressionFormat:" target="-2" id="1349"/>
                                            </connections>
                                        </popUpButton>
                                    </subviews>
                                </view>
                            </box>
                        </subviews>
                    </customView>
                    <button toolTip="Advanced export settings" horizontalHuggingPriority="750" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1300">
                        <rect key="frame" x="10" y="45" width="29" height="26"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="disclosureTriangle" bezelStyle="disclosure" imagePosition="left" alignment="center" borderStyle="border" imageScaling="proportionallyUpOrDown" inset="2" id="1303">
                            <behavior key="behavior" pushIn="YES" changeBackground="YES" changeGray="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent">a</string>
                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                        </buttonCell>
                        <connections>
                            <action selector="toggleAdvancedExportOptionsView:" target="-2" id="1314"/>
                        </connections>
                    </button>
                    <button toolTip="Advanced export settings" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1301">
                        <rect key="frame" x="33" y="46" width="400" height="22"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="bevel" title="Advanced" bezelStyle="rounded" alignment="left" controlSize="small" state="on" inset="2" id="1302">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                        </buttonCell>
                        <connections>
                            <action selector="toggleAdvancedExportOptionsView:" target="-2" id="1312"/>
                        </connections>
                    </button>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1378">
                        <rect key="frame" x="17" y="22" width="507" height="14"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                        <textFieldCell key="cell" controlSize="small" lineBreakMode="truncatingTail" allowsUndo="NO" sendsActionOnEndEditing="YES" id="1379">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" white="0.50403225419999997" alpha="1" colorSpace="calibratedWhite"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                </subviews>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="266"/>
            </connections>
            <point key="canvasLocation" x="139" y="147"/>
        </window>
        <window title="Export Progress" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="294" userLabel="Export Progress Sheet" customClass="NSPanel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="101" y="476" width="379" height="139"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2560" height="1417"/>
            <value key="minSize" type="size" width="213" height="50"/>
            <view key="contentView" id="295">
                <rect key="frame" x="0.0" y="0.0" width="379" height="139"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <progressIndicator verticalHuggingPriority="750" fixedFrame="YES" maxValue="100" bezeled="NO" indeterminate="YES" style="bar" translatesAutoresizingMaskIntoConstraints="NO" id="298">
                        <rect key="frame" x="18" y="56" width="343" height="20"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </progressIndicator>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="299">
                        <rect key="frame" x="59" y="84" width="300" height="17"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <textFieldCell key="cell" lineBreakMode="truncatingMiddle" truncatesLastVisibleLine="YES" sendsActionOnEndEditing="YES" alignment="left" title="Exporting…" id="302">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" white="0.5" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="296">
                        <rect key="frame" x="265" y="12" width="100" height="32"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" borderStyle="border" inset="2" id="304">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="cancelExport:" target="-2" id="309"/>
                        </connections>
                    </button>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="297">
                        <rect key="frame" x="59" y="104" width="300" height="17"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <textFieldCell key="cell" lineBreakMode="truncatingTail" sendsActionOnEndEditing="YES" alignment="left" title="Doing Stuff…" id="303">
                            <font key="font" metaFont="systemBold"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <imageView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="300">
                        <rect key="frame" x="20" y="87" width="32" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyDown" image="NSApplicationIcon" id="301"/>
                    </imageView>
                </subviews>
            </view>
            <point key="canvasLocation" x="-426" y="581"/>
        </window>
        <window title="Error" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="441" userLabel="Error Sheet">
            <windowStyleMask key="styleMask" titled="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="467" y="379" width="405" height="267"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2560" height="1417"/>
            <value key="minSize" type="size" width="213" height="107"/>
            <view key="contentView" id="442">
                <rect key="frame" x="0.0" y="0.0" width="405" height="267"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="367" translatesAutoresizingMaskIntoConstraints="NO" id="446">
                        <rect key="frame" x="17" y="233" width="371" height="14"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinY="YES"/>
                        <textFieldCell key="cell" sendsActionOnEndEditing="YES" alignment="left" title="The following export errors occurred:" id="447">
                            <font key="font" metaFont="smallSystemBold"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <scrollView fixedFrame="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="444">
                        <rect key="frame" x="20" y="45" width="365" height="180"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <clipView key="contentView" drawsBackground="NO" id="kbp-v6-xrL">
                            <rect key="frame" x="1" y="1" width="363" height="178"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <textView editable="NO" importsGraphics="NO" richText="NO" verticallyResizable="YES" id="451">
                                    <rect key="frame" x="0.0" y="0.0" width="363" height="178"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <size key="minSize" width="363" height="178"/>
                                    <size key="maxSize" width="717" height="10000000"/>
                                    <color key="insertionPointColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                </textView>
                            </subviews>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" controlSize="small" horizontal="YES" id="449">
                            <rect key="frame" x="-100" y="-100" width="87" height="18"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" controlSize="small" horizontal="NO" id="450">
                            <rect key="frame" x="350" y="1" width="14" height="178"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                    </scrollView>
                    <button focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="445">
                        <rect key="frame" x="301" y="13" width="89" height="28"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES"/>
                        <buttonCell key="cell" type="push" title="Close" bezelStyle="rounded" alignment="center" borderStyle="border" focusRingType="none" inset="2" id="448">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSheet:" target="-2" id="457"/>
                        </connections>
                    </button>
                    <button focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="443" userLabel="Push Button (Close Dummy for ESC)">
                        <rect key="frame" x="350" y="-130" width="80" height="28"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES"/>
                        <buttonCell key="cell" type="push" title="Close" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" focusRingType="none" inset="2" id="452">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                    </button>
                </subviews>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="455"/>
                <outlet property="initialFirstResponder" destination="445" id="453"/>
            </connections>
            <point key="canvasLocation" x="48" y="631"/>
        </window>
        <customView id="1086" userLabel="Exporter View">
            <rect key="frame" x="0.0" y="0.0" width="730" height="398"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <subviews>
                <tabView fixedFrame="YES" type="noTabsBezelBorder" initialItem="1144" translatesAutoresizingMaskIntoConstraints="NO" id="1088">
                    <rect key="frame" x="438" y="16" width="276" height="288"/>
                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" heightSizable="YES"/>
                    <font key="font" metaFont="system"/>
                    <tabViewItems>
                        <tabViewItem label="SQL" identifier="sql" id="1144">
                            <view key="view" id="1145">
                                <rect key="frame" x="10" y="7" width="256" height="268"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1157">
                                        <rect key="frame" x="11" y="215" width="233" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <buttonCell key="cell" type="check" title="Structure" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" state="on" inset="2" id="1158">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="toggleSQLIncludeStructure:" target="-2" id="1244"/>
                                        </connections>
                                    </button>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1156">
                                        <rect key="frame" x="12" y="239" width="233" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Include:" id="1159">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1152">
                                        <rect key="frame" x="12" y="115" width="233" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Advanced:" id="1163">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1154">
                                        <rect key="frame" x="12" y="135" width="232" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <buttonCell key="cell" type="check" title="Errors" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="1161">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                    </button>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1153">
                                        <rect key="frame" x="12" y="155" width="232" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <buttonCell key="cell" type="check" title="Content" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" state="on" inset="2" id="1162">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="toggleSQLIncludeContent:" target="-2" id="1254"/>
                                        </connections>
                                    </button>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1150">
                                        <rect key="frame" x="12" y="91" width="232" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <buttonCell key="cell" type="check" title="Add UTF-8 BOM to output" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="1165">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                    </button>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1149">
                                        <rect key="frame" x="12" y="71" width="232" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <buttonCell key="cell" type="check" title="Output BLOB fields as hex" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" state="on" inset="2" id="1166">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                    </button>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="OuX-71-K6z">
                                        <rect key="frame" x="12" y="51" width="232" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <buttonCell key="cell" type="check" title="Include GENERATED columns" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="auv-V6-aUQ">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                    </button>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1148">
                                        <rect key="frame" x="12" y="31" width="233" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="New INSERT statement every:" id="1167">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1147">
                                        <rect key="frame" x="15" y="4" width="60" height="19"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="1168">
                                            <numberFormatter key="formatter" formatterBehavior="default10_4" numberStyle="decimal" minimumIntegerDigits="1" maximumIntegerDigits="2000000000" maximumFractionDigits="3" id="1169">
                                                <real key="minimum" value="0.0"/>
                                            </numberFormatter>
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1146">
                                        <rect key="frame" x="80" y="1" width="117" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <popUpButtonCell key="cell" type="push" title="KiB of data" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="1172" id="1170">
                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                            <menu key="menu" title="OtherViews" id="1171">
                                                <items>
                                                    <menuItem title="KiB of data" state="on" id="1172"/>
                                                    <menuItem title="Rows" id="1173"/>
                                                </items>
                                            </menu>
                                        </popUpButtonCell>
                                    </popUpButton>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1392">
                                        <rect key="frame" x="29" y="195" width="215" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <buttonCell key="cell" type="check" title="Auto increment value" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="1393">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                    </button>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1395">
                                        <rect key="frame" x="29" y="176" width="215" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <buttonCell key="cell" type="check" title="DROP TABLE syntax" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" state="on" inset="2" id="1396">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                        <connections>
                                            <action selector="toggleSQLIncludeDropSyntax:" target="-2" id="1397"/>
                                        </connections>
                                    </button>
                                </subviews>
                            </view>
                        </tabViewItem>
                        <tabViewItem label="Excel" identifier="excel" id="1143">
                            <view key="view" id="1174">
                                <rect key="frame" x="10" y="7" width="256" height="248"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <matrix verticalHuggingPriority="750" fixedFrame="YES" selectionByRect="NO" allowsEmptySelection="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1177">
                                        <rect key="frame" x="14" y="135" width="106" height="100"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        <size key="cellSize" width="106" height="20"/>
                                        <size key="intercellSpacing" width="4" height="60"/>
                                        <buttonCell key="prototype" type="radio" title="Radio" imagePosition="left" alignment="left" controlSize="small" inset="2" id="1178">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                        <cells>
                                            <column>
                                                <buttonCell type="radio" title="Sheet per table" imagePosition="left" alignment="left" controlSize="small" state="on" tag="1" inset="2" id="1180">
                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                    <font key="font" metaFont="message" size="11"/>
                                                </buttonCell>
                                                <buttonCell type="radio" title="File per table" imagePosition="left" alignment="left" controlSize="small" inset="2" id="1179">
                                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                    <font key="font" metaFont="message" size="11"/>
                                                </buttonCell>
                                            </column>
                                        </cells>
                                    </matrix>
                                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="150" translatesAutoresizingMaskIntoConstraints="NO" id="1176">
                                        <rect key="frame" x="33" y="166" width="154" height="42"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" sendsActionOnEndEditing="YES" title="This creates a sheet for each table. The sheet names match the table names." id="1181">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="150" translatesAutoresizingMaskIntoConstraints="NO" id="1175">
                                        <rect key="frame" x="33" y="85" width="154" height="42"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" sendsActionOnEndEditing="YES" title="This creates a new file for each table. The file names match the table names." id="1182">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                </subviews>
                            </view>
                        </tabViewItem>
                        <tabViewItem label="CSV" identifier="csv" id="1142">
                            <view key="view" id="1183">
                                <rect key="frame" x="10" y="7" width="256" height="248"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1192">
                                        <rect key="frame" x="5" y="197" width="240" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Fields:" id="1201">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1195">
                                        <rect key="frame" x="5" y="98" width="240" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Lines:" id="1198">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="195" translatesAutoresizingMaskIntoConstraints="NO" id="1191">
                                        <rect key="frame" x="46" y="144" width="199" height="17"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" sendsActionOnEndEditing="YES" alignment="left" title="Wrap" id="1202">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="195" translatesAutoresizingMaskIntoConstraints="NO" id="1190">
                                        <rect key="frame" x="46" y="169" width="199" height="17"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" sendsActionOnEndEditing="YES" alignment="left" title="Terminate" id="1203">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <comboBox toolTip="Character used to separate fields" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1189">
                                        <rect key="frame" x="8" y="168" width="36" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <comboBoxCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" borderStyle="bezel" alignment="left" title="," drawsBackground="YES" usesSingleLineMode="YES" completes="NO" numberOfVisibleItems="3" id="1204">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            <objectValues>
                                                <string>,</string>
                                                <string>;</string>
                                                <string>\t</string>
                                            </objectValues>
                                        </comboBoxCell>
                                        <connections>
                                            <outlet property="delegate" destination="-2" id="1350"/>
                                        </connections>
                                    </comboBox>
                                    <comboBox toolTip="Character used to enclose fields" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1188">
                                        <rect key="frame" x="8" y="143" width="36" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <comboBoxCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" borderStyle="bezel" alignment="left" title="&quot;" drawsBackground="YES" usesSingleLineMode="YES" completes="NO" numberOfVisibleItems="2" id="1205">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            <objectValues>
                                                <string>"</string>
                                                <string></string>
                                            </objectValues>
                                        </comboBoxCell>
                                    </comboBox>
                                    <comboBox toolTip="Character used to escape special characters" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1187">
                                        <rect key="frame" x="8" y="116" width="36" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <comboBoxCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" borderStyle="bezel" alignment="left" title="&quot;" drawsBackground="YES" usesSingleLineMode="YES" completes="NO" numberOfVisibleItems="2" id="1206">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            <objectValues>
                                                <string>"</string>
                                                <string>\</string>
                                            </objectValues>
                                        </comboBoxCell>
                                    </comboBox>
                                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="195" translatesAutoresizingMaskIntoConstraints="NO" id="1186">
                                        <rect key="frame" x="46" y="117" width="199" height="17"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" sendsActionOnEndEditing="YES" alignment="left" title="Escape" id="1207">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <comboBox toolTip="Character used to terminate lines" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1194">
                                        <rect key="frame" x="8" y="69" width="36" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <comboBoxCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" borderStyle="bezel" alignment="left" title="\n" drawsBackground="YES" usesSingleLineMode="YES" completes="NO" numberOfVisibleItems="3" id="1199">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            <objectValues>
                                                <string>\n</string>
                                                <string>\r\n</string>
                                                <string>\r</string>
                                            </objectValues>
                                        </comboBoxCell>
                                    </comboBox>
                                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="195" translatesAutoresizingMaskIntoConstraints="NO" id="1193">
                                        <rect key="frame" x="46" y="70" width="199" height="17"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" sendsActionOnEndEditing="YES" alignment="left" title="Terminate" id="1200">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1196">
                                        <rect key="frame" x="5" y="217" width="259" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <buttonCell key="cell" type="check" title="Put field names in first row" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" state="on" inset="2" id="1197">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                    </button>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1185">
                                        <rect key="frame" x="5" y="48" width="240" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="NULL values:" id="1208">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1184">
                                        <rect key="frame" x="8" y="21" width="55" height="19"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="1209">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                </subviews>
                            </view>
                        </tabViewItem>
                        <tabViewItem label="HTML" identifier="html" id="1141">
                            <view key="view" id="1210">
                                <rect key="frame" x="10" y="7" width="256" height="248"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            </view>
                        </tabViewItem>
                        <tabViewItem label="XML" identifier="xml" id="1140">
                            <view key="view" id="1215">
                                <rect key="frame" x="10" y="7" width="256" height="248"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1217">
                                        <rect key="frame" x="11" y="131" width="72" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="NULL values:" id="1218">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1216">
                                        <rect key="frame" x="14" y="104" width="55" height="19"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" enabled="NO" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="1219">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1355">
                                        <rect key="frame" x="11" y="171" width="179" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <buttonCell key="cell" type="check" title="Structure" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" state="on" inset="2" id="1360">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                    </button>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1356">
                                        <rect key="frame" x="12" y="195" width="180" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Include:" id="1359">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1357">
                                        <rect key="frame" x="11" y="151" width="179" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <buttonCell key="cell" type="check" title="Content" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" state="on" inset="2" id="1358">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                    </button>
                                    <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1365">
                                        <rect key="frame" x="59" y="212" width="119" height="22"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <popUpButtonCell key="cell" type="push" title="MySQL Schema" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="1368" id="1366">
                                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                            <menu key="menu" title="OtherViews" id="1367">
                                                <items>
                                                    <menuItem title="MySQL Schema" state="on" id="1368"/>
                                                    <menuItem title="Plain Schema" id="1369"/>
                                                </items>
                                            </menu>
                                        </popUpButtonCell>
                                        <connections>
                                            <action selector="toggleXMLOutputFormat:" target="-2" id="1374"/>
                                        </connections>
                                    </popUpButton>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1371">
                                        <rect key="frame" x="11" y="217" width="46" height="14"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Format:" id="1372">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                </subviews>
                            </view>
                        </tabViewItem>
                        <tabViewItem label="PDF" identifier="pdf" id="1139">
                            <view key="view" id="1220">
                                <rect key="frame" x="10" y="7" width="256" height="248"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            </view>
                        </tabViewItem>
                        <tabViewItem label="Dot" identifier="dot" id="1138">
                            <view key="view" id="1225">
                                <rect key="frame" x="10" y="7" width="256" height="248"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1375">
                                        <rect key="frame" x="2" y="217" width="262" height="18"/>
                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                        <buttonCell key="cell" type="check" title="Use case-insensitive links" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="1376">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                        <attributedString key="userComments">
                                            <fragment>
                                                <string key="content">When the default configuration of MySQL server is running on Windows or macOS, or case sensitivity has been disabled, this option should be enabled to allow table relations to work correctly.</string>
                                                <attributes>
                                                    <color key="NSColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                    <font key="NSFont" metaFont="message" size="11"/>
                                                    <font key="NSOriginalFont" metaFont="message" size="11"/>
                                                    <paragraphStyle key="NSParagraphStyle" alignment="natural" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0"/>
                                                </attributes>
                                            </fragment>
                                        </attributedString>
                                    </button>
                                </subviews>
                            </view>
                        </tabViewItem>
                    </tabViewItems>
                </tabView>
                <button hidden="YES" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1092">
                    <rect key="frame" x="226" y="308" width="273" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="check" title="New file per table" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" state="on" inset="2" id="1130">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="message" size="11"/>
                    </buttonCell>
                    <connections>
                        <action selector="toggleNewFilePerTable:" target="-2" id="1381"/>
                    </connections>
                </button>
                <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1093">
                    <rect key="frame" x="17" y="371" width="105" height="14"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Path:" id="1129">
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1094">
                    <rect key="frame" x="127" y="369" width="450" height="19"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                    <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" focusRingType="none" drawsBackground="YES" id="1128">
                        <font key="font" metaFont="smallSystem"/>
                        <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <box verticalHuggingPriority="750" fixedFrame="YES" boxType="separator" translatesAutoresizingMaskIntoConstraints="NO" id="1095">
                    <rect key="frame" x="20" y="332" width="690" height="5"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                </box>
                <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1096">
                    <rect key="frame" x="579" y="364" width="96" height="29"/>
                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="push" title="Change..." bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="1127">
                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                    <connections>
                        <action selector="changeExportOutputPath:" target="-2" id="1243"/>
                    </connections>
                </button>
                <button toolTip="Customize filename" horizontalHuggingPriority="750" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1100">
                    <rect key="frame" x="9" y="338" width="29" height="26"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="disclosureTriangle" bezelStyle="disclosure" imagePosition="left" alignment="center" refusesFirstResponder="YES" borderStyle="border" imageScaling="proportionallyUpOrDown" inset="2" id="1118">
                        <behavior key="behavior" pushIn="YES" changeBackground="YES" changeGray="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <action selector="toggleCustomFilenameFormatView:" target="-2" id="1246"/>
                    </connections>
                </button>
                <button toolTip="Customize filename" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1101">
                    <rect key="frame" x="32" y="336" width="680" height="28"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="bevel" title="Customize Filename" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" truncatesLastVisibleLine="YES" refusesFirstResponder="YES" state="on" inset="2" id="1117">
                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="message" size="11"/>
                    </buttonCell>
                    <connections>
                        <action selector="toggleCustomFilenameFormatView:" target="-2" id="1252"/>
                    </connections>
                </button>
                <customView hidden="YES" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1102">
                    <rect key="frame" x="0.0" y="227" width="730" height="108"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                    <subviews>
                        <box autoresizesSubviews="NO" fixedFrame="YES" borderType="line" title="Box" titlePosition="noTitle" translatesAutoresizingMaskIntoConstraints="NO" id="1109">
                            <rect key="frame" x="-11" y="-4" width="748" height="114"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                            <view key="contentView" id="TKV-dD-vlv">
                                <rect key="frame" x="3" y="3" width="742" height="108"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <subviews>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1110" customClass="NSTokenField">
                                        <rect key="frame" x="29" y="55" width="695" height="19"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="1116">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <connections>
                                            <outlet property="delegate" destination="-2" id="1239"/>
                                        </connections>
                                    </textField>
                                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1112">
                                        <rect key="frame" x="29" y="82" width="698" height="14"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Type or drag the tokens to customize the filename format." id="1113">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                    </textField>
                                    <box autoresizesSubviews="NO" fixedFrame="YES" borderType="line" title="Box" titlePosition="noTitle" translatesAutoresizingMaskIntoConstraints="NO" id="1111">
                                        <rect key="frame" x="26" y="6" width="701" height="40"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                        <view key="contentView" id="ZpD-mY-jmD">
                                            <rect key="frame" x="3" y="3" width="695" height="34"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <subviews>
                                                <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1114" customClass="NSTokenField">
                                                    <rect key="frame" x="21" y="4" width="659" height="22"/>
                                                    <autoresizingMask key="autoresizingMask" widthSizable="YES"/>
                                                    <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" allowsUndo="NO" sendsActionOnEndEditing="YES" state="on" alignment="left" id="1115">
                                                        <font key="font" metaFont="message" size="11"/>
                                                        <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                    </textFieldCell>
                                                    <connections>
                                                        <outlet property="delegate" destination="-2" id="SPs-so-H4L"/>
                                                    </connections>
                                                </textField>
                                            </subviews>
                                        </view>
                                    </box>
                                </subviews>
                            </view>
                        </box>
                    </subviews>
                </customView>
                <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1103">
                    <rect key="frame" x="17" y="305" width="207" height="22"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" autoenablesItems="NO" id="1104">
                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="message" size="11"/>
                        <menu key="menu" title="OtherViews" autoenablesItems="NO" id="1105">
                            <items>
                                <menuItem title="Filtered Results" id="1108"/>
                                <menuItem title="Query Results" id="1107"/>
                                <menuItem title="Tables" id="1106"/>
                            </items>
                        </menu>
                    </popUpButtonCell>
                    <connections>
                        <action selector="switchInput:" target="-2" id="1241"/>
                    </connections>
                </popUpButton>
                <box autoresizesSubviews="NO" fixedFrame="YES" boxType="custom" borderType="none" title="Box" translatesAutoresizingMaskIntoConstraints="NO" id="1408">
                    <rect key="frame" x="21" y="19" width="414" height="24"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                    <view key="contentView" id="LUo-wY-Z8D">
                        <rect key="frame" x="0.0" y="0.0" width="414" height="24"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <button toolTip="Refresh table list" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1404">
                                <rect key="frame" x="0.0" y="0.0" width="25" height="25"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSRefreshTemplate" imagePosition="only" alignment="center" alternateImage="NSRefreshTemplate" inset="2" id="1405">
                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                    <font key="font" metaFont="system"/>
                                </buttonCell>
                                <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                <connections>
                                    <action selector="refreshTableList:" target="-2" id="1416"/>
                                </connections>
                            </button>
                            <button toolTip="Mark all tables" fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="1410">
                                <rect key="frame" x="389" y="0.0" width="25" height="25"/>
                                <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSMenuOnStateTemplate" imagePosition="only" alignment="center" alternateImage="NSMenuOnStateTemplate" inset="2" id="1411">
                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                    <font key="font" metaFont="system"/>
                                </buttonCell>
                                <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                <connections>
                                    <action selector="selectDeselectAllTables:" target="-2" id="1420"/>
                                </connections>
                            </button>
                            <button toolTip="Unmark all tables" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="1414">
                                <rect key="frame" x="356" y="0.0" width="26" height="25"/>
                                <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSMenuMixedStateTemplate" imagePosition="only" alignment="center" alternateImage="NSMenuMixedStateTemplate" inset="2" id="1415">
                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                    <font key="font" metaFont="system"/>
                                </buttonCell>
                                <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                <connections>
                                    <action selector="selectDeselectAllTables:" target="-2" id="1418"/>
                                </connections>
                            </button>
                        </subviews>
                    </view>
                </box>
                <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="fTa-Pi-oat">
                    <rect key="frame" x="674" y="367" width="41" height="22"/>
                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                    <popUpButtonCell key="cell" type="push" title="S" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" pullsDown="YES" selectedItem="n0o-5Z-jWq" id="CFF-pW-Ooq">
                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="message" size="11"/>
                        <menu key="menu" id="FZy-tc-1Lv">
                            <items>
                                <menuItem title="S" state="on" hidden="YES" id="7YA-Zh-COE"/>
                                <menuItem title="Apply saved settings…" keyEquivalent="o" toolTip="Replace the current export settings with settings loaded from disk" id="ENy-fo-Iwf">
                                    <connections>
                                        <action selector="importCurrentSettings:" target="-2" id="abL-95-pHf"/>
                                    </connections>
                                </menuItem>
                                <menuItem title="Save current settings…" keyEquivalent="s" toolTip="Save the current export settings to disk" id="n0o-5Z-jWq">
                                    <connections>
                                        <action selector="exportCurrentSettings:" target="-2" id="qlz-iS-nue"/>
                                    </connections>
                                </menuItem>
                            </items>
                        </menu>
                    </popUpButtonCell>
                </popUpButton>
                <scrollView focusRingType="none" fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="19" horizontalPageScroll="10" verticalLineScroll="19" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1087">
                    <rect key="frame" x="21" y="42" width="414" height="259"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    <clipView key="contentView" id="tJ1-mx-cYK">
                        <rect key="frame" x="1" y="0.0" width="412" height="258"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView focusRingType="none" verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="firstColumnOnly" alternatingRowBackgroundColors="YES" columnReordering="NO" columnResizing="NO" multipleSelection="NO" autosaveColumns="NO" headerView="1226" id="1229">
                                <rect key="frame" x="0.0" y="0.0" width="412" height="241"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <size key="intercellSpacing" width="3" height="2"/>
                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                <tableColumns>
                                    <tableColumn identifier="tables" editable="NO" width="297" minWidth="10" maxWidth="1000" id="1233">
                                        <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Table">
                                            <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                        </tableHeaderCell>
                                        <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" alignment="left" title="Text Cell" bezelStyle="round" id="1234">
                                            <font key="font" metaFont="message" size="11"/>
                                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                        </textFieldCell>
                                        <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                    </tableColumn>
                                    <tableColumn identifier="structure" width="15" minWidth="15" maxWidth="15" headerToolTip="Include structure" id="1232">
                                        <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="S">
                                            <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                        </tableHeaderCell>
                                        <buttonCell key="dataCell" type="check" title="Check" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="1235">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                        <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                    </tableColumn>
                                    <tableColumn identifier="content" width="15" minWidth="15" maxWidth="15" headerToolTip="Include content" id="1231">
                                        <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="C">
                                            <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                        </tableHeaderCell>
                                        <buttonCell key="dataCell" type="check" title="Check" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="1236">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                        </buttonCell>
                                        <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                    </tableColumn>
                                    <tableColumn identifier="drop" width="35" minWidth="35" maxWidth="35" headerToolTip="Include DROP TABLE syntax" id="1230">
                                        <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="D">
                                            <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                        </tableHeaderCell>
                                        <buttonCell key="dataCell" type="check" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="1237">
                                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                            <font key="font" metaFont="message" size="11"/>
                                            <accessibility description="Check"/>
                                        </buttonCell>
                                        <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                    </tableColumn>
                                </tableColumns>
                                <connections>
                                    <outlet property="dataSource" destination="-2" id="1238"/>
                                    <outlet property="delegate" destination="-2" id="1240"/>
                                </connections>
                            </tableView>
                        </subviews>
                    </clipView>
                    <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="1228">
                        <rect key="frame" x="-100" y="-100" width="191" height="15"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </scroller>
                    <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="NO" id="1227">
                        <rect key="frame" x="148" y="1" width="11" height="228"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </scroller>
                    <tableHeaderView key="headerView" wantsLayer="YES" id="1226">
                        <rect key="frame" x="0.0" y="0.0" width="412" height="17"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </tableHeaderView>
                </scrollView>
            </subviews>
            <constraints>
                <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="411" id="1uC-QN-wbV"/>
            </constraints>
        </customView>
        <userDefaultsController representsSharedInstance="YES" id="1385"/>
    </objects>
    <resources>
        <image name="NSApplicationIcon" width="32" height="32"/>
        <image name="NSMenuMixedStateTemplate" width="10" height="2"/>
        <image name="NSMenuOnStateTemplate" width="12" height="12"/>
        <image name="NSRefreshTemplate" width="11" height="15"/>
    </resources>
</document>
