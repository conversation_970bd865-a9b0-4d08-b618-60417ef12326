<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="17701" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="17701"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPWindowController">
            <connections>
                <outlet property="window" destination="1" id="15"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="Window" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" frameAutosaveName="MainWindow" animationBehavior="default" id="1" customClass="SPWindow">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <rect key="contentRect" x="892" y="421" width="1200" height="630"/>
            <rect key="screenRect" x="0.0" y="0.0" width="3008" height="1667"/>
            <value key="minSize" type="size" width="800" height="400"/>
            <view key="contentView" translatesAutoresizingMaskIntoConstraints="NO" id="2">
                <rect key="frame" x="0.0" y="0.0" width="1200" height="630"/>
                <autoresizingMask key="autoresizingMask"/>
            </view>
            <point key="canvasLocation" x="185" y="110"/>
        </window>
    </objects>
</document>
