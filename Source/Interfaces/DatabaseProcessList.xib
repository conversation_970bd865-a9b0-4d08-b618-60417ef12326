<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="17506" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="17506"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPProcessListController">
            <connections>
                <outlet property="autoRefreshButton" destination="105" id="108"/>
                <outlet property="autoRefreshIntervalMenuItem" destination="113" id="132"/>
                <outlet property="customIntervalButton" destination="135" id="180"/>
                <outlet property="customIntervalTextField" destination="176" id="183"/>
                <outlet property="customIntervalWindow" destination="133" id="179"/>
                <outlet property="filterProcessesSearchField" destination="6" id="68"/>
                <outlet property="processListTableView" destination="17" id="45"/>
                <outlet property="processesCountTextField" destination="93" id="95"/>
                <outlet property="refreshProcessesButton" destination="75" id="91"/>
                <outlet property="refreshProgressIndicator" destination="69" id="70"/>
                <outlet property="saveProcessesButton" destination="87" id="92"/>
                <outlet property="window" destination="3" id="40"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="Processes" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="3" userLabel="Process List">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="386" y="573" width="640" height="203"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2880" height="1595"/>
            <value key="minSize" type="size" width="640" height="200"/>
            <view key="contentView" id="4">
                <rect key="frame" x="0.0" y="0.0" width="640" height="203"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <scrollView focusRingType="none" fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="18" horizontalPageScroll="10" verticalLineScroll="18" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7">
                        <rect key="frame" x="0.0" y="30" width="642" height="138"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <clipView key="contentView" id="ze9-NR-RuY">
                            <rect key="frame" x="1" y="1" width="640" height="136"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="lastColumnOnly" alternatingRowBackgroundColors="YES" columnReordering="NO" autosaveColumns="NO" rowHeight="16" headerView="14" id="17">
                                    <rect key="frame" x="0.0" y="0.0" width="640" height="119"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <size key="intercellSpacing" width="3" height="2"/>
                                    <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                    <tableColumns>
                                        <tableColumn identifier="Id" editable="NO" width="61" minWidth="40" maxWidth="1000" id="19">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="ID">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" alignment="left" title="Text Cell" id="20">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                            </textFieldCell>
                                            <sortDescriptor key="sortDescriptorPrototype" selector="compare:" sortKey="Id"/>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="User" editable="NO" width="84" minWidth="40" maxWidth="1000" id="18">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="User">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="21">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                            </textFieldCell>
                                            <sortDescriptor key="sortDescriptorPrototype" selector="compare:" sortKey="User"/>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="Host" editable="NO" width="120" minWidth="10" maxWidth="3.4028234663852886e+38" id="28">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Host">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="29">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <sortDescriptor key="sortDescriptorPrototype" selector="compare:" sortKey="Host"/>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="db" editable="NO" width="68" minWidth="10" maxWidth="3.4028234663852886e+38" id="30">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Database">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="31">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <sortDescriptor key="sortDescriptorPrototype" selector="compare:" sortKey="db"/>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="Command" editable="NO" width="97" minWidth="10" maxWidth="3.4028234663852886e+38" id="32">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Command">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="33">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <sortDescriptor key="sortDescriptorPrototype" selector="compare:" sortKey="Command"/>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="Time" editable="NO" width="55" minWidth="10" maxWidth="3.4028234663852886e+38" id="34">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Time">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="35">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <sortDescriptor key="sortDescriptorPrototype" selector="compare:" sortKey="Time"/>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="State" editable="NO" width="73" minWidth="10" maxWidth="3.4028234663852886e+38" id="36">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="State">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="37">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <sortDescriptor key="sortDescriptorPrototype" selector="compare:" sortKey="State"/>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="Info" editable="NO" width="49" minWidth="10" maxWidth="3.4028234663852886e+38" id="38">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Info">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="39">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <sortDescriptor key="sortDescriptorPrototype" selector="compare:" sortKey="Info"/>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="Progress" editable="NO" width="73" minWidth="10" maxWidth="3.4028234663852886e+38" id="KAh-gr-TIJ">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Progress">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="UhB-2O-IP2">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <sortDescriptor key="sortDescriptorPrototype" selector="compare:" sortKey="Progress"/>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                    </tableColumns>
                                    <connections>
                                        <outlet property="dataSource" destination="-2" id="43"/>
                                        <outlet property="delegate" destination="-2" id="44"/>
                                        <outlet property="menu" destination="51" id="61"/>
                                    </connections>
                                </tableView>
                            </subviews>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="YES" id="15">
                            <rect key="frame" x="-100" y="-100" width="358" height="11"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="NO" id="16">
                            <rect key="frame" x="576" y="17" width="11" height="251"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <tableHeaderView key="headerView" wantsLayer="YES" id="14">
                            <rect key="frame" x="0.0" y="0.0" width="640" height="17"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </tableHeaderView>
                    </scrollView>
                    <searchField wantsLayer="YES" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="6">
                        <rect key="frame" x="10" y="176" width="601" height="19"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                        <searchFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" borderStyle="bezel" placeholderString="Filter" usesSingleLineMode="YES" bezelStyle="round" sendsSearchStringImmediately="YES" id="22">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </searchFieldCell>
                        <connections>
                            <outlet property="delegate" destination="-2" id="42"/>
                        </connections>
                    </searchField>
                    <progressIndicator hidden="YES" horizontalHuggingPriority="750" verticalHuggingPriority="750" fixedFrame="YES" maxValue="100" displayedWhenStopped="NO" bezeled="NO" indeterminate="YES" controlSize="small" style="spinning" translatesAutoresizingMaskIntoConstraints="NO" id="69">
                        <rect key="frame" x="619" y="178" width="16" height="16"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                    </progressIndicator>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="87">
                        <rect key="frame" x="472" y="0.0" width="170" height="28"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Save As..." bezelStyle="rounded" alignment="center" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="88">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="saveServerProcesses:" target="-2" id="89"/>
                        </connections>
                    </button>
                    <button toolTip="Refresh process list (⌘R)" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="75">
                        <rect key="frame" x="35" y="0.0" width="25" height="25"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSRefreshTemplate" imagePosition="only" alignment="center" alternateImage="NSRefreshTemplate" state="on" imageScaling="proportionallyDown" inset="2" id="85">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent">r</string>
                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                        </buttonCell>
                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <connections>
                            <action selector="refreshProcessList:" target="-2" id="90"/>
                        </connections>
                    </button>
                    <popUpButton fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="76">
                        <rect key="frame" x="0.0" y="0.0" width="35" height="25"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <popUpButtonCell key="cell" type="bevel" bezelStyle="regularSquare" imagePosition="only" alignment="center" alternateImage="NSActionTemplate" lineBreakMode="truncatingTail" inset="2" pullsDown="YES" selectedItem="84" id="77">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <menu key="menu" title="OtherViews" id="78">
                                <items>
                                    <menuItem image="NSActionTemplate" hidden="YES" id="82"/>
                                    <menuItem title="Show Process ID" state="on" toolTip="Toggle the display of the Process ID column" id="84">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                        <connections>
                                            <action selector="toggleShowProcessID:" target="-2" id="100"/>
                                            <binding destination="96" name="value" keyPath="values.ProcessListShowProcessID" id="99">
                                                <dictionary key="options">
                                                    <bool key="NSConditionallySetsEnabled" value="NO"/>
                                                </dictionary>
                                            </binding>
                                        </connections>
                                    </menuItem>
                                    <menuItem title="Show FULL Process List" id="186">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                        <connections>
                                            <action selector="toggeleShowFullProcessList:" target="-2" id="187"/>
                                            <binding destination="96" name="value" keyPath="values.ProcessListShowFullProcessList" id="188"/>
                                        </connections>
                                    </menuItem>
                                    <menuItem isSeparatorItem="YES" id="112"/>
                                    <menuItem title="Auto Refresh Interval" id="113">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                        <menu key="submenu" title="Auto Refresh Interval" id="114">
                                            <items>
                                                <menuItem title="2 Seconds" tag="2" id="115">
                                                    <modifierMask key="keyEquivalentModifierMask"/>
                                                    <connections>
                                                        <action selector="setAutoRefreshInterval:" target="-2" id="123"/>
                                                    </connections>
                                                </menuItem>
                                                <menuItem title="5 Seconds" tag="5" id="116">
                                                    <modifierMask key="keyEquivalentModifierMask"/>
                                                    <connections>
                                                        <action selector="setAutoRefreshInterval:" target="-2" id="124"/>
                                                    </connections>
                                                </menuItem>
                                                <menuItem title="10 Seconds" tag="10" id="117">
                                                    <modifierMask key="keyEquivalentModifierMask"/>
                                                    <connections>
                                                        <action selector="setAutoRefreshInterval:" target="-2" id="129"/>
                                                    </connections>
                                                </menuItem>
                                                <menuItem title="30 Seconds" tag="30" id="118">
                                                    <modifierMask key="keyEquivalentModifierMask"/>
                                                    <connections>
                                                        <action selector="setAutoRefreshInterval:" target="-2" id="130"/>
                                                    </connections>
                                                </menuItem>
                                                <menuItem title="60 Seconds" tag="60" id="119">
                                                    <modifierMask key="keyEquivalentModifierMask"/>
                                                    <connections>
                                                        <action selector="setAutoRefreshInterval:" target="-2" id="131"/>
                                                    </connections>
                                                </menuItem>
                                                <menuItem isSeparatorItem="YES" id="120"/>
                                                <menuItem title="Custom..." id="121">
                                                    <modifierMask key="keyEquivalentModifierMask"/>
                                                    <connections>
                                                        <action selector="setCustomAutoRefreshInterval:" target="-2" id="128"/>
                                                    </connections>
                                                </menuItem>
                                            </items>
                                        </menu>
                                    </menuItem>
                                </items>
                            </menu>
                        </popUpButtonCell>
                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                    </popUpButton>
                    <button toolTip="Enable/disable auto-refresh (⇧⌘R)" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="105">
                        <rect key="frame" x="65" y="0.0" width="122" height="25"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="check" title="Auto refresh" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="106">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent">R</string>
                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                        </buttonCell>
                        <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                        <connections>
                            <action selector="toggleProcessListAutoRefresh:" target="-2" id="107"/>
                            <binding destination="96" name="value" keyPath="values.ProcessListEnableAutoRefresh" id="110"/>
                        </connections>
                    </button>
                    <textField hidden="YES" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="93">
                        <rect key="frame" x="193" y="0.0" width="280" height="23"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                        <textFieldCell key="cell" lineBreakMode="truncatingTail" sendsActionOnEndEditing="YES" alignment="right" title="0 of 0" id="94">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                </subviews>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="111"/>
            </connections>
            <point key="canvasLocation" x="139" y="161.5"/>
        </window>
        <menu id="51" userLabel="Contextual Menu">
            <items>
                <menuItem title="Copy" id="55">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="copy:" target="-2" id="62"/>
                    </connections>
                </menuItem>
                <menuItem isSeparatorItem="YES" id="58"/>
                <menuItem title="Kill Query" id="56">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="killProcessQuery:" target="-2" id="59"/>
                    </connections>
                </menuItem>
                <menuItem title="Kill Connection" id="57">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="killProcessConnection:" target="-2" id="60"/>
                    </connections>
                </menuItem>
            </items>
            <connections>
                <outlet property="delegate" destination="-2" id="63"/>
            </connections>
        </menu>
        <userDefaultsController representsSharedInstance="YES" id="96"/>
        <window title="Custom Interval" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="133" userLabel="Custom Interval">
            <windowStyleMask key="styleMask" titled="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="343" y="502" width="229" height="93"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2880" height="1595"/>
            <value key="minSize" type="size" width="189" height="93"/>
            <value key="maxSize" type="size" width="292" height="112"/>
            <view key="contentView" id="134">
                <rect key="frame" x="0.0" y="0.0" width="229" height="93"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="138">
                        <rect key="frame" x="8" y="56" width="69" height="14"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Interval:" id="143">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="173">
                        <rect key="frame" x="136" y="56" width="76" height="14"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="left" title="seconds" id="174">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button verticalHuggingPriority="750" fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="135">
                        <rect key="frame" x="134" y="13" width="80" height="28"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" widthSizable="YES"/>
                        <buttonCell key="cell" type="push" title="OK" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" tag="1" inset="2" id="172">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSheet:" target="-2" id="185"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="140">
                        <rect key="frame" x="50" y="13" width="86" height="28"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" widthSizable="YES"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" inset="2" id="141">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSheet:" target="-2" id="184"/>
                        </connections>
                    </button>
                    <textField toolTip="Custom refresh interval. Must be greater than 0." verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="176">
                        <rect key="frame" x="82" y="52" width="49" height="22"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="177">
                            <numberFormatter key="formatter" formatterBehavior="default10_4" numberStyle="decimal" minimumIntegerDigits="1" maximumIntegerDigits="309" maximumFractionDigits="3" id="178">
                                <real key="minimum" value="1"/>
                            </numberFormatter>
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                        <connections>
                            <outlet property="delegate" destination="-2" id="182"/>
                        </connections>
                    </textField>
                </subviews>
            </view>
        </window>
    </objects>
    <resources>
        <image name="NSActionTemplate" width="15" height="15"/>
        <image name="NSRefreshTemplate" width="14" height="16"/>
    </resources>
</document>
