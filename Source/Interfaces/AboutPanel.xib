<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="17701" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="17701"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPAboutController">
            <connections>
                <outlet property="appBuildVersionTextField" destination="43" id="45"/>
                <outlet property="appCreditsTextView" destination="25" id="50"/>
                <outlet property="appLicensePanel" destination="7" id="56"/>
                <outlet property="appLicenseTextView" destination="13" id="57"/>
                <outlet property="appNameVersionTextField" destination="19" id="40"/>
                <outlet property="window" destination="6" id="54"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" visibleAtLaunch="NO" animationBehavior="default" titlebarAppearsTransparent="YES" id="6" userLabel="About Panel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" fullSizeContentView="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="428" y="316" width="600" height="320"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2560" height="1415"/>
            <value key="minSize" type="size" width="213" height="107"/>
            <view key="contentView" id="15">
                <rect key="frame" x="0.0" y="0.0" width="600" height="320"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <scrollView horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="20">
                        <rect key="frame" x="210" y="20" width="370" height="280"/>
                        <clipView key="contentView" id="GrD-eR-6P6">
                            <rect key="frame" x="1" y="1" width="368" height="278"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <textView editable="NO" importsGraphics="NO" verticallyResizable="YES" id="25">
                                    <rect key="frame" x="0.0" y="0.0" width="368" height="278"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <size key="minSize" width="368" height="278"/>
                                    <size key="maxSize" width="537" height="10000000"/>
                                    <color key="insertionPointColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                </textView>
                            </subviews>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="23">
                            <rect key="frame" x="-100" y="-100" width="87" height="18"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="NO" id="24">
                            <rect key="frame" x="355" y="1" width="14" height="278"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                    </scrollView>
                    <textField verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="19">
                        <rect key="frame" x="18" y="121" width="174" height="21"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" state="on" alignment="center" title="Sequel Ace" id="26">
                            <font key="font" metaFont="system" size="18"/>
                            <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="16">
                        <rect key="frame" x="23" y="52" width="164" height="32"/>
                        <constraints>
                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="150" id="8ny-qN-gUR"/>
                        </constraints>
                        <buttonCell key="cell" type="push" title="License" bezelStyle="rounded" alignment="center" borderStyle="border" inset="2" id="29">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="openApplicationLicenseSheet:" target="-2" id="53"/>
                        </connections>
                    </button>
                    <imageView translatesAutoresizingMaskIntoConstraints="NO" id="35">
                        <rect key="frame" x="30" y="150" width="150" height="150"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="150" id="Il9-Lu-1nr"/>
                            <constraint firstAttribute="height" constant="150" id="c98-eY-t62"/>
                        </constraints>
                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" imageScaling="proportionallyUpOrDown" image="AppIconImage" id="36"/>
                    </imageView>
                    <textField verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="43">
                        <rect key="frame" x="8" y="91" width="194" height="14"/>
                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" allowsUndo="NO" sendsActionOnEndEditing="YES" alignment="center" title="Build Version" id="44">
                            <font key="font" metaFont="label" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                </subviews>
                <constraints>
                    <constraint firstItem="16" firstAttribute="centerX" secondItem="35" secondAttribute="centerX" id="4Jg-sN-yoi"/>
                    <constraint firstAttribute="bottom" secondItem="20" secondAttribute="bottom" constant="20" id="91C-29-3TU"/>
                    <constraint firstItem="20" firstAttribute="leading" secondItem="35" secondAttribute="trailing" constant="30" id="DJh-bc-ETQ"/>
                    <constraint firstItem="43" firstAttribute="top" secondItem="19" secondAttribute="bottom" constant="16" id="DmY-oH-rik"/>
                    <constraint firstItem="35" firstAttribute="top" secondItem="15" secondAttribute="top" constant="20" id="QDv-3g-XjA"/>
                    <constraint firstAttribute="trailing" secondItem="20" secondAttribute="trailing" constant="20" id="RMJ-sg-f40"/>
                    <constraint firstItem="20" firstAttribute="leading" secondItem="19" secondAttribute="trailing" constant="20" id="Wne-Wf-8Yn"/>
                    <constraint firstItem="43" firstAttribute="leading" secondItem="15" secondAttribute="leading" constant="10" id="cAM-T0-47w"/>
                    <constraint firstItem="20" firstAttribute="leading" secondItem="43" secondAttribute="trailing" constant="10" id="fqL-7x-MUM"/>
                    <constraint firstItem="19" firstAttribute="leading" secondItem="15" secondAttribute="leading" constant="20" id="gKn-5q-6Co"/>
                    <constraint firstItem="19" firstAttribute="top" secondItem="35" secondAttribute="bottom" constant="8" id="iFe-f6-AuV"/>
                    <constraint firstItem="35" firstAttribute="leading" secondItem="15" secondAttribute="leading" constant="30" id="nqW-1e-QM5"/>
                    <constraint firstItem="20" firstAttribute="top" secondItem="35" secondAttribute="top" id="qTL-OJ-leS"/>
                    <constraint firstItem="16" firstAttribute="top" secondItem="43" secondAttribute="bottom" constant="12" id="t3B-yw-awQ"/>
                </constraints>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="55"/>
            </connections>
            <point key="canvasLocation" x="139" y="147.5"/>
        </window>
        <window title="License" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="7" userLabel="License Panel" customClass="NSPanel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="197" y="142" width="500" height="400"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2560" height="1415"/>
            <value key="minSize" type="size" width="500" height="400"/>
            <value key="maxSize" type="size" width="500" height="400"/>
            <view key="contentView" id="8">
                <rect key="frame" x="0.0" y="0.0" width="500" height="400"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <scrollView horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="10">
                        <rect key="frame" x="20" y="60" width="460" height="320"/>
                        <clipView key="contentView" drawsBackground="NO" id="1cm-5U-29V">
                            <rect key="frame" x="1" y="1" width="458" height="318"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <textView editable="NO" importsGraphics="NO" richText="NO" verticallyResizable="YES" id="13">
                                    <rect key="frame" x="0.0" y="0.0" width="458" height="318"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <size key="minSize" width="458" height="318"/>
                                    <size key="maxSize" width="941" height="10000000"/>
                                    <color key="insertionPointColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                </textView>
                            </subviews>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="11">
                            <rect key="frame" x="-100" y="-100" width="87" height="18"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="NO" id="12">
                            <rect key="frame" x="445" y="1" width="14" height="318"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                    </scrollView>
                    <button verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="9">
                        <rect key="frame" x="372" y="13" width="114" height="32"/>
                        <constraints>
                            <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="100" id="VWh-9D-bYy"/>
                        </constraints>
                        <buttonCell key="cell" type="push" title="OK" bezelStyle="rounded" alignment="center" borderStyle="border" inset="2" id="14">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeApplicationLicenseSheet:" target="-2" id="52"/>
                        </connections>
                    </button>
                </subviews>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="9" secondAttribute="bottom" constant="20" id="2Lz-Ew-b09"/>
                    <constraint firstItem="9" firstAttribute="trailing" secondItem="13" secondAttribute="trailing" id="7Ek-q2-SOC"/>
                    <constraint firstItem="10" firstAttribute="leading" secondItem="8" secondAttribute="leading" constant="20" id="ReR-5C-ySB"/>
                    <constraint firstItem="9" firstAttribute="top" secondItem="10" secondAttribute="bottom" constant="20" id="S5v-Jf-Lre"/>
                    <constraint firstItem="10" firstAttribute="top" secondItem="8" secondAttribute="top" constant="20" id="Xnh-Vx-f2g"/>
                    <constraint firstItem="9" firstAttribute="leading" relation="greaterThanOrEqual" secondItem="13" secondAttribute="leading" id="mgU-lA-NBO"/>
                    <constraint firstAttribute="trailing" secondItem="10" secondAttribute="trailing" constant="20" id="wiI-fl-rwd"/>
                </constraints>
            </view>
            <point key="canvasLocation" x="139" y="598"/>
        </window>
    </objects>
    <resources>
        <image name="AppIconImage" width="256" height="256"/>
    </resources>
</document>
