<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="17506" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="17506"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPSSHTunnel">
            <connections>
                <outlet property="sshPasswordDialog" destination="468" id="482"/>
                <outlet property="sshPasswordField" destination="484" id="488"/>
                <outlet property="sshPasswordKeychainCheckbox" destination="491" id="493"/>
                <outlet property="sshPasswordText" destination="473" id="483"/>
                <outlet property="sshQuestionDialog" destination="367" id="465"/>
                <outlet property="sshQuestionText" destination="450" id="464"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="SSH Tunnel Query" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" visibleAtLaunch="NO" animationBehavior="default" id="367">
            <windowStyleMask key="styleMask" titled="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="196" y="301" width="620" height="209"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2880" height="1595"/>
            <view key="contentView" id="368">
                <rect key="frame" x="0.0" y="0.0" width="620" height="209"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="473" translatesAutoresizingMaskIntoConstraints="NO" id="450">
                        <rect key="frame" x="126" y="60" width="477" height="129"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <textFieldCell key="cell" controlSize="mini" sendsActionOnEndEditing="YES" id="451">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button verticalHuggingPriority="750" fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="458">
                        <rect key="frame" x="510" y="12" width="96" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Yes" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="459">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <action selector="closeSSHQuestionSheet:" target="-2" id="496"/>
                            <outlet property="nextKeyView" destination="460" id="499"/>
                        </connections>
                    </button>
                    <imageView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="462">
                        <rect key="frame" x="20" y="115" width="75" height="74"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" animates="YES" imageScaling="proportionallyDown" image="AppIconImage" id="463"/>
                    </imageView>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="460">
                        <rect key="frame" x="414" y="12" width="96" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="No" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="461">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSSHQuestionSheet:" target="-2" id="497"/>
                            <outlet property="nextKeyView" destination="458" id="500"/>
                        </connections>
                    </button>
                    <imageView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="456">
                        <rect key="frame" x="68" y="113" width="32" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" animates="YES" imageScaling="proportionallyDown" image="toolbar-preferences-network" id="457"/>
                    </imageView>
                </subviews>
            </view>
            <connections>
                <outlet property="initialFirstResponder" destination="458" id="501"/>
            </connections>
            <point key="canvasLocation" x="139" y="148"/>
        </window>
        <window title="SSH Tunnel Password Query" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" visibleAtLaunch="NO" animationBehavior="default" id="468">
            <windowStyleMask key="styleMask" titled="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="196" y="301" width="471" height="209"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2880" height="1595"/>
            <view key="contentView" id="469">
                <rect key="frame" x="0.0" y="0.0" width="471" height="209"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="324" translatesAutoresizingMaskIntoConstraints="NO" id="473">
                        <rect key="frame" x="126" y="113" width="328" height="76"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxX="YES" heightSizable="YES"/>
                        <textFieldCell key="cell" controlSize="mini" sendsActionOnEndEditing="YES" id="476">
                            <font key="font" metaFont="systemBold"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button verticalHuggingPriority="750" fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="471">
                        <rect key="frame" x="361" y="12" width="96" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="OK" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="478">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSSHPasswordSheet:" target="-2" id="494"/>
                            <outlet property="nextKeyView" destination="484" id="509"/>
                        </connections>
                    </button>
                    <imageView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="472">
                        <rect key="frame" x="20" y="115" width="75" height="74"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" animates="YES" imageScaling="proportionallyDown" image="AppIconImage" id="477"/>
                    </imageView>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="470">
                        <rect key="frame" x="265" y="12" width="96" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="479">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSSHPasswordSheet:" target="-2" id="495"/>
                            <outlet property="nextKeyView" destination="471" id="508"/>
                        </connections>
                    </button>
                    <imageView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="474">
                        <rect key="frame" x="68" y="113" width="32" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <imageCell key="cell" refusesFirstResponder="YES" alignment="left" animates="YES" imageScaling="proportionallyDown" image="toolbar-preferences-network" id="475"/>
                    </imageView>
                    <secureTextField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="484">
                        <rect key="frame" x="199" y="83" width="252" height="22"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" widthSizable="YES" flexibleMaxY="YES"/>
                        <secureTextFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="485">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                            <allowedInputSourceLocales>
                                <string>NSAllRomanInputSourcesLocaleIdentifier</string>
                            </allowedInputSourceLocales>
                        </secureTextFieldCell>
                        <connections>
                            <outlet property="nextKeyView" destination="491" id="510"/>
                        </connections>
                    </secureTextField>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="489">
                        <rect key="frame" x="126" y="85" width="68" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Password:" id="490">
                            <font key="font" metaFont="system"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="491">
                        <rect key="frame" x="197" y="59" width="253" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="check" title="Remember password in my keychain" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="492">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                        </buttonCell>
                        <connections>
                            <outlet property="nextKeyView" destination="470" id="511"/>
                        </connections>
                    </button>
                </subviews>
            </view>
            <connections>
                <outlet property="initialFirstResponder" destination="484" id="502"/>
            </connections>
            <point key="canvasLocation" x="139" y="455"/>
        </window>
    </objects>
    <resources>
        <image name="AppIconImage" width="256" height="256"/>
        <image name="toolbar-preferences-network" width="32" height="32"/>
    </resources>
</document>
