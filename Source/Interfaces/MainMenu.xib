<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="22154" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="22154"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="NSApplication">
            <connections>
                <outlet property="dockMenu" destination="956" id="1160"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject">
            <connections>
                <outlet property="delegate" destination="213" id="1001"/>
            </connections>
        </customObject>
        <menu title="MainMenu" systemMenu="main" id="29" userLabel="MainMenu">
            <items>
                <menuItem title="Sequel Ace" id="56">
                    <menu key="submenu" title="Sequel Ace" systemMenu="apple" id="57">
                        <items>
                            <menuItem title="About Sequel Ace" id="58">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="openAboutPanel:" target="213" id="1037"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="199">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Preferences..." keyEquivalent="," id="129">
                                <connections>
                                    <action selector="openPreferences:" target="213" id="218"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="143">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Visit Website..." id="516">
                                <connections>
                                    <action selector="visitWebsite:" target="213" id="517"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="435">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Services" id="131">
                                <menu key="submenu" title="Services" systemMenu="services" id="130"/>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="144">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Hide Sequel Ace" keyEquivalent="h" id="134">
                                <connections>
                                    <action selector="hide:" target="-2" id="152"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Hide Others" keyEquivalent="h" id="145">
                                <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                <connections>
                                    <action selector="hideOtherApplications:" target="-2" id="146"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Show All" id="150">
                                <connections>
                                    <action selector="unhideAllApplications:" target="-2" id="153"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="149">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Quit Sequel Ace" keyEquivalent="q" id="136">
                                <connections>
                                    <action selector="terminate:" target="-1" id="810"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="File" tag="1" id="83">
                    <menu key="submenu" title="File" id="81">
                        <items>
                            <menuItem title="New Connection Window" tag="1000" keyEquivalent="n" id="82">
                                <connections>
                                    <action selector="newWindow:" target="-1" id="1085"/>
                                </connections>
                            </menuItem>
                            <menuItem title="New Connection Tab" tag="1102" keyEquivalent="t" id="1086">
                                <connections>
                                    <action selector="newTab:" target="-1" id="1088"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Duplicate Connection Tab" tag="1104" keyEquivalent="T" toolTip="Duplicate the current connection tab" id="1186">
                                <connections>
                                    <action selector="duplicateTab:" target="-1" id="1188"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="1092"/>
                            <menuItem title="Add To Favorites" tag="1001" keyEquivalent="A" id="910">
                                <connections>
                                    <action selector="addConnectionToFavorites:" target="-1" id="911"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="984"/>
                            <menuItem title="Open…" tag="1002" keyEquivalent="o" toolTip="Open a connection file (*.spf), a connection session file (*.spfs), or load a SQL file (*.sql) into the Custom Query editor" id="943">
                                <connections>
                                    <action selector="openConnectionSheet:" target="-1" id="948"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Open Recent" id="971">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Open Recent" systemMenu="recentDocuments" id="980">
                                    <items>
                                        <menuItem title="Clear Menu" id="981">
                                            <connections>
                                                <action selector="clearRecentDocuments:" target="-1" id="982"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="324">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Save Connection" tag="1004" keyEquivalent="s" toolTip="Save active connection" id="963">
                                <connections>
                                    <action selector="saveConnectionSheet:" target="-1" id="964"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Save Session" tag="1020" keyEquivalent="s" toolTip="Save all opened connection windows and tabs respectively" id="1116">
                                <modifierMask key="keyEquivalentModifierMask" control="YES" option="YES" command="YES"/>
                                <connections>
                                    <action selector="saveConnectionSheet:" target="-1" id="1121"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Save Query" tag="1006" keyEquivalent="s" toolTip="Save Custom Query editor content as SQL file" id="944">
                                <modifierMask key="keyEquivalentModifierMask" control="YES" command="YES"/>
                                <connections>
                                    <action selector="saveConnectionSheet:" target="-1" id="949"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="79">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Import…" tag="1007" keyEquivalent="I" id="369">
                                <connections>
                                    <action selector="import:" target="-1" id="811"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Import from Clipboard…" tag="1101" alternate="YES" keyEquivalent="I" id="1071">
                                <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                <connections>
                                    <action selector="importFromClipboard:" target="-1" id="1072"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Export…" tag="-1" keyEquivalent="E" id="908">
                                <connections>
                                    <action selector="export:" target="-1" id="909"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="1000"/>
                            <menuItem title="Close" tag="1003" keyEquivalent="w" id="73">
                                <connections>
                                    <action selector="performClose:" target="-1" id="193"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="74">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Print..." tag="1010" keyEquivalent="p" id="78">
                                <connections>
                                    <action selector="printDocument:" target="-1" id="813"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="Edit" tag="2" id="163">
                    <menu key="submenu" title="Edit" id="169">
                        <items>
                            <menuItem title="Undo" keyEquivalent="z" id="158">
                                <connections>
                                    <action selector="undo:" target="-1" id="180"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Redo" keyEquivalent="Z" id="173">
                                <connections>
                                    <action selector="redo:" target="-1" id="178"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="844"/>
                            <menuItem title="Cut" keyEquivalent="x" id="160">
                                <connections>
                                    <action selector="cut:" target="-1" id="175"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Copy" tag="2001" keyEquivalent="c" id="157">
                                <connections>
                                    <action selector="copy:" target="-1" id="181"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Copy with Column Names" tag="2002" keyEquivalent="c" id="926">
                                <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                <connections>
                                    <action selector="copy:" target="-1" id="927"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Copy as SQL INSERT" tag="2003" keyEquivalent="c" id="935">
                                <modifierMask key="keyEquivalentModifierMask" control="YES" option="YES" command="YES"/>
                                <connections>
                                    <action selector="copy:" target="-1" id="936"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Copy as SQL INSERT (no auto_inc)" tag="2004" keyEquivalent="C" id="OrU-Yu-rQZ">
                                <modifierMask key="keyEquivalentModifierMask" control="YES" command="YES"/>
                                <connections>
                                    <action selector="copy:" target="-1" id="01y-O6-uCb"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Paste" keyEquivalent="v" id="171">
                                <connections>
                                    <action selector="paste:" target="-1" id="176"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Select" id="848">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Select" id="849">
                                    <items>
                                        <menuItem title="Word" keyEquivalent="w" toolTip="Select current word" id="850">
                                            <modifierMask key="keyEquivalentModifierMask" control="YES"/>
                                            <connections>
                                                <action selector="selectCurrentWord:" target="-1" id="895"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Line" keyEquivalent="l" toolTip="Select current line" id="863">
                                            <modifierMask key="keyEquivalentModifierMask" control="YES"/>
                                            <connections>
                                                <action selector="selectCurrentLine:" target="-1" id="896"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Active Query" keyEquivalent="y" id="kPp-CM-X9x">
                                            <modifierMask key="keyEquivalentModifierMask" control="YES"/>
                                            <connections>
                                                <action selector="selectCurrentQuery:" target="-1" id="ree-o4-Jbl"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Enclosing Brackets" keyEquivalent="B" id="932">
                                            <connections>
                                                <action selector="selectEnclosingBrackets:" target="-1" id="933"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="All" keyEquivalent="a" id="864">
                                            <connections>
                                                <action selector="selectAll:" target="-1" id="866"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="940"/>
                            <menuItem title="Insert NULL value" keyEquivalent="N" id="941">
                                <modifierMask key="keyEquivalentModifierMask" control="YES"/>
                                <connections>
                                    <action selector="insertNULLvalue:" target="-1" id="942"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Edit All Fields in Pop-up Sheet" id="1209">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <binding destination="1210" name="value" keyPath="values.EditInSheetEnabled" id="1212"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="174">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Convert" id="845">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Convert" id="846">
                                    <items>
                                        <menuItem title="to Uppercase" keyEquivalent="u" toolTip="Convert the selection or the current word into upper case" id="847">
                                            <modifierMask key="keyEquivalentModifierMask" control="YES"/>
                                            <connections>
                                                <action selector="doSelectionUpperCase:" target="-1" id="897"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="to Lowercase" keyEquivalent="U" toolTip="Convert the selection or the current word into lower case" id="851">
                                            <modifierMask key="keyEquivalentModifierMask" control="YES"/>
                                            <connections>
                                                <action selector="doSelectionLowerCase:" target="-1" id="898"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="to Titlecase" keyEquivalent="u" toolTip="Convert the first character of the selection or of the current word into upper case" id="852">
                                            <modifierMask key="keyEquivalentModifierMask" control="YES" option="YES"/>
                                            <connections>
                                                <action selector="doSelectionTitleCase:" target="-1" id="899"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="857"/>
                                        <menuItem title="Transpose" keyEquivalent="t" id="856">
                                            <modifierMask key="keyEquivalentModifierMask" control="YES"/>
                                            <connections>
                                                <action selector="doTranspose:" target="-1" id="904"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="858"/>
                                        <menuItem title="Remove Diacritics" enabled="NO" toolTip="Remove all diacritics from selection or current word [façadè → facade]" id="859">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="doRemoveDiacritics:" target="-1" id="905"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Normalization" id="853">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <menu key="submenu" title="Normalization" id="854">
                                                <items>
                                                    <menuItem title="Canonical Decomposing (NFD)" id="855">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="doDecomposedStringWithCanonicalMapping:" target="-1" id="900"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Canonical Composing (NFC)" id="860">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="doPrecomposedStringWithCanonicalMapping:" target="-1" id="901"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem isSeparatorItem="YES" id="865"/>
                                                    <menuItem title="Compatibility Decomposition (NFKD)" id="861">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="doDecomposedStringWithCompatibilityMapping:" target="-1" id="902"/>
                                                        </connections>
                                                    </menuItem>
                                                    <menuItem title="Compatibility Composition (NFKC)" id="862">
                                                        <modifierMask key="keyEquivalentModifierMask"/>
                                                        <connections>
                                                            <action selector="doPrecomposedStringWithCompatibilityMapping:" target="-1" id="903"/>
                                                        </connections>
                                                    </menuItem>
                                                </items>
                                            </menu>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Move" id="1168">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Move" id="1169">
                                    <items>
                                        <menuItem title="Line Up" keyEquivalent="" toolTip="Move the current line or the selected lines one line up" id="1170">
                                            <modifierMask key="keyEquivalentModifierMask" control="YES" command="YES"/>
                                            <connections>
                                                <action selector="moveSelectionLineUp:" target="-1" id="1172"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Line Down" keyEquivalent="" toolTip="Move the current line or the selected lines one line down" id="1171">
                                            <modifierMask key="keyEquivalentModifierMask" control="YES" command="YES"/>
                                            <connections>
                                                <action selector="moveSelectionLineDown:" target="-1" id="1173"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="156">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Find" id="168">
                                <menu key="submenu" title="Find" id="159">
                                    <items>
                                        <menuItem title="Find..." tag="1" keyEquivalent="f" id="154">
                                            <connections>
                                                <action selector="performFindPanelAction:" target="-1" id="922"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Find Next" tag="2" keyEquivalent="g" id="keD-kh-KVf">
                                            <connections>
                                                <action selector="performTextFinderAction:" target="-1" id="96h-iu-ZEx"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Find Previous" tag="3" keyEquivalent="G" id="JjG-V7-bdr">
                                            <connections>
                                                <action selector="performTextFinderAction:" target="-1" id="CUl-2X-EH5"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Jump to Selection" keyEquivalent="j" id="155">
                                            <connections>
                                                <action selector="centerSelectionInVisibleArea:" target="-1" id="1175"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Spelling and Grammar" id="KPx-re-8EE">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Spelling" id="UYw-Xs-8vf">
                                    <items>
                                        <menuItem title="Show Spelling and Grammar" keyEquivalent=":" id="05T-H3-24V">
                                            <connections>
                                                <action selector="showGuessPanel:" target="-1" id="kGB-mB-OYr"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Check Document Now" keyEquivalent=";" id="fCE-bz-bNv">
                                            <connections>
                                                <action selector="checkSpelling:" target="-1" id="ecI-hi-eLa"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="Wgq-So-DN8"/>
                                        <menuItem title="Check Spelling While Typing" id="1zB-4V-BF2">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="toggleContinuousSpellChecking:" target="-1" id="nn2-tQ-SiI"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Check Grammar With Spelling" id="oqd-zp-I6d">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="toggleGrammarChecking:" target="-1" id="Ovb-9u-igz"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Correct Spelling Automatically" id="Es7-1o-KcI">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="toggleAutomaticSpellingCorrection:" target="-1" id="mhI-Hy-VAZ"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Substitutions" id="zbw-kN-6gM">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Substitutions" id="tMm-qx-zUV">
                                    <items>
                                        <menuItem title="Show Substitutions" id="Kk3-QH-mmH">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="orderFrontSubstitutionsPanel:" target="-1" id="khk-AE-cvv"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" id="LHC-3N-tJv"/>
                                        <menuItem title="Smart Copy/Paste" id="c7b-hr-jQv">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="toggleSmartInsertDelete:" target="-1" id="UZx-xw-zrS"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Smart Quotes" id="u1T-CX-dx3">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="toggleAutomaticQuoteSubstitution:" target="-1" id="i1g-fk-Nyq"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Smart Dashes" id="Fct-0j-ck4">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="toggleAutomaticDashSubstitution:" target="-1" id="HEV-LH-OBw"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Smart Links" id="sGf-4f-QUu">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="toggleAutomaticLinkDetection:" target="-1" id="2QJ-2Z-Syo"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Data Detectors" id="rv0-EH-lPj">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="toggleAutomaticDataDetection:" target="-1" id="RxE-Rn-s3M"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Text Replacement" id="D2l-4I-GKa">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="toggleAutomaticTextReplacement:" target="-1" id="mFh-hJ-6K7"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem title="Speech" id="qK5-Ty-Uao">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <menu key="submenu" title="Speech" id="mGA-xh-Uwq">
                                    <items>
                                        <menuItem title="Start Speaking" id="elh-eq-jVU">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="startSpeaking:" target="-1" id="bd2-UP-v4o"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Stop Speaking" id="YwR-Sa-nn5">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="stopSpeaking:" target="-1" id="yqH-pe-byl"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="View" tag="3" id="496">
                    <menu key="submenu" title="View" id="498">
                        <items>
                            <menuItem title="Table Structure" keyEquivalent="1" id="497">
                                <connections>
                                    <action selector="viewStructure:" target="-1" id="501"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Table Content" keyEquivalent="2" id="499">
                                <connections>
                                    <action selector="viewContent:" target="-1" id="502"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Table Relations" keyEquivalent="3" id="928">
                                <connections>
                                    <action selector="viewRelations:" target="-1" id="929"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Table Triggers" keyEquivalent="4" id="1038">
                                <connections>
                                    <action selector="viewTriggers:" target="-1" id="1040"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Table Info" keyEquivalent="5" id="504">
                                <connections>
                                    <action selector="viewStatus:" target="-1" id="506"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Custom Query" keyEquivalent="6" id="500">
                                <connections>
                                    <action selector="viewQuery:" target="-1" id="503"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="990"/>
                            <menuItem title="Back In History" keyEquivalent="" id="991">
                                <modifierMask key="keyEquivalentModifierMask" control="YES" option="YES"/>
                                <connections>
                                    <action selector="backForwardInHistory:" target="-1" id="993"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Forward In History" tag="1" keyEquivalent="" id="992">
                                <modifierMask key="keyEquivalentModifierMask" control="YES" option="YES"/>
                                <connections>
                                    <action selector="backForwardInHistory:" target="-1" id="994"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="1095"/>
                            <menuItem title="Show Console" keyEquivalent="K" id="791">
                                <connections>
                                    <action selector="toggleConsole:" target="-1" id="832"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Clear Console" keyEquivalent="k" id="790">
                                <connections>
                                    <action selector="clearConsole:" target="-1" id="793"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="1194"/>
                            <menuItem title="Show Navigator" keyEquivalent="n" id="1050">
                                <modifierMask key="keyEquivalentModifierMask" control="YES" option="YES" command="YES"/>
                                <connections>
                                    <action selector="toggleNavigator:" target="-1" id="1051"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="1061"/>
                            <menuItem title="Display Binary Data as Hex" id="78w-kO-LU2">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <binding destination="1210" name="value" keyPath="values.DisplayBinaryDataAsHex" id="dOb-jz-pgr"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="0ww-n8-YRC"/>
                            <menuItem title="Hide Toolbar" keyEquivalent="t" id="807">
                                <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                <connections>
                                    <action selector="toggleToolbarShown:" target="-1" id="809"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Customize Toolbar..." id="806">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="runToolbarCustomizationPalette:" target="-1" id="808"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="1217"/>
                            <menuItem title="Enter Full Screen" keyEquivalent="f" id="1218">
                                <modifierMask key="keyEquivalentModifierMask" control="YES" command="YES"/>
                                <connections>
                                    <action selector="toggleFullScreen:" target="-1" id="1220"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="Database" tag="4" id="627">
                    <attributedString key="attributedTitle"/>
                    <menu key="submenu" title="Database" id="628">
                        <items>
                            <menuItem title="Go to Database…" keyEquivalent="D" id="8MG-hk-1qS">
                                <connections>
                                    <action selector="showGotoDatabase:" target="-1" id="jS0-aP-Y7o"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="Yzp-Qk-v7Y"/>
                            <menuItem title="Add Database..." keyEquivalent="A" id="629">
                                <modifierMask key="keyEquivalentModifierMask" option="YES"/>
                                <connections>
                                    <action selector="addDatabase:" target="-1" id="630"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Delete Database..." id="631">
                                <connections>
                                    <action selector="removeDatabase:" target="-1" id="659"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="1084"/>
                            <menuItem title="Duplicate Database..." id="1077">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="copyDatabase:" target="-1" id="1080"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Rename Database..." id="1081">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="renameDatabase:" target="-1" id="1083"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Alter Database…" id="1232">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="alterDatabase:" target="-1" id="1233"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="1079"/>
                            <menuItem title="Refresh Tables" keyEquivalent="r" id="1164">
                                <modifierMask key="keyEquivalentModifierMask" control="YES" command="YES"/>
                                <connections>
                                    <action selector="refreshTables:" target="-1" id="1167"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="1165"/>
                            <menuItem title="Flush Privileges" keyEquivalent="F" id="785">
                                <modifierMask key="keyEquivalentModifierMask" control="YES" command="YES"/>
                                <connections>
                                    <action selector="flushPrivileges:" target="-1" id="786"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Refresh Databases" keyEquivalent="R" id="633">
                                <connections>
                                    <action selector="setDatabases:" target="-1" id="660"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="634">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="User Accounts..." keyEquivalent="u" id="996">
                                <connections>
                                    <action selector="showUserManager:" target="-1" id="997"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="782">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="View Using Encoding" tag="1" id="658">
                                <menu key="submenu" title="View Using Encoding" id="635">
                                    <items>
                                        <menuItem title="UCS-2 Unicode (ucs2)" tag="10" id="649">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="662"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="UTF-8 Unicode BMP (utf8)" tag="20" id="642">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="663"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="UTF-8 Full Unicode (utf8mb4)" tag="190" id="JtW-Ko-SuZ">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="KxK-Oo-Rsq"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="UTF-8 Unicode via Latin 1" tag="30" id="835">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="836"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" tag="-1" id="647">
                                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                        </menuItem>
                                        <menuItem title="US ASCII (ascii)" tag="40" id="638">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="664"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="ISO Latin 1 (latin1)" tag="50" id="648">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="665"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Mac Roman (macroman)" tag="60" id="641">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="666"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" tag="-1" id="636">
                                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                        </menuItem>
                                        <menuItem title="Windows Latin 2 (cp1250)" tag="70" id="640">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="667"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="ISO Latin 2 (latin2)" tag="80" id="637">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="668"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" tag="-1" id="652">
                                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                        </menuItem>
                                        <menuItem title="Windows Arabic (cp1256)" tag="90" id="653">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="669"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="ISO Greek (greek)" tag="100" id="644">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="670"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="ISO Hebrew (hebrew)" tag="110" id="654">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="671"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="ISO Turkish (latin5)" tag="120" id="643">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="672"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" tag="-1" id="650">
                                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                        </menuItem>
                                        <menuItem title="Windows Baltic (cp1257)" tag="130" id="656">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="673"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" tag="-1" id="655">
                                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                        </menuItem>
                                        <menuItem title="Windows Cyrillic (cp1251)" tag="140" id="639">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="674"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem isSeparatorItem="YES" tag="-1" id="651">
                                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                        </menuItem>
                                        <menuItem title="Big5 Traditional Chinese (big5)" tag="150" id="657">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="675"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="Shift-JIS Japanese (sjis)" tag="160" id="645">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="676"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="EUC-JP Japanese (ujis)" tag="170" id="646">
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="677"/>
                                            </connections>
                                        </menuItem>
                                        <menuItem title="EUC-KR Korean (euckr)" tag="180" id="833">
                                            <modifierMask key="keyEquivalentModifierMask"/>
                                            <connections>
                                                <action selector="chooseEncoding:" target="-1" id="834"/>
                                            </connections>
                                        </menuItem>
                                    </items>
                                </menu>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="1195"/>
                            <menuItem title="Open Database in New Tab" id="1189">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="openDatabaseInNewTab:" target="-1" id="1190"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="1033"/>
                            <menuItem title="Show Server Variables..." keyEquivalent="V" id="783">
                                <connections>
                                    <action selector="showServerVariables:" target="-1" id="1035"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Show Server Processes..." keyEquivalent="p" id="1031">
                                <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                <connections>
                                    <action selector="showServerProcesses:" target="-1" id="1036"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="rzU-6x-Xih"/>
                            <menuItem title="Shutdown Server…" id="jZI-ad-FsC">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="shutdownServer:" target="-1" id="S1h-UE-mTv"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="Table" tag="5" id="757">
                    <menu key="submenu" title="Table" id="758">
                        <items>
                            <menuItem title="Filter Content" keyEquivalent="f" toolTip="Move the keyboard focus to the Content Table filter table fields (⌘F is an available shortcut on the Content view)" id="1041">
                                <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                <connections>
                                    <action selector="focusOnTableContentFilter:" target="-1" id="1044"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Advanced Filter Content" keyEquivalent="F" toolTip="Show the fast, or advanced, filter content table interface" id="1214">
                                <connections>
                                    <action selector="showFilterTable:" target="-1" id="1231"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Filter Tables" keyEquivalent="f" id="1045">
                                <modifierMask key="keyEquivalentModifierMask" control="YES" option="YES" command="YES"/>
                                <connections>
                                    <action selector="makeTableListFilterHaveFocus:" target="-1" id="1230"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="1162"/>
                            <menuItem title="Copy Create Table Syntax" keyEquivalent="C" id="759">
                                <modifierMask key="keyEquivalentModifierMask" shift="YES" command="YES"/>
                                <connections>
                                    <action selector="copyCreateTableSyntax:" target="-1" id="781"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Show Create Table Syntax..." keyEquivalent="s" id="778">
                                <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                <connections>
                                    <action selector="showCreateTableSyntax:" target="-1" id="780"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="765">
                                <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            </menuItem>
                            <menuItem title="Check Table" id="760">
                                <connections>
                                    <action selector="checkTable:" target="-1" id="767"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Repair Table" id="763">
                                <connections>
                                    <action selector="repairTable:" target="-1" id="770"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="906"/>
                            <menuItem title="Analyze Table" id="761">
                                <connections>
                                    <action selector="analyzeTable:" target="-1" id="768"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Optimize Table" id="762">
                                <connections>
                                    <action selector="optimizeTable:" target="-1" id="769"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Flush Table" id="764">
                                <connections>
                                    <action selector="flushTable:" target="-1" id="771"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Checksum Table" id="797">
                                <connections>
                                    <action selector="checksumTable:" target="-1" id="798"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="Bundles" tag="6" id="1178">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <menu key="submenu" title="Bundles" id="1179"/>
                </menuItem>
                <menuItem title="Window" tag="7" id="19">
                    <menu key="submenu" title="Window" systemMenu="window" id="24">
                        <items>
                            <menuItem title="Minimize" keyEquivalent="m" id="23">
                                <connections>
                                    <action selector="performMiniaturize:" target="-1" id="37"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Zoom" id="842">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="performZoom:" target="-1" id="843"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="985"/>
                            <menuItem title="Bring All to Front" id="5">
                                <connections>
                                    <action selector="arrangeInFront:" target="-1" id="39"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
                <menuItem title="Help" tag="8" id="103">
                    <menu key="submenu" title="Help" id="106">
                        <items>
                            <menuItem title="Sequel Ace Help" hidden="YES" keyEquivalent="?" id="111">
                                <connections>
                                    <action selector="showHelp:" target="-1" id="122"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Online Help" id="518">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="visitHelpWebsite:" target="213" id="520"/>
                                </connections>
                            </menuItem>
                            <menuItem title="Online FAQ" id="938">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="visitFAQWebsite:" target="213" id="939"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" hidden="YES" id="ZLx-5a-0BY"/>
                            <menuItem title="SSH Tunnel Debugging Info" hidden="YES" enabled="NO" id="Nzm-iu-YJV">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="showConnectionDebugMessages:" target="-1" id="PAE-lH-krZ"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="1048"/>
                            <menuItem title="Keyboard Shortcuts" id="1047">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="viewKeyboardShortcuts:" target="213" id="1049"/>
                                </connections>
                            </menuItem>
                            <menuItem isSeparatorItem="YES" id="913"/>
                            <menuItem title="MySQL Help" id="914">
                                <modifierMask key="keyEquivalentModifierMask"/>
                                <connections>
                                    <action selector="showMySQLHelp:" target="-1" id="916"/>
                                </connections>
                            </menuItem>
                            <menuItem title="MySQL Help for Word" keyEquivalent="H" id="nXE-rI-RyI">
                                <connections>
                                    <action selector="showMySQLHelpForCurrentWord:" target="-1" id="BUY-hF-nKy"/>
                                </connections>
                            </menuItem>
                        </items>
                        <connections>
                            <outlet property="delegate" destination="103" id="uyy-MC-UPd"/>
                        </connections>
                    </menu>
                </menuItem>
            </items>
            <connections>
                <outlet property="delegate" destination="213" id="1193"/>
            </connections>
            <point key="canvasLocation" x="-382" y="-36"/>
        </menu>
        <customObject id="213" userLabel="SPAppController" customClass="SPAppController">
            <connections>
                <outlet property="mainMenu" destination="57" id="GZs-gV-yIt"/>
                <outlet property="staleBookmarkHelpView" destination="Ipz-ua-fp5" id="psg-8l-YU5"/>
                <outlet property="staleBookmarkTextField" destination="Bea-mS-nZN" id="m3E-QZ-KCA"/>
                <outlet property="staleBookmarkTextFieldCell" destination="jQo-Yb-Hvj" id="8Ts-TE-F5t"/>
            </connections>
        </customObject>
        <menu id="956" userLabel="Dock Menu">
            <items>
                <menuItem title="New Connection Window..." keyEquivalent="n" id="960">
                    <connections>
                        <action selector="newWindow:" target="-1" id="1114"/>
                    </connections>
                </menuItem>
                <menuItem title="New Connection Tab..." keyEquivalent="t" id="1111">
                    <connections>
                        <action selector="newTab:" target="-1" id="1113"/>
                    </connections>
                </menuItem>
                <menuItem isSeparatorItem="YES" id="1112"/>
                <menuItem title="Open…" keyEquivalent="o" id="1005">
                    <connections>
                        <action selector="openConnectionSheet:" target="-1" id="1026"/>
                    </connections>
                </menuItem>
                <menuItem title="Open Recent" id="1006">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <menu key="submenu" title="Open Recent" systemMenu="recentDocuments" id="1015">
                        <items>
                            <menuItem title="Clear Menu" id="1016">
                                <connections>
                                    <action selector="clearRecentDocuments:" target="-1" id="1021"/>
                                </connections>
                            </menuItem>
                        </items>
                    </menu>
                </menuItem>
            </items>
            <point key="canvasLocation" x="139" y="32"/>
        </menu>
        <userDefaultsController representsSharedInstance="YES" id="1210"/>
        <customView id="Ipz-ua-fp5" userLabel="Bookmark Help Link">
            <rect key="frame" x="0.0" y="0.0" width="193" height="24"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <subviews>
                <button horizontalHuggingPriority="750" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="UmH-Wv-wVE">
                    <rect key="frame" x="-2" y="-2" width="25" height="25"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="help" bezelStyle="helpButton" alignment="center" enabled="NO" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="5qI-xv-uWF">
                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                </button>
                <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" textCompletion="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Bea-mS-nZN" customClass="HyperlinkTextField" customModule="Sequel_Ace" customModuleProvider="target">
                    <rect key="frame" x="23" y="0.0" width="137" height="21"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" allowsUndo="NO" sendsActionOnEndEditing="YES" borderStyle="bezel" alignment="left" title="Stale Bookmark Help" id="jQo-Yb-Hvj">
                        <font key="font" size="11" name="HelveticaNeue"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="string" keyPath="href" value="https://sequel-ace.com/ref/bookmarks.html#stale-security-scoped-bookmarks"/>
                    </userDefinedRuntimeAttributes>
                </textField>
            </subviews>
            <point key="canvasLocation" x="-108.5" y="295"/>
        </customView>
    </objects>
</document>
