<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="16097" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="16097"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPEncodingPopupAccessory">
            <connections>
                <outlet property="encodingAccessoryView" destination="1" id="11"/>
                <outlet property="encodingPopUp" destination="2" id="10"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <customView id="1" userLabel="Encoding Accessory">
            <rect key="frame" x="0.0" y="0.0" width="348" height="34"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <subviews>
                <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="2">
                    <rect key="frame" x="151" y="5" width="180" height="22"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <popUpButtonCell key="cell" type="push" title="Item 1" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" enabled="NO" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="9" id="5">
                        <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                        <font key="font" metaFont="message" size="11"/>
                        <menu key="menu" title="OtherViews" id="6">
                            <items>
                                <menuItem title="Item 1" state="on" id="9"/>
                                <menuItem title="Item 2" tag="1" id="8"/>
                                <menuItem title="Item 3" tag="2" id="7"/>
                            </items>
                        </menu>
                    </popUpButtonCell>
                </popUpButton>
                <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="3">
                    <rect key="frame" x="6" y="10" width="143" height="14"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="File Encoding:" id="4">
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
            </subviews>
            <point key="canvasLocation" x="139" y="154"/>
        </customView>
    </objects>
</document>
