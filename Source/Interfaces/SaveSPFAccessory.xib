<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="21219" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="21219"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPDatabaseDocument">
            <connections>
                <outlet property="saveConnectionAccessory" destination="1" id="32"/>
                <outlet property="saveConnectionAutoConnect" destination="13" id="30"/>
                <outlet property="saveConnectionEncrypt" destination="3" id="28"/>
                <outlet property="saveConnectionEncryptString" destination="2" id="29"/>
                <outlet property="saveConnectionIncludeData" destination="4" id="27"/>
                <outlet property="saveConnectionIncludeQuery" destination="15" id="26"/>
                <outlet property="saveConnectionSavePassword" destination="5" id="31"/>
                <outlet property="saveConnectionSavePasswordAlert" destination="35" id="44"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject">
            <connections>
                <outlet property="saveConnectionAccessory" destination="1" id="55"/>
            </connections>
        </customObject>
        <customView id="1" userLabel="Save SPF Accessory">
            <rect key="frame" x="0.0" y="0.0" width="432" height="138"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <subviews>
                <textField verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="199" translatesAutoresizingMaskIntoConstraints="NO" id="35">
                    <rect key="frame" x="210" y="106" width="203" height="28"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <textFieldCell key="cell" controlSize="small" sendsActionOnEndEditing="YES" alignment="center" title="It is highly recommended to encrypt the entire data!" id="36">
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                    <connections>
                        <binding destination="37" name="fontBold" keyPath="values._dummy" id="42">
                            <dictionary key="options">
                                <integer key="NSMultipleValuesPlaceholder" value="1"/>
                                <integer key="NSNoSelectionPlaceholder" value="1"/>
                                <integer key="NSNotApplicablePlaceholder" value="1"/>
                                <integer key="NSNullPlaceholder" value="1"/>
                            </dictionary>
                        </binding>
                    </connections>
                </textField>
                <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="13">
                    <rect key="frame" x="18" y="59" width="394" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="check" title="Auto connect when opening" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="14">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                </button>
                <secureTextField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="2">
                    <rect key="frame" x="233" y="84" width="157" height="19"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <secureTextFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="9">
                        <font key="font" metaFont="message" size="11"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        <allowedInputSourceLocales>
                            <string>NSAllRomanInputSourcesLocaleIdentifier</string>
                        </allowedInputSourceLocales>
                    </secureTextFieldCell>
                    <connections>
                        <outlet property="delegate" destination="-2" id="52"/>
                        <outlet property="nextKeyView" destination="4" id="51"/>
                    </connections>
                </secureTextField>
                <button toolTip="Encrypt all connection and window state data with a password." fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="3">
                    <rect key="frame" x="18" y="85" width="209" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="check" title="Encrypt with password:" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="8">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <action selector="validateSaveConnectionAccessory:" target="-2" id="54"/>
                    </connections>
                </button>
                <button toolTip="ATTENTION: If enabled all passwords will be saved in the file! It is highly recommended to encrypt the entire data!" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5">
                    <rect key="frame" x="18" y="111" width="189" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="check" title="Include passwords" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="6">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <action selector="validateSaveConnectionAccessory:" target="-2" id="57"/>
                    </connections>
                </button>
                <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="15">
                    <rect key="frame" x="18" y="7" width="394" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="check" title="Remember Query Editor content" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="16">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                </button>
                <button toolTip="Remember selected table, view status, filter settings, etc." fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="4">
                    <rect key="frame" x="18" y="33" width="394" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <buttonCell key="cell" type="check" title="Remember window state" bezelStyle="regularSquare" imagePosition="left" alignment="left" state="on" inset="2" id="7">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                </button>
            </subviews>
            <point key="canvasLocation" x="139" y="154"/>
        </customView>
        <userDefaultsController representsSharedInstance="YES" id="37"/>
    </objects>
</document>
