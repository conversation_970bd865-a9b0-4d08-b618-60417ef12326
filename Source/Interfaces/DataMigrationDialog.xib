<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="16097" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="16097"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPFieldMapperController">
            <connections>
                <outlet property="addGlobalValueButton" destination="171" id="185"/>
                <outlet property="addNewColumnMenuItem" destination="423" id="430"/>
                <outlet property="addRemainingDataSwitch" destination="257" id="259"/>
                <outlet property="advancedBox" destination="371" id="372"/>
                <outlet property="advancedButton" destination="139" id="256"/>
                <outlet property="advancedInsertView" destination="268" id="353"/>
                <outlet property="advancedLabel" destination="392" id="394"/>
                <outlet property="advancedReplaceView" destination="313" id="355"/>
                <outlet property="advancedUpdateView" destination="332" id="354"/>
                <outlet property="alignByPopup" destination="131" id="151"/>
                <outlet property="alignByPopupLabel" destination="137" id="358"/>
                <outlet property="delayedCheckBox" destination="296" id="363"/>
                <outlet property="delayedReplaceCheckBox" destination="318" id="383"/>
                <outlet property="fieldMapperTableScrollView" destination="13" id="356"/>
                <outlet property="fieldMapperTableView" destination="42" id="413"/>
                <outlet property="fileSourcePath" destination="25" id="60"/>
                <outlet property="globalValuesSheet" destination="160" id="249"/>
                <outlet property="globalValuesTableView" destination="165" id="182"/>
                <outlet property="gobackButton" destination="157" id="521"/>
                <outlet property="highPriorityCheckBox" destination="300" id="367"/>
                <outlet property="ignoreCheckBox" destination="295" id="379"/>
                <outlet property="ignoreUpdateCheckBox" destination="338" id="381"/>
                <outlet property="importButton" destination="15" id="150"/>
                <outlet property="importFieldNamesHeaderSwitch" destination="111" id="113"/>
                <outlet property="importMethodLabel" destination="21" id="357"/>
                <outlet property="importMethodPopup" destination="24" id="61"/>
                <outlet property="insertPullDownButton" destination="506" id="514"/>
                <outlet property="lowPriorityCheckBox" destination="299" id="380"/>
                <outlet property="lowPriorityReplaceCheckBox" destination="314" id="384"/>
                <outlet property="lowPriorityUpdateCheckBox" destination="333" id="382"/>
                <outlet property="matchingNameMenuItem" destination="134" id="154"/>
                <outlet property="newTableButton" destination="439" id="441"/>
                <outlet property="newTableInfoEncodingPopup" destination="464" id="478"/>
                <outlet property="newTableInfoEnginePopup" destination="461" id="479"/>
                <outlet property="newTableInfoWindow" destination="457" id="480"/>
                <outlet property="newTableNameInfoButton" destination="433" id="435"/>
                <outlet property="newTableNameLabel" destination="405" id="408"/>
                <outlet property="newTableNameTextField" destination="403" id="407"/>
                <outlet property="onupdateCheckBox" destination="297" id="364"/>
                <outlet property="onupdateTextView" destination="388" id="390"/>
                <outlet property="recentGlobalValueMenu" destination="516" id="519"/>
                <outlet property="recordCountLabel" destination="19" id="65"/>
                <outlet property="removeGlobalValueButton" destination="173" id="186"/>
                <outlet property="replaceAfterSavingCheckBox" destination="221" id="223"/>
                <outlet property="rowDownButton" destination="17" id="67"/>
                <outlet property="rowUpButton" destination="18" id="66"/>
                <outlet property="setAllTypesToMenuItem" destination="424" id="429"/>
                <outlet property="skipexistingRowsCheckBox" destination="396" id="398"/>
                <outlet property="tableTargetPopup" destination="20" id="58"/>
                <outlet property="typeComboxBox" destination="416" id="418"/>
                <outlet property="window" destination="11" id="217"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <window title="CSV Field Mapping" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" frameAutosaveName="SPCSVFieldMappingSheet" animationBehavior="default" id="11" userLabel="CSV Field Mapping Sheet">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="366" y="230" width="634" height="348"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2560" height="1417"/>
            <value key="minSize" type="size" width="634" height="348"/>
            <view key="contentView" id="12">
                <rect key="frame" x="0.0" y="0.0" width="634" height="348"/>
                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                <subviews>
                    <box autoresizesSubviews="NO" fixedFrame="YES" boxType="custom" borderType="line" title="Box" titlePosition="noTitle" translatesAutoresizingMaskIntoConstraints="NO" id="371" userLabel="Advanced Box (Box)">
                        <rect key="frame" x="-12" y="53" width="658" height="2"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                        <view key="contentView" id="XlC-iQ-HVh">
                            <rect key="frame" x="1" y="1" width="656" height="0.0"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        </view>
                        <color key="borderColor" white="0.0" alpha="0.41999999999999998" colorSpace="calibratedWhite"/>
                        <color key="fillColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                    </box>
                    <box autoresizesSubviews="NO" fixedFrame="YES" boxType="custom" borderType="line" title="Box" titlePosition="noTitle" translatesAutoresizingMaskIntoConstraints="NO" id="14" userLabel="Header Box (Box)">
                        <rect key="frame" x="-1" y="308" width="636" height="41"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                        <view key="contentView" id="KAE-TE-VdI">
                            <rect key="frame" x="1" y="1" width="634" height="39"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="56">
                                    <rect key="frame" x="14" y="12" width="604" height="17"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" widthSizable="YES" flexibleMaxX="YES" flexibleMinY="YES"/>
                                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="center" title="CSV Import Field Mapping" id="57">
                                        <font key="font" metaFont="system"/>
                                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                    </textFieldCell>
                                </textField>
                                <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="436">
                                    <rect key="frame" x="512" y="51" width="226" height="32"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                                    <buttonCell key="cell" type="push" title="SetAllTypeTo_Dummy_Button" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="437">
                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                        <font key="font" metaFont="system"/>
                                        <string key="keyEquivalent">=</string>
                                        <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                    </buttonCell>
                                    <connections>
                                        <action selector="setAllTypesTo:" target="-2" id="438"/>
                                    </connections>
                                </button>
                                <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="493" userLabel="Push Button (AddNewColumn_Dummy_Button)">
                                    <rect key="frame" x="664" y="1" width="226" height="32"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                                    <buttonCell key="cell" type="push" title="AddNewColumn_Dummy_Button" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="494">
                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                        <font key="font" metaFont="system"/>
                                        <string key="keyEquivalent">a</string>
                                        <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                    </buttonCell>
                                    <connections>
                                        <action selector="addNewColumn:" target="-2" id="496"/>
                                    </connections>
                                </button>
                                <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="497" userLabel="Push Button (RemoveNewColumn_Dummy_Button)">
                                    <rect key="frame" x="668" y="1" width="226" height="32"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                                    <buttonCell key="cell" type="push" title="RemoveNewColumn_Dummy_Button" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="498">
                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                        <font key="font" metaFont="system"/>
                                        <string key="keyEquivalent"></string>
                                        <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                    </buttonCell>
                                    <connections>
                                        <action selector="removeNewColumn:" target="-2" id="500"/>
                                    </connections>
                                </button>
                            </subviews>
                        </view>
                        <color key="borderColor" white="0.0" alpha="0.41999999999999998" colorSpace="calibratedWhite"/>
                        <color key="fillColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                    </box>
                    <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="18" horizontalPageScroll="10" verticalLineScroll="18" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="13">
                        <rect key="frame" x="-1" y="129" width="636" height="144"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <clipView key="contentView" copiesOnScroll="NO" id="ug1-11-hPT">
                            <rect key="frame" x="1" y="0.0" width="634" height="143"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" alternatingRowBackgroundColors="YES" columnReordering="NO" multipleSelection="NO" autosaveColumns="NO" autosaveName="SPCSVFieldMappingTableView" rowHeight="16" headerView="45" id="42" customClass="SPTableView">
                                    <rect key="frame" x="0.0" y="0.0" width="634" height="126"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <size key="intercellSpacing" width="3" height="2"/>
                                    <color key="backgroundColor" name="_sourceListBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                    <tableColumns>
                                        <tableColumn identifier="import_value" editable="NO" width="233" minWidth="15" maxWidth="1000" id="47">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="CSV Fields">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                            </tableHeaderCell>
                                            <popUpButtonCell key="dataCell" type="bevel" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" borderStyle="bezel" imageScaling="proportionallyDown" inset="2" arrowPosition="arrowAtCenter" preferredEdge="maxY" id="89">
                                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                <font key="font" metaFont="message" size="11"/>
                                                <menu key="menu" title="OtherViews" id="90">
                                                    <items>
                                                        <menuItem title="Pop Up" id="91"/>
                                                    </items>
                                                    <connections>
                                                        <outlet property="delegate" destination="-2" id="9Jp-DU-vql"/>
                                                    </connections>
                                                </menu>
                                            </popUpButtonCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="operator" editable="NO" width="28" minWidth="28" maxWidth="28" id="46">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="center">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                            </tableHeaderCell>
                                            <popUpButtonCell key="dataCell" type="bevel" title="Pop Up" bezelStyle="rounded" alignment="center" lineBreakMode="truncatingTail" state="on" borderStyle="bezel" imageScaling="proportionallyDown" inset="2" arrowPosition="noArrow" preferredEdge="maxY" selectedItem="97" id="95">
                                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                <font key="font" metaFont="message" size="11"/>
                                                <menu key="menu" title="OtherViews" id="96">
                                                    <items>
                                                        <menuItem title="Pop Up" state="on" id="97"/>
                                                    </items>
                                                </menu>
                                            </popUpButtonCell>
                                        </tableColumn>
                                        <tableColumn identifier="target_field" width="166" minWidth="10" maxWidth="3.4028234663852886e+38" id="48">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Table Target Fields">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" title="Text" id="1SY-mK-knt">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="type" width="195" minWidth="10" maxWidth="3.4028234663852886e+38" id="144">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Text" id="417" customClass="NSSecureTextFieldCell">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                    </tableColumns>
                                    <connections>
                                        <outlet property="dataSource" destination="-2" id="420"/>
                                        <outlet property="delegate" destination="-2" id="421"/>
                                        <outlet property="menu" destination="422" id="427"/>
                                    </connections>
                                </tableView>
                            </subviews>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="0.035674519836902618" controlSize="small" horizontal="YES" id="44">
                            <rect key="frame" x="-100" y="-100" width="227" height="11"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" controlSize="small" horizontal="NO" id="43">
                            <rect key="frame" x="413" y="18" width="11" height="140"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <tableHeaderView key="headerView" id="45">
                            <rect key="frame" x="0.0" y="0.0" width="634" height="17"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </tableHeaderView>
                    </scrollView>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="16">
                        <rect key="frame" x="415" y="12" width="102" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <string key="toolTip" base64-UTF8="YES">
Q2FuY2VsIEltcG9ydCAoD+KOiyk
</string>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" borderStyle="border" inset="2" id="40">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSheet:" target="-2" id="84"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="15">
                        <rect key="frame" x="517" y="12" width="102" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <string key="toolTip" base64-UTF8="YES">
SW1wb3J0ICgS4oapKQ
</string>
                        <buttonCell key="cell" type="push" title="Import" bezelStyle="rounded" alignment="center" borderStyle="border" tag="1" inset="2" id="41">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeSheet:" target="-2" id="83"/>
                            <outlet property="nextKeyView" destination="16" id="98"/>
                        </connections>
                    </button>
                    <button toolTip="Show previous source row (⌘←)" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="17">
                        <rect key="frame" x="20" y="19" width="32" height="24"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSGoLeftTemplate" imagePosition="only" alignment="center" enabled="NO" borderStyle="border" inset="2" id="39">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent"></string>
                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                        </buttonCell>
                        <connections>
                            <action selector="stepRow:" target="-2" id="68"/>
                        </connections>
                    </button>
                    <button toolTip="Show next source row (⌘→)" verticalHuggingPriority="750" fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="18">
                        <rect key="frame" x="51" y="19" width="32" height="24"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSGoRightTemplate" imagePosition="only" alignment="center" borderStyle="border" tag="1" inset="2" id="38">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent"></string>
                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                        </buttonCell>
                        <connections>
                            <action selector="stepRow:" target="-2" id="69"/>
                        </connections>
                    </button>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="19">
                        <rect key="frame" x="88" y="23" width="195" height="14"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <textFieldCell key="cell" controlSize="small" lineBreakMode="truncatingTail" truncatesLastVisibleLine="YES" sendsActionOnEndEditing="YES" title="x of y records" id="37">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <popUpButton toolTip="Choose database table in which the data will be inserted (⌘T)" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="20">
                        <rect key="frame" x="361" y="278" width="200" height="22"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" widthSizable="YES" flexibleMinY="YES"/>
                        <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" id="35">
                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent">t</string>
                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                            <menu key="menu" title="OtherViews" id="36"/>
                        </popUpButtonCell>
                        <connections>
                            <action selector="changeTableTarget:" target="-2" id="59"/>
                        </connections>
                    </popUpButton>
                    <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="24">
                        <rect key="frame" x="484" y="97" width="133" height="22"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <popUpButtonCell key="cell" type="push" title="INSERT" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" selectedItem="29" id="27">
                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <menu key="menu" title="OtherViews" id="28">
                                <items>
                                    <menuItem title="INSERT" state="on" id="29">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                    </menuItem>
                                    <menuItem title="REPLACE" tag="1" id="30">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                    </menuItem>
                                    <menuItem title="UPDATE" tag="2" id="31">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                    </menuItem>
                                </items>
                            </menu>
                        </popUpButtonCell>
                        <connections>
                            <action selector="changeImportMethod:" target="-2" id="62"/>
                        </connections>
                    </popUpButton>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="21">
                        <rect key="frame" x="302" y="102" width="180" height="14"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Import Method:" id="34">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <pathControl toolTip="Source file path. Double-click to go back to file chooser." focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" allowsExpansionToolTips="YES" translatesAutoresizingMaskIntoConstraints="NO" id="25">
                        <rect key="frame" x="5" y="280" width="316" height="20"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxX="YES" flexibleMinY="YES"/>
                        <pathCell key="cell" controlSize="small" lineBreakMode="truncatingMiddle" selectable="YES" focusRingType="none" alignment="left" id="26">
                            <font key="font" metaFont="message" size="11"/>
                        </pathCell>
                    </pathControl>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="111">
                        <rect key="frame" x="17" y="100" width="275" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="check" title="First line contains field names" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" state="on" inset="2" id="112">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                        </buttonCell>
                        <connections>
                            <action selector="changeHasHeaderCheckbox:" target="-2" id="155"/>
                        </connections>
                    </button>
                    <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="131">
                        <rect key="frame" x="484" y="73" width="133" height="22"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <popUpButtonCell key="cell" type="push" title="Matching names" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" tag="2" imageScaling="proportionallyDown" inset="2" autoenablesItems="NO" selectedItem="134" id="132">
                            <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <menu key="menu" title="OtherViews" autoenablesItems="NO" id="133">
                                <items>
                                    <menuItem title="File order" id="135"/>
                                    <menuItem title="Reversed file order" tag="1" id="141">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                    </menuItem>
                                    <menuItem title="Matching names" state="on" tag="2" enabled="NO" id="134"/>
                                    <menuItem title="Custom order" tag="3" enabled="NO" id="224">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                    </menuItem>
                                </items>
                            </menu>
                        </popUpButtonCell>
                        <connections>
                            <action selector="changeFieldAlignment:" target="-2" id="152"/>
                        </connections>
                    </popUpButton>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="137">
                        <rect key="frame" x="302" y="79" width="180" height="14"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Align field names by:" id="138">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button toolTip="Go back to file chooser (⇧⌥⌘←)" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="157">
                        <rect key="frame" x="326" y="12" width="82" height="32"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="Go Back" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="158">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent"></string>
                            <modifierMask key="keyEquivalentModifierMask" shift="YES" option="YES" command="YES"/>
                        </buttonCell>
                        <connections>
                            <action selector="goBackToFileChooser:" target="-2" id="159"/>
                        </connections>
                    </button>
                    <button toolTip="Advanced settings for import methods (⌥⌘A)" horizontalHuggingPriority="750" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="139">
                        <rect key="frame" x="11" y="56" width="29" height="26"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="disclosureTriangle" bezelStyle="disclosure" imagePosition="left" alignment="center" borderStyle="border" imageScaling="proportionallyUpOrDown" inset="2" id="140">
                            <behavior key="behavior" pushIn="YES" changeBackground="YES" changeGray="YES" lightByContents="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent">a</string>
                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                        </buttonCell>
                        <connections>
                            <action selector="openAdvancedSheet:" target="-2" id="248"/>
                        </connections>
                    </button>
                    <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="313" userLabel="Advanced View for REPLACE">
                        <rect key="frame" x="200" y="20" width="230" height="44"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <subviews>
                            <button fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="318">
                                <rect key="frame" x="132" y="8" width="80" height="18"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                <buttonCell key="cell" type="check" title="DELAYED" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="321">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" metaFont="message" size="11"/>
                                </buttonCell>
                                <connections>
                                    <action selector="advancedCheckboxValidation:" target="-2" id="330"/>
                                </connections>
                            </button>
                            <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="314">
                                <rect key="frame" x="17" y="8" width="100" height="18"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                <buttonCell key="cell" type="check" title="LOW_PRIORITY" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="327">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" metaFont="message" size="11"/>
                                </buttonCell>
                                <connections>
                                    <action selector="advancedCheckboxValidation:" target="-2" id="329"/>
                                </connections>
                            </button>
                        </subviews>
                    </customView>
                    <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="268" userLabel="Advanced View for INSERT">
                        <rect key="frame" x="93" y="-57" width="442" height="121"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <subviews>
                            <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="295">
                                <rect key="frame" x="354" y="85" width="70" height="18"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                <buttonCell key="cell" type="check" title="IGNORE" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="308">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" metaFont="message" size="11"/>
                                </buttonCell>
                            </button>
                            <button fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="296">
                                <rect key="frame" x="137" y="85" width="80" height="18"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                <buttonCell key="cell" type="check" title="DELAYED" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="307">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" metaFont="message" size="11"/>
                                </buttonCell>
                                <connections>
                                    <action selector="advancedCheckboxValidation:" target="-2" id="312"/>
                                </connections>
                            </button>
                            <button fixedFrame="YES" tag="2" translatesAutoresizingMaskIntoConstraints="NO" id="297">
                                <rect key="frame" x="17" y="62" width="181" height="18"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                <buttonCell key="cell" type="check" title="ON DUPLICATE KEY UPDATE:" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="306">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" metaFont="message" size="11"/>
                                </buttonCell>
                                <connections>
                                    <action selector="advancedCheckboxValidation:" target="-2" id="310"/>
                                </connections>
                            </button>
                            <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="299">
                                <rect key="frame" x="17" y="85" width="100" height="18"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                <buttonCell key="cell" type="check" title="LOW_PRIORITY" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="302">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" metaFont="message" size="11"/>
                                </buttonCell>
                                <connections>
                                    <action selector="advancedCheckboxValidation:" target="-2" id="309"/>
                                </connections>
                            </button>
                            <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="300">
                                <rect key="frame" x="227" y="85" width="104" height="18"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                <buttonCell key="cell" type="check" title="HIGH_PRIORITY" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="301">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" metaFont="message" size="11"/>
                                </buttonCell>
                                <connections>
                                    <action selector="advancedCheckboxValidation:" target="-2" id="311"/>
                                </connections>
                            </button>
                            <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="385">
                                <rect key="frame" x="20" y="20" width="402" height="39"/>
                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                <clipView key="contentView" drawsBackground="NO" id="7h1-U3-DDX">
                                    <rect key="frame" x="1" y="1" width="400" height="37"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <subviews>
                                        <textView importsGraphics="NO" verticallyResizable="YES" usesFontPanel="YES" findStyle="panel" continuousSpellChecking="YES" allowsUndo="YES" usesRuler="YES" allowsNonContiguousLayout="YES" smartInsertDelete="YES" id="388" customClass="SPTextView">
                                            <rect key="frame" x="0.0" y="0.0" width="400" height="37"/>
                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                            <color key="textColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            <size key="minSize" width="400" height="37"/>
                                            <size key="maxSize" width="802" height="10000000"/>
                                            <color key="insertionPointColor" name="textColor" catalog="System" colorSpace="catalog"/>
                                        </textView>
                                    </subviews>
                                </clipView>
                                <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" controlSize="mini" horizontal="YES" id="387">
                                    <rect key="frame" x="-100" y="-100" width="87" height="15"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                </scroller>
                                <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" controlSize="mini" horizontal="NO" id="386">
                                    <rect key="frame" x="386" y="1" width="15" height="37"/>
                                    <autoresizingMask key="autoresizingMask"/>
                                </scroller>
                            </scrollView>
                            <button toolTip="Do not import existing rows identified by the PRIMARY KEY" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="396">
                                <rect key="frame" x="227" y="62" width="335" height="18"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                <buttonCell key="cell" type="check" title="Skip existing rows" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" state="on" inset="2" id="397">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" metaFont="message" size="11"/>
                                </buttonCell>
                                <connections>
                                    <action selector="advancedCheckboxValidation:" target="-2" id="399"/>
                                </connections>
                            </button>
                        </subviews>
                    </customView>
                    <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="332" userLabel="Advanced View for UPDATE">
                        <rect key="frame" x="110" y="20" width="390" height="44"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <subviews>
                            <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="338">
                                <rect key="frame" x="132" y="8" width="70" height="18"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                <buttonCell key="cell" type="check" title="IGNORE" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="339">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" metaFont="message" size="11"/>
                                </buttonCell>
                            </button>
                            <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="333">
                                <rect key="frame" x="17" y="8" width="100" height="18"/>
                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                <buttonCell key="cell" type="check" title="LOW_PRIORITY" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="346">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" metaFont="message" size="11"/>
                                </buttonCell>
                                <connections>
                                    <action selector="advancedCheckboxValidation:" target="-2" id="348"/>
                                </connections>
                            </button>
                            <button toolTip="All rows which doesn't match the WHERE clause in the UPDATE statement will be inserted via INSERT INTO…" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="257">
                                <rect key="frame" x="212" y="8" width="335" height="18"/>
                                <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                <buttonCell key="cell" type="check" title="Insert remaining rows" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" inset="2" id="258">
                                    <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                    <font key="font" metaFont="message" size="11"/>
                                </buttonCell>
                            </button>
                        </subviews>
                    </customView>
                    <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="392">
                        <rect key="frame" x="34" y="58" width="58" height="23"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="smallSquare" title="Advanced" bezelStyle="smallSquare" imagePosition="overlaps" alignment="left" controlSize="small" state="on" imageScaling="proportionallyDown" inset="2" id="393">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                        </buttonCell>
                        <connections>
                            <action selector="openAdvancedSheet:" target="-2" id="395"/>
                        </connections>
                    </button>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="403">
                        <rect key="frame" x="487" y="280" width="106" height="19"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="404">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                        <connections>
                            <outlet property="delegate" destination="-2" id="409"/>
                        </connections>
                    </textField>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="405">
                        <rect key="frame" x="402" y="283" width="80" height="14"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                        <textFieldCell key="cell" controlSize="small" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Name:" id="406">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button toolTip="Manage table details (⌘I)" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="433">
                        <rect key="frame" x="599" y="280" width="20" height="20"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="square" bezelStyle="shadowlessSquare" image="NSInfo" imagePosition="only" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="434">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="system"/>
                            <string key="keyEquivalent">i</string>
                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                        </buttonCell>
                        <connections>
                            <action selector="newTableInfo:" target="-2" id="481"/>
                        </connections>
                    </button>
                    <button toolTip="Import into a new TABLE (⇧⌘N)" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="439">
                        <rect key="frame" x="566" y="281" width="61" height="17"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                        <buttonCell key="cell" type="roundRect" title="New" bezelStyle="roundedRect" alignment="center" controlSize="small" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="440">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent">N</string>
                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                        </buttonCell>
                        <connections>
                            <action selector="newTable:" target="-2" id="442"/>
                        </connections>
                    </button>
                </subviews>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="204"/>
            </connections>
            <point key="canvasLocation" x="139" y="147"/>
        </window>
        <window title="Window" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" frameAutosaveName="" animationBehavior="default" id="160" userLabel="Global Values Sheet">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="131" y="292" width="311" height="210"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2560" height="1417"/>
            <value key="minSize" type="size" width="311" height="186"/>
            <view key="contentView" id="161">
                <rect key="frame" x="0.0" y="0.0" width="311" height="210"/>
                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                <subviews>
                    <scrollView fixedFrame="YES" autohidesScrollers="YES" horizontalLineScroll="16" horizontalPageScroll="10" verticalLineScroll="16" verticalPageScroll="10" hasHorizontalScroller="NO" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="162">
                        <rect key="frame" x="-1" y="71" width="313" height="129"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <clipView key="contentView" drawsBackground="NO" id="y3W-sL-hVi">
                            <rect key="frame" x="1" y="0.0" width="311" height="128"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <subviews>
                                <tableView verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="sequential" selectionHighlightStyle="sourceList" alternatingRowBackgroundColors="YES" columnReordering="NO" autosaveColumns="NO" rowHeight="14" headerView="166" id="165" customClass="SPTableView">
                                    <rect key="frame" x="0.0" y="0.0" width="311" height="111"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <size key="intercellSpacing" width="3" height="2"/>
                                    <color key="backgroundColor" name="_sourceListBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                    <tableColumns>
                                        <tableColumn identifier="value_index" editable="NO" width="30" minWidth="30" maxWidth="1000" id="167">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="right">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" alignment="right" title="Text Cell" id="170">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="global_value" width="240" minWidth="10" maxWidth="3.4028234663852886e+38" id="190">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Global Source Values or SQL Expressions">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <textFieldCell key="dataCell" controlSize="small" lineBreakMode="truncatingTail" selectable="YES" editable="YES" alignment="left" title="Text Cell" id="191">
                                                <font key="font" metaFont="message" size="11"/>
                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="controlBackgroundColor" catalog="System" colorSpace="catalog"/>
                                            </textFieldCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                        </tableColumn>
                                        <tableColumn identifier="sql" width="32" minWidth="32" maxWidth="32" id="400">
                                            <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="SQL">
                                                <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                <color key="backgroundColor" name="headerColor" catalog="System" colorSpace="catalog"/>
                                            </tableHeaderCell>
                                            <buttonCell key="dataCell" type="check" bezelStyle="regularSquare" imagePosition="only" alignment="justified" controlSize="small" inset="2" id="402">
                                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                <font key="font" metaFont="message" size="11"/>
                                            </buttonCell>
                                            <tableColumnResizingMask key="resizingMask" resizeWithTable="YES"/>
                                        </tableColumn>
                                    </tableColumns>
                                    <connections>
                                        <outlet property="dataSource" destination="-2" id="179"/>
                                        <outlet property="delegate" destination="-2" id="180"/>
                                    </connections>
                                </tableView>
                            </subviews>
                            <nil key="backgroundColor"/>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="164">
                            <rect key="frame" x="-100" y="-100" width="238" height="15"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="163">
                            <rect key="frame" x="-100" y="-100" width="15" height="102"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <tableHeaderView key="headerView" id="166">
                            <rect key="frame" x="0.0" y="0.0" width="311" height="17"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </tableHeaderView>
                    </scrollView>
                    <button toolTip="Add new value (⌥⌘A)" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="171">
                        <rect key="frame" x="17" y="42" width="22" height="22"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSAddTemplate" imagePosition="overlaps" alignment="center" controlSize="small" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="172">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent">a</string>
                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                        </buttonCell>
                        <connections>
                            <action selector="addGlobalValue:" target="-2" id="189"/>
                        </connections>
                    </button>
                    <button toolTip="Remove selected value (⌘⌫)" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="173">
                        <rect key="frame" x="38" y="42" width="22" height="22"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="NSRemoveTemplate" imagePosition="overlaps" alignment="center" controlSize="small" state="on" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="174">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent"></string>
                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                        </buttonCell>
                        <connections>
                            <action selector="removeGlobalValue:" target="-2" id="183"/>
                        </connections>
                    </button>
                    <button verticalHuggingPriority="750" fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="175">
                        <rect key="frame" x="216" y="13" width="80" height="28"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="push" title="OK" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="176">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeGlobalValuesSheet:" target="-2" id="187"/>
                        </connections>
                    </button>
                    <button toolTip="Use current selected value after saving as source for selected mapping pair (⌥⌘L)" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="221">
                        <rect key="frame" x="18" y="17" width="197" height="18"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <buttonCell key="cell" type="check" title="Use last edited value" bezelStyle="regularSquare" imagePosition="left" alignment="left" controlSize="small" state="on" inset="2" id="222">
                            <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                        </buttonCell>
                    </button>
                    <popUpButton toolTip="Insert NULL or CSV column placeholders (⌥⌘I)" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="506">
                        <rect key="frame" x="70" y="42" width="105" height="22"/>
                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                        <popUpButtonCell key="cell" type="smallSquare" title="Insert" bezelStyle="smallSquare" alignment="center" controlSize="small" lineBreakMode="truncatingTail" state="on" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" pullsDown="YES" selectedItem="509" id="507">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent">i</string>
                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                            <menu key="menu" title="OtherViews" id="508">
                                <items>
                                    <menuItem title="Insert" state="on" hidden="YES" id="509"/>
                                    <menuItem title="NULL" toolTip="Insert NULL value into currently selected row" id="510">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                        <connections>
                                            <action selector="insertNULLValue:" target="-2" id="513"/>
                                        </connections>
                                    </menuItem>
                                    <menuItem isSeparatorItem="YES" hidden="YES" id="512"/>
                                    <menuItem title="Recent" id="515">
                                        <modifierMask key="keyEquivalentModifierMask"/>
                                        <menu key="submenu" title="Recent" id="516"/>
                                    </menuItem>
                                    <menuItem isSeparatorItem="YES" id="518"/>
                                </items>
                            </menu>
                        </popUpButtonCell>
                        <connections>
                            <action selector="insertPulldownValue:" target="-2" id="520"/>
                        </connections>
                    </popUpButton>
                </subviews>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="205"/>
                <outlet property="initialFirstResponder" destination="165" id="197"/>
            </connections>
        </window>
        <userDefaultsController representsSharedInstance="YES" id="99"/>
        <comboBoxCell controlSize="small" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" buttonBordered="NO" usesDataSource="YES" numberOfVisibleItems="9" id="416" userLabel="Combo Box Cell for new table types">
            <font key="font" metaFont="message" size="11"/>
            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
            <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
            <connections>
                <outlet property="dataSource" destination="-2" id="419"/>
            </connections>
        </comboBoxCell>
        <menu id="422" userLabel="Context Menu">
            <items>
                <menuItem title="Set all Field Types to:" keyEquivalent="=" id="424">
                    <connections>
                        <action selector="setAllTypesTo:" target="-2" id="432"/>
                    </connections>
                </menuItem>
                <menuItem isSeparatorItem="YES" id="426"/>
                <menuItem title="Add Column to Target Table…" keyEquivalent="a" id="423">
                    <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                    <connections>
                        <action selector="addNewColumn:" target="-2" id="489"/>
                    </connections>
                </menuItem>
                <menuItem title="Remove New Column:" id="491">
                    <string key="keyEquivalent" base64-UTF8="YES">
CA
</string>
                    <connections>
                        <action selector="removeNewColumn:" target="-2" id="492"/>
                    </connections>
                </menuItem>
                <menuItem title="Edit Field Type for Column:" hidden="YES" enabled="NO" id="488">
                    <modifierMask key="keyEquivalentModifierMask"/>
                </menuItem>
            </items>
            <point key="canvasLocation" x="139" y="448"/>
        </menu>
        <window title="New Table" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" releasedWhenClosed="NO" visibleAtLaunch="NO" animationBehavior="default" id="457" userLabel="New Table Info Sheet">
            <windowStyleMask key="styleMask" titled="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" rightStrut="YES" topStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="343" y="475" width="269" height="120"/>
            <rect key="screenRect" x="0.0" y="0.0" width="2560" height="1417"/>
            <value key="minSize" type="size" width="269" height="120"/>
            <value key="maxSize" type="size" width="269" height="120"/>
            <view key="contentView" id="458">
                <rect key="frame" x="0.0" y="0.0" width="269" height="120"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="463">
                        <rect key="frame" x="17" y="80" width="90" height="14"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Table Encoding:" id="471">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <button verticalHuggingPriority="750" fixedFrame="YES" tag="1" translatesAutoresizingMaskIntoConstraints="NO" id="460">
                        <rect key="frame" x="184" y="13" width="70" height="28"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES"/>
                        <buttonCell key="cell" type="push" title="OK" bezelStyle="rounded" alignment="center" controlSize="small" state="on" borderStyle="border" tag="1" inset="2" id="475">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeInfoSheet:" target="-2" id="483"/>
                        </connections>
                    </button>
                    <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="464">
                        <rect key="frame" x="109" y="75" width="143" height="22"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="clipping" borderStyle="borderAndBezel" inset="2" arrowPosition="arrowAtCenter" preferredEdge="maxY" id="469">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <menu key="menu" title="OtherViews" id="470"/>
                        </popUpButtonCell>
                    </popUpButton>
                    <textField verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="462">
                        <rect key="frame" x="41" y="54" width="66" height="14"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Table Type:" id="472">
                            <font key="font" metaFont="message" size="11"/>
                            <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                            <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                        </textFieldCell>
                    </textField>
                    <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="461">
                        <rect key="frame" x="109" y="50" width="143" height="22"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" controlSize="small" lineBreakMode="clipping" borderStyle="borderAndBezel" inset="2" arrowPosition="arrowAtCenter" preferredEdge="maxY" id="473">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <menu key="menu" title="OtherViews" id="474"/>
                        </popUpButtonCell>
                    </popUpButton>
                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="485">
                        <rect key="frame" x="116" y="12" width="70" height="28"/>
                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES"/>
                        <buttonCell key="cell" type="push" title="Cancel" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" inset="2" id="486">
                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                            <font key="font" metaFont="message" size="11"/>
                            <string key="keyEquivalent" base64-UTF8="YES">
Gw
</string>
                        </buttonCell>
                        <connections>
                            <action selector="closeInfoSheet:" target="-2" id="487"/>
                        </connections>
                    </button>
                </subviews>
            </view>
        </window>
    </objects>
    <resources>
        <image name="NSAddTemplate" width="11" height="11"/>
        <image name="NSGoLeftTemplate" width="9" height="12"/>
        <image name="NSGoRightTemplate" width="9" height="12"/>
        <image name="NSInfo" width="32" height="32"/>
        <image name="NSRemoveTemplate" width="11" height="11"/>
    </resources>
</document>
