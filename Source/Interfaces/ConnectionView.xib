<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.Cocoa.XIB" version="3.0" toolsVersion="22154" targetRuntime="MacOSX.Cocoa" propertyAccessControl="none" useAutolayout="YES">
    <dependencies>
        <deployment identifier="macosx"/>
        <plugIn identifier="com.apple.InterfaceBuilder.CocoaPlugin" version="22154"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <customObject id="-2" userLabel="File's Owner" customClass="SPConnectionController">
            <connections>
                <outlet property="connectButton" destination="5157" id="5444"/>
                <outlet property="connectionDetailsScrollView" destination="5588" id="5592"/>
                <outlet property="connectionInstructionsTextField" destination="5154" id="5800"/>
                <outlet property="connectionResizeContainer" destination="4888" id="5164"/>
                <outlet property="connectionSplitView" destination="5741" id="5745"/>
                <outlet property="connectionView" destination="5739" id="5740"/>
                <outlet property="editButtonsView" destination="5862" id="5882"/>
                <outlet property="errorDetailText" destination="5434" id="5437"/>
                <outlet property="errorDetailWindow" destination="5431" id="5438"/>
                <outlet property="exportPanelAccessoryView" destination="5796" id="5799"/>
                <outlet property="favoritesOutlineView" destination="4913" id="5768"/>
                <outlet property="favoritesSortByMenuItem" destination="5837" id="5850"/>
                <outlet property="helpButton" destination="4829" id="5458"/>
                <outlet property="progressIndicator" destination="5422" id="5426"/>
                <outlet property="progressIndicatorText" destination="5423" id="5425"/>
                <outlet property="saveFavoriteButton" destination="5869" id="5874"/>
                <outlet property="socketColorField" destination="5896" id="5897"/>
                <outlet property="socketConnectionFormContainer" destination="5162" id="5163"/>
                <outlet property="socketConnectionSSLDetailsContainer" destination="5688" id="5724"/>
                <outlet property="socketNameField" destination="5385" id="5806"/>
                <outlet property="socketPasswordField" destination="5214" id="5428"/>
                <outlet property="socketSSLCACertButton" destination="5691" id="5725"/>
                <outlet property="socketSSLCertificateButton" destination="5694" id="5726"/>
                <outlet property="socketSSLKeyFileButton" destination="5695" id="nQE-St-ai8"/>
                <outlet property="socketTimeZoneField" destination="jLB-fx-dGx" id="doZ-bz-Q3x"/>
                <outlet property="socketUserField" destination="5212" id="5807"/>
                <outlet property="sshColorField" destination="5898" id="5899"/>
                <outlet property="sshConnectionFormContainer" destination="5166" id="5167"/>
                <outlet property="sshConnectionSSLDetailsContainer" destination="9t3-JG-c1l" id="K8K-hl-nke"/>
                <outlet property="sshKeyLocationHelp" destination="5576" id="5579"/>
                <outlet property="sshNameField" destination="5403" id="5893"/>
                <outlet property="sshPasswordField" destination="5268" id="5429"/>
                <outlet property="sshSQLHostField" destination="5264" id="5445"/>
                <outlet property="sshSSHKeyButton" destination="5492" id="5494"/>
                <outlet property="sshSSHPasswordField" destination="5288" id="5430"/>
                <outlet property="sshTimeZoneField" destination="XXN-zL-2iW" id="3e8-OQ-OBS"/>
                <outlet property="sshUserField" destination="5266" id="1O2-uu-pMp"/>
                <outlet property="sslCACertLocationHelp" destination="5680" id="5686"/>
                <outlet property="sslCertificateLocationHelp" destination="5683" id="5687"/>
                <outlet property="sslKeyFileLocationHelp" destination="5668" id="5671"/>
                <outlet property="sslOverSSHCACertButton" destination="7eD-3o-HAJ" id="1Wh-DE-z2l"/>
                <outlet property="sslOverSSHCertificateButton" destination="VqZ-mo-IXW" id="Ync-fQ-3aq"/>
                <outlet property="sslOverSSHKeyFileButton" destination="vPP-gd-c4a" id="VTR-AE-QPt"/>
                <outlet property="standardColorField" destination="5894" id="5895"/>
                <outlet property="standardConnectionFormContainer" destination="5156" id="5161"/>
                <outlet property="standardConnectionSSLDetailsContainer" destination="5595" id="5620"/>
                <outlet property="standardNameField" destination="5376" id="5810"/>
                <outlet property="standardPasswordField" destination="5179" id="5427"/>
                <outlet property="standardSQLHostField" destination="5171" id="5446"/>
                <outlet property="standardSSLCACertButton" destination="5612" id="5667"/>
                <outlet property="standardSSLCertificateButton" destination="5604" id="5666"/>
                <outlet property="standardSSLKeyFileButton" destination="5600" id="5665"/>
                <outlet property="standardTimeZoneField" destination="JYc-99-MtL" id="YnV-26-UNx"/>
                <outlet property="standardUserField" destination="5175" id="5811"/>
                <outlet property="testConnectButton" destination="5863" id="5875"/>
            </connections>
        </customObject>
        <customObject id="-1" userLabel="First Responder" customClass="FirstResponder"/>
        <customObject id="-3" userLabel="Application" customClass="NSObject"/>
        <customView id="5739" userLabel="ConnectionView">
            <rect key="frame" x="0.0" y="0.0" width="882" height="759"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <splitView fixedFrame="YES" autosaveName="DBConnectionViewSplitter" dividerStyle="thin" vertical="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5741" customClass="SPSplitView">
                    <rect key="frame" x="0.0" y="0.0" width="882" height="759"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    <subviews>
                        <view fixedFrame="YES" id="5742">
                            <rect key="frame" x="0.0" y="0.0" width="200" height="759"/>
                            <autoresizingMask key="autoresizingMask" heightSizable="YES"/>
                            <subviews>
                                <scrollView fixedFrame="YES" borderType="none" autohidesScrollers="YES" horizontalLineScroll="19" horizontalPageScroll="10" verticalLineScroll="19" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4910">
                                    <rect key="frame" x="0.0" y="25" width="200" height="734"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <clipView key="contentView" drawsBackground="NO" id="cjP-S4-7Nz">
                                        <rect key="frame" x="0.0" y="0.0" width="200" height="734"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        <subviews>
                                            <tableView focusRingType="none" verticalHuggingPriority="750" allowsExpansionToolTips="YES" columnAutoresizingStyle="firstColumnOnly" tableStyle="sourceList" selectionHighlightStyle="sourceList" columnReordering="NO" columnSelection="YES" columnResizing="NO" autosaveColumns="NO" id="4913" customClass="SPFavoritesOutlineView">
                                                <rect key="frame" x="0.0" y="0.0" width="200" height="734"/>
                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                <size key="intercellSpacing" width="3" height="2"/>
                                                <color key="backgroundColor" name="_sourceListBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                <color key="gridColor" name="gridColor" catalog="System" colorSpace="catalog"/>
                                                <tableColumns>
                                                    <tableColumn width="168" minWidth="40" maxWidth="3000" id="4915">
                                                        <tableHeaderCell key="headerCell" lineBreakMode="truncatingTail" borderStyle="border" alignment="left" title="Favorites">
                                                            <color key="textColor" name="headerTextColor" catalog="System" colorSpace="catalog"/>
                                                            <color key="backgroundColor" white="0.33333298560000002" alpha="1" colorSpace="calibratedWhite"/>
                                                        </tableHeaderCell>
                                                        <textFieldCell key="dataCell" lineBreakMode="truncatingTail" selectable="YES" editable="YES" allowsUndo="NO" alignment="left" title="Text Cell" usesSingleLineMode="YES" id="4918" customClass="SPFavoriteTextFieldCell">
                                                            <font key="font" metaFont="system"/>
                                                            <color key="textColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
                                                        </textFieldCell>
                                                        <tableColumnResizingMask key="resizingMask" resizeWithTable="YES" userResizable="YES"/>
                                                    </tableColumn>
                                                </tableColumns>
                                                <connections>
                                                    <outlet property="dataSource" destination="-2" id="5341"/>
                                                    <outlet property="delegate" destination="-2" id="4930"/>
                                                    <outlet property="menu" destination="5788" id="5812"/>
                                                    <outlet property="nextKeyView" destination="4980" id="5466"/>
                                                </connections>
                                            </tableView>
                                        </subviews>
                                        <nil key="backgroundColor"/>
                                    </clipView>
                                    <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="4912">
                                        <rect key="frame" x="0.0" y="724" width="200" height="16"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                    </scroller>
                                    <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="4911">
                                        <rect key="frame" x="229" y="1" width="15" height="524"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                    </scroller>
                                </scrollView>
                                <popUpButton fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5824">
                                    <rect key="frame" x="0.0" y="0.0" width="35" height="25"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <popUpButtonCell key="cell" type="smallSquare" bezelStyle="smallSquare" imagePosition="only" alignment="center" alternateImage="NSActionTemplate" lineBreakMode="truncatingTail" state="on" inset="2" pullsDown="YES" selectedItem="5833" id="5825">
                                        <behavior key="behavior" lightByContents="YES"/>
                                        <font key="font" metaFont="menu"/>
                                        <menu key="menu" title="OtherViews" id="5826">
                                            <items>
                                                <menuItem state="on" image="NSActionTemplate" hidden="YES" id="5833"/>
                                                <menuItem title="Rename" id="5883">
                                                    <modifierMask key="keyEquivalentModifierMask"/>
                                                    <connections>
                                                        <action selector="renameNode:" target="-2" id="5890"/>
                                                    </connections>
                                                </menuItem>
                                                <menuItem title="Remove" id="5884">
                                                    <modifierMask key="keyEquivalentModifierMask"/>
                                                    <connections>
                                                        <action selector="removeNode:" target="-2" id="5891"/>
                                                    </connections>
                                                </menuItem>
                                                <menuItem isSeparatorItem="YES" id="5885"/>
                                                <menuItem title="Duplicate" id="5886">
                                                    <modifierMask key="keyEquivalentModifierMask"/>
                                                    <connections>
                                                        <action selector="duplicateFavorite:" target="-2" id="5888"/>
                                                    </connections>
                                                </menuItem>
                                                <menuItem isSeparatorItem="YES" id="5892"/>
                                                <menuItem title="Import..." id="5834">
                                                    <modifierMask key="keyEquivalentModifierMask"/>
                                                    <connections>
                                                        <action selector="importFavorites:" target="-2" id="5849"/>
                                                    </connections>
                                                </menuItem>
                                                <menuItem title="Export..." id="5835">
                                                    <modifierMask key="keyEquivalentModifierMask"/>
                                                    <connections>
                                                        <action selector="exportFavorites:" target="-2" id="5844"/>
                                                    </connections>
                                                </menuItem>
                                                <menuItem isSeparatorItem="YES" id="5836"/>
                                                <menuItem title="Make Default" id="5887">
                                                    <modifierMask key="keyEquivalentModifierMask"/>
                                                    <connections>
                                                        <action selector="makeSelectedFavoriteDefault:" target="-2" id="5889"/>
                                                    </connections>
                                                </menuItem>
                                                <menuItem title="Sort By" id="5837">
                                                    <menu key="submenu" title="Sort By" id="5838">
                                                        <items>
                                                            <menuItem title="Name" id="5843">
                                                                <modifierMask key="keyEquivalentModifierMask"/>
                                                                <connections>
                                                                    <action selector="sortFavorites:" target="-2" id="5846"/>
                                                                </connections>
                                                            </menuItem>
                                                            <menuItem title="Host" id="5842">
                                                                <modifierMask key="keyEquivalentModifierMask"/>
                                                                <connections>
                                                                    <action selector="sortFavorites:" target="-2" id="5847"/>
                                                                </connections>
                                                            </menuItem>
                                                            <menuItem title="Type" id="5841">
                                                                <modifierMask key="keyEquivalentModifierMask"/>
                                                                <connections>
                                                                    <action selector="sortFavorites:" target="-2" id="5845"/>
                                                                </connections>
                                                            </menuItem>
                                                            <menuItem title="Color" id="25V-E5-s4p">
                                                                <modifierMask key="keyEquivalentModifierMask"/>
                                                                <connections>
                                                                    <action selector="sortFavorites:" target="-2" id="Q9D-q1-jts"/>
                                                                </connections>
                                                            </menuItem>
                                                            <menuItem isSeparatorItem="YES" id="5840"/>
                                                            <menuItem title="Reverse Sort Order" id="5839">
                                                                <modifierMask key="keyEquivalentModifierMask"/>
                                                                <connections>
                                                                    <action selector="reverseSortFavorites:" target="-2" id="5848"/>
                                                                </connections>
                                                            </menuItem>
                                                        </items>
                                                    </menu>
                                                </menuItem>
                                            </items>
                                        </menu>
                                    </popUpButtonCell>
                                </popUpButton>
                                <imageView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5858">
                                    <rect key="frame" x="175" y="0.0" width="25" height="23"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                    <imageCell key="cell" refusesFirstResponder="YES" alignment="left" image="button_bar_handleTemplate" id="5859"/>
                                </imageView>
                                <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5853">
                                    <rect key="frame" x="80" y="0.0" width="25" height="25"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <buttonCell key="cell" type="square" bezelStyle="shadowlessSquare" image="NSAddTemplate" imagePosition="only" alignment="center" alternateImage="NSAddTemplate" state="on" inset="2" id="5854">
                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                        <font key="font" metaFont="system"/>
                                    </buttonCell>
                                    <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                    <connections>
                                        <action selector="addFavorite:" target="-2" id="5857"/>
                                    </connections>
                                </button>
                                <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5851">
                                    <rect key="frame" x="45" y="0.0" width="25" height="25"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <buttonCell key="cell" type="square" bezelStyle="shadowlessSquare" image="Add_folder" imagePosition="only" alignment="center" alternateImage="Add_folder" state="on" imageScaling="proportionallyDown" inset="2" id="5852">
                                        <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                        <font key="font" metaFont="system"/>
                                    </buttonCell>
                                    <color key="contentTintColor" name="labelColor" catalog="System" colorSpace="catalog"/>
                                    <connections>
                                        <action selector="addGroup:" target="-2" id="5856"/>
                                    </connections>
                                </button>
                            </subviews>
                        </view>
                        <view fixedFrame="YES" id="5743">
                            <rect key="frame" x="201" y="0.0" width="681" height="759"/>
                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                            <subviews>
                                <scrollView fixedFrame="YES" borderType="none" autohidesScrollers="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" usesPredominantAxisScrolling="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5588">
                                    <rect key="frame" x="0.0" y="0.0" width="681" height="759"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <clipView key="contentView" drawsBackground="NO" copiesOnScroll="NO" id="gFs-TM-wGu">
                                        <rect key="frame" x="0.0" y="0.0" width="681" height="759"/>
                                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                        <subviews>
                                            <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5447">
                                                <rect key="frame" x="0.0" y="0.0" width="681" height="700"/>
                                                <autoresizingMask key="autoresizingMask" widthSizable="YES"/>
                                                <subviews>
                                                    <customView wantsLayer="YES" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="4888" customClass="NSCustomView">
                                                        <rect key="frame" x="116" y="50" width="446" height="600"/>
                                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxX="YES" flexibleMinY="YES" flexibleMaxY="YES"/>
                                                        <subviews>
                                                            <button horizontalHuggingPriority="750" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="4829">
                                                                <rect key="frame" x="3" y="37" width="25" height="25"/>
                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                                                <buttonCell key="cell" type="help" bezelStyle="helpButton" alignment="center" state="on" borderStyle="border" inset="2" id="4839">
                                                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                                    <font key="font" metaFont="system"/>
                                                                </buttonCell>
                                                                <connections>
                                                                    <action selector="showHelp:" target="-2" id="5351"/>
                                                                </connections>
                                                            </button>
                                                            <textField hidden="YES" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5423">
                                                                <rect key="frame" x="29" y="43" width="264" height="17"/>
                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                                                <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" title="Connecting..." id="5424">
                                                                    <font key="font" metaFont="system"/>
                                                                    <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                    <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                </textFieldCell>
                                                            </textField>
                                                            <progressIndicator wantsLayer="YES" horizontalHuggingPriority="750" verticalHuggingPriority="750" fixedFrame="YES" minValue="16" maxValue="100" doubleValue="16" displayedWhenStopped="NO" bezeled="NO" indeterminate="YES" controlSize="small" style="spinning" translatesAutoresizingMaskIntoConstraints="NO" id="5422">
                                                                <rect key="frame" x="7" y="43" width="16" height="16"/>
                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                                            </progressIndicator>
                                                            <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5157">
                                                                <rect key="frame" x="295" y="33" width="147" height="32"/>
                                                                <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxY="YES"/>
                                                                <buttonCell key="cell" type="push" title="Connect" bezelStyle="rounded" alignment="center" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="5158">
                                                                    <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                                    <font key="font" metaFont="system"/>
                                                                    <string key="keyEquivalent" base64-UTF8="YES">
DQ
</string>
                                                                </buttonCell>
                                                                <connections>
                                                                    <action selector="initiateConnection:" target="-2" id="5439"/>
                                                                </connections>
                                                            </button>
                                                            <tabView fixedFrame="YES" initialItem="4981" translatesAutoresizingMaskIntoConstraints="NO" id="4980">
                                                                <rect key="frame" x="3" y="60" width="440" height="540"/>
                                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                <font key="font" metaFont="system"/>
                                                                <tabViewItems>
                                                                    <tabViewItem label="TCP/IP" identifier="1" id="4981">
                                                                        <view key="view" id="4984">
                                                                            <rect key="frame" x="10" y="33" width="420" height="494"/>
                                                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                            <subviews>
                                                                                <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5156">
                                                                                    <rect key="frame" x="22" y="137" width="377" height="376"/>
                                                                                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                    <subviews>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5188">
                                                                                            <rect key="frame" x="7" y="131" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Port:" id="5189">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the port to use when connecting to the MySQL server.  The default MySQL port is 3306" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5187">
                                                                                            <rect key="frame" x="110" y="129" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="3306" drawsBackground="YES" usesSingleLineMode="YES" id="5190">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="port" id="5418">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                        <string key="NSNullPlaceholder">3306</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5362"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5184">
                                                                                            <rect key="frame" x="7" y="165" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Database:" id="5185">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Optionally enter a database to select after successfully connecting to the MySQL server" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5183">
                                                                                            <rect key="frame" x="110" y="163" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="optional" drawsBackground="YES" usesSingleLineMode="YES" id="5186">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="database" id="5417">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                        <string key="NSNullPlaceholder">optional</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5361"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5180">
                                                                                            <rect key="frame" x="7" y="199" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Password:" id="5181">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the password to use when connecting to the MySQL server" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5179" customClass="NSSecureTextField">
                                                                                            <rect key="frame" x="110" y="197" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="5182">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="password" id="5203">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5360"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5176">
                                                                                            <rect key="frame" x="7" y="233" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Username:" id="5177">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the MySQL username to connect with" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5175">
                                                                                            <rect key="frame" x="110" y="231" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="5178">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="user" id="5199">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5359"/>
                                                                                                <outlet property="nextKeyView" destination="5179" id="5464"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5377">
                                                                                            <rect key="frame" x="7" y="327" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Name:" id="5378">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter a nickname to use if adding to favorites. Optional." focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5376">
                                                                                            <rect key="frame" x="110" y="325" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="optional" drawsBackground="YES" usesSingleLineMode="YES" id="5379">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="name" id="5416">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                        <string key="NSNullPlaceholder">optional</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5402"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5173">
                                                                                            <rect key="frame" x="7" y="267" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Host:" id="5174">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the hostname of the MySQL server you want to connect to.  Enter 127.0.0.1 to connect to a server on this computer" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5171">
                                                                                            <rect key="frame" x="110" y="265" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="5172">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="host" id="5195">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                        <bool key="NSValidatesImmediately" value="YES"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5358"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5894" customClass="SPColorSelectorView">
                                                                                            <rect key="frame" x="110" y="295" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <connections>
                                                                                                <outlet property="delegate" destination="-2" id="5900"/>
                                                                                            </connections>
                                                                                        </customView>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Qjy-56-fjI">
                                                                                            <rect key="frame" x="7" y="97" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Time Zone:" id="htb-Ht-iA8">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="JYc-99-MtL">
                                                                                            <rect key="frame" x="108" y="92" width="252" height="25"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" id="MXg-Pz-0Nh">
                                                                                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                                                                <font key="font" metaFont="menu"/>
                                                                                                <menu key="menu" id="qjt-9z-owe"/>
                                                                                            </popUpButtonCell>
                                                                                            <connections>
                                                                                                <action selector="didChangeSelectedTimeZone:" target="-2" id="jDI-am-jCC"/>
                                                                                            </connections>
                                                                                        </popUpButton>
                                                                                        <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="oIH-sM-vBJ">
                                                                                            <rect key="frame" x="108" y="64" width="251" height="18"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="check" title="Allow LOCAL_DATA_INFILE (insecure)" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="Ot7-jd-TIL">
                                                                                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="allowLocalDataInfileChanged:" target="-2" id="Eth-Xl-lgv"/>
                                                                                                <binding destination="-2" name="value" keyPath="allowDataLocalInfile" id="jk7-hb-Saf">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="2k2-ul-dtp">
                                                                                            <rect key="frame" x="108" y="37" width="251" height="18"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="check" title="Enable Cleartext plugin (insecure)" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="mSn-DP-BWp">
                                                                                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="updateClearTextPlugin:" target="-2" id="f3x-0d-TpI"/>
                                                                                                <binding destination="-2" name="value" keyPath="enableClearTextPlugin" id="TSv-Nq-ohb">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5593">
                                                                                            <rect key="frame" x="108" y="9" width="251" height="18"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="check" title="Require SSL" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="5594">
                                                                                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="updateSSLInterface:" target="-2" id="5651"/>
                                                                                                <binding destination="-2" name="value" keyPath="useSSL" id="5627">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                    </subviews>
                                                                                </customView>
                                                                                <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5595">
                                                                                    <rect key="frame" x="22" y="34" width="377" height="104"/>
                                                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                    <subviews>
                                                                                        <button toolTip="Click to choose the SSL private key file to use when establishing a secure connection." verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5600">
                                                                                            <rect key="frame" x="328" y="80" width="29" height="24"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="key-icon" imagePosition="overlaps" alignment="center" alternateImage="key-icon-alternate" borderStyle="border" inset="2" id="5601">
                                                                                                <behavior key="behavior" pushIn="YES" changeContents="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="chooseKeyLocation:" target="-2" id="5662"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslKeyFileLocationEnabled" id="5673">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5596">
                                                                                            <rect key="frame" x="109" y="81" width="220" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" lineBreakMode="truncatingHead" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="none set" drawsBackground="YES" id="5597">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" white="0.50403225419999997" alpha="1" colorSpace="calibratedWhite"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="toolTip" keyPath="sslKeyFileLocation" id="5641"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslKeyFileLocation" id="5732">
                                                                                                    <dictionary key="options">
                                                                                                        <string key="NSNullPlaceholder">none set</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5598">
                                                                                            <rect key="frame" x="-3" y="83" width="107" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Key File:" id="5599">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <button toolTip="Click to choose the client SSL certificate file to use when establishing a secure connection." verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5604">
                                                                                            <rect key="frame" x="328" y="46" width="29" height="24"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="key-icon" imagePosition="overlaps" alignment="center" alternateImage="key-icon-alternate" borderStyle="border" inset="2" id="5609">
                                                                                                <behavior key="behavior" pushIn="YES" changeContents="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="chooseKeyLocation:" target="-2" id="5663"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslCertificateFileLocationEnabled" id="5677">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5605">
                                                                                            <rect key="frame" x="109" y="47" width="220" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" lineBreakMode="truncatingHead" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="none set" drawsBackground="YES" id="5608">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" white="0.5" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="toolTip" keyPath="sslCertificateFileLocation" id="5639"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslCertificateFileLocation" id="5731">
                                                                                                    <dictionary key="options">
                                                                                                        <string key="NSNullPlaceholder">none set</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5606">
                                                                                            <rect key="frame" x="-3" y="49" width="107" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Certificate:" id="5607">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <button toolTip="Click to choose the SSL Certificate Authority certificate to verify the peer against when establishing a secure connection." verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5612">
                                                                                            <rect key="frame" x="328" y="12" width="29" height="24"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="key-icon" imagePosition="overlaps" alignment="center" alternateImage="key-icon-alternate" borderStyle="border" inset="2" id="5617">
                                                                                                <behavior key="behavior" pushIn="YES" changeContents="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="chooseKeyLocation:" target="-2" id="5664"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslCACertFileLocationEnabled" id="5679">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5613">
                                                                                            <rect key="frame" x="109" y="13" width="220" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" lineBreakMode="truncatingHead" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="none set" drawsBackground="YES" id="5616">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" white="0.5" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="toolTip" keyPath="sslCACertFileLocation" id="5637"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslCACertFileLocation" id="5730">
                                                                                                    <dictionary key="options">
                                                                                                        <string key="NSNullPlaceholder">none set</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5614">
                                                                                            <rect key="frame" x="-3" y="15" width="107" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="CA Cert:" id="5615">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                    </subviews>
                                                                                    <connections>
                                                                                        <binding destination="-2" name="hidden" keyPath="useSSL" id="5649">
                                                                                            <dictionary key="options">
                                                                                                <integer key="NSMultipleValuesPlaceholder" value="1"/>
                                                                                                <integer key="NSNoSelectionPlaceholder" value="1"/>
                                                                                                <integer key="NSNotApplicablePlaceholder" value="1"/>
                                                                                                <integer key="NSNullPlaceholder" value="1"/>
                                                                                                <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                                                            </dictionary>
                                                                                        </binding>
                                                                                    </connections>
                                                                                </customView>
                                                                            </subviews>
                                                                        </view>
                                                                    </tabViewItem>
                                                                    <tabViewItem label="Socket" identifier="2" id="4982">
                                                                        <view key="view" id="4983">
                                                                            <rect key="frame" x="10" y="33" width="420" height="494"/>
                                                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                            <subviews>
                                                                                <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5162">
                                                                                    <rect key="frame" x="22" y="169" width="377" height="342"/>
                                                                                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                    <subviews>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5386">
                                                                                            <rect key="frame" x="7" y="295" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Name:" id="5387">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter a nickname to use if adding to favorites. Optional." focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5385">
                                                                                            <rect key="frame" x="110" y="293" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="optional" drawsBackground="YES" usesSingleLineMode="YES" id="5388">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="name" id="5400">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                        <string key="NSNullPlaceholder">optional</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5401"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5219">
                                                                                            <rect key="frame" x="7" y="133" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Socket:" id="5220">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the path to the socket file to use when connecting to MySQL.  Leave this field blank to use the default socket location" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5218">
                                                                                            <rect key="frame" x="110" y="131" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="optional" drawsBackground="YES" usesSingleLineMode="YES" id="5221">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="socket" id="5415">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                        <string key="NSNullPlaceholder">optional</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5366"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5217">
                                                                                            <rect key="frame" x="7" y="167" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Database:" id="5222">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Optionally enter a database to select after successfully connecting to the MySQL server" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5216">
                                                                                            <rect key="frame" x="110" y="165" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="optional" drawsBackground="YES" usesSingleLineMode="YES" id="5223">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="database" id="5413">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                        <string key="NSNullPlaceholder">optional</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5365"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5215">
                                                                                            <rect key="frame" x="7" y="201" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Password:" id="5224">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the password to use when connecting to the MySQL server" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5214" customClass="NSSecureTextField">
                                                                                            <rect key="frame" x="110" y="199" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="5225">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="password" id="5234">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5364"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5213">
                                                                                            <rect key="frame" x="7" y="235" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Username:" id="5226">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the MySQL username to connect with" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5212">
                                                                                            <rect key="frame" x="110" y="233" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="5227">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="user" id="5231">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5363"/>
                                                                                                <outlet property="nextKeyView" destination="5214" id="5463"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5896" customClass="SPColorSelectorView">
                                                                                            <rect key="frame" x="110" y="263" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <connections>
                                                                                                <outlet property="delegate" destination="-2" id="5901"/>
                                                                                            </connections>
                                                                                        </customView>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="zQi-oD-96m">
                                                                                            <rect key="frame" x="7" y="99" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Time Zone:" id="oq0-5L-7Ai">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="jLB-fx-dGx">
                                                                                            <rect key="frame" x="108" y="94" width="252" height="25"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" id="bmK-iD-Bdh">
                                                                                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                                                                <font key="font" metaFont="menu"/>
                                                                                                <menu key="menu" id="l5Y-4J-e8H"/>
                                                                                            </popUpButtonCell>
                                                                                            <connections>
                                                                                                <action selector="didChangeSelectedTimeZone:" target="-2" id="8lK-1u-ZGr"/>
                                                                                            </connections>
                                                                                        </popUpButton>
                                                                                        <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="UTU-OQ-h6m">
                                                                                            <rect key="frame" x="108" y="66" width="251" height="18"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="check" title="Allow LOCAL_DATA_INFILE (insecure)" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="6E5-jx-aGT">
                                                                                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="allowLocalDataInfileChanged:" target="-2" id="AOg-eK-20m"/>
                                                                                                <binding destination="-2" name="value" keyPath="allowDataLocalInfile" id="P7M-Db-xqP">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="f6I-gs-bxP">
                                                                                            <rect key="frame" x="108" y="38" width="251" height="18"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="check" title="Enable Cleartext plugin (insecure)" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="ls3-qw-kiN">
                                                                                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="updateClearTextPlugin:" target="-2" id="uXa-7C-RDw"/>
                                                                                                <binding destination="-2" name="value" keyPath="enableClearTextPlugin" id="17G-p6-JcU">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5720">
                                                                                            <rect key="frame" x="108" y="10" width="251" height="18"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES"/>
                                                                                            <buttonCell key="cell" type="check" title="Require SSL" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="5721">
                                                                                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="updateSSLInterface:" target="-2" id="5723"/>
                                                                                                <binding destination="-2" name="value" keyPath="useSSL" id="5722">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                    </subviews>
                                                                                </customView>
                                                                                <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5688">
                                                                                    <rect key="frame" x="22" y="66" width="377" height="104"/>
                                                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                    <subviews>
                                                                                        <button toolTip="Click to choose the SSL private key file to use when establishing a secure connection." verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5695">
                                                                                            <rect key="frame" x="328" y="80" width="29" height="24"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="key-icon" imagePosition="overlaps" alignment="center" alternateImage="key-icon-alternate" borderStyle="border" inset="2" id="5700">
                                                                                                <behavior key="behavior" pushIn="YES" changeContents="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="chooseKeyLocation:" target="-2" id="5717"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslKeyFileLocationEnabled" id="5711">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5697">
                                                                                            <rect key="frame" x="109" y="81" width="220" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" lineBreakMode="truncatingHead" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="none set" drawsBackground="YES" id="5698">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" white="0.50403225419999997" alpha="1" colorSpace="calibratedWhite"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="toolTip" keyPath="sslKeyFileLocation" id="5714"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslKeyFileLocation" id="5733">
                                                                                                    <dictionary key="options">
                                                                                                        <string key="NSNullPlaceholder">none set</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5696">
                                                                                            <rect key="frame" x="-3" y="83" width="107" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Key File:" id="5699">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <button toolTip="Click to choose the client SSL certificate file to use when establishing a secure connection." verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5694">
                                                                                            <rect key="frame" x="328" y="46" width="29" height="24"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="key-icon" imagePosition="overlaps" alignment="center" alternateImage="key-icon-alternate" borderStyle="border" inset="2" id="5701">
                                                                                                <behavior key="behavior" pushIn="YES" changeContents="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="chooseKeyLocation:" target="-2" id="5719"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslCertificateFileLocationEnabled" id="5707">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5693">
                                                                                            <rect key="frame" x="109" y="47" width="220" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" lineBreakMode="truncatingHead" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="none set" drawsBackground="YES" id="5702">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" white="0.5" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="toolTip" keyPath="sslCertificateFileLocation" id="5708"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslCertificateFileLocation" id="5735">
                                                                                                    <dictionary key="options">
                                                                                                        <string key="NSNullPlaceholder">none set</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5692">
                                                                                            <rect key="frame" x="-3" y="49" width="107" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Certificate:" id="5703">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <button toolTip="Click to choose the SSL Certificate Authority certificate to verify the peer against when establishing a secure connection." verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5691">
                                                                                            <rect key="frame" x="328" y="12" width="29" height="24"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="key-icon" imagePosition="overlaps" alignment="center" alternateImage="key-icon-alternate" borderStyle="border" inset="2" id="5704">
                                                                                                <behavior key="behavior" pushIn="YES" changeContents="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="chooseKeyLocation:" target="-2" id="5718"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslCACertFileLocationEnabled" id="5715">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5690">
                                                                                            <rect key="frame" x="109" y="13" width="220" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" lineBreakMode="truncatingHead" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="none set" drawsBackground="YES" id="5705">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" white="0.5" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="toolTip" keyPath="sslCACertFileLocation" id="5712"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslCACertFileLocation" id="5734">
                                                                                                    <dictionary key="options">
                                                                                                        <string key="NSNullPlaceholder">none set</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5689">
                                                                                            <rect key="frame" x="-3" y="15" width="107" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="CA Cert:" id="5706">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                    </subviews>
                                                                                    <connections>
                                                                                        <binding destination="-2" name="hidden" keyPath="useSSL" id="5710">
                                                                                            <dictionary key="options">
                                                                                                <integer key="NSMultipleValuesPlaceholder" value="1"/>
                                                                                                <integer key="NSNoSelectionPlaceholder" value="1"/>
                                                                                                <integer key="NSNotApplicablePlaceholder" value="1"/>
                                                                                                <integer key="NSNullPlaceholder" value="1"/>
                                                                                                <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                                                            </dictionary>
                                                                                        </binding>
                                                                                    </connections>
                                                                                </customView>
                                                                            </subviews>
                                                                        </view>
                                                                    </tabViewItem>
                                                                    <tabViewItem label="SSH" identifier="Item 2" id="4985">
                                                                        <view key="view" id="4986">
                                                                            <rect key="frame" x="10" y="33" width="420" height="494"/>
                                                                            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                                                            <subviews>
                                                                                <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5166">
                                                                                    <rect key="frame" x="22" y="-14" width="377" height="525"/>
                                                                                    <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                    <subviews>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5523">
                                                                                            <rect key="frame" x="110" y="130" width="219" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" lineBreakMode="truncatingHead" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="5524">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" white="0.49596774580000003" alpha="1" colorSpace="calibratedWhite"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="sshKeyLocation" id="5563">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <binding destination="-2" name="toolTip" keyPath="sshKeyLocation" id="5570"/>
                                                                                                <binding destination="-2" name="hidden" keyPath="sshKeyLocationEnabled" id="5562">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="1"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="1"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="1"/>
                                                                                                        <integer key="NSNullPlaceholder" value="1"/>
                                                                                                        <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5525">
                                                                                            <rect key="frame" x="7" y="132" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="SSH Key:" id="5526">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="hidden" keyPath="sshKeyLocationEnabled" id="5561">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="1"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="1"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="1"/>
                                                                                                        <integer key="NSNullPlaceholder" value="1"/>
                                                                                                        <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5404">
                                                                                            <rect key="frame" x="7" y="478" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Name:" id="5405">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter a nickname to use if adding to favorites. Optional." focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5403">
                                                                                            <rect key="frame" x="110" y="476" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="optional" drawsBackground="YES" usesSingleLineMode="YES" id="5406">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="name" id="5411">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                        <string key="NSNullPlaceholder">optional</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5414"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5291">
                                                                                            <rect key="frame" x="7" y="98" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="SSH Port:" id="5292">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the port to use when connecting to the SSH server.  The default SSH port is 22" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5290">
                                                                                            <rect key="frame" x="110" y="96" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="optional" drawsBackground="YES" usesSingleLineMode="YES" id="5293">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="sshPort" id="5737">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                        <string key="NSNullPlaceholder">optional</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5375"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5289">
                                                                                            <rect key="frame" x="7" y="132" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="SSH Password:" id="5294">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="hidden" keyPath="sshKeyLocationEnabled" id="5549">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the password to use when connecting to the SSH server." focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5288" customClass="NSSecureTextField">
                                                                                            <rect key="frame" x="110" y="130" width="219" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="5295">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="hidden" keyPath="sshKeyLocationEnabled" id="5559"/>
                                                                                                <binding destination="-2" name="value" keyPath="sshPassword" id="5504">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5374"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5287">
                                                                                            <rect key="frame" x="7" y="166" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="SSH User:" id="5296">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the SSH username to connect with" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5286">
                                                                                            <rect key="frame" x="110" y="164" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="5297">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="sshUser" id="5327">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5373"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5285">
                                                                                            <rect key="frame" x="10" y="200" width="95" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="SSH Host:" id="5298">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the hostname of the SSH server to tunnel via when connecting to the MySQL server" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5284">
                                                                                            <rect key="frame" x="110" y="198" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="5299">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="sshHost" id="5323">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5372"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5273">
                                                                                            <rect key="frame" x="10" y="282" width="95" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Port:" id="5274">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the port to use when connecting to the MySQL server.  The default MySQL port is 3306" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5272">
                                                                                            <rect key="frame" x="110" y="280" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="3306" drawsBackground="YES" usesSingleLineMode="YES" id="5275">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="port" id="5419">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                        <string key="NSNullPlaceholder">3306</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5371"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5271">
                                                                                            <rect key="frame" x="7" y="316" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Database:" id="5276">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Optionally enter a database to select after successfully connecting to the MySQL server" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5270">
                                                                                            <rect key="frame" x="110" y="314" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="optional" drawsBackground="YES" usesSingleLineMode="YES" id="5277">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="database" id="5412">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                        <string key="NSNullPlaceholder">optional</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5370"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5269">
                                                                                            <rect key="frame" x="7" y="350" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Password:" id="5278">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the password to use when connecting to the MySQL server" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5268" customClass="NSSecureTextField">
                                                                                            <rect key="frame" x="110" y="348" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="5279">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="password" id="5311">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5369"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5267">
                                                                                            <rect key="frame" x="7" y="384" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Username:" id="5280">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField toolTip="Enter the MySQL username to connect with" focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5266">
                                                                                            <rect key="frame" x="110" y="382" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="5281">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="user" id="5307">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5368"/>
                                                                                                <outlet property="nextKeyView" destination="5268" id="5465"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5265">
                                                                                            <rect key="frame" x="7" y="418" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="MySQL Host:" id="5282">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5264">
                                                                                            <rect key="frame" x="110" y="416" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <string key="toolTip">Enter the hostname of the MySQL server you want to connect to.  Enter 127.0.0.1 to connect to a server running on the machine you are tunnelling to</string>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" selectable="YES" editable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" drawsBackground="YES" usesSingleLineMode="YES" id="5283">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="host" id="5303">
                                                                                                    <dictionary key="options">
                                                                                                        <bool key="NSContinuouslyUpdatesValue" value="YES"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <outlet property="delegate" destination="-2" id="5367"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5898" customClass="SPColorSelectorView">
                                                                                            <rect key="frame" x="110" y="446" width="247" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <connections>
                                                                                                <outlet property="delegate" destination="-2" id="5902"/>
                                                                                            </connections>
                                                                                        </customView>
                                                                                        <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="FtV-HG-DIU">
                                                                                            <rect key="frame" x="108" y="10" width="251" height="18"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="check" title="Require SSL" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="w3N-9r-DzN">
                                                                                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="updateSSLInterface:" target="-2" id="KEh-s5-TWL"/>
                                                                                                <binding destination="-2" name="value" keyPath="useSSL" id="WuC-tH-Z8g">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="pI7-sy-bsp">
                                                                                            <rect key="frame" x="7" y="248" width="98" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Time Zone:" id="UEC-JC-iN3">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5DJ-XN-oYd">
                                                                                            <rect key="frame" x="108" y="66" width="251" height="18"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="check" title="Allow LOCAL_DATA_INFILE (insecure)" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="FJo-A3-GTr">
                                                                                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="allowLocalDataInfileChanged:" target="-2" id="dx2-ku-5cK"/>
                                                                                                <binding destination="-2" name="value" keyPath="allowDataLocalInfile" id="Yfp-Xh-UpB">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="9zy-Sk-eFd">
                                                                                            <rect key="frame" x="108" y="38" width="251" height="18"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="check" title="Enable Cleartext plugin (insecure)" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="Dcr-B7-c20">
                                                                                                <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="updateClearTextPlugin:" target="-2" id="j6g-5U-p1O"/>
                                                                                                <binding destination="-2" name="value" keyPath="enableClearTextPlugin" id="THS-Fx-kCH">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <popUpButton verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="XXN-zL-2iW">
                                                                                            <rect key="frame" x="108" y="243" width="252" height="25"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <popUpButtonCell key="cell" type="push" bezelStyle="rounded" alignment="left" lineBreakMode="truncatingTail" borderStyle="borderAndBezel" imageScaling="proportionallyDown" inset="2" id="gcf-uF-VQA">
                                                                                                <behavior key="behavior" lightByBackground="YES" lightByGray="YES"/>
                                                                                                <font key="font" metaFont="menu"/>
                                                                                                <menu key="menu" id="d3E-sN-gKB"/>
                                                                                            </popUpButtonCell>
                                                                                            <connections>
                                                                                                <action selector="didChangeSelectedTimeZone:" target="-2" id="vNL-jC-ecd"/>
                                                                                            </connections>
                                                                                        </popUpButton>
                                                                                        <button toolTip="Click to choose the SSH key file to use with this connection.  Standard locations like ~/.ssh are NOT checked automatically." verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5492">
                                                                                            <rect key="frame" x="328" y="129" width="29" height="24"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="key-icon" imagePosition="overlaps" alignment="center" alternateImage="key-icon-alternate" borderStyle="border" inset="2" id="5493">
                                                                                                <behavior key="behavior" pushIn="YES" changeContents="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="chooseKeyLocation:" target="-2" id="5661"/>
                                                                                                <binding destination="-2" name="value" keyPath="sshKeyLocationEnabled" id="5551">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                    </subviews>
                                                                                </customView>
                                                                                <customView fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="9t3-JG-c1l">
                                                                                    <rect key="frame" x="22" y="-117" width="377" height="104"/>
                                                                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                    <subviews>
                                                                                        <button toolTip="Click to choose the SSL private key file to use when establishing a secure connection." verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="vPP-gd-c4a">
                                                                                            <rect key="frame" x="328" y="80" width="29" height="24"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="key-icon" imagePosition="overlaps" alignment="center" alternateImage="key-icon-alternate" borderStyle="border" inset="2" id="OCU-fz-H9W">
                                                                                                <behavior key="behavior" pushIn="YES" changeContents="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="chooseKeyLocation:" target="-2" id="zge-vf-RDs"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslKeyFileLocationEnabled" id="AQO-79-Qoa">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="hLW-S6-RZ9">
                                                                                            <rect key="frame" x="109" y="81" width="220" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" lineBreakMode="truncatingHead" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="none set" drawsBackground="YES" id="wO6-xE-Gjg">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" white="0.50403225419999997" alpha="1" colorSpace="calibratedWhite"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="toolTip" keyPath="sslKeyFileLocation" id="1dY-ya-XZT"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslKeyFileLocation" id="HxQ-8U-qCi">
                                                                                                    <dictionary key="options">
                                                                                                        <string key="NSNullPlaceholder">none set</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="Mkf-Q4-7vt">
                                                                                            <rect key="frame" x="-3" y="83" width="107" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Key File:" id="xGU-pV-iYc">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <button toolTip="Click to choose the client SSL certificate file to use when establishing a secure connection." verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="VqZ-mo-IXW">
                                                                                            <rect key="frame" x="328" y="46" width="29" height="24"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="key-icon" imagePosition="overlaps" alignment="center" alternateImage="key-icon-alternate" borderStyle="border" inset="2" id="dlS-N8-67W">
                                                                                                <behavior key="behavior" pushIn="YES" changeContents="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="chooseKeyLocation:" target="-2" id="Cyu-po-GQE"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslCertificateFileLocationEnabled" id="Ccl-JC-0KZ">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="3Y2-Rj-0be">
                                                                                            <rect key="frame" x="109" y="47" width="220" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" lineBreakMode="truncatingHead" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="none set" drawsBackground="YES" id="ldB-Kc-oTh">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" white="0.5" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="sslCertificateFileLocation" id="aQT-Hc-RZK">
                                                                                                    <dictionary key="options">
                                                                                                        <string key="NSNullPlaceholder">none set</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <binding destination="-2" name="toolTip" keyPath="sslCertificateFileLocation" id="aUS-Id-i02"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="zor-qs-az3">
                                                                                            <rect key="frame" x="-3" y="49" width="107" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="Certificate:" id="m4Z-qz-MVB">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                        <button toolTip="Click to choose the SSL Certificate Authority certificate to verify the peer against when establishing a secure connection." verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="7eD-3o-HAJ">
                                                                                            <rect key="frame" x="328" y="12" width="29" height="24"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <buttonCell key="cell" type="smallSquare" bezelStyle="smallSquare" image="key-icon" imagePosition="overlaps" alignment="center" alternateImage="key-icon-alternate" borderStyle="border" inset="2" id="XZJ-kZ-hgG">
                                                                                                <behavior key="behavior" pushIn="YES" changeContents="YES" lightByContents="YES"/>
                                                                                                <font key="font" metaFont="system"/>
                                                                                            </buttonCell>
                                                                                            <connections>
                                                                                                <action selector="chooseKeyLocation:" target="-2" id="cQ2-mu-0Ph"/>
                                                                                                <binding destination="-2" name="value" keyPath="sslCACertFileLocationEnabled" id="SW6-dc-cub">
                                                                                                    <dictionary key="options">
                                                                                                        <integer key="NSMultipleValuesPlaceholder" value="0"/>
                                                                                                        <integer key="NSNoSelectionPlaceholder" value="0"/>
                                                                                                        <integer key="NSNotApplicablePlaceholder" value="0"/>
                                                                                                        <integer key="NSNullPlaceholder" value="0"/>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                            </connections>
                                                                                        </button>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="7nJ-pZ-buy">
                                                                                            <rect key="frame" x="109" y="13" width="220" height="22"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" lineBreakMode="truncatingHead" selectable="YES" sendsActionOnEndEditing="YES" state="on" borderStyle="bezel" placeholderString="none set" drawsBackground="YES" id="30l-FF-qhV">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" white="0.5" alpha="1" colorSpace="custom" customColorSpace="calibratedWhite"/>
                                                                                                <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                            <connections>
                                                                                                <binding destination="-2" name="value" keyPath="sslCACertFileLocation" id="bez-TY-JVB">
                                                                                                    <dictionary key="options">
                                                                                                        <string key="NSNullPlaceholder">none set</string>
                                                                                                    </dictionary>
                                                                                                </binding>
                                                                                                <binding destination="-2" name="toolTip" keyPath="sslCACertFileLocation" id="s1j-iY-B0W"/>
                                                                                            </connections>
                                                                                        </textField>
                                                                                        <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="XLC-w6-o5W">
                                                                                            <rect key="frame" x="-3" y="15" width="107" height="17"/>
                                                                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                                            <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="right" title="CA Cert:" id="QTm-ne-rcS">
                                                                                                <font key="font" metaFont="system"/>
                                                                                                <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                                                                                <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                                                                            </textFieldCell>
                                                                                        </textField>
                                                                                    </subviews>
                                                                                    <connections>
                                                                                        <binding destination="-2" name="hidden" keyPath="useSSL" id="6oe-5y-dVM">
                                                                                            <dictionary key="options">
                                                                                                <integer key="NSMultipleValuesPlaceholder" value="1"/>
                                                                                                <integer key="NSNoSelectionPlaceholder" value="1"/>
                                                                                                <integer key="NSNotApplicablePlaceholder" value="1"/>
                                                                                                <integer key="NSNullPlaceholder" value="1"/>
                                                                                                <string key="NSValueTransformerName">NSNegateBoolean</string>
                                                                                            </dictionary>
                                                                                        </binding>
                                                                                    </connections>
                                                                                </customView>
                                                                            </subviews>
                                                                        </view>
                                                                    </tabViewItem>
                                                                </tabViewItems>
                                                                <connections>
                                                                    <binding destination="-2" name="selectedIndex" keyPath="type" id="4992"/>
                                                                    <outlet property="delegate" destination="-2" id="5165"/>
                                                                </connections>
                                                            </tabView>
                                                            <customView hidden="YES" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5862" userLabel="Edit Connection Buttons Box">
                                                                <rect key="frame" x="0.0" y="0.0" width="446" height="37"/>
                                                                <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMaxY="YES"/>
                                                                <subviews>
                                                                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5863">
                                                                        <rect key="frame" x="297" y="3" width="146" height="28"/>
                                                                        <autoresizingMask key="autoresizingMask" flexibleMinX="YES" flexibleMinY="YES"/>
                                                                        <buttonCell key="cell" type="push" title="Test connection" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="5864">
                                                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                                            <font key="font" metaFont="controlContent" size="11"/>
                                                                        </buttonCell>
                                                                        <connections>
                                                                            <action selector="initiateConnection:" target="-2" id="5881"/>
                                                                        </connections>
                                                                    </button>
                                                                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5865">
                                                                        <rect key="frame" x="3" y="3" width="157" height="28"/>
                                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                        <buttonCell key="cell" type="push" title="Add to Favorites" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="5866">
                                                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                                            <font key="font" metaFont="controlContent" size="11"/>
                                                                            <string key="keyEquivalent">a</string>
                                                                            <modifierMask key="keyEquivalentModifierMask" option="YES" command="YES"/>
                                                                        </buttonCell>
                                                                        <connections>
                                                                            <action selector="addFavoriteUsingCurrentDetails:" target="-2" id="5871"/>
                                                                        </connections>
                                                                    </button>
                                                                    <button verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5869">
                                                                        <rect key="frame" x="159" y="3" width="139" height="28"/>
                                                                        <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                                                                        <buttonCell key="cell" type="push" title="Save changes" bezelStyle="rounded" alignment="center" controlSize="small" borderStyle="border" imageScaling="proportionallyDown" inset="2" id="5870">
                                                                            <behavior key="behavior" pushIn="YES" lightByBackground="YES" lightByGray="YES"/>
                                                                            <font key="font" metaFont="controlContent" size="11"/>
                                                                            <string key="keyEquivalent">s</string>
                                                                            <modifierMask key="keyEquivalentModifierMask" command="YES"/>
                                                                        </buttonCell>
                                                                        <connections>
                                                                            <action selector="saveFavorite:" target="-2" id="5880"/>
                                                                        </connections>
                                                                    </button>
                                                                </subviews>
                                                            </customView>
                                                        </subviews>
                                                    </customView>
                                                </subviews>
                                            </customView>
                                        </subviews>
                                    </clipView>
                                    <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="YES" id="5590">
                                        <rect key="frame" x="0.0" y="711" width="681" height="16"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                    </scroller>
                                    <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="NO" id="5589">
                                        <rect key="frame" x="712" y="0.0" width="16" height="517"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                    </scroller>
                                </scrollView>
                                <textField focusRingType="none" verticalHuggingPriority="750" fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5154">
                                    <rect key="frame" x="17" y="735" width="647" height="17"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" flexibleMinY="YES"/>
                                    <textFieldCell key="cell" scrollable="YES" lineBreakMode="clipping" sendsActionOnEndEditing="YES" alignment="center" title="Enter connection details below, or choose a favorite" id="5155">
                                        <font key="font" metaFont="systemBold"/>
                                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                                    </textFieldCell>
                                </textField>
                            </subviews>
                        </view>
                    </subviews>
                    <holdingPriorities>
                        <real value="250"/>
                        <real value="250"/>
                    </holdingPriorities>
                    <connections>
                        <outlet property="additionalDragHandleView" destination="5858" id="5860"/>
                        <outlet property="delegate" destination="-2" id="5861"/>
                    </connections>
                </splitView>
            </subviews>
            <point key="canvasLocation" x="139" y="276.5"/>
        </customView>
        <window title="Error Detail" allowsToolTipsWhenApplicationIsInactive="NO" autorecalculatesKeyViewLoop="NO" restorable="NO" hidesOnDeactivate="YES" visibleAtLaunch="NO" frameAutosaveName="" animationBehavior="default" id="5431" userLabel="Error Detail HUD" customClass="NSPanel">
            <windowStyleMask key="styleMask" titled="YES" closable="YES" miniaturizable="YES" resizable="YES" utility="YES" HUD="YES"/>
            <windowPositionMask key="initialPositionMask" leftStrut="YES" bottomStrut="YES"/>
            <rect key="contentRect" x="439" y="291" width="580" height="320"/>
            <rect key="screenRect" x="0.0" y="0.0" width="3008" height="1667"/>
            <value key="minSize" type="size" width="100" height="100"/>
            <view key="contentView" id="5432">
                <rect key="frame" x="0.0" y="0.0" width="580" height="320"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <scrollView borderType="none" autohidesScrollers="YES" horizontalLineScroll="10" horizontalPageScroll="10" verticalLineScroll="10" verticalPageScroll="10" hasHorizontalScroller="NO" translatesAutoresizingMaskIntoConstraints="NO" id="5433">
                        <rect key="frame" x="0.0" y="0.0" width="580" height="320"/>
                        <clipView key="contentView" drawsBackground="NO" copiesOnScroll="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1Qd-RL-aJ4">
                            <rect key="frame" x="0.0" y="0.0" width="580" height="320"/>
                            <autoresizingMask key="autoresizingMask"/>
                            <subviews>
                                <textView editable="NO" drawsBackground="NO" importsGraphics="NO" richText="NO" verticallyResizable="NO" allowsCharacterPickerTouchBarItem="NO" textCompletion="NO" id="5434" customClass="SPAutosizingTextView" customModule="Sequel_Ace" customModuleProvider="target">
                                    <rect key="frame" x="0.0" y="0.0" width="580" height="320"/>
                                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                                    <color key="textColor" red="0.90196078999999996" green="0.90196078999999996" blue="0.90196078999999996" alpha="1" colorSpace="calibratedRGB"/>
                                    <color key="backgroundColor" name="textBackgroundColor" catalog="System" colorSpace="catalog"/>
                                    <size key="minSize" width="580" height="320"/>
                                    <size key="maxSize" width="1156" height="10000000"/>
                                    <attributedString key="textStorage">
                                        <fragment content="Lorem ipsum dolor sit er ">
                                            <attributes>
                                                <color key="NSColor" red="0.90196078999999996" green="0.90196078999999996" blue="0.90196078999999996" alpha="1" colorSpace="calibratedRGB"/>
                                                <font key="NSFont" metaFont="menu" size="10"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="justified" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO">
                                                    <tabStops>
                                                        <textTab alignment="left" location="0.0">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="56">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="112">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="168">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="224">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="280">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="336">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="392">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="448">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="504">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="560">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="616">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="672">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="728">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="784">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="840">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="896">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="952">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1008">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1064">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1120">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1176">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1232">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1288">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1344">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1400">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1456">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1512">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1568">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1624">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1680">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1736">
                                                            <options/>
                                                        </textTab>
                                                    </tabStops>
                                                </paragraphStyle>
                                            </attributes>
                                        </fragment>
                                        <fragment content="elit">
                                            <attributes>
                                                <color key="NSColor" red="0.90196078999999996" green="0.90196078999999996" blue="0.90196078999999996" alpha="1" colorSpace="calibratedRGB"/>
                                                <font key="NSFont" size="10" name="LucidaGrande-Bold"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="justified" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO">
                                                    <tabStops>
                                                        <textTab alignment="left" location="0.0">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="56">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="112">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="168">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="224">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="280">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="336">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="392">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="448">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="504">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="560">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="616">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="672">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="728">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="784">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="840">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="896">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="952">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1008">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1064">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1120">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1176">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1232">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1288">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1344">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1400">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1456">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1512">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1568">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1624">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1680">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1736">
                                                            <options/>
                                                        </textTab>
                                                    </tabStops>
                                                </paragraphStyle>
                                            </attributes>
                                        </fragment>
                                        <fragment content=" lamet, ">
                                            <attributes>
                                                <color key="NSColor" red="0.90196078999999996" green="0.90196078999999996" blue="0.90196078999999996" alpha="1" colorSpace="calibratedRGB"/>
                                                <font key="NSFont" metaFont="menu" size="10"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="justified" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO">
                                                    <tabStops>
                                                        <textTab alignment="left" location="0.0">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="56">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="112">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="168">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="224">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="280">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="336">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="392">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="448">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="504">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="560">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="616">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="672">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="728">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="784">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="840">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="896">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="952">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1008">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1064">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1120">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1176">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1232">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1288">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1344">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1400">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1456">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1512">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1568">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1624">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1680">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1736">
                                                            <options/>
                                                        </textTab>
                                                    </tabStops>
                                                </paragraphStyle>
                                            </attributes>
                                        </fragment>
                                        <fragment content="consectetaur">
                                            <attributes>
                                                <color key="NSColor" red="0.90196078999999996" green="0.90196078999999996" blue="0.90196078999999996" alpha="1" colorSpace="calibratedRGB"/>
                                                <font key="NSFont" size="10" name="LucidaGrande-Bold"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="justified" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO">
                                                    <tabStops>
                                                        <textTab alignment="left" location="0.0">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="56">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="112">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="168">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="224">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="280">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="336">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="392">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="448">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="504">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="560">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="616">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="672">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="728">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="784">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="840">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="896">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="952">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1008">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1064">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1120">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1176">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1232">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1288">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1344">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1400">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1456">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1512">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1568">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1624">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1680">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1736">
                                                            <options/>
                                                        </textTab>
                                                    </tabStops>
                                                </paragraphStyle>
                                            </attributes>
                                        </fragment>
                                        <fragment content=" cillium adipisicing pecu, sed do ">
                                            <attributes>
                                                <color key="NSColor" red="0.90196078999999996" green="0.90196078999999996" blue="0.90196078999999996" alpha="1" colorSpace="calibratedRGB"/>
                                                <font key="NSFont" metaFont="menu" size="10"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="justified" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO">
                                                    <tabStops>
                                                        <textTab alignment="left" location="0.0">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="56">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="112">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="168">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="224">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="280">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="336">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="392">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="448">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="504">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="560">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="616">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="672">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="728">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="784">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="840">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="896">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="952">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1008">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1064">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1120">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1176">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1232">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1288">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1344">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1400">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1456">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1512">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1568">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1624">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1680">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1736">
                                                            <options/>
                                                        </textTab>
                                                    </tabStops>
                                                </paragraphStyle>
                                            </attributes>
                                        </fragment>
                                        <fragment content="eiusmod">
                                            <attributes>
                                                <color key="NSColor" red="0.90196078999999996" green="0.90196078999999996" blue="0.90196078999999996" alpha="1" colorSpace="calibratedRGB"/>
                                                <font key="NSFont" size="10" name="LucidaGrande-Bold"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="justified" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO">
                                                    <tabStops>
                                                        <textTab alignment="left" location="0.0">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="56">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="112">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="168">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="224">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="280">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="336">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="392">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="448">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="504">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="560">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="616">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="672">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="728">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="784">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="840">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="896">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="952">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1008">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1064">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1120">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1176">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1232">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1288">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1344">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1400">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1456">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1512">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1568">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1624">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1680">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1736">
                                                            <options/>
                                                        </textTab>
                                                    </tabStops>
                                                </paragraphStyle>
                                            </attributes>
                                        </fragment>
                                        <fragment>
                                            <mutableString key="content"> tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum Et harumd und lookum like Greek to me, dereud facilis est er expedit distinct. Nam liber te conscient to factor tum poen legum odioque civiuda</mutableString>
                                            <attributes>
                                                <color key="NSColor" red="0.90196078999999996" green="0.90196078999999996" blue="0.90196078999999996" alpha="1" colorSpace="calibratedRGB"/>
                                                <font key="NSFont" metaFont="menu" size="10"/>
                                                <paragraphStyle key="NSParagraphStyle" alignment="justified" lineBreakMode="wordWrapping" baseWritingDirection="natural" tighteningFactorForTruncation="0.0" allowsDefaultTighteningForTruncation="NO">
                                                    <tabStops>
                                                        <textTab alignment="left" location="0.0">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="56">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="112">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="168">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="224">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="280">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="336">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="392">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="448">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="504">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="560">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="616">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="672">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="728">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="784">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="840">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="896">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="952">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1008">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1064">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1120">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1176">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1232">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1288">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1344">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1400">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1456">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1512">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1568">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1624">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1680">
                                                            <options/>
                                                        </textTab>
                                                        <textTab alignment="left" location="1736">
                                                            <options/>
                                                        </textTab>
                                                    </tabStops>
                                                </paragraphStyle>
                                            </attributes>
                                        </fragment>
                                    </attributedString>
                                    <color key="insertionPointColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                                    <connections>
                                        <outlet property="delegate" destination="-2" id="5462"/>
                                    </connections>
                                </textView>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="0.0" colorSpace="calibratedWhite"/>
                        </clipView>
                        <scroller key="horizontalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" doubleValue="1" horizontal="YES" id="5435">
                            <rect key="frame" x="-100" y="-100" width="87" height="18"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                        <scroller key="verticalScroller" hidden="YES" wantsLayer="YES" verticalHuggingPriority="750" horizontal="NO" id="5436">
                            <rect key="frame" x="564" y="0.0" width="16" height="320"/>
                            <autoresizingMask key="autoresizingMask"/>
                        </scroller>
                    </scrollView>
                </subviews>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="5433" secondAttribute="trailing" id="1ck-nt-LwQ"/>
                    <constraint firstItem="5433" firstAttribute="top" secondItem="5432" secondAttribute="top" id="CuO-aH-Llg"/>
                    <constraint firstAttribute="bottom" secondItem="5433" secondAttribute="bottom" id="RVW-lz-2Af"/>
                    <constraint firstItem="5433" firstAttribute="leading" secondItem="5432" secondAttribute="leading" id="Zzz-a8-uKC"/>
                </constraints>
            </view>
            <connections>
                <outlet property="delegate" destination="-2" id="5461"/>
            </connections>
            <point key="canvasLocation" x="-51" y="-388"/>
        </window>
        <userDefaultsController representsSharedInstance="YES" id="5455"/>
        <customView id="5576" userLabel="SSH Key Selection Help">
            <rect key="frame" x="0.0" y="0.0" width="579" height="83"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <textField focusRingType="none" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="571" translatesAutoresizingMaskIntoConstraints="NO" id="5577">
                    <rect key="frame" x="2" y="42" width="575" height="34"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    <textFieldCell key="cell" controlSize="mini" sendsActionOnEndEditing="YES" alignment="center" id="5578">
                        <font key="font" metaFont="system"/>
                        <string key="title">Please select the SSH key file to use with this connection. Note that standard locations like ~/.ssh are NOT checked automatically, nor are any files in your SSH configuration.</string>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5748">
                    <rect key="frame" x="14" y="14" width="273" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <buttonCell key="cell" type="check" title="Show hidden files" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="5749">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <action selector="updateKeyLocationFileVisibility:" target="-2" id="5755"/>
                        <binding destination="5455" name="value" keyPath="values.KeySelectionHiddenFilesVisibility" id="5754">
                            <dictionary key="options">
                                <integer key="NSNoSelectionPlaceholder" value="0"/>
                                <integer key="NSNotApplicablePlaceholder" value="0"/>
                                <integer key="NSNullPlaceholder" value="0"/>
                            </dictionary>
                        </binding>
                    </connections>
                </button>
            </subviews>
            <point key="canvasLocation" x="139" y="-210"/>
        </customView>
        <customView id="5668" userLabel="SSL Key File Selection Help">
            <rect key="frame" x="0.0" y="0.0" width="579" height="66"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <textField focusRingType="none" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="571" translatesAutoresizingMaskIntoConstraints="NO" id="5669">
                    <rect key="frame" x="2" y="42" width="575" height="17"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    <textFieldCell key="cell" controlSize="mini" sendsActionOnEndEditing="YES" alignment="center" title="Please select the SSL key file to use when establishing a secure connection." id="5670">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5756">
                    <rect key="frame" x="14" y="14" width="273" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <buttonCell key="cell" type="check" title="Show hidden files" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="5757">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <action selector="updateKeyLocationFileVisibility:" target="-2" id="5759"/>
                        <binding destination="5455" name="value" keyPath="values.KeySelectionHiddenFilesVisibility" id="5758">
                            <dictionary key="options">
                                <integer key="NSNoSelectionPlaceholder" value="0"/>
                                <integer key="NSNotApplicablePlaceholder" value="0"/>
                                <integer key="NSNullPlaceholder" value="0"/>
                            </dictionary>
                        </binding>
                    </connections>
                </button>
            </subviews>
        </customView>
        <customView id="5683" userLabel="SSL Certificate File Selection Help">
            <rect key="frame" x="0.0" y="0.0" width="599" height="66"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <textField focusRingType="none" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="572" translatesAutoresizingMaskIntoConstraints="NO" id="5684">
                    <rect key="frame" x="6" y="42" width="576" height="17"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    <textFieldCell key="cell" controlSize="mini" sendsActionOnEndEditing="YES" alignment="center" title="Please select the client SSL certificate file to use when establishing a secure connection." id="5685">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5760">
                    <rect key="frame" x="14" y="14" width="273" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <buttonCell key="cell" type="check" title="Show hidden files" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="5761">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <action selector="updateKeyLocationFileVisibility:" target="-2" id="5763"/>
                        <binding destination="5455" name="value" keyPath="values.KeySelectionHiddenFilesVisibility" id="5762">
                            <dictionary key="options">
                                <integer key="NSNoSelectionPlaceholder" value="0"/>
                                <integer key="NSNotApplicablePlaceholder" value="0"/>
                                <integer key="NSNullPlaceholder" value="0"/>
                            </dictionary>
                        </binding>
                    </connections>
                </button>
            </subviews>
        </customView>
        <customView id="5680" userLabel="SSL CA Cert File Selection Help">
            <rect key="frame" x="0.0" y="0.0" width="579" height="83"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <textField focusRingType="none" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="571" translatesAutoresizingMaskIntoConstraints="NO" id="5681">
                    <rect key="frame" x="2" y="42" width="575" height="34"/>
                    <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                    <textFieldCell key="cell" controlSize="mini" sendsActionOnEndEditing="YES" alignment="center" id="5682">
                        <font key="font" metaFont="system"/>
                        <string key="title">Please select the client SSL Certificate Authority certificate to use when establishing a secure connection.  This must be the same as the server CA certificate.</string>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
                <button fixedFrame="YES" translatesAutoresizingMaskIntoConstraints="NO" id="5764">
                    <rect key="frame" x="14" y="14" width="273" height="18"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                    <buttonCell key="cell" type="check" title="Show hidden files" bezelStyle="regularSquare" imagePosition="left" alignment="left" inset="2" id="5765">
                        <behavior key="behavior" changeContents="YES" doesNotDimImage="YES" lightByContents="YES"/>
                        <font key="font" metaFont="system"/>
                    </buttonCell>
                    <connections>
                        <action selector="updateKeyLocationFileVisibility:" target="-2" id="5767"/>
                        <binding destination="5455" name="value" keyPath="values.KeySelectionHiddenFilesVisibility" id="5766">
                            <dictionary key="options">
                                <integer key="NSNoSelectionPlaceholder" value="0"/>
                                <integer key="NSNotApplicablePlaceholder" value="0"/>
                                <integer key="NSNullPlaceholder" value="0"/>
                            </dictionary>
                        </binding>
                    </connections>
                </button>
            </subviews>
        </customView>
        <menu id="5788" userLabel="Context Menu">
            <items>
                <menuItem title="Rename" id="5795">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="renameNode:" target="-2" id="5819"/>
                    </connections>
                </menuItem>
                <menuItem title="Delete" id="5794">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="removeNode:" target="-2" id="5802"/>
                    </connections>
                </menuItem>
                <menuItem isSeparatorItem="YES" id="5791"/>
                <menuItem title="Duplicate" id="5789">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="duplicateFavorite:" target="-2" id="5803"/>
                    </connections>
                </menuItem>
                <menuItem title="Make Default" id="5790">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="makeSelectedFavoriteDefault:" target="-2" id="5804"/>
                    </connections>
                </menuItem>
                <menuItem isSeparatorItem="YES" id="5793"/>
                <menuItem title="Export..." id="5792">
                    <modifierMask key="keyEquivalentModifierMask"/>
                    <connections>
                        <action selector="exportFavorites:" target="-2" id="5805"/>
                    </connections>
                </menuItem>
            </items>
            <connections>
                <outlet property="delegate" destination="-2" id="5818"/>
            </connections>
        </menu>
        <customView id="5796" userLabel="AccessoryView">
            <rect key="frame" x="0.0" y="0.0" width="365" height="52"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
            <subviews>
                <textField focusRingType="none" verticalHuggingPriority="750" horizontalCompressionResistancePriority="250" fixedFrame="YES" preferredMaxLayoutWidth="367" translatesAutoresizingMaskIntoConstraints="NO" id="5797">
                    <rect key="frame" x="-3" y="12" width="371" height="34"/>
                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMinY="YES"/>
                    <textFieldCell key="cell" sendsActionOnEndEditing="YES" alignment="center" title="For security reasons, your connection favorite passwords are not included in the export." id="5798">
                        <font key="font" metaFont="system"/>
                        <color key="textColor" name="controlTextColor" catalog="System" colorSpace="catalog"/>
                        <color key="backgroundColor" name="controlColor" catalog="System" colorSpace="catalog"/>
                    </textFieldCell>
                </textField>
            </subviews>
            <point key="canvasLocation" x="-101" y="474"/>
        </customView>
    </objects>
    <resources>
        <image name="Add_folder" width="32" height="32"/>
        <image name="NSActionTemplate" width="20" height="20"/>
        <image name="NSAddTemplate" width="18" height="17"/>
        <image name="button_bar_handleTemplate" width="6" height="10"/>
        <image name="key-icon" width="16" height="9"/>
        <image name="key-icon-alternate" width="16" height="9"/>
    </resources>
</document>
