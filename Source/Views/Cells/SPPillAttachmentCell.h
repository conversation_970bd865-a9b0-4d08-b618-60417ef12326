//
//  SPPillAttachmentCell.h
//  sequel-pro
//
//  Created by <PERSON> on 01.11.15.
//  Copyright (c) 2015 <PERSON>. All rights reserved.
//
//  Permission is hereby granted, free of charge, to any person
//  obtaining a copy of this software and associated documentation
//  files (the "Software"), to deal in the Software without
//  restriction, including without limitation the rights to use,
//  copy, modify, merge, publish, distribute, sublicense, and/or sell
//  copies of the Software, and to permit persons to whom the
//  Software is furnished to do so, subject to the following
//  conditions:
//
//  The above copyright notice and this permission notice shall be
//  included in all copies or substantial portions of the Software.
//
//  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
//  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
//  OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
//  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
//  HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
//  WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
//  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
//  OTHER DEALINGS IN THE SOFTWARE.
//
//  More info at <https://github.com/sequelpro/sequelpro>

#import <Foundation/Foundation.h>

/**
 * This is a simple TextAttachmentCell which takes the stringValue
 * and displays it in a "pill", much like the tokens in an NSTokenTextField.
 *
 * It is designed to be used with static labels rather than inside user input text fields.
 *
 * To use it:
 *
 * NSTextAttachment *attachment = [[NSTextAttachment alloc] init];
 * SPPillAttachmentCell *cell   = [[SPPillAttachmentCell alloc] init];
 * [cell setStringValue:@"..."];
 * attachment setAttachmentCell:[cell;
 * NSAttributedString *att = ];
 * [otherAttributedString appendAttributedString:att];
 *
 */
@interface SPPillAttachmentCell : NSTextAttachmentCell {
    NSColor *_borderColor;
    NSGradient *_gradient;
}
@end
