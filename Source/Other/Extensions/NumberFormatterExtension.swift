//
//  NumberFormatterExtention.swift
//  Sequel Ace
//
//  Created by <PERSON> on 3/11/2020.
//  Copyright © 2020-2022 Sequel-Ace. All rights reserved.
//

import Foundation

extension NumberFormatter {
	@objc public static var decimalStyleFormatter: NumberFormatter = {
		let formatter = NumberFormatter()
		formatter.numberStyle = .decimal
		return formatter
	}()

	@objc public static func formatter(format: String) -> NumberFormatter {
		let formatter = NumberFormatter()
		formatter.numberStyle = .decimal
		formatter.format = format
		return formatter
	}
}
