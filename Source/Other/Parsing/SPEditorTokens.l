%{

//
//  SPEditorTokens.l
//  sequel-pro
//
//  Created by <PERSON> on March 15, 2009.
//  Copyright (c) 2009 Jakob. All rights reserved.
//
//  Permission is hereby granted, free of charge, to any person
//  obtaining a copy of this software and associated documentation
//  files (the "Software"), to deal in the Software without
//  restriction, including without limitation the rights to use,
//  copy, modify, merge, publish, distribute, sublicense, and/or sell
//  copies of the Software, and to permit persons to whom the
//  Software is furnished to do so, subject to the following
//  conditions:
//
//  The above copyright notice and this permission notice shall be
//  included in all copies or substantial portions of the Software.
//
//  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
//  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
//  OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
//  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
//  HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
//  WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
//  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
//  OTHER DEALINGS IN THE SOFTWARE.
//
//  More info at <https://github.com/sequelpro/sequelpro>

/**
 * This is the lex file used for syntax coloring.
 * To add new keywords, just add a line where the other
 * keywords are and replace spaces with {s}
 *
 * If you're new to lex and interested what the code below does, I found
 * "The Lex And Yacc Page" at http://dinosaur.compilertools.net/ to be
 * very helpful. Keep in mind that Xcode actually uses flex, the GNU
 * version of lex. There's a very thorough Texinfo manual for flex
 * available. (type 'info flex' in the Terminal)
 */
 
#import "SPEditorTokens.h"
#include "SPParserUtils.h"

size_t yyuoffset, yyuleng;

//keep track of the current utf-8 character (not byte) offset and token length
#define YY_USER_ACTION { yyuoffset += yyuleng; yyuleng = utf8strlen(yytext); }
%}
%option noyywrap
%option nounput
%option noinput
%option case-insensitive

s		[ \t\n\r]+
alpha	[a-z_\.\x80-\xEF]
numeric	([+-]?(([0-9]+\.[0-9]+)|([0-9]*\.[0-9]+)|([0-9]+))(e[+-]?[0-9]+)?)
ops		"+"|"-"|"*"|"/"
word	[a-z_\.0-9\x80-\xEF@]
variable	@{1,2}[a-z_\.0-9\x80-\xEF$]+
keyworda	(G(R(OUP{s}BY|ANT(S)?)|E(NERAL|T_FORMAT|OMETRY(COLLECTION)?)|LOBAL)|B(Y(TE)?|TREE|I(GINT|N(LOG|ARY)|T)|O(TH|OL(EAN)?)|E(GIN|TWEEN|FORE)|LOB|ACKUP{s}TABLE)|H(IGH_PRIORITY|O(ST(S)?|UR(_(MI(NUTE|CROSECOND)|SECOND))?)|ELP|A(SH|NDLER|VING))|C(R(OSS|EATE)|H(ECK(SUM)?|A(R(SET|ACTER)?|NGE(D)?|IN))|IPHER|O(M(M(IT(TED)?|ENT)|P(RESSED|LETION|ACT))|N(S(TRAINT(_(SCHEMA|NAME|CATALOG))?|ISTENT)|NECTION|CURRENT|T(RIBUTORS|INUE|AINS)|DITION|VERT)|DE|L(UMN(S|_(NAME|FORMAT))?|LATE)|ALESCE{s}PARTITION)|U(R(RENT_(TIME(STAMP)?|DATE|USER)|SOR(_NAME)?)|BE)|L(IENT|OSE|ASS_ORIGIN)|A(S(CADE(D)?|E)|CHE{s}INDEX|TALOG_NAME|LL))|I(GNORE(_SERVER_IDS)?|MPORT{s}TABLESPACE|S(SUER|OLATION)?|N(S(TALL({s}PLUGIN)?|E(RT(_METHOD)?|NSITIVE))|N(O(BASE|DB)|ER)|T(1|2|8|3|O({s}(DUMP|OUT)FILE)?|4|E(RVAL|GER))?|ITIAL_SIZE|OUT|DEX(ES)?|VOKER|FILE)?|TERATE|O_THREAD|DENTIFIED|F)|D(ROP|YNAMIC|I(RECTORY|S(CARD{s}TABLESPACE|TINCT(ROW)?|K|ABLE{s}KEYS)|V)|O(UBLE)?|U(MPFILE|PLICATE|AL)|E(S(C(RIBE)?|_KEY_FILE)|C(IMAL|LARE)?|TERMINISTIC|F(INER|AULT)|L(ETE|AY(_KEY_WRITE|ED))|ALLOCATE)|A(Y(_(MI(NUTE|CROSECOND)|SECOND|HOUR))?|T(E(TIME)?|A(BASE(S)?|FILE)?)))|J(OI|SO)N|E(RRORS|X(TEN(T_SIZE|DED)|I(STS|T)|P(LAIN|ANSION)|ECUTE)|SCAPE(D{s}BY)?|N(GINE(S)?|CLOSED{s}BY|D(S)?|UM|ABLE{s}KEYS)|VE(RY|NT)|LSE(IF)?|ACH)|K(ILL({s}(CONNECTION|QUERY))?|EY(S|_BLOCK_SIZE)?)|F(R(OM|AC_SECOND)|I(RST|XED|LE)|O(R(CE|EIGN)?|UND)|U(NCTION|LL(TEXT)?)|ETCH|L(OAT(8|4)?|USH)|A(ST|LSE))|A(G(GREGATE|AINST)|S(C(II)?|ENSITIVE)?|N(Y|D|ALYZE)|C(CESSIBLE|TION)|T|DD|UT(HORS|O(_INCREMENT|EXTEND_SIZE))|VG(_ROW_LENGTH)?|FTER|L(GORITHM|TER|L)))
keywordl	(R(TREE|IGHT|O(UTINE|W(S|_FORMAT)?|LL(BACK|UP))|E(GEXP|MOVE{s}PARTITIONING|BUILD{s}PARTITION|S(T(RICT|ORE{s}TABLE)|IGNAL|UME|ET)|NAME|COVER|TURN(S)?|ORGANIZE{s}PARTITION|D(O(_BUFFER_SIZE|FILE)|UNDANT)|P(EAT(ABLE)?|L(ICATION|ACE)|AIR)|VOKE|QUIRE|FERENCES|L(OAD|EASE|AY_(THREAD|LOG_(POS|FILE)))|A(D(S|_(ONLY|WRITE))?|L))|LIKE|ANGE)|M(YSQL_ERRNO|I(GRATE|N(_ROWS|UTE(_(MICROSECOND|SECOND))?)|CROSECOND|DDLEINT)|O(NTH|D(IF(Y|IES)|E)?)|U(TEX|LTI(PO(INT|LYGON)|LINESTRING))|E(RGE|MORY|SSAGE_TEXT|DIUM(BLOB|TEXT|INT)?)|A(X(_(ROWS|SIZE|CONNECTIONS_PER_HOUR|U(SER_CONNECTIONS|PDATES_PER_HOUR)|QUERIES_PER_HOUR)|VALUE)|STER(_(S(SL(_(C(IPHER|ERT|A(PATH)?)|VERIFY_SERVER_CERT|KEY))?|ERVER_ID)|H(OST|EARTBEAT_PERIOD)|CONNECT_RETRY|USER|P(ORT|ASSWORD)|LOG_(POS|FILE)))?|TCH))|N(CHAR|O(NE|_W(RITE_TO_BINLOG|AIT)|T|DEGROUP)?|DB(CLUSTER)?|U(MERIC|LL)|E(XT|W)|VARCHAR|A(ME(S)?|T(IONAL|URAL)))|O(R(DER{s}BY)?|N(({s}DUPLICATE{s}KEY{s}UPDATE)?|E(_SHOT)?|LINE)?|UT(ER|FILE)?|P(TI(MIZE|ON(S|ALLY)?)|EN)|FF(SET|LINE)|WNER|LD_PASSWORD)|P(R(I(MARY|VILEGES)|OCE(SS|DURE{s}(ANALYSE)?)|E(SERVE|CISION|PARE|V))|HASE|O(RT|INT|LYGON)|URGE|A(R(SER|TI(TION(S|ING)?|AL))|SSWORD|CK_KEYS))|QU(ICK|ERY|ARTER)|L(I(MIT|ST|NE(S(TRING)?|AR)|KE)|O(G(S|FILE({s}GROUP))|NG(BLOB|TEXT)?|C(K(S)?|AL(TIME(STAMP)?)?)|OP|W_PRIORITY|AD{s}(DATA|INDEX{s}INTO{s}CACHE|XML))|E(SS|VEL|FT|A(DING|VE(S)?))|A(ST|NGUAGE)))
keywords	(X(OR|509|A)|S(MALLINT|SL|H(OW({s}(E(NGINE(S)?|RRORS)|M(ASTER|UTEX)|BINLOG|GRANTS|INNODB|P(RIVILEGES|ROFILE(S)?|ROCEDURE{s}CODE)|SLAVE{s}(HOSTS|STATUS)|TRIGGERS|VARIABLES|WARNINGS|(FULL{s})?PROCESSLIST|FIELDS|PLUGIN(S)?|STORAGE{s}ENGINES|TABLE{s}TYPES|CO(LUMNS|LLATION)|BINLOG{s}EVENTS))?|UTDOWN|ARE)|NAPSHOT|CHE(MA(S|_NAME)?|DULE(R)?)|T(R(ING|AIGHT_JOIN)|O(RAGE|P)|A(RT(S|ING{s}BY)?|TUS))|I(GN(ED|AL)|MPLE)|O(ME|NAME|CKET|UNDS)|U(B(CLASS_ORIGIN|JECT|PARTITION(S)?)|SPEND|PER)|P(ECIFIC|ATIAL)|E(R(IAL(IZABLE)?|VER)|SSION|NSITIVE|C(OND(_MICROSECOND)?|URITY)|T({s}(PASSWORD|NAMES|ONE_SHOT))?|PARATOR|LECT)|QL(STATE|_(MAX_JOIN_SIZE|B(IG_(RESULT|SELECTS|TABLES)|UFFER_RESULT)|S(MALL_RESULT|ELECT_LIMIT|LAVE_SKIP_COUNTER|AFE_UPDATES)|NO_CACHE|CA(CHE|LC_FOUND_ROWS)|T(SI_(M(INUTE|ONTH)|SECOND|HOUR|YEAR|DAY|QUARTER|FRAC_SECOND|WEEK)|HREAD)|QUOTE_SHOW_CREATE|WARNINGS|LO(G_(BIN|OFF|UPDATE)|W_PRIORITY_UPDATES)|AUTO_IS_NULL)|EXCEPTION|WARNING)?|L(OW|AVE)|AVEPOINT)|YEAR(_MONTH)?|T(R(IGGER(S)?|U(NCATE|E)|A(NSACTION|ILING))|H(EN|AN)|YPE|I(ME(STAMP(DIFF|ADD)?)?|NY(BLOB|TEXT|INT))|O|E(RMINATED{s}BY|XT|MP(TABLE|ORARY))|ABLE(S(PACE)?|_NAME)?)|ZEROFILL|U(S(ING|E(R(_RESOURCES)?|_FRM)?|AGE)|N(SIGNED|COMMITTED|TIL|I(NSTALL({s}PLUGIN)?|CODE|ON|QUE)|D(O(_BUFFER_SIZE|FILE)?|EFINED)|KNOWN|LOCK)|TC_(TIME(STAMP)?|DATE)|P(GRADE|DATE))|V(IEW|A(R(BINARY|YING|CHAR(ACTER)?|IABLES)|LUE(S)?))|W(R(ITE|APPER)|H(ILE|E(RE|N))|ITH({s}PARSER)?|ORK|EEK|A(RNINGS|IT)))

%x comment
%x snippet
%x equation
%x varequation
%%

\$\{1?[0-9]:						{ BEGIN(snippet); return SPT_OTHER; }
<snippet>{word}						{ return SPT_VARIABLE; }
<snippet>\}							{ BEGIN(INITIAL); return SPT_OTHER; }

\"([^"\\]|\\(.|[\n\r]))*\"?			{ return SPT_DOUBLE_QUOTED_TEXT;   }      /* double quoted strings          */
'([^'\\]|\\(.|[\n\r]))*'?			{ return SPT_SINGLE_QUOTED_TEXT;   }      /* single quoted strings          */
`(``|[^`])*`?						{ return SPT_BACKTICK_QUOTED_TEXT; }      /* backtick quoted string         */

"/*"								{ BEGIN(comment); return SPT_COMMENT; }   /* beginning of a c style comment */
<comment>[^*]* 						{ return SPT_COMMENT; }                   /* anything except * in a c cmnt  */
<comment>"*"+						{ return SPT_COMMENT; }                   /* a range of *                   */
<comment>"*"+"/" 					{ BEGIN(INITIAL); return SPT_COMMENT; }   /* a range of * with trailing /   
                                                                             Thanks to John Dickinson for publishing 
                                                                             this method of parsing C comments on
                                                                             http://www.stillhq.com/pdfdb/000561/data.pdf
                                                                                                            */
                                                                                                            
#[^\n\r]*(\n|\r)?			        |											/* # Comments                     */
--[\n\r]						{ return SPT_COMMENT; }							/* --                             */
--[ \t][^\n\r]*(\n|\r)?			{ return SPT_COMMENT; }							/* -- Comments                    */

{variable}/{ops}			{ BEGIN(varequation); return SPT_VARIABLE; }/* SQL variables before operator*/
<varequation>{ops}			{ BEGIN(INITIAL); return SPT_OTHER; }
{variable}					{ return SPT_VARIABLE; }					/* SQL variables */

{numeric}/{ops}				{ BEGIN(equation); return SPT_NUMERIC; }	/* numeric before operator        */
<equation>{ops}				{ BEGIN(INITIAL); return SPT_OTHER; }		/* set operator after a numeric   */
{numeric}/{alpha}			{ return SPT_WORD; }						/* catch numeric followed by char */

{s}+						{ return SPT_WHITESPACE; }					/* ignore spaces                  */

{keyworda}					{ return SPT_RESERVED_WORD; }				/* all the mysql reserved words   */
{keywordl}					{ return SPT_RESERVED_WORD; }				/* all the mysql reserved words   */
{keywords}					{ return SPT_RESERVED_WORD; }				/* all the mysql reserved words   */

{numeric}					{ return SPT_NUMERIC; }						/* single numeric value           */

{word}+						{ return SPT_WORD; }						/* return any word                */

.							{ return SPT_OTHER; }						/* return anything else           */

                    			
<<EOF>>   						{
                                    BEGIN(INITIAL);   /* make sure we return to initial state when finished! */
            						yy_delete_buffer(YY_CURRENT_BUFFER);
            						return 0;
          						}
%%

/* un-optimized keywords:
ACCESSIBLE
ACTION
ADD
AFTER
AGAINST
AGGREGATE
ALGORITHM
ALL
ALTER
ANALYZE
AND
ANY
AS
ASC
ASCII
ASENSITIVE
AT
AUTHORS
AUTOEXTEND_SIZE
AUTO_INCREMENT
AVG
AVG_ROW_LENGTH
BACKUP{s}TABLE
BEFORE
BEGIN
BETWEEN
BIGINT
BINARY
BINLOG
BIT
BLOB
BOOL
BOOLEAN
BOTH
BTREE
BY
BYTE
CACHE{s}INDEX
CALL
CASCADE
CASCADED
CASE
CATALOG_NAME
CHAIN
CHANGE
CHANGED
CHAR
CHARACTER
CHARSET
CHECK
CHECKSUM
CIPHER
CLASS_ORIGIN
CLIENT
CLOSE
COALESCE{s}PARTITION
CODE
COLLATE
COLUMN
COLUMN_FORMAT
COLUMN_NAME
COLUMNS
COMMENT
COMMIT
COMMITTED
COMPACT
COMPLETION
COMPRESSED
CONCURRENT
CONDITION
CONNECTION
CONSISTENT
CONSTRAINT
CONSTRAINT_CATALOG
CONSTRAINT_NAME
CONSTRAINT_SCHEMA
CONTAINS
CONTINUE
CONTRIBUTORS
CONVERT
CREATE
CROSS
CUBE
CURRENT_DATE
CURRENT_TIME
CURRENT_TIMESTAMP
CURRENT_USER
CURSOR
CURSOR_NAME
DATA
DATABASE
DATABASES
DATAFILE
DATE
DATETIME
DAY
DAY_HOUR
DAY_MICROSECOND
DAY_MINUTE
DAY_SECOND
DEALLOCATE
DEC
DECIMAL
DECLARE
DEFAULT
DEFINER
DELAYED
DELAY_KEY_WRITE
DELETE
DESC
DESCRIBE
DES_KEY_FILE
DETERMINISTIC
DIRECTORY
DISABLE{s}KEYS
DISCARD{s}TABLESPACE
DISK
DISTINCT
DISTINCTROW
DIV
DO
DOUBLE
DROP
DUAL
DUMPFILE
DUPLICATE
DYNAMIC
EACH
ELSE
ELSEIF
ENABLE{s}KEYS
ENCLOSED{s}BY
END
ENDS
ENGINE
ENGINES
ENUM
ERRORS
ESCAPE
ESCAPED{s}BY
EVENT
EVERY
EXECUTE
EXISTS
EXIT
EXPANSION
EXPLAIN
EXTENDED
EXTENT_SIZE
FALSE
FAST
FETCH
FILE
FIRST
FIXED
FLOAT
FLOAT4
FLOAT8
FLUSH
FOR
FORCE
FOREIGN
FOUND
FRAC_SECOND
FROM
FULL
FULLTEXT
FUNCTION
GENERAL
GEOMETRY
GEOMETRYCOLLECTION
GET_FORMAT
GLOBAL
GRANT
GRANTS
GROUP{s}BY
HANDLER
HASH
HAVING
HELP
HIGH_PRIORITY
HOST
HOSTS
HOUR
HOUR_MICROSECOND
HOUR_MINUTE
HOUR_SECOND
IDENTIFIED
IF
IGNORE
IGNORE_SERVER_IDS
IMPORT{s}TABLESPACE
IN
INDEX
INDEXES
INFILE
INITIAL_SIZE
INNER
INNOBASE
INNODB
INOUT
INSENSITIVE
INSERT
INSERT_METHOD
INSTALL({s}PLUGIN)?
INT
INT1
INT2
INT3
INT4
INT8
INTEGER
INTERVAL
INTO({s}(DUMP|OUT)FILE)?
INVOKER
IO_THREAD
IS
ISOLATION
ISSUER
ITERATE
JOIN
JSON
KEY
KEYS
KEY_BLOCK_SIZE
KILL({s}(CONNECTION|QUERY))?
LANGUAGE
LAST
LEADING
LEAVE
LEAVES
LEFT
LESS
LEVEL
LIKE
LIMIT
LINEAR
LINES
LINESTRING
LIST
LOAD{s}(DATA|INDEX{s}INTO{s}CACHE|XML)
LOCAL
LOCALTIME
LOCALTIMESTAMP
LOCK
LOCKS
LOGFILE({s}GROUP)
LOGS
LONG
LONGBLOB
LONGTEXT
LOOP
LOW_PRIORITY
MASTER
MASTER_CONNECT_RETRY
MASTER_HEARTBEAT_PERIOD
MASTER_HOST
MASTER_LOG_FILE
MASTER_LOG_POS
MASTER_PASSWORD
MASTER_PORT
MASTER_SERVER_ID
MASTER_SSL
MASTER_SSL_CA
MASTER_SSL_CAPATH
MASTER_SSL_CERT
MASTER_SSL_CIPHER
MASTER_SSL_KEY
MASTER_SSL_VERIFY_SERVER_CERT
MASTER_USER
MATCH
MAXVALUE
MAX_CONNECTIONS_PER_HOUR
MAX_QUERIES_PER_HOUR
MAX_ROWS
MAX_SIZE
MAX_UPDATES_PER_HOUR
MAX_USER_CONNECTIONS
MEDIUM
MEDIUMBLOB
MEDIUMINT
MEDIUMTEXT
MEMORY
MERGE
MESSAGE_TEXT
MICROSECOND
MIDDLEINT
MIGRATE
MINUTE
MINUTE_MICROSECOND
MINUTE_SECOND
MIN_ROWS
MOD
MODE
MODIFIES
MODIFY
MONTH
MULTILINESTRING
MULTIPOINT
MULTIPOLYGON
MUTEX
MYSQL_ERRNO
NAME
NAMES
NATIONAL
NATURAL
NCHAR
NDB
NDBCLUSTER
NEW
NEXT
NO
NODEGROUP
NONE
NOT
NO_WAIT
NO_WRITE_TO_BINLOG
NULL
NUMERIC
NVARCHAR
OFFLINE
OFFSET
OLD_PASSWORD
ONE
ONE_SHOT
ONLINE
ON{s}(DUPLICATE{s}KEY{s}UPDATE)?
OPEN
OPTIMIZE
OPTION
OPTIONALLY
OPTIONS
OR
ORDER{s}BY
OUT
OUTER
OUTFILE
OWNER
PACK_KEYS
PARSER
PARTIAL
PARTITION
PARTITIONING
PARTITIONS
PASSWORD
PHASE
POINT
POLYGON
PORT
PRECISION
PREPARE
PRESERVE
PREV
PRIMARY
PRIVILEGES
PROCEDURE{s}(ANALYSE)?
PROCESS
PURGE
QUARTER
QUERY
QUICK
RANGE
READ
READS
READ_ONLY
READ_WRITE
REAL
REBUILD{s}PARTITION
RECOVER
REDOFILE
REDO_BUFFER_SIZE
REDUNDANT
REFERENCES
REGEXP
RELAY_LOG_FILE
RELAY_LOG_POS
RELAY_THREAD
RELEASE
RELOAD
REMOVE{s}PARTITIONING
RENAME
REORGANIZE{s}PARTITION
REPAIR
REPEAT
REPEATABLE
REPLACE
REPLICATION
REQUIRE
RESET
RESIGNAL
RESTORE{s}TABLE
RESTRICT
RESUME
RETURN
RETURNS
REVOKE
RIGHT
RLIKE
ROLLBACK
ROLLUP
ROUTINE
ROW
ROWS
ROW_FORMAT
RTREE
SAVEPOINT
SCHEDULE
SCHEDULER
SCHEMA
SCHEMAS
SCHEMA_NAME
SECOND
SECOND_MICROSECOND
SECURITY
SELECT
SENSITIVE
SEPARATOR
SERIAL
SERIALIZABLE
SERVER
SESSION
SET({s}(PASSWORD|NAMES|ONE_SHOT))?
SHARE
SHOW({s}(E(NGINE(S)?|RRORS)|M(ASTER|UTEX)|BINLOG|GRANTS|INNODB|P(RIVILEGES|ROFILE(S)?|ROCEDURE{s}CODE)|SLAVE{s}(HOSTS|STATUS)|TRIGGERS|VARIABLES|WARNINGS|(FULL{s})?PROCESSLIST|FIELDS|PLUGIN(S)?|STORAGE{s}ENGINES|TABLE{s}TYPES|CO(LUMNS|LLATION)|BINLOG{s}EVENTS))?
SHUTDOWN
SIGNAL
SIGNED
SIMPLE
SLAVE
SLOW
SMALLINT
SNAPSHOT
SOCKET
SOME
SONAME
SOUNDS
SPATIAL
SPECIFIC
SQL
SQLEXCEPTION
SQLSTATE
SQLWARNING
SQL_AUTO_IS_NULL
SQL_BIG_RESULT
SQL_BIG_SELECTS
SQL_BIG_TABLES
SQL_BUFFER_RESULT
SQL_CACHE
SQL_CALC_FOUND_ROWS
SQL_LOG_BIN
SQL_LOG_OFF
SQL_LOG_UPDATE
SQL_LOW_PRIORITY_UPDATES
SQL_MAX_JOIN_SIZE
SQL_NO_CACHE
SQL_QUOTE_SHOW_CREATE
SQL_SAFE_UPDATES
SQL_SELECT_LIMIT
SQL_SLAVE_SKIP_COUNTER
SQL_SMALL_RESULT
SQL_THREAD
SQL_TSI_DAY
SQL_TSI_FRAC_SECOND
SQL_TSI_HOUR
SQL_TSI_MINUTE
SQL_TSI_MONTH
SQL_TSI_QUARTER
SQL_TSI_SECOND
SQL_TSI_WEEK
SQL_TSI_YEAR
SQL_WARNINGS
SSL
START
STARTING{s}BY
STARTS
STATUS
STOP
STORAGE
STRAIGHT_JOIN
STRING
SUBCLASS_ORIGIN
SUBJECT
SUBPARTITION
SUBPARTITIONS
SUPER
SUSPEND
TABLE
TABLES
TABLESPACE
TABLE_NAME
TEMPORARY
TEMPTABLE
TERMINATED{s}BY
TEXT
THAN
THEN
TIME
TIMESTAMP
TIMESTAMPADD
TIMESTAMPDIFF
TINYBLOB
TINYINT
TINYTEXT
TO
TRAILING
TRANSACTION
TRIGGER
TRIGGERS
TRUE
TRUNCATE
TYPE
UNCOMMITTED
UNDEFINED
UNDO
UNDOFILE
UNDO_BUFFER_SIZE
UNICODE
UNINSTALL({s}PLUGIN)?
UNION
UNIQUE
UNKNOWN
UNLOCK
UNSIGNED
UNTIL
UPDATE
UPGRADE
USAGE
USE
USER
USER_RESOURCES
USE_FRM
USING
UTC_DATE
UTC_TIME
UTC_TIMESTAMP
VALUE
VALUES
VARBINARY
VARCHAR
VARCHARACTER
VARIABLES
VARYING
VIEW
WAIT
WARNINGS
WEEK
WHEN
WHERE
WHILE
WITH({s}PARSER)?
WORK
WRAPPER
WRITE
X509
XA
XOR
YEAR
YEAR_MONTH
ZEROFILL
*/
