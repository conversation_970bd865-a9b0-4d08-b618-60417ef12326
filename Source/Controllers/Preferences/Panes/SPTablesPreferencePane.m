//
//  SPTablesPreferencePane.m
//  sequel-pro
//
//  Created by <PERSON> (stuconnolly.com) on October 31, 2010.
//  Copyright (c) 2010 <PERSON>. All rights reserved.
//
//  Permission is hereby granted, free of charge, to any person
//  obtaining a copy of this software and associated documentation
//  files (the "Software"), to deal in the Software without
//  restriction, including without limitation the rights to use,
//  copy, modify, merge, publish, distribute, sublicense, and/or sell
//  copies of the Software, and to permit persons to whom the
//  Software is furnished to do so, subject to the following
//  conditions:
//
//  The above copyright notice and this permission notice shall be
//  included in all copies or substantial portions of the Software.
//
//  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
//  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
//  OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
//  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
//  HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
//  WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
//  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
//  OTHER DEALINGS IN THE SOFTWARE.
//
//  More info at <https://github.com/sequelpro/sequelpro>

#import "SPTablesPreferencePane.h"
#import "SPPreferenceController.h"

@implementation SPTablesPreferencePane

#pragma mark -
#pragma mark Preference pane protocol methods

- (NSView *)preferencePaneView
{
	return [self view];
}

- (NSImage *)preferencePaneIcon
{
	if (@available(macOS 11.0, *)) {
		return [NSImage imageWithSystemSymbolName:@"tablecells" accessibilityDescription:nil];
	} else {
		return [NSImage imageNamed:@"toolbar-preferences-tables"];
	}
}

- (NSString *)preferencePaneName
{
	return NSLocalizedString(@"Tables", @"tables preference pane name");
}

- (NSString *)preferencePaneIdentifier
{
	return SPPreferenceToolbarTables;
}

- (NSString *)preferencePaneToolTip
{
	return NSLocalizedString(@"Table Preferences", @"general preference pane tooltip");
}

- (void)preferencePaneWillBeShown
{
}

@end
